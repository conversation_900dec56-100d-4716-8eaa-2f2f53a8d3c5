/**
 * Store service for managing products, cart, and orders
 * Handles e-commerce functionality with offline support
 * This service now acts as a facade for the comprehensive e-commerce services
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import {
    Product,
    ProductCategory,
    ProductSearchParams,
    CartItem,
    ShoppingCart,
    Order,
    ProductRecommendation,
    ProductFilter,
    ShippingAddress,
    PaymentMethod,
} from '../types/product';
import { productCatalogService } from './productCatalog';
import { shoppingCartService } from './shoppingCart';
import { inventoryManagerService } from './inventoryManager';
import { aiRecommendationsService, RecommendationContext } from './aiRecommendations';

// Mock data for development - replace with actual API calls
const MOCK_PRODUCTS: Product[] = [
    {
        id: '1',
        name: 'Hybrid Corn Seeds',
        description: 'High-yield hybrid corn seeds suitable for various soil types. Drought resistant with excellent germination rate.',
        category: 'seeds',
        price: 25.99,
        originalPrice: 29.99,
        currency: 'USD',
        imageUrls: [
            'https://images.unsplash.com/photo-1551836022-deb4988cc6c0?w=400',
            'https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=400',
        ],
        specifications: [
            { name: 'Variety', value: 'Hybrid' },
            { name: 'Maturity', value: '90-95 days' },
            { name: 'Yield', value: '8-10 tons/hectare' },
            { name: 'Planting Season', value: 'Spring/Summer' },
        ],
        stockQuantity: 150,
        rating: 4.5,
        reviewCount: 89,
        tags: ['drought-resistant', 'high-yield', 'hybrid'],
        brand: 'AgriSeeds Pro',
        isRecommended: true,
        isFeatured: true,
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-20'),
    },
    {
        id: '2',
        name: 'Organic Tomato Seeds',
        description: 'Premium organic tomato seeds for greenhouse and field cultivation. Disease resistant variety.',
        category: 'seeds',
        price: 12.50,
        currency: 'USD',
        imageUrls: [
            'https://images.unsplash.com/photo-1592841200221-a6898f307baa?w=400',
        ],
        specifications: [
            { name: 'Type', value: 'Organic' },
            { name: 'Variety', value: 'Determinate' },
            { name: 'Fruit Size', value: 'Medium' },
            { name: 'Days to Harvest', value: '75-80 days' },
        ],
        stockQuantity: 200,
        rating: 4.7,
        reviewCount: 156,
        tags: ['organic', 'disease-resistant', 'greenhouse'],
        brand: 'EcoGrow',
        isRecommended: false,
        isFeatured: false,
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-18'),
    },
    {
        id: '3',
        name: 'NPK Fertilizer 20-20-20',
        description: 'Balanced NPK fertilizer suitable for all crops. Water-soluble formula for easy application.',
        category: 'fertilizers',
        price: 45.00,
        currency: 'USD',
        imageUrls: [
            'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400',
        ],
        specifications: [
            { name: 'N-P-K Ratio', value: '20-20-20' },
            { name: 'Form', value: 'Water Soluble' },
            { name: 'Package Size', value: '25 kg' },
            { name: 'Application Rate', value: '2-3 kg/hectare' },
        ],
        stockQuantity: 75,
        rating: 4.3,
        reviewCount: 67,
        tags: ['balanced', 'water-soluble', 'all-crops'],
        brand: 'FertMax',
        isRecommended: true,
        isFeatured: false,
        createdAt: new Date('2024-01-12'),
        updatedAt: new Date('2024-01-19'),
    },
    {
        id: '4',
        name: 'Garden Hand Trowel',
        description: 'Durable stainless steel hand trowel with ergonomic handle. Perfect for planting and weeding.',
        category: 'tools',
        price: 18.99,
        currency: 'USD',
        imageUrls: [
            'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400',
        ],
        specifications: [
            { name: 'Material', value: 'Stainless Steel' },
            { name: 'Handle', value: 'Ergonomic Grip' },
            { name: 'Length', value: '30 cm' },
            { name: 'Weight', value: '200g' },
        ],
        stockQuantity: 120,
        rating: 4.6,
        reviewCount: 234,
        tags: ['durable', 'ergonomic', 'stainless-steel'],
        brand: 'GardenPro',
        isRecommended: false,
        isFeatured: true,
        createdAt: new Date('2024-01-08'),
        updatedAt: new Date('2024-01-16'),
    },
];

const STORAGE_KEYS = {
    CART: '@farming_app_cart',
    FAVORITES: '@farming_app_favorites',
    RECENT_SEARCHES: '@farming_app_recent_searches',
};

class StoreService {
    private favorites: string[] = [];

    /**
     * Initialize cart - delegates to shopping cart service
     */
    async initializeCart(userId?: string): Promise<ShoppingCart> {
        return shoppingCartService.initializeCart(userId);
    }

    /**
     * Search products - delegates to product catalog service
     */
    async searchProducts(params: ProductSearchParams): Promise<{
        products: Product[];
        totalCount: number;
        hasMore: boolean;
    }> {
        return productCatalogService.searchProducts(params);
    }

    /**
     * Get product by ID - delegates to product catalog service
     */
    async getProductById(id: string): Promise<Product | null> {
        return productCatalogService.getProductById(id);
    }

    /**
     * Get featured products - delegates to product catalog service
     */
    async getFeaturedProducts(limit?: number): Promise<Product[]> {
        return productCatalogService.getFeaturedProducts(limit);
    }

    /**
     * Get recommended products - delegates to product catalog service
     */
    async getRecommendedProducts(limit?: number): Promise<Product[]> {
        return productCatalogService.getRecommendedProducts(limit);
    }

    /**
     * Get products by category - delegates to product catalog service
     */
    async getProductsByCategory(category: ProductCategory, limit?: number): Promise<Product[]> {
        return productCatalogService.getProductsByCategory(category, limit);
    }

    /**
     * Cart management methods - delegate to shopping cart service
     */
    async addToCart(
        productId: string,
        quantity: number = 1,
        variantId?: string,
        userId?: string
    ): Promise<ShoppingCart> {
        return shoppingCartService.addToCart(productId, quantity, variantId, userId);
    }

    async removeFromCart(itemId: string, userId?: string): Promise<ShoppingCart> {
        return shoppingCartService.removeFromCart(itemId, userId);
    }

    async updateCartItemQuantity(
        itemId: string,
        quantity: number,
        userId?: string
    ): Promise<ShoppingCart> {
        return shoppingCartService.updateItemQuantity(itemId, quantity, userId);
    }

    async getCart(userId?: string): Promise<ShoppingCart> {
        return shoppingCartService.getCart(userId);
    }

    async clearCart(userId?: string): Promise<ShoppingCart> {
        return shoppingCartService.clearCart(userId);
    }

    /**
     * Get AI-based product recommendations
     */
    async getProductRecommendations(
        userId: string,
        basedOn?: string,
        context?: Partial<RecommendationContext>
    ): Promise<ProductRecommendation[]> {
        if (basedOn) {
            return productCatalogService.getAIRecommendations(userId, basedOn);
        }

        // Get personalized recommendations
        const fullContext: RecommendationContext = {
            userId,
            ...context,
        };

        return aiRecommendationsService.getPersonalizedRecommendations(fullContext);
    }

    /**
     * Get seasonal recommendations
     */
    async getSeasonalRecommendations(context: RecommendationContext): Promise<ProductRecommendation[]> {
        return aiRecommendationsService.getSeasonalRecommendations(context);
    }

    /**
     * Get recommendations based on AI analysis
     */
    async getRecommendationsFromAnalysis(
        analysisResult: any,
        context: RecommendationContext
    ): Promise<ProductRecommendation[]> {
        return aiRecommendationsService.generateRecommendationsFromAnalysis(analysisResult, context);
    }

    /**
     * Get product categories
     */
    async getCategories(): Promise<Array<{
        id: ProductCategory;
        name: string;
        description: string | null;
        icon: string | null;
        productCount: number;
    }>> {
        return productCatalogService.getCategories();
    }

    /**
     * Checkout and order management methods
     */
    async calculateShipping(
        cartItems: CartItem[],
        shippingAddress: ShippingAddress
    ): Promise<number> {
        return shoppingCartService.calculateShipping(cartItems, shippingAddress);
    }

    async calculateTax(
        cartItems: CartItem[],
        shippingAddress: ShippingAddress
    ): Promise<number> {
        return shoppingCartService.calculateTax(cartItems, shippingAddress);
    }

    async createOrder(
        userId: string,
        shippingAddress: ShippingAddress,
        paymentMethod: PaymentMethod,
        billingAddress?: ShippingAddress
    ): Promise<Order> {
        return shoppingCartService.createOrder(userId, shippingAddress, paymentMethod, billingAddress);
    }

    async getOrderHistory(userId: string, limit?: number): Promise<Order[]> {
        return shoppingCartService.getOrderHistory(userId, limit);
    }

    async trackOrder(orderId: string): Promise<Order | null> {
        return shoppingCartService.trackOrder(orderId);
    }

    /**
     * Inventory management methods
     */
    async getInventoryStatus(productId: string) {
        return inventoryManagerService.getInventoryStatus(productId);
    }

    async getInventoryAlerts() {
        return inventoryManagerService.getInventoryAlerts();
    }

    /**
     * Product reviews
     */
    async getProductReviews(productId: string, limit?: number) {
        return productCatalogService.getProductReviews(productId, limit);
    }

    async addProductReview(
        productId: string,
        userId: string,
        rating: number,
        title?: string,
        comment?: string
    ): Promise<void> {
        return productCatalogService.addProductReview(productId, userId, rating, title, comment);
    }
}

export const storeService = new StoreService();