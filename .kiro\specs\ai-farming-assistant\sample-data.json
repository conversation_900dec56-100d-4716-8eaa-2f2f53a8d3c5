{"users": [{"id": "550e8400-e29b-41d4-a716-************", "email": "<EMAIL>", "phone": "******-0123", "firstName": "<PERSON>", "lastName": "<PERSON>", "created_at": "2024-01-15T08:00:00Z", "profile": {"id": "550e8400-e29b-41d4-a716-************", "farm_location": {"latitude": 40.7128, "longitude": -74.006, "address": "Rural County, NY, USA"}, "crop_types": ["corn", "soybeans", "wheat"], "experience_level": "intermediate", "preferred_language": "en", "voice_enabled": true, "points": 1250, "subscription_tier": "premium", "subscription_expires_at": "2024-12-31T23:59:59Z"}}, {"id": "550e8400-e29b-41d4-a716-446655440002", "email": "<EMAIL>", "phone": "******-0456", "firstName": "<PERSON>", "lastName": "<PERSON>", "created_at": "2024-02-01T10:30:00Z", "profile": {"id": "550e8400-e29b-41d4-a716-446655440002", "farm_location": {"latitude": 34.0522, "longitude": -118.2437, "address": "Central Valley, CA, USA"}, "crop_types": ["tomatoes", "lettuce", "peppers", "strawberries"], "experience_level": "expert", "preferred_language": "es", "voice_enabled": true, "points": 3420, "subscription_tier": "pro", "subscription_expires_at": "2024-11-15T23:59:59Z"}}], "crop_plans": [{"id": "plan-001", "user_id": "550e8400-e29b-41d4-a716-************", "crop_type": "corn", "planting_date": "2024-04-15", "harvest_date": "2024-09-30", "location": {"latitude": 40.7128, "longitude": -74.006}, "status": "active", "created_at": "2024-03-01T09:00:00Z"}, {"id": "plan-002", "user_id": "550e8400-e29b-41d4-a716-446655440002", "crop_type": "tomatoes", "planting_date": "2024-03-20", "harvest_date": "2024-08-15", "location": {"latitude": 34.0522, "longitude": -118.2437}, "status": "active", "created_at": "2024-02-15T14:30:00Z"}], "tasks": [{"id": "task-001", "crop_plan_id": "plan-001", "title": "Water corn seedlings", "description": "Provide deep watering to newly planted corn. Check soil moisture 2 inches deep.", "due_date": "2024-04-20T06:00:00Z", "task_type": "watering", "completed": false, "points_reward": 10, "created_at": "2024-04-15T08:00:00Z"}, {"id": "task-002", "crop_plan_id": "plan-001", "title": "Apply nitrogen fertilizer", "description": "Apply 150 lbs/acre of nitrogen fertilizer when corn reaches V6 stage (6 leaves).", "due_date": "2024-05-15T07:00:00Z", "task_type": "fertilizing", "completed": false, "points_reward": 25, "created_at": "2024-04-15T08:00:00Z"}, {"id": "task-003", "crop_plan_id": "plan-002", "title": "Check for early blight", "description": "Inspect tomato plants for brown spots on lower leaves. Look for concentric rings pattern.", "due_date": "2024-05-01T08:00:00Z", "task_type": "monitoring", "completed": true, "points_reward": 15, "created_at": "2024-03-20T10:00:00Z"}], "chat_sessions": [{"id": "session-001", "user_id": "550e8400-e29b-41d4-a716-************", "session_type": "plant_health", "created_at": "2024-04-18T14:30:00Z"}], "chat_messages": [{"id": "msg-001", "session_id": "session-001", "message_type": "user", "content": "My corn leaves are turning yellow. What could be the problem?", "image_url": null, "analysis_result": null, "created_at": "2024-04-18T14:30:00Z"}, {"id": "msg-002", "session_id": "session-001", "message_type": "ai", "content": "Yellow corn leaves can indicate several issues: 1) Nitrogen deficiency - most common, starts with lower leaves 2) Overwatering - check soil drainage 3) Natural aging of lower leaves. Based on your location and planting date, nitrogen deficiency is most likely. I recommend soil testing and applying nitrogen fertilizer if levels are low.", "image_url": null, "analysis_result": {"category": "plant", "issues": [{"type": "nutrient_deficiency", "severity": "moderate", "description": "Possible nitrogen deficiency"}], "recommendations": [{"action": "soil_test", "priority": "high", "description": "Test soil nitrogen levels"}, {"action": "fertilize", "priority": "medium", "description": "Apply nitrogen fertilizer if deficient"}], "confidence": 0.85}, "created_at": "2024-04-18T14:31:00Z"}], "community_posts": [{"id": "post-001", "user_id": "550e8400-e29b-41d4-a716-446655440002", "title": "Great tomato harvest this season!", "content": "Just wanted to share my success with the cherry tomato variety 'Sweet 100'. Got amazing yields using the AI recommendations for watering schedule. Anyone else trying this variety?", "image_urls": ["https://example.com/images/tomato-harvest-1.jpg", "https://example.com/images/tomato-harvest-2.jpg"], "location": {"latitude": 34.0522, "longitude": -118.2437}, "likes_count": 23, "comments_count": 8, "created_at": "2024-08-20T16:45:00Z"}, {"id": "post-002", "user_id": "550e8400-e29b-41d4-a716-************", "title": "Dealing with corn rootworm - need advice", "content": "Found some corn rootworm damage in my field. Has anyone tried the beneficial nematodes approach? Looking for organic solutions.", "image_urls": ["https://example.com/images/corn-rootworm-damage.jpg"], "location": {"latitude": 40.7128, "longitude": -74.006}, "likes_count": 12, "comments_count": 15, "created_at": "2024-07-10T11:20:00Z"}], "post_comments": [{"id": "comment-001", "post_id": "post-002", "user_id": "550e8400-e29b-41d4-a716-446655440002", "content": "I've had success with beneficial nematodes! Apply them in early morning or evening when soil is moist. Takes about 2-3 weeks to see results.", "created_at": "2024-07-10T13:45:00Z"}], "products": [{"id": "prod-001", "name": "Organic Nitrogen Fertilizer 10-0-0", "description": "Slow-release organic nitrogen fertilizer perfect for corn and leafy greens. Made from composted chicken manure.", "category": "fertilizers", "price": 24.99, "image_urls": ["https://example.com/images/nitrogen-fertilizer.jpg"], "stock_quantity": 150, "created_at": "2024-01-01T00:00:00Z"}, {"id": "prod-002", "name": "Heirloom Tomato Seed Collection", "description": "Collection of 5 heirloom tomato varieties: Cherokee Purple, Brandywine, Green Zebra, Black Krim, and Mortgage Lifter.", "category": "seeds", "price": 18.95, "image_urls": ["https://example.com/images/tomato-seeds.jpg"], "stock_quantity": 75, "created_at": "2024-01-01T00:00:00Z"}, {"id": "prod-003", "name": "Digital Soil pH Meter", "description": "Accurate digital soil pH meter with moisture and light sensors. Essential for optimal crop growth.", "category": "tools", "price": 45.0, "image_urls": ["https://example.com/images/ph-meter.jpg"], "stock_quantity": 30, "created_at": "2024-01-01T00:00:00Z"}], "orders": [{"id": "order-001", "user_id": "550e8400-e29b-41d4-a716-************", "total_amount": 69.99, "status": "delivered", "shipping_address": {"name": "<PERSON>", "street": "123 Farm Road", "city": "Rural County", "state": "NY", "zip": "12345", "country": "USA"}, "created_at": "2024-04-01T10:00:00Z"}], "order_items": [{"id": "item-001", "order_id": "order-001", "product_id": "prod-001", "quantity": 2, "unit_price": 24.99}, {"id": "item-002", "order_id": "order-001", "product_id": "prod-003", "quantity": 1, "unit_price": 45.0}], "subscription_tiers": [{"tier": "free", "name": "Basic Farmer", "monthly_price": 0, "features": ["Basic crop planning", "5 AI consultations per month", "Weather alerts", "Community access"], "ai_consultations_limit": 5, "points_per_month": 100}, {"tier": "premium", "name": "Smart Farmer", "monthly_price": 9.99, "features": ["Advanced crop planning", "25 AI consultations per month", "Image analysis", "Priority support", "Weather alerts", "Community access", "Store discounts (10%)"], "ai_consultations_limit": 25, "points_per_month": 300}, {"tier": "pro", "name": "Expert Farmer", "monthly_price": 19.99, "features": ["Unlimited crop planning", "Unlimited AI consultations", "Advanced image analysis", "Priority support", "Weather alerts", "Community access", "Store discounts (20%)", "Custom recommendations", "Export data"], "ai_consultations_limit": -1, "points_per_month": 500}], "weather_data_example": {"location": {"latitude": 40.7128, "longitude": -74.006}, "current": {"temperature": 72, "humidity": 65, "wind_speed": 8, "conditions": "partly_cloudy", "precipitation": 0, "uv_index": 6}, "forecast": [{"date": "2024-04-20", "high": 75, "low": 58, "conditions": "sunny", "precipitation_chance": 10, "wind_speed": 12}, {"date": "2024-04-21", "high": 68, "low": 52, "conditions": "rainy", "precipitation_chance": 85, "wind_speed": 15}], "alerts": [{"type": "frost_warning", "severity": "moderate", "message": "Frost possible tonight. Protect sensitive plants.", "start_time": "2024-04-21T02:00:00Z", "end_time": "2024-04-21T08:00:00Z"}]}, "image_analysis_examples": {"plant_disease_analysis": {"id": "analysis-001", "user_id": "550e8400-e29b-41d4-a716-************", "image_url": "https://example.com/images/tomato-blight.jpg", "analysis_type": "plant_disease", "crop_type": "tomato", "results": {"category": "plant", "primary_issue": {"disease_name": "Early Blight", "scientific_name": "Alternaria solani", "confidence": 0.92, "severity": "moderate", "affected_area_percentage": 25}, "secondary_issues": [{"issue_type": "nutrient_deficiency", "nutrient": "potassium", "confidence": 0.67, "severity": "mild"}], "symptoms_detected": ["brown_spots_with_concentric_rings", "yellowing_lower_leaves", "leaf_drop"], "recommendations": [{"action": "fungicide_application", "priority": "high", "description": "Apply copper-based fungicide every 7-10 days", "timing": "early_morning_or_evening", "products": ["copper_sulfate", "chlorothalonil"]}, {"action": "improve_air_circulation", "priority": "high", "description": "Prune lower branches and increase plant spacing"}, {"action": "potassium_fertilizer", "priority": "medium", "description": "Apply potassium-rich fertilizer to strengthen plant immunity"}, {"action": "remove_affected_leaves", "priority": "high", "description": "Remove and dispose of infected leaves immediately"}], "prevention_tips": ["Water at soil level to avoid wetting leaves", "Rotate crops annually", "Use disease-resistant varieties", "Maintain proper plant spacing"]}, "created_at": "2024-06-15T10:30:00Z"}, "pest_identification": {"id": "analysis-002", "user_id": "550e8400-e29b-41d4-a716-446655440002", "image_url": "https://example.com/images/aphids-on-corn.jpg", "analysis_type": "pest_identification", "crop_type": "corn", "results": {"category": "plant", "primary_issue": {"pest_name": "Corn Leaf Aphid", "scientific_name": "Rhopalosiph<PERSON> maidis", "confidence": 0.89, "infestation_level": "moderate", "estimated_count": "50-100 per plant"}, "pest_characteristics": {"size": "2-3mm", "color": "blue-green", "location_on_plant": "underside_of_leaves", "damage_type": "sap_sucking"}, "damage_assessment": {"current_damage": "yellowing_leaves", "potential_damage": ["stunted_growth", "reduced_yield", "virus_transmission"], "economic_threshold": "not_reached"}, "recommendations": [{"action": "beneficial_insects", "priority": "high", "description": "Introduce ladybugs or lacewings for biological control", "timing": "immediately"}, {"action": "insecticidal_soap", "priority": "medium", "description": "Spray with insecticidal soap if population increases", "application_rate": "2-3% solution"}, {"action": "monitor_weekly", "priority": "high", "description": "Check plants weekly for population changes"}], "natural_enemies": ["ladybugs", "lacewings", "parasitic_wasps", "syrphid_flies"]}, "created_at": "2024-07-02T14:20:00Z"}, "soil_analysis": {"id": "analysis-003", "user_id": "550e8400-e29b-41d4-a716-************", "image_url": "https://example.com/images/soil-sample.jpg", "analysis_type": "soil_condition", "location": {"latitude": 40.7128, "longitude": -74.006}, "results": {"category": "soil", "soil_type": {"primary_type": "loam", "confidence": 0.85, "texture": "medium", "drainage": "well_drained"}, "visual_indicators": {"color": "dark_brown", "organic_matter": "high", "structure": "granular", "moisture_level": "optimal"}, "estimated_properties": {"ph_range": "6.5-7.0", "organic_matter_percentage": "4-6%", "nitrogen_level": "moderate", "phosphorus_level": "adequate", "potassium_level": "high"}, "suitability": {"excellent_for": ["corn", "soybeans", "wheat", "vegetables"], "good_for": ["fruit_trees", "berries"], "challenging_for": ["blueberries", "azaleas"]}, "recommendations": [{"action": "soil_test", "priority": "medium", "description": "Conduct professional soil test to confirm pH and nutrient levels"}, {"action": "maintain_organic_matter", "priority": "high", "description": "Continue adding compost or organic matter annually"}, {"action": "crop_rotation", "priority": "medium", "description": "Implement 3-4 year crop rotation to maintain soil health"}]}, "created_at": "2024-03-10T09:15:00Z"}, "nutrient_deficiency": {"id": "analysis-004", "user_id": "550e8400-e29b-41d4-a716-446655440002", "image_url": "https://example.com/images/nitrogen-deficient-corn.jpg", "analysis_type": "nutrient_deficiency", "crop_type": "corn", "results": {"category": "plant", "primary_deficiency": {"nutrient": "nitrogen", "confidence": 0.94, "severity": "moderate_to_severe", "stage_detected": "V6"}, "symptoms_identified": ["yellowing_lower_leaves", "v_shaped_yellowing_pattern", "stunted_growth", "pale_green_upper_leaves"], "growth_stage_analysis": {"current_stage": "V6", "expected_height": "24-30 inches", "actual_height": "18-22 inches", "development_delay": "7-10 days"}, "recommendations": [{"action": "immediate_nitrogen_application", "priority": "urgent", "description": "Apply 30-40 lbs N/acre immediately", "fertilizer_options": ["urea_46-0-0", "ammonium_nitrate_34-0-0", "liquid_nitrogen_28-0-0"], "application_method": "side_dress_or_broadcast"}, {"action": "soil_moisture_check", "priority": "high", "description": "Ensure adequate soil moisture for nutrient uptake"}, {"action": "follow_up_assessment", "priority": "medium", "description": "Monitor plant response in 7-10 days", "expected_improvement": "greening_of_new_growth"}], "yield_impact": {"potential_loss": "15-25%", "recovery_potential": "good_if_treated_promptly"}}, "created_at": "2024-05-20T11:45:00Z"}, "weed_identification": {"id": "analysis-005", "user_id": "550e8400-e29b-41d4-a716-************", "image_url": "https://example.com/images/pigweed-in-soybeans.jpg", "analysis_type": "weed_identification", "crop_type": "soybeans", "results": {"category": "plant", "weed_species": {"common_name": "<PERSON>", "scientific_name": "<PERSON><PERSON><PERSON>", "confidence": 0.91, "growth_stage": "4-6_leaf", "density": "moderate"}, "weed_characteristics": {"leaf_shape": "diamond_shaped", "stem": "smooth_reddish", "height": "6-12_inches", "growth_habit": "upright"}, "threat_assessment": {"competitiveness": "very_high", "herbicide_resistance": "multiple_modes_of_action", "seed_production": "up_to_1_million_seeds_per_plant", "economic_impact": "severe"}, "recommendations": [{"action": "immediate_control", "priority": "urgent", "description": "Control before plants reach 4 inches tall", "methods": ["mechanical_cultivation", "hand_removal", "targeted_herbicide"]}, {"action": "herbicide_rotation", "priority": "high", "description": "Use multiple modes of action to prevent resistance", "options": ["glyphosate_plus_2_4_D", "dicamba_based", "residual_herbicides"]}, {"action": "prevention_strategy", "priority": "high", "description": "Implement integrated weed management", "tactics": ["crop_rotation", "cover_crops", "clean_equipment", "field_scouting"]}]}, "created_at": "2024-06-08T16:30:00Z"}, "crop_maturity_assessment": {"id": "analysis-006", "user_id": "550e8400-e29b-41d4-a716-446655440002", "image_url": "https://example.com/images/tomato-ripeness.jpg", "analysis_type": "maturity_assessment", "crop_type": "tomato", "results": {"category": "plant", "maturity_stage": {"stage": "breaker", "confidence": 0.88, "days_to_harvest": "3-5", "optimal_harvest": false}, "ripeness_indicators": {"color_change": "green_to_pink_blush", "firmness": "firm", "size": "full_size_achieved", "shoulder_color": "light_green"}, "quality_assessment": {"blemishes": "none_detected", "cracking": "none", "sunscald": "none", "pest_damage": "minimal"}, "recommendations": [{"action": "wait_for_full_ripeness", "priority": "medium", "description": "Allow 3-5 more days for optimal flavor development", "target_stage": "full_red"}, {"action": "harvest_if_needed", "priority": "low", "description": "Can harvest now if frost threatens or for shipping", "ripening_method": "room_temperature_ripening"}, {"action": "continue_monitoring", "priority": "high", "description": "Check daily for optimal harvest timing"}], "storage_recommendations": {"if_harvested_now": {"temperature": "65-70°F", "humidity": "85-90%", "expected_shelf_life": "7-10 days"}, "if_fully_ripe": {"temperature": "55-60°F", "humidity": "85-90%", "expected_shelf_life": "3-5 days"}}}, "created_at": "2024-08-12T13:20:00Z"}}}