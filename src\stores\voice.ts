import { create } from 'zustand';
import * as Speech from 'expo-speech';

export interface VoiceStore {
    isVoiceEnabled: boolean;
    isListening: boolean;
    isSpeaking: boolean;

    // Actions
    toggleVoiceMode: () => void;
    enableVoiceMode: () => void;
    disableVoiceMode: () => void;
    speak: (text: string, options?: Speech.SpeechOptions) => Promise<void>;
    startListening: () => Promise<string>;
    stopListening: () => void;
    stopSpeaking: () => void;
}

export const useVoiceStore = create<VoiceStore>((set, get) => ({
    isVoiceEnabled: false,
    isListening: false,
    isSpeaking: false,

    toggleVoiceMode: () => {
        const { isVoiceEnabled } = get();
        set({ isVoiceEnabled: !isVoiceEnabled });
    },

    enableVoiceMode: () => {
        set({ isVoiceEnabled: true });
    },

    disableVoiceMode: () => {
        set({ isVoiceEnabled: false });
        get().stopSpeaking();
        get().stopListening();
    },

    speak: async (text: string, options?: Speech.SpeechOptions) => {
        const { isVoiceEnabled } = get();
        if (!isVoiceEnabled) return;

        try {
            set({ isSpeaking: true });

            const defaultOptions: Speech.SpeechOptions = {
                language: 'en-US',
                pitch: 1.0,
                rate: 0.8,
                ...options,
            };

            await Speech.speak(text, {
                ...defaultOptions,
                onDone: () => set({ isSpeaking: false }),
                onError: () => set({ isSpeaking: false }),
            });
        } catch (error) {
            console.error('Speech error:', error);
            set({ isSpeaking: false });
        }
    },

    startListening: async (): Promise<string> => {
        const { isVoiceEnabled } = get();
        if (!isVoiceEnabled) return '';

        try {
            set({ isListening: true });

            // This is a mock implementation
            // In a real app, you would use a speech recognition library
            // like @react-native-voice/voice or expo-speech-recognition

            return new Promise((resolve) => {
                setTimeout(() => {
                    set({ isListening: false });
                    resolve('Mock voice input text');
                }, 2000);
            });
        } catch (error) {
            console.error('Voice recognition error:', error);
            set({ isListening: false });
            return '';
        }
    },

    stopListening: () => {
        set({ isListening: false });
    },

    stopSpeaking: () => {
        Speech.stop();
        set({ isSpeaking: false });
    },
}));