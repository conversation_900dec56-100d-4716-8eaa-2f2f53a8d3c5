import { offlineDatabase } from './database';
import { connectivityService } from './connectivity';
import { cachingService } from './caching';
import { offlineModeService } from './offlineMode';
import { conflictResolutionService } from './conflictResolution';
import { syncService } from './syncService';
import { backgroundSyncService } from './backgroundSync';
import { OfflineAction, SyncQueueItem, OfflineConfig, SyncStatus } from '../../types/offline';

export class OfflineService {
    private initialized = false;
    private syncInProgress = false;
    private syncListeners: ((status: SyncStatus) => void)[] = [];

    async initialize(config?: Partial<OfflineConfig>): Promise<void> {
        if (this.initialized) return;

        console.log('Initializing offline service...');

        try {
            // Initialize all sub-services
            await offlineDatabase.initialize();
            await connectivityService.initialize();
            await cachingService.initialize(config);
            await offlineModeService.initialize();
            await syncService.initialize();
            await backgroundSyncService.initialize();

            // Set up connectivity listeners for automatic sync
            connectivityService.addListener((state) => {
                if (state.isConnected && state.isInternetReachable && !this.syncInProgress) {
                    this.processSyncQueue();
                }
            });

            this.initialized = true;
            console.log('Offline service initialized successfully');
        } catch (error) {
            console.error('Failed to initialize offline service:', error);
            throw error;
        }
    }

    // Queue management
    async addToSyncQueue(action: OfflineAction): Promise<void> {
        if (!this.initialized) {
            throw new Error('Offline service not initialized');
        }

        console.log(`Adding action to sync queue: ${action.type} ${action.table}`);
        await offlineDatabase.addToSyncQueue(action);

        // Try to sync immediately if online
        if (connectivityService.isOnline() && !this.syncInProgress) {
            setTimeout(() => this.processSyncQueue(), 1000);
        }
    }

    async processSyncQueue(): Promise<void> {
        if (!this.initialized || !connectivityService.isOnline()) {
            return;
        }

        try {
            console.log('Processing sync queue via sync service...');
            const result = await syncService.performIncrementalSync();
            console.log('Sync queue processing completed:', result);
            this.notifySyncListeners();
        } catch (error) {
            console.error('Error processing sync queue:', error);
            this.notifySyncListeners();
        }
    }

    private async processSyncAction(action: SyncQueueItem): Promise<void> {
        try {
            // Mark as syncing
            await offlineDatabase.updateSyncQueueItem(action.id, { status: 'syncing' });

            // Process based on action type
            let success = false;
            switch (action.type) {
                case 'CREATE':
                    success = await this.syncCreate(action);
                    break;
                case 'UPDATE':
                    success = await this.syncUpdate(action);
                    break;
                case 'DELETE':
                    success = await this.syncDelete(action);
                    break;
            }

            if (success) {
                await offlineDatabase.updateSyncQueueItem(action.id, { status: 'completed' });
                // Remove completed actions after a delay
                setTimeout(() => {
                    offlineDatabase.removeSyncQueueItem(action.id);
                }, 5000);
            } else {
                await this.handleSyncFailure(action);
            }
        } catch (error) {
            console.error(`Error syncing action ${action.id}:`, error);
            await this.handleSyncFailure(action);
        }
    }

    private async syncCreate(action: SyncQueueItem): Promise<boolean> {
        try {
            // This would typically call the appropriate Supabase service
            // For now, we'll simulate the sync
            console.log(`Syncing CREATE for ${action.table}:`, action.data);

            // Simulate API call delay
            await new Promise(resolve => setTimeout(resolve, 1000));

            // In a real implementation, you would:
            // 1. Call the appropriate service (e.g., supabaseService.create())
            // 2. Handle the response
            // 3. Update local cache with server response
            // 4. Resolve conflicts if any

            return true;
        } catch (error) {
            console.error('Create sync failed:', error);
            return false;
        }
    }

    private async syncUpdate(action: SyncQueueItem): Promise<boolean> {
        try {
            console.log(`Syncing UPDATE for ${action.table}:`, action.data);

            // Simulate API call delay
            await new Promise(resolve => setTimeout(resolve, 1000));

            // In a real implementation, you would:
            // 1. Fetch current server data
            // 2. Check for conflicts
            // 3. Resolve conflicts using conflict resolution service
            // 4. Send update to server
            // 5. Update local cache

            return true;
        } catch (error) {
            console.error('Update sync failed:', error);
            return false;
        }
    }

    private async syncDelete(action: SyncQueueItem): Promise<boolean> {
        try {
            console.log(`Syncing DELETE for ${action.table}:`, action.data);

            // Simulate API call delay
            await new Promise(resolve => setTimeout(resolve, 500));

            // In a real implementation, you would:
            // 1. Call the appropriate service to delete
            // 2. Remove from local cache
            // 3. Handle any cascade deletions

            return true;
        } catch (error) {
            console.error('Delete sync failed:', error);
            return false;
        }
    }

    private async handleSyncFailure(action: SyncQueueItem): Promise<void> {
        const newRetryCount = action.retryCount + 1;

        if (newRetryCount >= action.maxRetries) {
            console.log(`Action ${action.id} exceeded max retries, marking as failed`);
            await offlineDatabase.updateSyncQueueItem(action.id, {
                status: 'failed',
                retryCount: newRetryCount
            });
        } else {
            console.log(`Action ${action.id} failed, will retry (${newRetryCount}/${action.maxRetries})`);
            await offlineDatabase.updateSyncQueueItem(action.id, {
                status: 'pending',
                retryCount: newRetryCount
            });
        }
    }

    // Data operations with offline support
    async createData(table: string, data: any, userId?: string): Promise<any> {
        const id = this.generateId();
        const timestamp = Date.now();

        // Store locally immediately
        await offlineDatabase.cacheData(`offline_${table}`, id, { ...data, id }, userId);

        // Add to sync queue
        const action: OfflineAction = {
            id: this.generateId(),
            type: 'CREATE',
            table,
            data: { ...data, id },
            timestamp,
            userId,
            retryCount: 0,
            maxRetries: 3,
            status: 'pending'
        };

        await this.addToSyncQueue(action);

        return { ...data, id };
    }

    async updateData(table: string, id: string, data: any, userId?: string): Promise<any> {
        const timestamp = Date.now();
        const updatedData = { ...data, id, updated_at: new Date().toISOString() };

        // Update locally immediately
        await offlineDatabase.cacheData(`offline_${table}`, id, updatedData, userId);

        // Add to sync queue
        const action: OfflineAction = {
            id: this.generateId(),
            type: 'UPDATE',
            table,
            data: updatedData,
            timestamp,
            userId,
            retryCount: 0,
            maxRetries: 3,
            status: 'pending'
        };

        await this.addToSyncQueue(action);

        return updatedData;
    }

    async deleteData(table: string, id: string, userId?: string): Promise<void> {
        const timestamp = Date.now();

        // Remove from local cache
        await offlineDatabase.removeCachedData(`offline_${table}`, id);

        // Add to sync queue
        const action: OfflineAction = {
            id: this.generateId(),
            type: 'DELETE',
            table,
            data: { id },
            timestamp,
            userId,
            retryCount: 0,
            maxRetries: 3,
            status: 'pending'
        };

        await this.addToSyncQueue(action);
    }

    async getData(table: string, id?: string, userId?: string): Promise<any[]> {
        // Try to get from cache first
        const cachedData = await offlineDatabase.getCachedData(`offline_${table}`, id, userId);

        if (cachedData.length > 0) {
            return cachedData;
        }

        // If online, try to fetch from server and cache
        if (connectivityService.isOnline()) {
            try {
                // This would typically fetch from Supabase
                // For now, return empty array
                console.log(`Fetching ${table} data from server...`);
                return [];
            } catch (error) {
                console.error('Failed to fetch from server:', error);
                return [];
            }
        }

        return [];
    }

    // Status and monitoring
    getSyncStatus(): SyncStatus {
        return {
            isOnline: connectivityService.isOnline(),
            lastSyncTime: syncService.getLastSyncTime(),
            syncInProgress: syncService.isSyncInProgress(),
            pendingActions: 0, // This would be calculated from database
            failedActions: 0, // This would be calculated from database
            nextSyncAttempt: backgroundSyncService.getStatus().nextSyncTime
        };
    }

    async getSyncQueueStats(): Promise<{
        pending: number;
        syncing: number;
        completed: number;
        failed: number;
    }> {
        const [pending, syncing, completed, failed] = await Promise.all([
            offlineDatabase.getSyncQueue('pending'),
            offlineDatabase.getSyncQueue('syncing'),
            offlineDatabase.getSyncQueue('completed'),
            offlineDatabase.getSyncQueue('failed')
        ]);

        return {
            pending: pending.length,
            syncing: syncing.length,
            completed: completed.length,
            failed: failed.length
        };
    }

    // Event handling
    addSyncListener(listener: (status: SyncStatus) => void): () => void {
        this.syncListeners.push(listener);

        return () => {
            const index = this.syncListeners.indexOf(listener);
            if (index > -1) {
                this.syncListeners.splice(index, 1);
            }
        };
    }

    private notifySyncListeners(): void {
        const status = this.getSyncStatus();
        this.syncListeners.forEach(listener => {
            try {
                listener(status);
            } catch (error) {
                console.error('Error in sync listener:', error);
            }
        });
    }

    // Utility methods
    private generateId(): string {
        return `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    async clearOfflineData(): Promise<void> {
        await offlineDatabase.clearCache();
        await offlineDatabase.clearSyncQueue();
        await cachingService.clear();
    }

    async getStorageInfo(): Promise<{
        databaseSize: number;
        cacheStats: any;
        syncQueueStats: any;
    }> {
        const [databaseSize, cacheStats, syncQueueStats] = await Promise.all([
            offlineDatabase.getStorageSize(),
            cachingService.getStats(),
            this.getSyncQueueStats()
        ]);

        return {
            databaseSize,
            cacheStats,
            syncQueueStats
        };
    }

    // Cleanup and maintenance
    async performMaintenance(): Promise<void> {
        console.log('Performing offline service maintenance...');

        await Promise.all([
            offlineDatabase.vacuum(),
            cachingService.cleanup()
        ]);

        console.log('Maintenance completed');
    }

    async destroy(): Promise<void> {
        this.syncListeners = [];
        connectivityService.destroy();
        offlineModeService.destroy();
        await offlineDatabase.close();
    }
}

// Export singleton instance
export const offlineService = new OfflineService();

// Export all sub-services for direct access if needed
export {
    offlineDatabase,
    connectivityService,
    cachingService,
    offlineModeService,
    conflictResolutionService,
    syncService,
    backgroundSyncService
};