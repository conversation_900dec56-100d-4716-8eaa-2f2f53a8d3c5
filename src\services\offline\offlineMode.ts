import { connectivityService, ConnectivityState } from './connectivity';
import { cachingService } from './caching';
import { SyncStatus } from '../../types/offline';

export interface OfflineModeConfig {
    enableOfflineMode: boolean;
    showOfflineIndicator: boolean;
    offlineTimeout: number; // Time to wait before switching to offline mode
    retryInterval: number; // How often to retry connection
    maxRetryAttempts: number;
}

export interface OfflineModeState {
    isOfflineMode: boolean;
    isConnected: boolean;
    lastOnlineTime: number | null;
    offlineDuration: number;
    retryAttempts: number;
    nextRetryTime: number | null;
}

export class OfflineModeService {
    private config: OfflineModeConfig = {
        enableOfflineMode: true,
        showOfflineIndicator: true,
        offlineTimeout: 5000, // 5 seconds
        retryInterval: 30000, // 30 seconds
        maxRetryAttempts: 10
    };

    private state: OfflineModeState = {
        isOfflineMode: false,
        isConnected: false,
        lastOnlineTime: null,
        offlineDuration: 0,
        retryAttempts: 0,
        nextRetryTime: null
    };

    private listeners: ((state: OfflineModeState) => void)[] = [];
    private offlineTimer: NodeJS.Timeout | null = null;
    private retryTimer: NodeJS.Timeout | null = null;
    private connectivityUnsubscribe: (() => void) | null = null;

    async initialize(config?: Partial<OfflineModeConfig>): Promise<void> {
        if (config) {
            this.config = { ...this.config, ...config };
        }

        // Initialize connectivity service if not already done
        if (!connectivityService.getCurrentState) {
            await connectivityService.initialize();
        }

        // Set initial state based on current connectivity
        const connectivityState = connectivityService.getCurrentState();
        this.updateConnectivityState(connectivityState);

        // Listen for connectivity changes
        this.connectivityUnsubscribe = connectivityService.addListener((state) => {
            this.updateConnectivityState(state);
        });

        console.log('Offline mode service initialized');
    }

    private updateConnectivityState(connectivityState: ConnectivityState): void {
        const wasConnected = this.state.isConnected;
        const isNowConnected = connectivityState.isConnected && connectivityState.isInternetReachable;

        this.state.isConnected = isNowConnected;

        if (!wasConnected && isNowConnected) {
            // Connection restored
            this.handleConnectionRestored();
        } else if (wasConnected && !isNowConnected) {
            // Connection lost
            this.handleConnectionLost();
        }

        this.notifyListeners();
    }

    private handleConnectionLost(): void {
        console.log('Connection lost, preparing for offline mode...');

        // Clear any existing timers
        if (this.offlineTimer) {
            clearTimeout(this.offlineTimer);
        }

        // Wait for the configured timeout before switching to offline mode
        this.offlineTimer = setTimeout(() => {
            if (!this.state.isConnected) {
                this.enterOfflineMode();
            }
        }, this.config.offlineTimeout);
    }

    private handleConnectionRestored(): void {
        console.log('Connection restored');

        // Clear timers
        if (this.offlineTimer) {
            clearTimeout(this.offlineTimer);
            this.offlineTimer = null;
        }
        if (this.retryTimer) {
            clearTimeout(this.retryTimer);
            this.retryTimer = null;
        }

        // Exit offline mode
        this.exitOfflineMode();
    }

    private enterOfflineMode(): void {
        if (this.state.isOfflineMode) return;

        console.log('Entering offline mode');

        this.state.isOfflineMode = true;
        this.state.lastOnlineTime = Date.now();
        this.state.retryAttempts = 0;

        // Schedule retry attempts
        this.scheduleRetry();

        this.notifyListeners();
    }

    private exitOfflineMode(): void {
        if (!this.state.isOfflineMode) return;

        console.log('Exiting offline mode');

        const now = Date.now();
        if (this.state.lastOnlineTime) {
            this.state.offlineDuration = now - this.state.lastOnlineTime;
        }

        this.state.isOfflineMode = false;
        this.state.lastOnlineTime = now;
        this.state.retryAttempts = 0;
        this.state.nextRetryTime = null;

        this.notifyListeners();
    }

    private scheduleRetry(): void {
        if (this.state.retryAttempts >= this.config.maxRetryAttempts) {
            console.log('Max retry attempts reached');
            return;
        }

        const retryDelay = this.calculateRetryDelay();
        this.state.nextRetryTime = Date.now() + retryDelay;

        this.retryTimer = setTimeout(async () => {
            await this.attemptReconnection();
        }, retryDelay);
    }

    private calculateRetryDelay(): number {
        // Exponential backoff with jitter
        const baseDelay = this.config.retryInterval;
        const exponentialDelay = baseDelay * Math.pow(2, this.state.retryAttempts);
        const maxDelay = 5 * 60 * 1000; // 5 minutes max
        const delay = Math.min(exponentialDelay, maxDelay);

        // Add jitter (±25%)
        const jitter = delay * 0.25 * (Math.random() - 0.5);
        return delay + jitter;
    }

    private async attemptReconnection(): Promise<void> {
        console.log(`Attempting reconnection (attempt ${this.state.retryAttempts + 1})`);

        this.state.retryAttempts++;

        try {
            const isConnected = await connectivityService.testInternetConnection();

            if (isConnected) {
                console.log('Reconnection successful');
                this.handleConnectionRestored();
            } else {
                console.log('Reconnection failed, scheduling next attempt');
                this.scheduleRetry();
            }
        } catch (error) {
            console.error('Reconnection attempt failed:', error);
            this.scheduleRetry();
        }

        this.notifyListeners();
    }

    // Public API
    isOfflineMode(): boolean {
        return this.state.isOfflineMode;
    }

    isOnline(): boolean {
        return this.state.isConnected && !this.state.isOfflineMode;
    }

    getState(): OfflineModeState {
        return { ...this.state };
    }

    getSyncStatus(): SyncStatus {
        return {
            isOnline: this.isOnline(),
            lastSyncTime: this.state.lastOnlineTime,
            syncInProgress: false, // This would be updated by sync service
            pendingActions: 0, // This would be updated by sync service
            failedActions: 0, // This would be updated by sync service
            nextSyncAttempt: this.state.nextRetryTime
        };
    }

    async forceReconnectionAttempt(): Promise<boolean> {
        console.log('Forcing reconnection attempt...');

        try {
            const isConnected = await connectivityService.testInternetConnection();

            if (isConnected) {
                this.handleConnectionRestored();
                return true;
            }

            return false;
        } catch (error) {
            console.error('Forced reconnection failed:', error);
            return false;
        }
    }

    async enableOfflineMode(enable: boolean = true): Promise<void> {
        this.config.enableOfflineMode = enable;

        if (!enable && this.state.isOfflineMode) {
            // Try to reconnect immediately
            await this.forceReconnectionAttempt();
        }
    }

    setOfflineIndicatorVisibility(visible: boolean): void {
        this.config.showOfflineIndicator = visible;
        this.notifyListeners();
    }

    shouldShowOfflineIndicator(): boolean {
        return this.config.showOfflineIndicator && this.state.isOfflineMode;
    }

    getOfflineDuration(): number {
        if (!this.state.isOfflineMode || !this.state.lastOnlineTime) {
            return this.state.offlineDuration;
        }

        return Date.now() - this.state.lastOnlineTime;
    }

    getTimeUntilNextRetry(): number {
        if (!this.state.nextRetryTime) return 0;
        return Math.max(0, this.state.nextRetryTime - Date.now());
    }

    // Event handling
    addListener(listener: (state: OfflineModeState) => void): () => void {
        this.listeners.push(listener);

        return () => {
            const index = this.listeners.indexOf(listener);
            if (index > -1) {
                this.listeners.splice(index, 1);
            }
        };
    }

    private notifyListeners(): void {
        this.listeners.forEach(listener => {
            try {
                listener(this.getState());
            } catch (error) {
                console.error('Error in offline mode listener:', error);
            }
        });
    }

    // Lifecycle
    destroy(): void {
        if (this.offlineTimer) {
            clearTimeout(this.offlineTimer);
            this.offlineTimer = null;
        }

        if (this.retryTimer) {
            clearTimeout(this.retryTimer);
            this.retryTimer = null;
        }

        if (this.connectivityUnsubscribe) {
            this.connectivityUnsubscribe();
            this.connectivityUnsubscribe = null;
        }

        this.listeners = [];
    }

    // Utility methods for UI components
    getOfflineStatusMessage(): string {
        if (!this.state.isOfflineMode) {
            return 'Online';
        }

        const duration = this.getOfflineDuration();
        const minutes = Math.floor(duration / 60000);
        const seconds = Math.floor((duration % 60000) / 1000);

        if (minutes > 0) {
            return `Offline for ${minutes}m ${seconds}s`;
        } else {
            return `Offline for ${seconds}s`;
        }
    }

    getNextRetryMessage(): string {
        const timeUntilRetry = this.getTimeUntilNextRetry();

        if (timeUntilRetry <= 0) {
            return 'Retrying now...';
        }

        const seconds = Math.ceil(timeUntilRetry / 1000);
        return `Next retry in ${seconds}s`;
    }

    async warmupOfflineData(userId: string): Promise<void> {
        if (this.isOnline()) {
            console.log('Warming up offline data while online...');
            await cachingService.warmup(userId);
        }
    }
}

export const offlineModeService = new OfflineModeService();