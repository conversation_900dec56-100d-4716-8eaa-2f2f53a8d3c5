import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import notificationService, { getNotificationPreferences } from '../services/notifications';
import notificationScheduler from '../services/notificationScheduler';
import notificationAnalytics from '../services/notificationAnalytics';
import {
    NotificationPreferences,
    NotificationData,
    NotificationAnalytics,
    ScheduledNotification,
} from '../types/notifications';

interface NotificationState {
    // Preferences
    preferences: NotificationPreferences;
    isInitialized: boolean;
    pushToken: string | null;

    // Notification history
    notifications: NotificationData[];
    unreadCount: number;

    // Analytics
    analytics: NotificationAnalytics[];

    // Loading states
    isLoading: boolean;
    isUpdatingPreferences: boolean;

    // Actions
    initialize: () => Promise<void>;
    updatePreferences: (preferences: Partial<NotificationPreferences>) => Promise<void>;
    sendTestNotification: () => Promise<void>;
    markAsRead: (notificationId: string) => Promise<void>;
    markAllAsRead: () => Promise<void>;
    getNotificationHistory: (limit?: number) => Promise<void>;
    scheduleNotification: (notification: ScheduledNotification) => Promise<void>;
    cancelNotification: (identifier: string) => Promise<void>;
    getAnalytics: () => Promise<void>;
    clearNotifications: () => void;

    // Permission management
    requestPermissions: () => Promise<boolean>;
    checkPermissionStatus: () => Promise<string>;
}

export const useNotificationStore = create<NotificationState>()(
    persist(
        (set, get) => ({
            // Initial state
            preferences: {
                weatherAlerts: true,
                taskReminders: true,
                cropStageUpdates: true,
                communityUpdates: true,
                emergencyAlerts: true,
                aiInsights: true,
                marketingPromotions: false,
                soundEnabled: true,
                vibrationEnabled: true,
                quietHoursStart: '22:00',
                quietHoursEnd: '07:00',
            },
            isInitialized: false,
            pushToken: null,
            notifications: [],
            unreadCount: 0,
            analytics: [],
            isLoading: false,
            isUpdatingPreferences: false,

            // Actions
            initialize: async () => {
                try {
                    set({ isLoading: true });

                    // Initialize notification service
                    await notificationService.initialize();

                    // Initialize scheduler
                    await notificationScheduler.initialize();

                    // Load preferences
                    const preferences = await getNotificationPreferences();

                    // Get push token
                    const pushToken = notificationService.getPushToken();

                    // Load notification history
                    await get().getNotificationHistory(20);

                    set({
                        preferences,
                        pushToken,
                        isInitialized: true,
                        isLoading: false,
                    });

                    console.log('Notification store initialized');
                } catch (error) {
                    console.error('Failed to initialize notification store:', error);
                    set({ isLoading: false });
                }
            },

            updatePreferences: async (newPreferences: Partial<NotificationPreferences>) => {
                try {
                    set({ isUpdatingPreferences: true });

                    const currentPreferences = get().preferences;
                    const updatedPreferences = { ...currentPreferences, ...newPreferences };

                    // Update preferences in service
                    await notificationService.updateNotificationPreferences(updatedPreferences);

                    set({
                        preferences: updatedPreferences,
                        isUpdatingPreferences: false,
                    });

                    console.log('Notification preferences updated');
                } catch (error) {
                    console.error('Failed to update notification preferences:', error);
                    set({ isUpdatingPreferences: false });
                    throw error;
                }
            },

            sendTestNotification: async () => {
                try {
                    await notificationService.sendLocalNotification(
                        '🧪 Test Notification',
                        'This is a test notification to verify your settings are working correctly.',
                        {
                            type: 'system_maintenance',
                            testNotification: true,
                        },
                        {
                            priority: 'normal',
                        }
                    );

                    console.log('Test notification sent');
                } catch (error) {
                    console.error('Failed to send test notification:', error);
                    throw error;
                }
            },

            markAsRead: async (notificationId: string) => {
                try {
                    await notificationService.markNotificationAsRead(notificationId);

                    set(state => ({
                        notifications: state.notifications.map(notification =>
                            notification.id === notificationId
                                ? { ...notification, readAt: new Date() }
                                : notification
                        ),
                        unreadCount: Math.max(0, state.unreadCount - 1),
                    }));
                } catch (error) {
                    console.error('Failed to mark notification as read:', error);
                }
            },

            markAllAsRead: async () => {
                try {
                    const { notifications } = get();
                    const unreadNotifications = notifications.filter(n => !n.readAt);

                    // Mark all unread notifications as read
                    await Promise.all(
                        unreadNotifications.map(notification =>
                            notificationService.markNotificationAsRead(notification.id)
                        )
                    );

                    set(state => ({
                        notifications: state.notifications.map(notification => ({
                            ...notification,
                            readAt: notification.readAt || new Date(),
                        })),
                        unreadCount: 0,
                    }));
                } catch (error) {
                    console.error('Failed to mark all notifications as read:', error);
                }
            },

            getNotificationHistory: async (limit: number = 50) => {
                try {
                    const notifications = await notificationService.getNotificationHistory(limit);
                    const unreadCount = notifications.filter(n => !n.readAt).length;

                    set({
                        notifications,
                        unreadCount,
                    });
                } catch (error) {
                    console.error('Failed to get notification history:', error);
                }
            },

            scheduleNotification: async (notification: ScheduledNotification) => {
                try {
                    await notificationService.scheduleNotification(notification);
                    console.log('Notification scheduled successfully');
                } catch (error) {
                    console.error('Failed to schedule notification:', error);
                    throw error;
                }
            },

            cancelNotification: async (identifier: string) => {
                try {
                    await notificationService.cancelNotification(identifier);
                    console.log('Notification cancelled successfully');
                } catch (error) {
                    console.error('Failed to cancel notification:', error);
                    throw error;
                }
            },

            getAnalytics: async () => {
                try {
                    // This would get analytics for the current user
                    // Implementation depends on how user ID is accessed
                    const analytics = await notificationAnalytics.getNotificationHistory('current-user-id');

                    set({ analytics });
                } catch (error) {
                    console.error('Failed to get notification analytics:', error);
                }
            },

            clearNotifications: () => {
                set({
                    notifications: [],
                    unreadCount: 0,
                });
            },

            requestPermissions: async (): Promise<boolean> => {
                try {
                    return await notificationService.requestPermissions();
                } catch (error) {
                    console.error('Failed to request permissions:', error);
                    return false;
                }
            },

            checkPermissionStatus: async (): Promise<string> => {
                try {
                    // This would check the current permission status
                    // Implementation depends on Expo Notifications API
                    return 'granted'; // Placeholder
                } catch (error) {
                    console.error('Failed to check permission status:', error);
                    return 'denied';
                }
            },
        }),
        {
            name: 'notification-store',
            partialize: (state) => ({
                preferences: state.preferences,
                pushToken: state.pushToken,
            }),
        }
    )
);

// Selectors for easier access to specific parts of the state
export const useNotificationPreferences = () =>
    useNotificationStore(state => state.preferences);

export const useNotificationHistory = () =>
    useNotificationStore(state => ({
        notifications: state.notifications,
        unreadCount: state.unreadCount,
    }));

export const useNotificationActions = () =>
    useNotificationStore(state => ({
        updatePreferences: state.updatePreferences,
        sendTestNotification: state.sendTestNotification,
        markAsRead: state.markAsRead,
        markAllAsRead: state.markAllAsRead,
        scheduleNotification: state.scheduleNotification,
        cancelNotification: state.cancelNotification,
    }));

export const useNotificationStatus = () =>
    useNotificationStore(state => ({
        isInitialized: state.isInitialized,
        isLoading: state.isLoading,
        isUpdatingPreferences: state.isUpdatingPreferences,
        pushToken: state.pushToken,
    }));

export default useNotificationStore;