import React, { useState, useMemo } from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { Task } from '../../types/tasks';

interface TaskCalendarProps {
    tasks: Task[];
    selectedDate: Date;
    onDateSelect: (date: Date) => void;
    onTaskPress: (task: Task) => void;
    viewMode: 'daily' | 'weekly';
    onViewModeChange: (mode: 'daily' | 'weekly') => void;
    voiceEnabled?: boolean;
    onVoiceCommand?: (command: string) => void;
}

export default function TaskCalendar({
    tasks,
    selectedDate,
    onDateSelect,
    onTaskPress,
    viewMode,
    onViewModeChange,
    voiceEnabled = false,
    onVoiceCommand,
}: TaskCalendarProps) {
    const [currentWeek, setCurrentWeek] = useState(new Date());

    // Get tasks for a specific date
    const getTasksForDate = (date: Date): Task[] => {
        return tasks.filter(task => {
            const taskDate = new Date(task.dueDate);
            return (
                taskDate.getDate() === date.getDate() &&
                taskDate.getMonth() === date.getMonth() &&
                taskDate.getFullYear() === date.getFullYear()
            );
        });
    };

    // Get week dates starting from Monday
    const getWeekDates = (date: Date): Date[] => {
        const week = [];
        const startOfWeek = new Date(date);
        const day = startOfWeek.getDay();
        const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1); // Adjust for Monday start
        startOfWeek.setDate(diff);

        for (let i = 0; i < 7; i++) {
            const weekDate = new Date(startOfWeek);
            weekDate.setDate(startOfWeek.getDate() + i);
            week.push(weekDate);
        }
        return week;
    };

    const weekDates = useMemo(() => getWeekDates(currentWeek), [currentWeek]);
    const todaysTasks = useMemo(() => getTasksForDate(selectedDate), [tasks, selectedDate]);

    const navigateWeek = (direction: 'prev' | 'next') => {
        const newWeek = new Date(currentWeek);
        newWeek.setDate(currentWeek.getDate() + (direction === 'next' ? 7 : -7));
        setCurrentWeek(newWeek);
    };

    const navigateDay = (direction: 'prev' | 'next') => {
        const newDate = new Date(selectedDate);
        newDate.setDate(selectedDate.getDate() + (direction === 'next' ? 1 : -1));
        onDateSelect(newDate);
    };

    const isToday = (date: Date): boolean => {
        const today = new Date();
        return (
            date.getDate() === today.getDate() &&
            date.getMonth() === today.getMonth() &&
            date.getFullYear() === today.getFullYear()
        );
    };

    const isSelected = (date: Date): boolean => {
        return (
            date.getDate() === selectedDate.getDate() &&
            date.getMonth() === selectedDate.getMonth() &&
            date.getFullYear() === selectedDate.getFullYear()
        );
    };

    const getTaskTypeIcon = (type: Task['type']): string => {
        const icons = {
            watering: '💧',
            fertilizing: '🌱',
            monitoring: '👁️',
            harvesting: '🌾',
            planting: '🌱',
            pest_control: '🐛',
        };
        return icons[type] || '📋';
    };

    const getTaskPriorityColor = (priority: Task['priority']): string => {
        switch (priority) {
            case 'high': return 'bg-red-100 border-red-300 text-red-700';
            case 'medium': return 'bg-yellow-100 border-yellow-300 text-yellow-700';
            case 'low': return 'bg-green-100 border-green-300 text-green-700';
            default: return 'bg-gray-100 border-gray-300 text-gray-700';
        }
    };

    const handleVoiceCommand = () => {
        if (onVoiceCommand) {
            onVoiceCommand('navigate_calendar');
        }
    };

    const renderDailyView = () => (
        <View className="flex-1">
            {/* Date Navigation */}
            <View className="flex-row items-center justify-between p-4 bg-white border-b border-gray-200">
                <TouchableOpacity
                    onPress={() => navigateDay('prev')}
                    className="p-2"
                    accessibilityLabel="Previous day"
                >
                    <Text className="text-2xl text-gray-600">←</Text>
                </TouchableOpacity>

                <View className="flex-1 items-center">
                    <Text className="text-lg font-semibold text-gray-900">
                        {selectedDate.toLocaleDateString('en-US', {
                            weekday: 'long',
                            month: 'long',
                            day: 'numeric',
                        })}
                    </Text>
                    {isToday(selectedDate) && (
                        <Text className="text-sm text-green-600 font-medium">Today</Text>
                    )}
                </View>

                <TouchableOpacity
                    onPress={() => navigateDay('next')}
                    className="p-2"
                    accessibilityLabel="Next day"
                >
                    <Text className="text-2xl text-gray-600">→</Text>
                </TouchableOpacity>
            </View>

            {/* Tasks List */}
            <ScrollView className="flex-1 p-4">
                {todaysTasks.length > 0 ? (
                    <View className="gap-3">
                        {todaysTasks.map(task => (
                            <TouchableOpacity
                                key={task.id}
                                onPress={() => onTaskPress(task)}
                                className={`p-4 rounded-lg border ${task.completed
                                    ? 'bg-gray-50 border-gray-200 opacity-60'
                                    : 'bg-white border-gray-200'
                                    }`}
                                accessibilityLabel={`Task: ${task.title}`}
                                accessibilityHint={`${task.description}. Priority: ${task.priority}`}
                            >
                                <View className="flex-row items-start">
                                    <Text className="text-2xl mr-3">
                                        {getTaskTypeIcon(task.type)}
                                    </Text>

                                    <View className="flex-1">
                                        <View className="flex-row items-center justify-between mb-2">
                                            <Text className={`text-lg font-semibold ${task.completed ? 'text-gray-500 line-through' : 'text-gray-900'
                                                }`}>
                                                {task.title}
                                            </Text>

                                            <View className={`px-2 py-1 rounded-full border ${getTaskPriorityColor(task.priority)}`}>
                                                <Text className="text-xs font-medium">
                                                    {task.priority}
                                                </Text>
                                            </View>
                                        </View>

                                        <Text className={`text-sm mb-2 ${task.completed ? 'text-gray-400' : 'text-gray-600'
                                            }`}>
                                            {task.description}
                                        </Text>

                                        <View className="flex-row items-center justify-between">
                                            <Text className="text-xs text-gray-500">
                                                ⏱️ {task.estimatedDuration} min • 🏆 {task.pointsReward} pts
                                            </Text>

                                            {task.completed && (
                                                <Text className="text-xs text-green-600 font-medium">
                                                    ✅ Completed
                                                </Text>
                                            )}
                                        </View>
                                    </View>
                                </View>
                            </TouchableOpacity>
                        ))}
                    </View>
                ) : (
                    <View className="flex-1 items-center justify-center py-12">
                        <Text className="text-6xl mb-4">📅</Text>
                        <Text className="text-lg font-semibold text-gray-700 mb-2">
                            No tasks for this day
                        </Text>
                        <Text className="text-gray-500 text-center">
                            Enjoy your free day or check other dates for upcoming tasks
                        </Text>
                    </View>
                )}
            </ScrollView>
        </View>
    );

    const renderWeeklyView = () => (
        <View className="flex-1">
            {/* Week Navigation */}
            <View className="flex-row items-center justify-between p-4 bg-white border-b border-gray-200">
                <TouchableOpacity
                    onPress={() => navigateWeek('prev')}
                    className="p-2"
                    accessibilityLabel="Previous week"
                >
                    <Text className="text-2xl text-gray-600">←</Text>
                </TouchableOpacity>

                <Text className="text-lg font-semibold text-gray-900">
                    {weekDates[0].toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - {' '}
                    {weekDates[6].toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                </Text>

                <TouchableOpacity
                    onPress={() => navigateWeek('next')}
                    className="p-2"
                    accessibilityLabel="Next week"
                >
                    <Text className="text-2xl text-gray-600">→</Text>
                </TouchableOpacity>
            </View>

            {/* Week Grid */}
            <ScrollView className="flex-1">
                <View className="flex-row border-b border-gray-200">
                    {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => (
                        <View key={day} className="flex-1 p-2 border-r border-gray-200">
                            <Text className="text-center text-sm font-medium text-gray-600">
                                {day}
                            </Text>
                        </View>
                    ))}
                </View>

                <View className="flex-row min-h-[400px]">
                    {weekDates.map((date, index) => {
                        const dayTasks = getTasksForDate(date);
                        const isSelectedDate = isSelected(date);
                        const isTodayDate = isToday(date);

                        return (
                            <TouchableOpacity
                                key={index}
                                onPress={() => onDateSelect(date)}
                                className={`flex-1 p-2 border-r border-gray-200 ${isSelectedDate ? 'bg-green-50' : 'bg-white'
                                    }`}
                                accessibilityLabel={`Select ${date.toLocaleDateString()}`}
                            >
                                <View className={`w-8 h-8 rounded-full items-center justify-center mb-2 ${isTodayDate
                                    ? 'bg-green-500'
                                    : isSelectedDate
                                        ? 'bg-green-200'
                                        : 'bg-transparent'
                                    }`}>
                                    <Text className={`text-sm font-medium ${isTodayDate
                                        ? 'text-white'
                                        : isSelectedDate
                                            ? 'text-green-800'
                                            : 'text-gray-900'
                                        }`}>
                                        {date.getDate()}
                                    </Text>
                                </View>

                                <View className="gap-1">
                                    {dayTasks.slice(0, 3).map(task => (
                                        <TouchableOpacity
                                            key={task.id}
                                            onPress={() => onTaskPress(task)}
                                            className={`p-1 rounded text-xs ${task.completed
                                                ? 'bg-gray-100'
                                                : getTaskPriorityColor(task.priority)
                                                }`}
                                        >
                                            <Text className="text-xs truncate">
                                                {getTaskTypeIcon(task.type)} {task.title}
                                            </Text>
                                        </TouchableOpacity>
                                    ))}

                                    {dayTasks.length > 3 && (
                                        <Text className="text-xs text-gray-500 text-center">
                                            +{dayTasks.length - 3} more
                                        </Text>
                                    )}
                                </View>
                            </TouchableOpacity>
                        );
                    })}
                </View>
            </ScrollView>
        </View>
    );

    return (
        <View className="flex-1 bg-gray-50">
            {/* View Mode Toggle */}
            <View className="bg-white border-b border-gray-200 p-4">
                <View className="flex-row items-center justify-between">
                    <View className="flex-row bg-gray-100 rounded-lg p-1">
                        <TouchableOpacity
                            onPress={() => onViewModeChange('daily')}
                            className={`px-4 py-2 rounded-md ${viewMode === 'daily' ? 'bg-white shadow-sm' : ''
                                }`}
                            accessibilityLabel="Daily view"
                        >
                            <Text className={`text-sm font-medium ${viewMode === 'daily' ? 'text-gray-900' : 'text-gray-600'
                                }`}>
                                Daily
                            </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            onPress={() => onViewModeChange('weekly')}
                            className={`px-4 py-2 rounded-md ${viewMode === 'weekly' ? 'bg-white shadow-sm' : ''
                                }`}
                            accessibilityLabel="Weekly view"
                        >
                            <Text className={`text-sm font-medium ${viewMode === 'weekly' ? 'text-gray-900' : 'text-gray-600'
                                }`}>
                                Weekly
                            </Text>
                        </TouchableOpacity>
                    </View>

                    {voiceEnabled && (
                        <TouchableOpacity
                            onPress={handleVoiceCommand}
                            className="p-2 bg-green-500 rounded-full"
                            accessibilityLabel="Voice calendar navigation"
                        >
                            <Text className="text-white text-sm">🎤</Text>
                        </TouchableOpacity>
                    )}
                </View>
            </View>

            {viewMode === 'daily' ? renderDailyView() : renderWeeklyView()}
        </View>
    );
}