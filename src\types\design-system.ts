/**
 * TypeScript definitions for the AI Farming Assistant Design System
 */

// Color system types
export type ColorScale = {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string;
  600: string;
  700: string;
  800: string;
  900: string;
  950: string;
};

export type StatusColors = {
  success: string;
  warning: string;
  error: string;
  info: string;
};

// Component variant types
export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
export type ButtonSize = 'small' | 'medium' | 'large';

export type CardVariant = 'default' | 'elevated' | 'outlined' | 'filled';
export type CardPadding = 'none' | 'small' | 'medium' | 'large';

export type InputType = 'default' | 'email-address' | 'numeric' | 'phone-pad';

export type ModalSize = 'small' | 'medium' | 'large' | 'fullscreen';

// Agricultural component types
export type TaskType = 'watering' | 'fertilizing' | 'monitoring' | 'harvesting' | 'planting';
export type TaskPriority = 'low' | 'medium' | 'high';

export type WeatherCondition = 'sunny' | 'cloudy' | 'rainy' | 'stormy' | 'snowy' | 'foggy';

// Accessibility types
export type AccessibilityRole =
  | 'button'
  | 'checkbox'
  | 'text'
  | 'image'
  | 'link'
  | 'search'
  | 'header';

export interface AccessibilityProps {
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityRole?: AccessibilityRole;
  accessibilityState?: {
    disabled?: boolean;
    selected?: boolean;
    checked?: boolean;
    busy?: boolean;
    expanded?: boolean;
  };
  testID?: string;
}

// Voice interface types
export interface VoiceConfig {
  enabled: boolean;
  language: string;
  rate: number;
  pitch: number;
}

export interface VoiceCommand {
  phrase: string;
  action: string;
  category: 'navigation' | 'action' | 'query';
}

// Theme types
export interface ThemeConfig {
  colors: {
    primary: ColorScale;
    secondary: ColorScale;
    earth: ColorScale;
    status: StatusColors;
  };
  typography: {
    fontSizes: Record<string, number>;
    lineHeights: Record<string, number>;
    fontWeights: Record<string, string>;
  };
  spacing: Record<string, number>;
  accessibility: {
    minimumTouchTarget: number;
    recommendedTouchTarget: number;
    largeTextThreshold: number;
    highContrastMode: boolean;
  };
}

// Component prop types
export interface BaseComponentProps extends AccessibilityProps {
  className?: string;
  children?: React.ReactNode;
}

export interface InteractiveComponentProps extends BaseComponentProps {
  onPress?: () => void;
  disabled?: boolean;
  loading?: boolean;
}

// Agricultural data types
export interface FarmLocation {
  latitude: number;
  longitude: number;
  address: string;
  timezone: string;
}

export interface CropInfo {
  id: string;
  name: string;
  variety: string;
  plantingDate: Date;
  expectedHarvestDate: Date;
  growthStage: string;
}

export interface WeatherAlert {
  id: string;
  type: 'warning' | 'watch' | 'advisory';
  title: string;
  description: string;
  severity: 'minor' | 'moderate' | 'severe' | 'extreme';
  startTime: Date;
  endTime: Date;
}

// Form types
export interface FormFieldProps extends AccessibilityProps {
  label?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
}

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

// Animation types
export interface AnimationConfig {
  duration: number;
  easing: string;
  delay?: number;
}

// Responsive design types
export type Breakpoint = 'sm' | 'md' | 'lg' | 'xl';

export interface ResponsiveValue<T> {
  base: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredKeys<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
