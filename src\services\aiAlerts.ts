import { supabase } from './supabase/client';
import notificationService from './notifications';
import NotificationTemplateService from './notificationTemplates';
import { AIInsightNotification } from '../types/notifications';

export interface AIAnalysisResult {
    id: string;
    imageUrl?: string;
    analysisType: 'disease_detection' | 'nutrient_analysis' | 'pest_identification' | 'growth_assessment';
    findings: {
        primary: {
            issue: string;
            confidence: number;
            severity: 'low' | 'medium' | 'high' | 'critical';
            description: string;
        };
        secondary?: Array<{
            issue: string;
            confidence: number;
            description: string;
        }>;
    };
    recommendations: Array<{
        action: string;
        priority: 'immediate' | 'urgent' | 'moderate' | 'low';
        timeframe: string;
        cost?: 'low' | 'medium' | 'high';
    }>;
    affectedArea?: {
        cropPlanId?: string;
        location?: string;
        estimatedSpread?: string;
    };
    followUpRequired: boolean;
    estimatedImpact: {
        yieldLoss?: string;
        economicImpact?: string;
        spreadRisk?: string;
    };
}

export interface AIMonitoringRule {
    id: string;
    userId: string;
    cropType?: string;
    issueType: AIInsightNotification['insightType'];
    threshold: {
        confidence: number;
        severity: AIInsightNotification['urgency'];
    };
    frequency: 'immediate' | 'daily' | 'weekly';
    enabled: boolean;
    lastTriggered?: Date;
}

export class AIAlertService {
    private static instance: AIAlertService;
    private monitoringRules: Map<string, AIMonitoringRule[]> = new Map();

    static getInstance(): AIAlertService {
        if (!AIAlertService.instance) {
            AIAlertService.instance = new AIAlertService();
        }
        return AIAlertService.instance;
    }

    async initialize(): Promise<void> {
        try {
            await this.loadMonitoringRules();
            console.log('AI alert service initialized');
        } catch (error) {
            console.error('Failed to initialize AI alert service:', error);
        }
    }

    private async loadMonitoringRules(): Promise<void> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return;

            const { data: rules } = await supabase
                .from('ai_monitoring_rules')
                .select('*')
                .eq('user_id', user.id)
                .eq('enabled', true);

            if (rules) {
                this.monitoringRules.set(user.id, rules);
            } else {
                // Create default monitoring rules
                await this.createDefaultMonitoringRules(user.id);
            }
        } catch (error) {
            console.error('Failed to load monitoring rules:', error);
        }
    }

    private async createDefaultMonitoringRules(userId: string): Promise<void> {
        const defaultRules: Omit<AIMonitoringRule, 'id'>[] = [
            {
                userId,
                issueType: 'disease_detected',
                threshold: {
                    confidence: 0.7,
                    severity: 'medium',
                },
                frequency: 'immediate',
                enabled: true,
            },
            {
                userId,
                issueType: 'pest_warning',
                threshold: {
                    confidence: 0.6,
                    severity: 'medium',
                },
                frequency: 'immediate',
                enabled: true,
            },
            {
                userId,
                issueType: 'nutrient_deficiency',
                threshold: {
                    confidence: 0.8,
                    severity: 'low',
                },
                frequency: 'daily',
                enabled: true,
            },
            {
                userId,
                issueType: 'optimization_tip',
                threshold: {
                    confidence: 0.5,
                    severity: 'low',
                },
                frequency: 'weekly',
                enabled: true,
            },
        ];

        try {
            const { data: createdRules } = await supabase
                .from('ai_monitoring_rules')
                .insert(defaultRules)
                .select();

            if (createdRules) {
                this.monitoringRules.set(userId, createdRules);
            }
        } catch (error) {
            console.error('Failed to create default monitoring rules:', error);
        }
    }

    async processAIAnalysis(analysisResult: AIAnalysisResult): Promise<void> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return;

            // Check if this analysis should trigger an alert
            const shouldAlert = await this.shouldTriggerAlert(user.id, analysisResult);
            if (!shouldAlert) return;

            // Create AI insight notification
            const insight = this.createAIInsightFromAnalysis(analysisResult, user.id);

            // Send notification
            await this.sendAIInsightNotification(insight);

            // Store analysis result
            await this.storeAnalysisResult(analysisResult, user.id);

            // Update monitoring rule last triggered time
            await this.updateRuleLastTriggered(user.id, this.mapAnalysisTypeToInsightType(analysisResult.analysisType));

        } catch (error) {
            console.error('Failed to process AI analysis:', error);
        }
    }

    private async shouldTriggerAlert(userId: string, analysisResult: AIAnalysisResult): Promise<boolean> {
        const userRules = this.monitoringRules.get(userId) || [];
        const insightType = this.mapAnalysisTypeToInsightType(analysisResult.analysisType);

        const applicableRule = userRules.find(rule =>
            rule.issueType === insightType &&
            rule.enabled
        );

        if (!applicableRule) return false;

        // Check confidence threshold
        if (analysisResult.findings.primary.confidence < applicableRule.threshold.confidence) {
            return false;
        }

        // Check severity threshold
        const severityLevels = { low: 1, medium: 2, high: 3, critical: 4 };
        const analysisUrgency = this.mapSeverityToUrgency(analysisResult.findings.primary.severity);
        const thresholdUrgency = applicableRule.threshold.severity;

        if (severityLevels[analysisUrgency] < severityLevels[thresholdUrgency]) {
            return false;
        }

        // Check frequency limits
        if (applicableRule.lastTriggered) {
            const timeSinceLastTrigger = Date.now() - applicableRule.lastTriggered.getTime();
            const frequencyLimits = {
                immediate: 0, // No limit
                daily: 24 * 60 * 60 * 1000, // 24 hours
                weekly: 7 * 24 * 60 * 60 * 1000, // 7 days
            };

            if (timeSinceLastTrigger < frequencyLimits[applicableRule.frequency]) {
                return false;
            }
        }

        return true;
    }

    private createAIInsightFromAnalysis(analysisResult: AIAnalysisResult, userId: string): AIInsightNotification {
        const insightType = this.mapAnalysisTypeToInsightType(analysisResult.analysisType);
        const urgency = this.mapSeverityToUrgency(analysisResult.findings.primary.severity);

        return {
            id: `ai_insight_${Date.now()}`,
            userId,
            insightType,
            confidence: analysisResult.findings.primary.confidence,
            title: this.generateInsightTitle(insightType, analysisResult.findings.primary.issue),
            description: analysisResult.findings.primary.description,
            imageUrl: analysisResult.imageUrl,
            recommendations: analysisResult.recommendations.map(rec => rec.action),
            urgency,
            relatedCropPlan: analysisResult.affectedArea?.cropPlanId,
            actionRequired: analysisResult.followUpRequired,
            estimatedImpact: this.formatEstimatedImpact(analysisResult.estimatedImpact),
        };
    }

    private async sendAIInsightNotification(insight: AIInsightNotification): Promise<void> {
        try {
            const notification = NotificationTemplateService.createAIInsightNotification(insight);

            await notificationService.sendLocalNotification(
                notification.title,
                notification.body,
                notification.data,
                { priority: notification.priority }
            );

            // Store notification in database
            await supabase
                .from('notifications')
                .insert({
                    user_id: insight.userId,
                    type: 'ai_insight',
                    title: notification.title,
                    body: notification.body,
                    data: notification.data,
                    priority: notification.priority,
                    category: 'agricultural',
                });
        } catch (error) {
            console.error('Failed to send AI insight notification:', error);
        }
    }

    private async storeAnalysisResult(analysisResult: AIAnalysisResult, userId: string): Promise<void> {
        try {
            await supabase
                .from('ai_analysis_results')
                .insert({
                    user_id: userId,
                    analysis_id: analysisResult.id,
                    analysis_type: analysisResult.analysisType,
                    image_url: analysisResult.imageUrl,
                    findings: analysisResult.findings,
                    recommendations: analysisResult.recommendations,
                    affected_area: analysisResult.affectedArea,
                    estimated_impact: analysisResult.estimatedImpact,
                    follow_up_required: analysisResult.followUpRequired,
                    created_at: new Date().toISOString(),
                });
        } catch (error) {
            console.error('Failed to store analysis result:', error);
        }
    }

    private async updateRuleLastTriggered(userId: string, insightType: AIInsightNotification['insightType']): Promise<void> {
        try {
            await supabase
                .from('ai_monitoring_rules')
                .update({ last_triggered: new Date().toISOString() })
                .eq('user_id', userId)
                .eq('issue_type', insightType);

            // Update local cache
            const userRules = this.monitoringRules.get(userId);
            if (userRules) {
                const rule = userRules.find(r => r.issueType === insightType);
                if (rule) {
                    rule.lastTriggered = new Date();
                }
            }
        } catch (error) {
            console.error('Failed to update rule last triggered:', error);
        }
    }

    async createCustomAIAlert(
        userId: string,
        insightType: AIInsightNotification['insightType'],
        title: string,
        description: string,
        recommendations: string[],
        urgency: AIInsightNotification['urgency'] = 'medium',
        imageUrl?: string,
        relatedCropPlan?: string
    ): Promise<void> {
        try {
            const insight: AIInsightNotification = {
                id: `custom_ai_${Date.now()}`,
                userId,
                insightType,
                confidence: 1.0, // Custom alerts are 100% confident
                title,
                description,
                recommendations,
                urgency,
                imageUrl,
                relatedCropPlan,
                actionRequired: urgency === 'high' || urgency === 'critical',
                estimatedImpact: 'Custom alert - impact varies',
            };

            await this.sendAIInsightNotification(insight);
        } catch (error) {
            console.error('Failed to create custom AI alert:', error);
            throw error;
        }
    }

    async updateMonitoringRule(
        userId: string,
        ruleId: string,
        updates: Partial<AIMonitoringRule>
    ): Promise<void> {
        try {
            await supabase
                .from('ai_monitoring_rules')
                .update(updates)
                .eq('id', ruleId)
                .eq('user_id', userId);

            // Update local cache
            const userRules = this.monitoringRules.get(userId);
            if (userRules) {
                const ruleIndex = userRules.findIndex(r => r.id === ruleId);
                if (ruleIndex !== -1) {
                    userRules[ruleIndex] = { ...userRules[ruleIndex], ...updates };
                }
            }
        } catch (error) {
            console.error('Failed to update monitoring rule:', error);
            throw error;
        }
    }

    async getMonitoringRules(userId: string): Promise<AIMonitoringRule[]> {
        return this.monitoringRules.get(userId) || [];
    }

    async getAIInsightHistory(userId: string, limit: number = 20): Promise<AIInsightNotification[]> {
        try {
            const { data: notifications } = await supabase
                .from('notifications')
                .select('*')
                .eq('user_id', userId)
                .eq('type', 'ai_insight')
                .order('created_at', { ascending: false })
                .limit(limit);

            return notifications?.map(notification => ({
                id: notification.id,
                userId: notification.user_id,
                insightType: notification.data.insightType,
                confidence: notification.data.confidence,
                title: notification.title,
                description: notification.body,
                recommendations: notification.data.recommendations || [],
                urgency: notification.data.urgency,
                imageUrl: notification.data.imageUrl,
                relatedCropPlan: notification.data.relatedCropPlan,
                actionRequired: notification.data.actionRequired,
                estimatedImpact: notification.data.estimatedImpact,
            })) || [];
        } catch (error) {
            console.error('Failed to get AI insight history:', error);
            return [];
        }
    }

    // Helper methods
    private mapAnalysisTypeToInsightType(analysisType: AIAnalysisResult['analysisType']): AIInsightNotification['insightType'] {
        const mapping = {
            disease_detection: 'disease_detected' as const,
            nutrient_analysis: 'nutrient_deficiency' as const,
            pest_identification: 'pest_warning' as const,
            growth_assessment: 'optimization_tip' as const,
        };
        return mapping[analysisType];
    }

    private mapSeverityToUrgency(severity: AIAnalysisResult['findings']['primary']['severity']): AIInsightNotification['urgency'] {
        const mapping = {
            low: 'low' as const,
            medium: 'medium' as const,
            high: 'high' as const,
            critical: 'critical' as const,
        };
        return mapping[severity];
    }

    private generateInsightTitle(insightType: AIInsightNotification['insightType'], issue: string): string {
        const prefixes = {
            disease_detected: '🦠 Disease Alert',
            nutrient_deficiency: '🌱 Nutrient Issue',
            pest_warning: '🐛 Pest Warning',
            optimization_tip: '💡 Growth Tip',
        };
        return `${prefixes[insightType]}: ${issue}`;
    }

    private formatEstimatedImpact(impact: AIAnalysisResult['estimatedImpact']): string {
        const parts = [];
        if (impact.yieldLoss) parts.push(`Yield: ${impact.yieldLoss}`);
        if (impact.economicImpact) parts.push(`Cost: ${impact.economicImpact}`);
        if (impact.spreadRisk) parts.push(`Spread: ${impact.spreadRisk}`);
        return parts.join(', ') || 'Impact assessment pending';
    }
}

export const aiAlertService = AIAlertService.getInstance();
export default aiAlertService;