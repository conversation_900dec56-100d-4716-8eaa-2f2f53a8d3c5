import React, { useState } from 'react';
import { View, Text, Pressable, ScrollView } from 'react-native';

interface Category {
    id: string;
    name: string;
    icon: string;
    description: string;
    color: string;
    bgColor: string;
}

interface ConsultationCategoriesProps {
    onCategorySelect: (category: Category) => void;
    selectedCategory?: string;
    voiceEnabled?: boolean;
    onVoiceFeedback?: (text: string) => void;
}

const categories: Category[] = [
    {
        id: 'plant-health',
        name: 'صحة النباتات',
        icon: '🌱',
        description: 'تشخيص أمراض النباتات ونقص العناصر الغذائية ومشاكل النمو',
        color: 'text-green-600',
        bgColor: 'bg-green-100',
    },
    {
        id: 'soil',
        name: 'تحليل التربة',
        icon: '🌾',
        description: 'تحليل حالة التربة ومستوى الحموضة والمحتوى الغذائي',
        color: 'text-amber-600',
        bgColor: 'bg-amber-100',
    },
    {
        id: 'fertilizer',
        name: 'مساعدة الأسمدة',
        icon: '💧',
        description: 'الحصول على توصيات للأسمدة وتوقيت التطبيق',
        color: 'text-blue-600',
        bgColor: 'bg-blue-100',
    },
    {
        id: 'pests',
        name: 'مكافحة الآفات',
        icon: '🐛',
        description: 'تحديد الآفات والحصول على توصيات العلاج',
        color: 'text-red-600',
        bgColor: 'bg-red-100',
    },
];

export const ConsultationCategories: React.FC<ConsultationCategoriesProps> = ({
    onCategorySelect,
    selectedCategory,
    voiceEnabled = false,
    onVoiceFeedback,
}) => {
    const [expandedCategory, setExpandedCategory] = useState<string | null>(null);

    const handleCategoryPress = (category: Category) => {
        if (voiceEnabled && onVoiceFeedback) {
            onVoiceFeedback(`Selected ${category.name} consultation category`);
        }
        onCategorySelect(category);
        setExpandedCategory(expandedCategory === category.id ? null : category.id);
    };

    const handleVoiceSelection = () => {
        if (voiceEnabled && onVoiceFeedback) {
            const categoryNames = categories.map(c => c.name).join(', ');
            onVoiceFeedback(`Available categories: ${categoryNames}. Say the name of the category you want to select.`);
        }
    };

    return (
        <View className="bg-white">
            {/* Header */}
            <View className="px-4 py-3 border-b border-earth-200">
                <View className="flex-row items-center justify-between">
                    <Text className="text-lg font-semibold text-earth-900">
                        كيف يمكنني مساعدتك؟
                    </Text>

                    {voiceEnabled && (
                        <Pressable
                            onPress={handleVoiceSelection}
                            className="w-10 h-10 bg-primary-100 rounded-full items-center justify-center active:bg-primary-200"
                            accessibilityRole="button"
                            accessibilityLabel="Voice category selection"
                            accessibilityHint="Use voice to select a consultation category">
                            <Text className="text-primary-600 text-lg">🗣️</Text>
                        </Pressable>
                    )}
                </View>

                <Text className="text-sm text-earth-600 mt-1">
                    اختر فئة للحصول على نصائح زراعية متخصصة
                </Text>
            </View>

            {/* Categories Grid */}
            <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{ paddingHorizontal: 16, paddingVertical: 16 }}
                className="max-h-48">
                <View className="flex-row gap-3">
                    {categories.map((category) => {
                        const isSelected = selectedCategory === category.id;
                        const isExpanded = expandedCategory === category.id;

                        return (
                            <Pressable
                                key={category.id}
                                onPress={() => handleCategoryPress(category)}
                                className={`
                  w-40 rounded-2xl border-2 p-4 min-h-[120px]
                  ${isSelected
                                        ? `${category.bgColor} border-current ${category.color}`
                                        : 'bg-white border-earth-200'
                                    }
                  active:opacity-80 shadow-sm
                `}
                                accessibilityRole="button"
                                accessibilityLabel={`${category.name} category`}
                                accessibilityHint={category.description}
                                accessibilityState={{ selected: isSelected }}>

                                <View className="items-center">
                                    <Text className="text-3xl mb-2">{category.icon}</Text>

                                    <Text className={`
                    text-base font-semibold text-center mb-2
                    ${isSelected ? category.color : 'text-earth-900'}
                  `}>
                                        {category.name}
                                    </Text>

                                    <Text className={`
                    text-xs text-center leading-4
                    ${isSelected ? category.color.replace('600', '700') : 'text-earth-600'}
                  `}>
                                        {category.description}
                                    </Text>
                                </View>

                                {/* Selection Indicator */}
                                {isSelected && (
                                    <View className="absolute top-2 right-2">
                                        <View className={`w-6 h-6 rounded-full ${category.bgColor.replace('100', '600')} items-center justify-center`}>
                                            <Text className="text-white text-xs font-bold">✓</Text>
                                        </View>
                                    </View>
                                )}
                            </Pressable>
                        );
                    })}
                </View>
            </ScrollView>

            {/* Quick Start Suggestions */}
            {selectedCategory && (
                <View className="px-4 py-3 bg-earth-50 border-t border-earth-200">
                    <Text className="text-sm font-medium text-earth-700 mb-2">
                        أسئلة سريعة حول {categories.find(c => c.id === selectedCategory)?.name}:
                    </Text>

                    <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                        <View className="flex-row gap-2">
                            {getQuickQuestions(selectedCategory).map((question, index) => (
                                <Pressable
                                    key={index}
                                    onPress={() => {
                                        if (voiceEnabled && onVoiceFeedback) {
                                            onVoiceFeedback(`Selected quick question: ${question}`);
                                        }
                                        // This would trigger sending the question
                                    }}
                                    className="bg-white border border-earth-200 rounded-full px-3 py-2 active:bg-earth-100">
                                    <Text className="text-sm text-earth-700">{question}</Text>
                                </Pressable>
                            ))}
                        </View>
                    </ScrollView>
                </View>
            )}
        </View>
    );
};

const getQuickQuestions = (categoryId: string): string[] => {
    switch (categoryId) {
        case 'plant-health':
            return [
                'لماذا تتحول أوراق نباتاتي إلى اللون الأصفر؟',
                'ما هذه البقع على نباتاتي؟',
                'نباتاتي تبدو ذابلة، ماذا أفعل؟',
            ];
        case 'soil':
            return [
                'كيف أختبر حموضة التربة؟',
                'تربتي صلبة جداً، كيف أحسنها؟',
                'ما العناصر الغذائية التي تحتاجها تربتي؟',
            ];
        case 'fertilizer':
            return [
                'متى يجب أن أسمد محاصيلي؟',
                'ما أفضل سماد للطماطم؟',
                'كم كمية السماد التي يجب استخدامها؟',
            ];
        case 'pests':
            return [
                'كيف أحدد هذه الآفة؟',
                'طرق مكافحة الآفات الطبيعية؟',
                'متى أطبق المبيدات الحشرية؟',
            ];
        default:
            return [];
    }
};