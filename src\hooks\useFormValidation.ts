import { useState, useCallback } from 'react';

export interface ValidationRule {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    custom?: (value: string) => string | null;
}

export interface FormField {
    value: string;
    error: string | null;
    touched: boolean;
}

export interface FormState {
    [key: string]: FormField;
}

export interface ValidationRules {
    [key: string]: ValidationRule;
}

export const useFormValidation = (initialState: { [key: string]: string }, rules: ValidationRules) => {
    const [formState, setFormState] = useState<FormState>(() => {
        const state: FormState = {};
        Object.keys(initialState).forEach(key => {
            state[key] = {
                value: initialState[key],
                error: null,
                touched: false,
            };
        });
        return state;
    });

    const validateField = useCallback((fieldName: string, value: string): string | null => {
        const rule = rules[fieldName];
        if (!rule) return null;

        if (rule.required && !value.trim()) {
            return 'This field is required';
        }

        if (rule.minLength && value.length < rule.minLength) {
            return `Minimum ${rule.minLength} characters required`;
        }

        if (rule.maxLength && value.length > rule.maxLength) {
            return `Maximum ${rule.maxLength} characters allowed`;
        }

        if (rule.pattern && !rule.pattern.test(value)) {
            if (fieldName === 'email') {
                return 'Please enter a valid email address';
            }
            if (fieldName === 'phone') {
                return 'Please enter a valid phone number';
            }
            return 'Invalid format';
        }

        if (rule.custom) {
            return rule.custom(value);
        }

        return null;
    }, [rules]);

    const updateField = useCallback((fieldName: string, value: string, shouldValidate = true) => {
        setFormState(prev => ({
            ...prev,
            [fieldName]: {
                value,
                error: shouldValidate ? validateField(fieldName, value) : prev[fieldName].error,
                touched: true,
            },
        }));
    }, [validateField]);

    const validateForm = useCallback((): boolean => {
        let isValid = true;
        const newState = { ...formState };

        Object.keys(formState).forEach(fieldName => {
            const error = validateField(fieldName, formState[fieldName].value);
            newState[fieldName] = {
                ...formState[fieldName],
                error,
                touched: true,
            };
            if (error) isValid = false;
        });

        setFormState(newState);
        return isValid;
    }, [formState, validateField]);

    const resetForm = useCallback(() => {
        const state: FormState = {};
        Object.keys(initialState).forEach(key => {
            state[key] = {
                value: initialState[key],
                error: null,
                touched: false,
            };
        });
        setFormState(state);
    }, [initialState]);

    const getFieldProps = useCallback((fieldName: string) => ({
        value: formState[fieldName]?.value || '',
        error: formState[fieldName]?.touched ? formState[fieldName]?.error : null,
        onChangeText: (value: string) => updateField(fieldName, value),
    }), [formState, updateField]);

    return {
        formState,
        updateField,
        validateForm,
        resetForm,
        getFieldProps,
        isValid: Object.values(formState).every(field => !field.error),
        hasErrors: Object.values(formState).some(field => field.error && field.touched),
    };
};