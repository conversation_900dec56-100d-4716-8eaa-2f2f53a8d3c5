import { create } from 'zustand';
import {
    SubscriptionPlan,
    UserSubscription,
    SubscriptionUsage,
    FeatureAccess,
    getFeatureAccess,
    canAccessFeature,
    getAIConsultationLimit
} from '../types/subscription';
import { SubscriptionService } from '../services/supabase/subscription';

interface SubscriptionState {
    // State
    currentSubscription: UserSubscription | null;
    availablePlans: SubscriptionPlan[];
    usage: SubscriptionUsage | null;
    featureAccess: FeatureAccess | null;
    isLoading: boolean;
    error: string | null;

    // Actions
    loadSubscriptionPlans: () => Promise<void>;
    loadUserSubscription: (userId: string) => Promise<void>;
    loadUsage: (userId: string) => Promise<void>;
    createSubscription: (userId: string, planId: string, paymentMethodId?: string) => Promise<boolean>;
    cancelSubscription: (userId: string, cancelAtPeriodEnd?: boolean) => Promise<boolean>;
    changeSubscription: (userId: string, newPlanId: string, paymentMethodId?: string) => Promise<boolean>;
    checkFeatureAccess: (userId: string, feature: keyof FeatureAccess) => Promise<boolean>;
    checkAIConsultationLimit: (userId: string) => Promise<{ canUse: boolean; used: number; limit: number }>;
    updateUsage: (userId: string, usageType: 'ai_consultations' | 'image_analyses' | 'storage' | 'api_calls', increment?: number) => Promise<void>;
    getSubscriptionTier: (userId: string) => Promise<string>;
    clearError: () => void;
    reset: () => void;
}

export const useSubscriptionStore = create<SubscriptionState>((set, get) => ({
    // Initial state
    currentSubscription: null,
    availablePlans: [],
    usage: null,
    featureAccess: null,
    isLoading: false,
    error: null,

    // Load available subscription plans
    loadSubscriptionPlans: async () => {
        set({ isLoading: true, error: null });

        try {
            const { plans, error } = await SubscriptionService.getSubscriptionPlans();

            if (error) {
                set({ error: error.message, isLoading: false });
                return;
            }

            set({ availablePlans: plans, isLoading: false });
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to load subscription plans',
                isLoading: false,
            });
        }
    },

    // Load user's current subscription
    loadUserSubscription: async (userId: string) => {
        set({ isLoading: true, error: null });

        try {
            const { subscription, error } = await SubscriptionService.getUserSubscription(userId);

            if (error) {
                set({ error: error.message, isLoading: false });
                return;
            }

            // Determine subscription tier for feature access
            let tier = 'free';
            if (subscription) {
                const plan = get().availablePlans.find(p => p.id === subscription.plan_id);
                tier = plan?.name.toLowerCase() || 'free';
            }

            const featureAccess = getFeatureAccess(tier);

            set({
                currentSubscription: subscription,
                featureAccess,
                isLoading: false
            });

            // Load usage if subscription exists
            if (subscription) {
                await get().loadUsage(userId);
            }
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to load subscription',
                isLoading: false,
            });
        }
    },

    // Load subscription usage
    loadUsage: async (userId: string) => {
        try {
            const { usage, error } = await SubscriptionService.getSubscriptionUsage(userId);

            if (error) {
                set({ error: error.message });
                return;
            }

            set({ usage });
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to load usage',
            });
        }
    },

    // Create new subscription
    createSubscription: async (userId: string, planId: string, paymentMethodId?: string) => {
        set({ isLoading: true, error: null });

        try {
            const { subscription, error } = await SubscriptionService.createSubscription(userId, planId, paymentMethodId);

            if (error) {
                set({ error: error.message, isLoading: false });
                return false;
            }

            // Update local state
            const plan = get().availablePlans.find(p => p.id === planId);
            const featureAccess = getFeatureAccess(plan?.name.toLowerCase() || 'free');

            set({
                currentSubscription: subscription,
                featureAccess,
                isLoading: false
            });

            // Load usage for new subscription
            if (subscription) {
                await get().loadUsage(userId);
            }

            return true;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to create subscription',
                isLoading: false,
            });
            return false;
        }
    },

    // Cancel subscription
    cancelSubscription: async (userId: string, cancelAtPeriodEnd = true) => {
        set({ isLoading: true, error: null });

        try {
            const { error } = await SubscriptionService.cancelSubscription(userId, cancelAtPeriodEnd);

            if (error) {
                set({ error: error.message, isLoading: false });
                return false;
            }

            // Update local state
            if (get().currentSubscription) {
                set(state => ({
                    currentSubscription: state.currentSubscription ? {
                        ...state.currentSubscription,
                        cancel_at_period_end: cancelAtPeriodEnd,
                        status: cancelAtPeriodEnd ? state.currentSubscription.status : 'cancelled',
                        cancelled_at: cancelAtPeriodEnd ? undefined : new Date().toISOString(),
                    } : null,
                    featureAccess: cancelAtPeriodEnd ? state.featureAccess : getFeatureAccess('free'),
                    isLoading: false,
                }));
            }

            return true;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to cancel subscription',
                isLoading: false,
            });
            return false;
        }
    },

    // Change subscription plan
    changeSubscription: async (userId: string, newPlanId: string, paymentMethodId?: string) => {
        set({ isLoading: true, error: null });

        try {
            const { subscription, error } = await SubscriptionService.changeSubscription(userId, newPlanId, paymentMethodId);

            if (error) {
                set({ error: error.message, isLoading: false });
                return false;
            }

            // Update local state
            const plan = get().availablePlans.find(p => p.id === newPlanId);
            const featureAccess = getFeatureAccess(plan?.name.toLowerCase() || 'free');

            set({
                currentSubscription: subscription,
                featureAccess,
                isLoading: false
            });

            // Load usage for new subscription
            if (subscription) {
                await get().loadUsage(userId);
            }

            return true;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to change subscription',
                isLoading: false,
            });
            return false;
        }
    },

    // Check feature access
    checkFeatureAccess: async (userId: string, feature: keyof FeatureAccess) => {
        try {
            const { hasAccess, error } = await SubscriptionService.checkFeatureAccess(userId, feature);

            if (error) {
                set({ error: error.message });
                return false;
            }

            return hasAccess;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to check feature access',
            });
            return false;
        }
    },

    // Check AI consultation limits
    checkAIConsultationLimit: async (userId: string) => {
        try {
            const { canUse, used, limit, error } = await SubscriptionService.checkAIConsultationLimit(userId);

            if (error) {
                set({ error: error.message });
                return { canUse: false, used: 0, limit: 0 };
            }

            return { canUse, used, limit };
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to check AI consultation limit',
            });
            return { canUse: false, used: 0, limit: 0 };
        }
    },

    // Update usage
    updateUsage: async (userId: string, usageType: 'ai_consultations' | 'image_analyses' | 'storage' | 'api_calls', increment = 1) => {
        try {
            const { error } = await SubscriptionService.updateUsage(userId, usageType, increment);

            if (error) {
                set({ error: error.message });
                return;
            }

            // Update local usage state
            if (get().usage) {
                set(state => ({
                    usage: state.usage ? {
                        ...state.usage,
                        [`${usageType}_used`]: (state.usage as any)[`${usageType}_used`] + increment,
                        updated_at: new Date().toISOString(),
                    } : null,
                }));
            }
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to update usage',
            });
        }
    },

    // Get subscription tier
    getSubscriptionTier: async (userId: string) => {
        try {
            const subscription = get().currentSubscription;
            if (!subscription) {
                await get().loadUserSubscription(userId);
            }

            const currentSub = get().currentSubscription;
            if (!currentSub) {
                return 'free';
            }

            const plan = get().availablePlans.find(p => p.id === currentSub.plan_id);
            return plan?.name.toLowerCase() || 'free';
        } catch (error) {
            console.error('Error getting subscription tier:', error);
            return 'free';
        }
    },

    // Clear error
    clearError: () => {
        set({ error: null });
    },

    // Reset store
    reset: () => {
        set({
            currentSubscription: null,
            availablePlans: [],
            usage: null,
            featureAccess: null,
            isLoading: false,
            error: null,
        });
    },
}));