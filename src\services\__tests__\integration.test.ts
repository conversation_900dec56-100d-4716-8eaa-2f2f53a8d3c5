import { POINTS_EARNING_RULES, DEFAULT_ACHIEVEMENTS } from '../../types/points';
import { SUBSCRIPTION_PLANS, getFeatureAccess, canAccessFeature } from '../../types/subscription';
import { formatPoints, getTransactionIcon, calculateStreakMultiplier } from '../../utils/points';
import { formatPrice, getSubscriptionBenefits, isUpgrade } from '../../utils/subscription';

describe('Points and Subscription Integration', () => {
    describe('Points System Configuration', () => {
        it('should have valid points earning rules', () => {
            expect(POINTS_EARNING_RULES).toBeDefined();
            expect(POINTS_EARNING_RULES.length).toBeGreaterThan(0);

            // Check that all required fields are present
            POINTS_EARNING_RULES.forEach(rule => {
                expect(rule.source).toBeDefined();
                expect(rule.base_points).toBeDefined();
                expect(rule.description).toBeDefined();
                expect(typeof rule.is_active).toBe('boolean');
            });
        });

        it('should have valid default achievements', () => {
            expect(DEFAULT_ACHIEVEMENTS).toBeDefined();
            expect(DEFAULT_ACHIEVEMENTS.length).toBeGreaterThan(0);

            DEFAULT_ACHIEVEMENTS.forEach(achievement => {
                expect(achievement.name).toBeDefined();
                expect(achievement.description).toBeDefined();
                expect(achievement.points_reward).toBeGreaterThan(0);
                expect(achievement.requirements).toBeDefined();
                expect(['farming', 'community', 'learning', 'consistency', 'milestone']).toContain(achievement.category);
            });
        });

        it('should format points correctly', () => {
            expect(formatPoints(100)).toBe('100');
            expect(formatPoints(1500)).toBe('1.5K');
            expect(formatPoints(1500000)).toBe('1.5M');
        });

        it('should get correct transaction icons', () => {
            expect(getTransactionIcon('task_completion')).toBe('✅');
            expect(getTransactionIcon('daily_checkin')).toBe('📅');
            expect(getTransactionIcon('ai_consultation')).toBe('🤖');
        });

        it('should calculate streak multiplier correctly', () => {
            expect(calculateStreakMultiplier(0)).toBe(1.0);
            expect(calculateStreakMultiplier(10)).toBeCloseTo(1.33, 2);
            expect(calculateStreakMultiplier(30)).toBeCloseTo(2.0, 1);
            expect(calculateStreakMultiplier(50)).toBeCloseTo(2.0, 1); // Max cap
        });
    });

    describe('Subscription System Configuration', () => {
        it('should have valid subscription plans', () => {
            expect(SUBSCRIPTION_PLANS).toBeDefined();
            expect(SUBSCRIPTION_PLANS.length).toBe(4); // Free, Basic, Premium, Pro

            SUBSCRIPTION_PLANS.forEach(plan => {
                expect(plan.name).toBeDefined();
                expect(plan.description).toBeDefined();
                expect(plan.price).toBeGreaterThanOrEqual(0);
                expect(plan.currency).toBe('USD');
                expect(['monthly', 'yearly']).toContain(plan.billing_period);
                expect(Array.isArray(plan.features)).toBe(true);
                expect(plan.points_included).toBeGreaterThanOrEqual(0);
                expect(plan.ai_consultations_limit).toBeGreaterThanOrEqual(-1);
            });
        });

        it('should provide correct feature access for different tiers', () => {
            const freeAccess = getFeatureAccess('free');
            const basicAccess = getFeatureAccess('basic');
            const premiumAccess = getFeatureAccess('premium');
            const proAccess = getFeatureAccess('pro');

            // Free tier limitations
            expect(freeAccess.ai_consultations).toBe(true);
            expect(freeAccess.image_analysis).toBe(false);
            expect(freeAccess.advanced_analytics).toBe(false);
            expect(freeAccess.offline_mode).toBe(false);

            // Basic tier improvements
            expect(basicAccess.image_analysis).toBe(true);
            expect(basicAccess.offline_mode).toBe(true);
            expect(basicAccess.priority_support).toBe(true);

            // Premium tier advanced features
            expect(premiumAccess.advanced_analytics).toBe(true);
            expect(premiumAccess.api_access).toBe(true);

            // Pro tier enterprise features
            expect(proAccess.multi_farm_management).toBe(true);
            expect(proAccess.team_collaboration).toBe(true);
        });

        it('should correctly check feature access', () => {
            expect(canAccessFeature('free', 'ai_consultations')).toBe(true);
            expect(canAccessFeature('free', 'image_analysis')).toBe(false);
            expect(canAccessFeature('basic', 'image_analysis')).toBe(true);
            expect(canAccessFeature('premium', 'advanced_analytics')).toBe(true);
            expect(canAccessFeature('pro', 'multi_farm_management')).toBe(true);
        });

        it('should format prices correctly', () => {
            expect(formatPrice(9.99)).toBe('$9.99');
            expect(formatPrice(19.99)).toBe('$19.99');
            expect(formatPrice(0)).toBe('$0.00');
        });

        it('should get subscription benefits', () => {
            const freePlan = SUBSCRIPTION_PLANS.find(p => p.name === 'Free')!;
            const basicPlan = SUBSCRIPTION_PLANS.find(p => p.name === 'Basic')!;

            const freeBenefits = getSubscriptionBenefits(freePlan);
            const basicBenefits = getSubscriptionBenefits(basicPlan);

            expect(freeBenefits).toContain('5 AI consultations per month');
            expect(freeBenefits).toContain('100 bonus points monthly');

            expect(basicBenefits).toContain('25 AI consultations per month');
            expect(basicBenefits).toContain('500 bonus points monthly');
            expect(basicBenefits).toContain('Priority customer support');
        });

        it('should correctly identify plan upgrades', () => {
            const freePlan = SUBSCRIPTION_PLANS.find(p => p.name === 'Free')!;
            const basicPlan = SUBSCRIPTION_PLANS.find(p => p.name === 'Basic')!;
            const premiumPlan = SUBSCRIPTION_PLANS.find(p => p.name === 'Premium')!;

            expect(isUpgrade(freePlan, basicPlan)).toBe(true);
            expect(isUpgrade(basicPlan, premiumPlan)).toBe(true);
            expect(isUpgrade(premiumPlan, basicPlan)).toBe(false);
        });
    });

    describe('Points and Subscription Integration', () => {
        it('should have consistent points allocation across plans', () => {
            const plans = SUBSCRIPTION_PLANS;

            // Verify points increase with plan tier
            expect(plans[0].points_included).toBeLessThan(plans[1].points_included);
            expect(plans[1].points_included).toBeLessThan(plans[2].points_included);
            expect(plans[2].points_included).toBeLessThan(plans[3].points_included);
        });

        it('should have consistent AI consultation limits across plans', () => {
            const plans = SUBSCRIPTION_PLANS;

            // Free plan should have lowest limit
            expect(plans[0].ai_consultations_limit).toBe(5);

            // Basic plan should have higher limit
            expect(plans[1].ai_consultations_limit).toBe(25);

            // Premium and Pro should have unlimited
            expect(plans[2].ai_consultations_limit).toBe(-1);
            expect(plans[3].ai_consultations_limit).toBe(-1);
        });

        it('should have logical feature progression', () => {
            const freeAccess = getFeatureAccess('free');
            const basicAccess = getFeatureAccess('basic');
            const premiumAccess = getFeatureAccess('premium');
            const proAccess = getFeatureAccess('pro');

            // Basic features should be available in all tiers
            expect(freeAccess.ai_consultations).toBe(true);
            expect(basicAccess.ai_consultations).toBe(true);
            expect(premiumAccess.ai_consultations).toBe(true);
            expect(proAccess.ai_consultations).toBe(true);

            // Premium features should only be in premium+ tiers
            expect(freeAccess.advanced_analytics).toBe(false);
            expect(basicAccess.advanced_analytics).toBe(false);
            expect(premiumAccess.advanced_analytics).toBe(true);
            expect(proAccess.advanced_analytics).toBe(true);

            // Pro features should only be in pro tier
            expect(freeAccess.multi_farm_management).toBe(false);
            expect(basicAccess.multi_farm_management).toBe(false);
            expect(premiumAccess.multi_farm_management).toBe(false);
            expect(proAccess.multi_farm_management).toBe(true);
        });
    });
});