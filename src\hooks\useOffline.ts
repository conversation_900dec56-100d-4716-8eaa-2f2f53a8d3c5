import { useEffect, useState, useCallback, useRef } from 'react';
import { useOfflineStore, useConnectivity, useSyncStatus } from '../stores/offline';
import { offlineService, connectivityService, offlineModeService } from '../services/offline';
import { OfflineUtils } from '../utils/offline';

/**
 * Main hook for offline functionality
 */
export const useOffline = () => {
    const { isOnline, isOfflineMode } = useConnectivity();
    const { syncInProgress, pendingActions, failedActions, forceSync } = useSyncStatus();
    const { initialize, clearOfflineData } = useOfflineStore();

    const [isInitialized, setIsInitialized] = useState(false);

    useEffect(() => {
        const initializeOffline = async () => {
            try {
                await initialize();
                setIsInitialized(true);
            } catch (error) {
                console.error('Failed to initialize offline functionality:', error);
            }
        };

        initializeOffline();
    }, [initialize]);

    const retryConnection = useCallback(async () => {
        return await offlineModeService.forceReconnectionAttempt();
    }, []);

    const clearAllOfflineData = useCallback(async () => {
        await clearOfflineData();
    }, [clearOfflineData]);

    return {
        isInitialized,
        isOnline,
        isOfflineMode,
        syncInProgress,
        pendingActions,
        failedActions,
        statusMessage: OfflineUtils.getStatusMessage(),
        connectionQuality: OfflineUtils.getConnectionQuality(),
        forceSync,
        retryConnection,
        clearAllOfflineData
    };
};

/**
 * Hook for offline-aware data operations
 */
export const useOfflineData = <T = any>(
    table: string,
    userId?: string
) => {
    const { createOfflineData, updateOfflineData, deleteOfflineData, getOfflineData } = useOfflineStore();
    const { isOnline } = useConnectivity();

    const [data, setData] = useState<T[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const loadData = useCallback(async (id?: string) => {
        try {
            setLoading(true);
            setError(null);
            const result = await getOfflineData(table, id, userId);
            setData(result);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load data');
        } finally {
            setLoading(false);
        }
    }, [table, userId, getOfflineData]);

    const createData = useCallback(async (newData: Partial<T>) => {
        try {
            const result = await createOfflineData(table, newData, userId);
            await loadData(); // Refresh data
            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to create data');
            throw err;
        }
    }, [table, userId, createOfflineData, loadData]);

    const updateData = useCallback(async (id: string, updates: Partial<T>) => {
        try {
            const result = await updateOfflineData(table, id, updates, userId);
            await loadData(); // Refresh data
            return result;
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to update data');
            throw err;
        }
    }, [table, userId, updateOfflineData, loadData]);

    const deleteData = useCallback(async (id: string) => {
        try {
            await deleteOfflineData(table, id, userId);
            await loadData(); // Refresh data
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to delete data');
            throw err;
        }
    }, [table, userId, deleteOfflineData, loadData]);

    useEffect(() => {
        loadData();
    }, [loadData]);

    return {
        data,
        loading,
        error,
        isOnline,
        refresh: loadData,
        create: createData,
        update: updateData,
        delete: deleteData
    };
};

/**
 * Hook for managing offline queue and sync status
 */
export const useOfflineQueue = () => {
    const { pendingActions, failedActions, forceSync } = useSyncStatus();
    const [queueStats, setQueueStats] = useState({
        pending: 0,
        syncing: 0,
        completed: 0,
        failed: 0
    });

    const refreshStats = useCallback(async () => {
        try {
            const stats = await offlineService.getSyncQueueStats();
            setQueueStats(stats);
        } catch (error) {
            console.error('Failed to refresh queue stats:', error);
        }
    }, []);

    useEffect(() => {
        refreshStats();

        // Refresh stats periodically
        const interval = setInterval(refreshStats, 10000); // Every 10 seconds
        return () => clearInterval(interval);
    }, [refreshStats]);

    const retryFailedActions = useCallback(async () => {
        try {
            await useOfflineStore.getState().retryFailedActions();
            await refreshStats();
        } catch (error) {
            console.error('Failed to retry failed actions:', error);
        }
    }, [refreshStats]);

    const clearQueue = useCallback(async () => {
        try {
            await offlineService.clearOfflineData();
            await refreshStats();
        } catch (error) {
            console.error('Failed to clear queue:', error);
        }
    }, [refreshStats]);

    return {
        queueStats,
        pendingActions,
        failedActions,
        forceSync,
        retryFailedActions,
        clearQueue,
        refreshStats
    };
};

/**
 * Hook for offline-aware operations with automatic retry
 */
export const useOfflineOperation = <T extends any[], R>(
    operation: (...args: T) => Promise<R>,
    options: {
        maxRetries?: number;
        retryDelay?: number;
        fallback?: (...args: T) => Promise<R>;
    } = {}
) => {
    const { maxRetries = 3, retryDelay = 1000, fallback } = options;
    const { isOnline } = useConnectivity();

    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const execute = useCallback(async (...args: T): Promise<R> => {
        setLoading(true);
        setError(null);

        try {
            // If offline and fallback is provided, use fallback
            if (!isOnline && fallback) {
                return await fallback(...args);
            }

            // Try the operation with retry logic
            let lastError: Error;
            for (let attempt = 0; attempt <= maxRetries; attempt++) {
                try {
                    const result = await operation(...args);
                    return result;
                } catch (err) {
                    lastError = err as Error;

                    if (attempt < maxRetries) {
                        await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt)));
                    }
                }
            }

            throw lastError!;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Operation failed';
            setError(errorMessage);
            throw err;
        } finally {
            setLoading(false);
        }
    }, [operation, isOnline, maxRetries, retryDelay, fallback]);

    return {
        execute,
        loading,
        error,
        isOnline
    };
};

/**
 * Hook for monitoring storage usage
 */
export const useOfflineStorage = () => {
    const [storageInfo, setStorageInfo] = useState({
        databaseSize: 0,
        cacheStats: {},
        syncQueueStats: {}
    });
    const [loading, setLoading] = useState(false);

    const refreshStorageInfo = useCallback(async () => {
        try {
            setLoading(true);
            const info = await OfflineUtils.getStorageStats();
            setStorageInfo(info);
        } catch (error) {
            console.error('Failed to get storage info:', error);
        } finally {
            setLoading(false);
        }
    }, []);

    const cleanup = useCallback(async (maxAge?: number) => {
        try {
            await OfflineUtils.cleanupOldData(maxAge);
            await refreshStorageInfo();
        } catch (error) {
            console.error('Failed to cleanup storage:', error);
        }
    }, [refreshStorageInfo]);

    useEffect(() => {
        refreshStorageInfo();
    }, [refreshStorageInfo]);

    return {
        storageInfo,
        loading,
        refresh: refreshStorageInfo,
        cleanup,
        formattedSize: OfflineUtils.formatBytes(storageInfo.databaseSize)
    };
};

/**
 * Hook for debounced sync operations
 */
export const useDebouncedSync = (delay: number = 2000) => {
    const timeoutRef = useRef<NodeJS.Timeout>();
    const { forceSync } = useSyncStatus();
    const { isOnline } = useConnectivity();

    const debouncedSync = useCallback(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        timeoutRef.current = setTimeout(async () => {
            if (isOnline) {
                await forceSync();
            }
        }, delay);
    }, [delay, isOnline, forceSync]);

    useEffect(() => {
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);

    return debouncedSync;
};

/**
 * Hook for offline-aware form handling
 */
export const useOfflineForm = <T extends Record<string, any>>(
    table: string,
    userId?: string
) => {
    const { create, update } = useOfflineData<T>(table, userId);
    const { isOnline } = useConnectivity();
    const debouncedSync = useDebouncedSync();

    const [isDirty, setIsDirty] = useState(false);
    const [isSaving, setIsSaving] = useState(false);

    const saveForm = useCallback(async (data: T, id?: string) => {
        try {
            setIsSaving(true);

            let result;
            if (id) {
                result = await update(id, data);
            } else {
                result = await create(data);
            }

            setIsDirty(false);

            // Trigger debounced sync if offline
            if (!isOnline) {
                debouncedSync();
            }

            return result;
        } catch (error) {
            console.error('Failed to save form:', error);
            throw error;
        } finally {
            setIsSaving(false);
        }
    }, [create, update, isOnline, debouncedSync]);

    const markDirty = useCallback(() => {
        setIsDirty(true);
    }, []);

    return {
        saveForm,
        markDirty,
        isDirty,
        isSaving,
        isOnline
    };
};