import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Modal, Platform } from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { CropType } from '../../data/crops';
import { PlantingSchedule } from '../../types/crops';
import { CropPlanningService } from '../../services/cropPlanning';

interface PlantingDatePickerProps {
    selectedDate: Date;
    onDateChange: (date: Date) => void;
    selectedCrop?: CropType;
    location?: { latitude: number; longitude: number };
    voiceEnabled?: boolean;
    onVoiceCommand?: (command: string) => void;
}

export default function PlantingDatePicker({
    selectedDate,
    onDateChange,
    selectedCrop,
    location,
    voiceEnabled = false,
    onVoiceCommand,
}: PlantingDatePickerProps) {
    const [showDatePicker, setShowDatePicker] = useState(false);
    const [plantingSchedule, setPlantingSchedule] = useState<PlantingSchedule | null>(null);

    useEffect(() => {
        if (selectedCrop) {
            // Generate planting schedule for the selected crop
            const schedule = CropPlanningService['getPlantingSchedule'](selectedCrop, new Date());
            setPlantingSchedule(schedule);
        }
    }, [selectedCrop]);

    const formatDate = (date: Date): string => {
        return date.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const formatDateAr = (date: Date): string => {
        return date.toLocaleDateString('ar-SA', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const getDateRecommendation = (date: Date): {
        status: 'optimal' | 'good' | 'late' | 'too_early' | 'too_late';
        message: string;
        messageAr: string;
    } => {
        if (!plantingSchedule) {
            return {
                status: 'good',
                message: 'Select a crop to see planting recommendations',
                messageAr: 'اختر محصولاً لرؤية توصيات الزراعة',
            };
        }

        const dateTime = date.getTime();
        const optimalStart = plantingSchedule.optimalStartDate.getTime();
        const optimalEnd = plantingSchedule.optimalEndDate.getTime();
        const earlyStart = plantingSchedule.earlyStartDate.getTime();
        const lateEnd = plantingSchedule.lateStartDate.getTime();

        if (dateTime >= optimalStart && dateTime <= optimalEnd) {
            return {
                status: 'optimal',
                message: 'Perfect timing for planting!',
                messageAr: 'توقيت مثالي للزراعة!',
            };
        } else if (dateTime >= earlyStart && dateTime < optimalStart) {
            return {
                status: 'good',
                message: 'Good time to plant, slightly early',
                messageAr: 'وقت جيد للزراعة، مبكر قليلاً',
            };
        } else if (dateTime > optimalEnd && dateTime <= lateEnd) {
            return {
                status: 'late',
                message: 'Still okay to plant, but getting late',
                messageAr: 'لا يزال بإمكان الزراعة، لكن الوقت يتأخر',
            };
        } else if (dateTime < earlyStart) {
            return {
                status: 'too_early',
                message: 'Too early for optimal growth',
                messageAr: 'مبكر جداً للنمو الأمثل',
            };
        } else {
            return {
                status: 'too_late',
                message: 'Too late for this growing season',
                messageAr: 'متأخر جداً لهذا الموسم الزراعي',
            };
        }
    };

    const handleDateChange = (event: any, date?: Date) => {
        if (Platform.OS === 'android') {
            setShowDatePicker(false);
        }

        if (date) {
            onDateChange(date);
        }
    };

    const handleVoiceCommand = () => {
        if (onVoiceCommand) {
            onVoiceCommand('select_planting_date');
        }
    };

    const recommendation = getDateRecommendation(selectedDate);

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'optimal': return 'text-green-600 bg-green-50 border-green-200';
            case 'good': return 'text-blue-600 bg-blue-50 border-blue-200';
            case 'late': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
            case 'too_early': return 'text-orange-600 bg-orange-50 border-orange-200';
            case 'too_late': return 'text-red-600 bg-red-50 border-red-200';
            default: return 'text-gray-600 bg-gray-50 border-gray-200';
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'optimal': return '✅';
            case 'good': return '👍';
            case 'late': return '⚠️';
            case 'too_early': return '❄️';
            case 'too_late': return '🚫';
            default: return 'ℹ️';
        }
    };

    return (
        <View className="bg-white rounded-lg p-4 border border-gray-200">
            <Text className="text-lg font-semibold text-gray-900 mb-4">
                Select Planting Date
            </Text>

            {/* Date Selection Button */}
            <TouchableOpacity
                onPress={() => setShowDatePicker(true)}
                className="flex-row items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200 mb-4"
                accessibilityLabel="Select planting date"
                accessibilityHint="Opens date picker to choose when to plant"
            >
                <View className="flex-1">
                    <Text className="text-base font-medium text-gray-900">
                        {formatDate(selectedDate)}
                    </Text>
                    <Text className="text-sm text-gray-500 mt-1">
                        {formatDateAr(selectedDate)}
                    </Text>
                </View>
                <View className="flex-row items-center">
                    <Text className="text-2xl mr-2">📅</Text>
                    {voiceEnabled && (
                        <TouchableOpacity
                            onPress={handleVoiceCommand}
                            className="p-2 bg-green-500 rounded-full ml-2"
                            accessibilityLabel="Voice date selection"
                        >
                            <Text className="text-white text-sm">🎤</Text>
                        </TouchableOpacity>
                    )}
                </View>
            </TouchableOpacity>

            {/* Date Recommendation */}
            <View className={`p-4 rounded-lg border ${getStatusColor(recommendation.status)}`}>
                <View className="flex-row items-center mb-2">
                    <Text className="text-2xl mr-2">{getStatusIcon(recommendation.status)}</Text>
                    <Text className="text-base font-semibold">
                        Planting Recommendation
                    </Text>
                </View>
                <Text className="text-sm mb-1">{recommendation.message}</Text>
                <Text className="text-sm text-right">{recommendation.messageAr}</Text>
            </View>

            {/* Optimal Planting Window */}
            {plantingSchedule && (
                <View className="mt-4 p-4 bg-green-50 rounded-lg border border-green-200">
                    <Text className="text-base font-semibold text-green-800 mb-2">
                        🌱 Optimal Planting Window
                    </Text>
                    <View className="gap-2">
                        <View className="flex-row justify-between">
                            <Text className="text-sm text-green-700">Best Start:</Text>
                            <Text className="text-sm font-medium text-green-800">
                                {plantingSchedule.optimalStartDate.toLocaleDateString()}
                            </Text>
                        </View>
                        <View className="flex-row justify-between">
                            <Text className="text-sm text-green-700">Best End:</Text>
                            <Text className="text-sm font-medium text-green-800">
                                {plantingSchedule.optimalEndDate.toLocaleDateString()}
                            </Text>
                        </View>
                        <View className="flex-row justify-between">
                            <Text className="text-sm text-green-700">Expected Harvest:</Text>
                            <Text className="text-sm font-medium text-green-800">
                                {plantingSchedule.harvestWindow.start.toLocaleDateString()}
                            </Text>
                        </View>
                        <View className="flex-row justify-between">
                            <Text className="text-sm text-green-700">Growing Days:</Text>
                            <Text className="text-sm font-medium text-green-800">
                                {plantingSchedule.growingDays} days
                            </Text>
                        </View>
                    </View>
                </View>
            )}

            {/* Date Picker Modal */}
            {Platform.OS === 'ios' ? (
                <Modal
                    visible={showDatePicker}
                    transparent
                    animationType="slide"
                >
                    <View className="flex-1 justify-end bg-black bg-opacity-50">
                        <View className="bg-white rounded-t-lg p-4">
                            <View className="flex-row justify-between items-center mb-4">
                                <TouchableOpacity
                                    onPress={() => setShowDatePicker(false)}
                                    className="px-4 py-2"
                                >
                                    <Text className="text-blue-500 font-medium">Cancel</Text>
                                </TouchableOpacity>
                                <Text className="text-lg font-semibold">Select Date</Text>
                                <TouchableOpacity
                                    onPress={() => setShowDatePicker(false)}
                                    className="px-4 py-2"
                                >
                                    <Text className="text-blue-500 font-medium">Done</Text>
                                </TouchableOpacity>
                            </View>
                            <DateTimePicker
                                value={selectedDate}
                                mode="date"
                                display="spinner"
                                onChange={handleDateChange}
                                minimumDate={new Date()}
                                maximumDate={new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)}
                            />
                        </View>
                    </View>
                </Modal>
            ) : (
                showDatePicker && (
                    <DateTimePicker
                        value={selectedDate}
                        mode="date"
                        display="default"
                        onChange={handleDateChange}
                        minimumDate={new Date()}
                        maximumDate={new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)}
                    />
                )
            )}
        </View>
    );
}