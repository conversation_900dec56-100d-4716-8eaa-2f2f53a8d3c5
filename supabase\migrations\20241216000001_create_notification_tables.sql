-- Create notification-related tables

-- User notification preferences
CREATE TABLE IF NOT EXISTS user_notification_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    preferences JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Push tokens for devices
CREATE TABLE IF NOT EXISTS push_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    token TEXT NOT NULL,
    platform TEXT NOT NULL CHECK (platform IN ('ios', 'android', 'web')),
    device_id TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, device_id)
);

-- Notifications sent to users
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    type TEXT NOT NULL,
    title TEXT NOT NULL,
    body TEXT NOT NULL,
    data JSONB DEFAULT '{}',
    priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'critical')),
    category TEXT DEFAULT 'system' CHECK (category IN ('agricultural', 'weather', 'community', 'system', 'commercial')),
    status TEXT DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'read', 'failed')),
    scheduled_for TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    delivered_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Scheduled notifications
CREATE TABLE IF NOT EXISTS scheduled_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    identifier TEXT NOT NULL,
    notification_data JSONB NOT NULL,
    scheduled_for TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notification analytics
CREATE TABLE IF NOT EXISTS notification_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    notification_id UUID REFERENCES notifications(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    type TEXT NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE NOT NULL,
    delivered_at TIMESTAMP WITH TIME ZONE,
    opened_at TIMESTAMP WITH TIME ZONE,
    action_taken BOOLEAN DEFAULT false,
    device_info JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Weather alerts
CREATE TABLE IF NOT EXISTS weather_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    alert_data JSONB NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);
CREATE INDEX IF NOT EXISTS idx_notifications_status ON notifications(status);
CREATE INDEX IF NOT EXISTS idx_notifications_sent_at ON notifications(sent_at);
CREATE INDEX IF NOT EXISTS idx_notifications_read_at ON notifications(read_at);

CREATE INDEX IF NOT EXISTS idx_push_tokens_user_id ON push_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_push_tokens_is_active ON push_tokens(is_active);

CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_user_id ON scheduled_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_scheduled_for ON scheduled_notifications(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_is_active ON scheduled_notifications(is_active);

CREATE INDEX IF NOT EXISTS idx_notification_analytics_user_id ON notification_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_analytics_type ON notification_analytics(type);
CREATE INDEX IF NOT EXISTS idx_notification_analytics_sent_at ON notification_analytics(sent_at);

CREATE INDEX IF NOT EXISTS idx_weather_alerts_user_id ON weather_alerts(user_id);
CREATE INDEX IF NOT EXISTS idx_weather_alerts_sent_at ON weather_alerts(sent_at);

-- Row Level Security (RLS) policies
ALTER TABLE user_notification_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE push_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE scheduled_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE weather_alerts ENABLE ROW LEVEL SECURITY;

-- Policies for user_notification_preferences
CREATE POLICY "Users can view their own notification preferences" ON user_notification_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notification preferences" ON user_notification_preferences
    FOR ALL USING (auth.uid() = user_id);

-- Policies for push_tokens
CREATE POLICY "Users can manage their own push tokens" ON push_tokens
    FOR ALL USING (auth.uid() = user_id);

-- Policies for notifications
CREATE POLICY "Users can view their own notifications" ON notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- Policies for scheduled_notifications
CREATE POLICY "Users can manage their own scheduled notifications" ON scheduled_notifications
    FOR ALL USING (auth.uid() = user_id);

-- Policies for notification_analytics
CREATE POLICY "Users can view their own notification analytics" ON notification_analytics
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert notification analytics" ON notification_analytics
    FOR INSERT WITH CHECK (true);

-- Policies for weather_alerts
CREATE POLICY "Users can view their own weather alerts" ON weather_alerts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert weather alerts" ON weather_alerts
    FOR INSERT WITH CHECK (true);

-- Functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_user_notification_preferences_updated_at
    BEFORE UPDATE ON user_notification_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_push_tokens_updated_at
    BEFORE UPDATE ON push_tokens
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notifications_updated_at
    BEFORE UPDATE ON notifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scheduled_notifications_updated_at
    BEFORE UPDATE ON scheduled_notifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to clean up old notifications
CREATE OR REPLACE FUNCTION cleanup_old_notifications()
RETURNS void AS $$
BEGIN
    -- Delete notifications older than 90 days
    DELETE FROM notifications 
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    -- Delete analytics older than 180 days
    DELETE FROM notification_analytics 
    WHERE created_at < NOW() - INTERVAL '180 days';
    
    -- Delete expired scheduled notifications
    DELETE FROM scheduled_notifications 
    WHERE scheduled_for < NOW() - INTERVAL '7 days' AND is_active = false;
    
    -- Delete old weather alerts
    DELETE FROM weather_alerts 
    WHERE created_at < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Additional tables for agricultural alert system

-- AI monitoring rules
CREATE TABLE IF NOT EXISTS ai_monitoring_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    crop_type TEXT,
    issue_type TEXT NOT NULL CHECK (issue_type IN ('disease_detected', 'nutrient_deficiency', 'pest_warning', 'optimization_tip')),
    threshold JSONB NOT NULL DEFAULT '{}',
    frequency TEXT DEFAULT 'immediate' CHECK (frequency IN ('immediate', 'daily', 'weekly')),
    enabled BOOLEAN DEFAULT true,
    last_triggered TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI analysis results
CREATE TABLE IF NOT EXISTS ai_analysis_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    analysis_id TEXT NOT NULL,
    analysis_type TEXT NOT NULL CHECK (analysis_type IN ('disease_detection', 'nutrient_analysis', 'pest_identification', 'growth_assessment')),
    image_url TEXT,
    findings JSONB NOT NULL DEFAULT '{}',
    recommendations JSONB DEFAULT '[]',
    affected_area JSONB DEFAULT '{}',
    estimated_impact JSONB DEFAULT '{}',
    follow_up_required BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Community alert preferences
CREATE TABLE IF NOT EXISTS community_alert_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    nearby_events BOOLEAN DEFAULT true,
    event_reminders BOOLEAN DEFAULT true,
    new_posts BOOLEAN DEFAULT false,
    post_replies BOOLEAN DEFAULT true,
    mentions BOOLEAN DEFAULT true,
    urgent_posts BOOLEAN DEFAULT true,
    followed_users BOOLEAN DEFAULT true,
    market_updates BOOLEAN DEFAULT true,
    emergency_alerts BOOLEAN DEFAULT true,
    max_distance_km INTEGER DEFAULT 50,
    interested_tags TEXT[] DEFAULT ARRAY['farming', 'crops', 'weather', 'market'],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Community events
CREATE TABLE IF NOT EXISTS community_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    description TEXT,
    event_type TEXT NOT NULL CHECK (event_type IN ('workshop', 'market_day', 'field_visit', 'meeting', 'emergency', 'celebration')),
    location JSONB NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    organizer_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    max_participants INTEGER,
    current_participants INTEGER DEFAULT 0,
    tags TEXT[] DEFAULT '{}',
    is_public BOOLEAN DEFAULT true,
    requires_rsvp BOOLEAN DEFAULT false,
    cost JSONB DEFAULT '{}',
    materials TEXT[] DEFAULT '{}',
    prerequisites TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Event attendees
CREATE TABLE IF NOT EXISTS event_attendees (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID REFERENCES community_events(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'attending' CHECK (status IN ('attending', 'maybe', 'not_attending')),
    rsvp_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    notes TEXT,
    UNIQUE(event_id, user_id)
);

-- User follows (for community notifications)
CREATE TABLE IF NOT EXISTS user_follows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    follower_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    following_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(follower_id, following_id),
    CHECK (follower_id != following_id)
);

-- Create indexes for the new tables
CREATE INDEX IF NOT EXISTS idx_ai_monitoring_rules_user_id ON ai_monitoring_rules(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_monitoring_rules_enabled ON ai_monitoring_rules(enabled);
CREATE INDEX IF NOT EXISTS idx_ai_monitoring_rules_issue_type ON ai_monitoring_rules(issue_type);

CREATE INDEX IF NOT EXISTS idx_ai_analysis_results_user_id ON ai_analysis_results(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_analysis_results_analysis_type ON ai_analysis_results(analysis_type);
CREATE INDEX IF NOT EXISTS idx_ai_analysis_results_created_at ON ai_analysis_results(created_at);

CREATE INDEX IF NOT EXISTS idx_community_alert_preferences_user_id ON community_alert_preferences(user_id);

CREATE INDEX IF NOT EXISTS idx_community_events_organizer_id ON community_events(organizer_id);
CREATE INDEX IF NOT EXISTS idx_community_events_start_time ON community_events(start_time);
CREATE INDEX IF NOT EXISTS idx_community_events_event_type ON community_events(event_type);
CREATE INDEX IF NOT EXISTS idx_community_events_is_public ON community_events(is_public);

CREATE INDEX IF NOT EXISTS idx_event_attendees_event_id ON event_attendees(event_id);
CREATE INDEX IF NOT EXISTS idx_event_attendees_user_id ON event_attendees(user_id);
CREATE INDEX IF NOT EXISTS idx_event_attendees_status ON event_attendees(status);

CREATE INDEX IF NOT EXISTS idx_user_follows_follower_id ON user_follows(follower_id);
CREATE INDEX IF NOT EXISTS idx_user_follows_following_id ON user_follows(following_id);

-- RLS policies for new tables
ALTER TABLE ai_monitoring_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_analysis_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE community_alert_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE community_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE event_attendees ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_follows ENABLE ROW LEVEL SECURITY;

-- Policies for ai_monitoring_rules
CREATE POLICY "Users can manage their own AI monitoring rules" ON ai_monitoring_rules
    FOR ALL USING (auth.uid() = user_id);

-- Policies for ai_analysis_results
CREATE POLICY "Users can view their own AI analysis results" ON ai_analysis_results
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert AI analysis results" ON ai_analysis_results
    FOR INSERT WITH CHECK (true);

-- Policies for community_alert_preferences
CREATE POLICY "Users can manage their own community alert preferences" ON community_alert_preferences
    FOR ALL USING (auth.uid() = user_id);

-- Policies for community_events
CREATE POLICY "Users can view public community events" ON community_events
    FOR SELECT USING (is_public = true OR auth.uid() = organizer_id);

CREATE POLICY "Users can create community events" ON community_events
    FOR INSERT WITH CHECK (auth.uid() = organizer_id);

CREATE POLICY "Organizers can update their own events" ON community_events
    FOR UPDATE USING (auth.uid() = organizer_id);

CREATE POLICY "Organizers can delete their own events" ON community_events
    FOR DELETE USING (auth.uid() = organizer_id);

-- Policies for event_attendees
CREATE POLICY "Users can view event attendees for public events" ON event_attendees
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM community_events 
            WHERE id = event_id AND (is_public = true OR organizer_id = auth.uid())
        )
    );

CREATE POLICY "Users can manage their own event attendance" ON event_attendees
    FOR ALL USING (auth.uid() = user_id);

-- Policies for user_follows
CREATE POLICY "Users can view follows involving them" ON user_follows
    FOR SELECT USING (auth.uid() = follower_id OR auth.uid() = following_id);

CREATE POLICY "Users can manage their own follows" ON user_follows
    FOR ALL USING (auth.uid() = follower_id);

-- Triggers for automatic timestamp updates on new tables
CREATE TRIGGER update_ai_monitoring_rules_updated_at
    BEFORE UPDATE ON ai_monitoring_rules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_community_alert_preferences_updated_at
    BEFORE UPDATE ON community_alert_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_community_events_updated_at
    BEFORE UPDATE ON community_events
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update event participant count
CREATE OR REPLACE FUNCTION update_event_participant_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' AND NEW.status = 'attending' THEN
        UPDATE community_events 
        SET current_participants = current_participants + 1 
        WHERE id = NEW.event_id;
    ELSIF TG_OP = 'UPDATE' THEN
        IF OLD.status != 'attending' AND NEW.status = 'attending' THEN
            UPDATE community_events 
            SET current_participants = current_participants + 1 
            WHERE id = NEW.event_id;
        ELSIF OLD.status = 'attending' AND NEW.status != 'attending' THEN
            UPDATE community_events 
            SET current_participants = current_participants - 1 
            WHERE id = NEW.event_id;
        END IF;
    ELSIF TG_OP = 'DELETE' AND OLD.status = 'attending' THEN
        UPDATE community_events 
        SET current_participants = current_participants - 1 
        WHERE id = OLD.event_id;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update participant count
CREATE TRIGGER update_event_participants_trigger
    AFTER INSERT OR UPDATE OR DELETE ON event_attendees
    FOR EACH ROW EXECUTE FUNCTION update_event_participant_count();

-- Create a scheduled job to run cleanup (if pg_cron is available)
-- SELECT cron.schedule('cleanup-notifications', '0 2 * * *', 'SELECT cleanup_old_notifications();');