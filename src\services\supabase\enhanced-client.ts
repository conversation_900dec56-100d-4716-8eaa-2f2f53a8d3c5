import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../../types/database';
import { AppState } from 'react-native';
import { ExpoSecureStoreAdapter } from './secure-store-adapter';

class EnhancedSupabaseClient {
  private client: SupabaseClient<Database>;
  private isOnline: boolean = true;
  private retryQueue: Array<() => Promise<any>> = [];

  constructor() {
    this.client = createClient<Database>(
      process.env.EXPO_PUBLIC_SUPABASE_URL!,
      process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!,
      {
        auth: {
          storage: ExpoSecureStoreAdapter,
          autoRefreshToken: true,
          persistSession: true,
          detectSessionInUrl: false,
        },
        realtime: {
          params: {
            eventsPerSecond: 10,
          },
        },
        global: {
          headers: {
            'x-application-name': 'ai-farming-assistant',
          },
        },
      }
    );

    this.setupConnectionMonitoring();
    this.setupAppStateHandling();
  }

  private setupConnectionMonitoring() {
    // No-op: Supabase-js v2 does not expose realtime.onOpen/onClose on the client instance.
    // Connection is managed internally by supabase-js when you subscribe to channels.
  }

  private setupAppStateHandling() {
    // No-op: Explicit connect/disconnect are not required with supabase-js v2; it handles
    // socket lifecycle automatically. If needed, manage subscriptions per screen instead.
    AppState.addEventListener('change', () => {});
  }

  async executeWithRetry<T>(operation: () => Promise<T>, maxRetries: number = 3): Promise<T> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        if (attempt === maxRetries) throw error;

        await new Promise((resolve) => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
    throw new Error('Max retries exceeded');
  }

  getClient(): SupabaseClient<Database> {
    return this.client;
  }
}

export const supabaseClient = new EnhancedSupabaseClient();
export const supabase = supabaseClient.getClient();
