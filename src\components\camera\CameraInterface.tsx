import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, Alert, Dimensions } from 'react-native';
import { Camera, CameraType, FlashMode } from 'expo-camera';
import { Ionicons } from '@expo/vector-icons';
import { CaptureMode, CameraSettings } from '../../types/camera';
import { CameraService } from '../../services/camera';
import { VoiceService } from '../../services/voice';
import { colors } from '../../design-system';

interface CameraInterfaceProps {
    mode: CaptureMode;
    onCapture: (imageUri: string) => void;
    onModeChange: (mode: CaptureMode) => void;
    onClose: () => void;
}

const { width, height } = Dimensions.get('window');

export function CameraInterface({ mode, onCapture, onModeChange, onClose }: CameraInterfaceProps) {
    const [hasPermission, setHasPermission] = useState<boolean | null>(null);
    const [cameraType, setCameraType] = useState(CameraType.back);
    const [flashMode, setFlashMode] = useState<FlashMode>(FlashMode.auto);
    const [isReady, setIsReady] = useState(false);
    const [showGuidelines, setShowGuidelines] = useState(true);
    const cameraRef = useRef<Camera>(null);

    useEffect(() => {
        requestPermissions();
        announceMode();
    }, []);

    useEffect(() => {
        announceMode();
    }, [mode]);

    const requestPermissions = async () => {
        const granted = await CameraService.requestPermissions();
        setHasPermission(granted);

        if (!granted) {
            Alert.alert(
                'Camera Permission Required',
                'This app needs camera access to analyze your crops. Please enable camera permissions in settings.',
                [{ text: 'OK', onPress: onClose }]
            );
        }
    };

    const announceMode = async () => {
        const guideline = CameraService.getGuidelineForMode(mode);
        await VoiceService.speak(`${guideline.title} mode selected. ${guideline.voiceInstructions}`);
    };

    const handleCapture = async () => {
        if (!cameraRef.current || !isReady) return;

        try {
            await VoiceService.speak('Taking photo');

            const photo = await cameraRef.current.takePictureAsync({
                quality: 0.8,
                base64: false,
                skipProcessing: false,
            });

            if (photo?.uri) {
                await VoiceService.speak('Photo captured successfully');
                onCapture(photo.uri);
            }
        } catch (error) {
            console.error('Error taking picture:', error);
            await VoiceService.speak('Error taking photo. Please try again.');
            Alert.alert('Error', 'Failed to take picture. Please try again.');
        }
    };

    const toggleFlash = async () => {
        const modes: FlashMode[] = [FlashMode.auto, FlashMode.on, FlashMode.off];
        const currentIndex = modes.indexOf(flashMode);
        const nextMode = modes[(currentIndex + 1) % modes.length];
        setFlashMode(nextMode);

        const flashNames = {
            [FlashMode.auto]: 'auto',
            [FlashMode.on]: 'on',
            [FlashMode.off]: 'off'
        };

        await VoiceService.speak(`Flash ${flashNames[nextMode]}`);
    };

    const handleModeChange = async (newMode: CaptureMode) => {
        onModeChange(newMode);
    };

    const toggleGuidelines = async () => {
        setShowGuidelines(!showGuidelines);
        await VoiceService.speak(showGuidelines ? 'Guidelines hidden' : 'Guidelines shown');
    };

    if (hasPermission === null) {
        return (
            <View className="flex-1 justify-center items-center bg-black">
                <Text className="text-white text-lg">Requesting camera permission...</Text>
            </View>
        );
    }

    if (hasPermission === false) {
        return (
            <View className="flex-1 justify-center items-center bg-black">
                <Text className="text-white text-lg text-center px-4">
                    Camera permission is required to analyze your crops
                </Text>
                <TouchableOpacity
                    onPress={onClose}
                    className="mt-4 bg-green-600 px-6 py-3 rounded-lg"
                >
                    <Text className="text-white font-semibold">Go Back</Text>
                </TouchableOpacity>
            </View>
        );
    }

    const guideline = CameraService.getGuidelineForMode(mode);
    const modes: { key: CaptureMode; label: string; icon: string }[] = [
        { key: 'plant', label: 'Plant', icon: 'leaf' },
        { key: 'soil', label: 'Soil', icon: 'earth' },
        { key: 'fertilizer', label: 'Fertilizer', icon: 'water' }
    ];

    return (
        <View className="flex-1 bg-black">
            {/* Camera View */}
            <Camera
                ref={cameraRef}
                style={{ width, height: height * 0.7 }}
                type={cameraType}
                flashMode={flashMode}
                onCameraReady={() => setIsReady(true)}
                ratio="16:9"
            >
                {/* Header Controls */}
                <View className="flex-row justify-between items-center p-4 pt-12">
                    <TouchableOpacity
                        onPress={onClose}
                        className="bg-black/50 p-3 rounded-full"
                        accessibilityLabel="Close camera"
                    >
                        <Ionicons name="close" size={24} color="white" />
                    </TouchableOpacity>

                    <View className="bg-black/50 px-4 py-2 rounded-full">
                        <Text className="text-white font-semibold">{guideline.title}</Text>
                    </View>

                    <TouchableOpacity
                        onPress={toggleGuidelines}
                        className="bg-black/50 p-3 rounded-full"
                        accessibilityLabel="Toggle guidelines"
                    >
                        <Ionicons name="help-circle" size={24} color="white" />
                    </TouchableOpacity>
                </View>

                {/* Guidelines Overlay */}
                {showGuidelines && (
                    <View className="absolute bottom-0 left-0 right-0 bg-black/70 p-4">
                        <Text className="text-white font-semibold mb-2">Capture Tips:</Text>
                        {guideline.tips.map((tip, index) => (
                            <Text key={index} className="text-white text-sm mb-1">
                                • {tip}
                            </Text>
                        ))}
                    </View>
                )}

                {/* Focus Grid */}
                <View className="absolute inset-0 justify-center items-center pointer-events-none">
                    <View className="w-48 h-48 border-2 border-white/30 rounded-lg">
                        <View className="absolute top-0 left-0 w-6 h-6 border-t-2 border-l-2 border-white" />
                        <View className="absolute top-0 right-0 w-6 h-6 border-t-2 border-r-2 border-white" />
                        <View className="absolute bottom-0 left-0 w-6 h-6 border-b-2 border-l-2 border-white" />
                        <View className="absolute bottom-0 right-0 w-6 h-6 border-b-2 border-r-2 border-white" />
                    </View>
                </View>
            </Camera>

            {/* Bottom Controls */}
            <View className="flex-1 bg-black px-4 py-6">
                {/* Mode Selection */}
                <View className="flex-row justify-center mb-6">
                    {modes.map((modeOption) => (
                        <TouchableOpacity
                            key={modeOption.key}
                            onPress={() => handleModeChange(modeOption.key)}
                            className={`mx-2 px-4 py-2 rounded-full ${mode === modeOption.key ? 'bg-green-600' : 'bg-gray-600'
                                }`}
                            accessibilityLabel={`${modeOption.label} mode`}
                        >
                            <View className="flex-row items-center">
                                <Ionicons
                                    name={modeOption.icon as any}
                                    size={16}
                                    color="white"
                                    style={{ marginRight: 4 }}
                                />
                                <Text className="text-white font-medium">{modeOption.label}</Text>
                            </View>
                        </TouchableOpacity>
                    ))}
                </View>

                {/* Camera Controls */}
                <View className="flex-row justify-between items-center">
                    {/* Flash Control */}
                    <TouchableOpacity
                        onPress={toggleFlash}
                        className="bg-gray-700 p-4 rounded-full"
                        accessibilityLabel="Toggle flash"
                    >
                        <Ionicons
                            name={
                                flashMode === FlashMode.on ? 'flash' :
                                    flashMode === FlashMode.off ? 'flash-off' : 'flash-outline'
                            }
                            size={24}
                            color="white"
                        />
                    </TouchableOpacity>

                    {/* Capture Button */}
                    <TouchableOpacity
                        onPress={handleCapture}
                        disabled={!isReady}
                        className={`w-20 h-20 rounded-full border-4 border-white ${isReady ? 'bg-green-600' : 'bg-gray-600'
                            }`}
                        accessibilityLabel="Take photo"
                    >
                        <View className="flex-1 justify-center items-center">
                            <Ionicons name="camera" size={32} color="white" />
                        </View>
                    </TouchableOpacity>

                    {/* Camera Flip */}
                    <TouchableOpacity
                        onPress={() => setCameraType(
                            cameraType === CameraType.back ? CameraType.front : CameraType.back
                        )}
                        className="bg-gray-700 p-4 rounded-full"
                        accessibilityLabel="Flip camera"
                    >
                        <Ionicons name="camera-reverse" size={24} color="white" />
                    </TouchableOpacity>
                </View>

                {/* Voice Instructions */}
                <TouchableOpacity
                    onPress={() => VoiceService.speak(guideline.voiceInstructions)}
                    className="mt-4 bg-blue-600 py-3 px-6 rounded-lg self-center"
                    accessibilityLabel="Repeat voice instructions"
                >
                    <View className="flex-row items-center">
                        <Ionicons name="volume-high" size={20} color="white" style={{ marginRight: 8 }} />
                        <Text className="text-white font-medium">Repeat Instructions</Text>
                    </View>
                </TouchableOpacity>
            </View>
        </View>
    );
}