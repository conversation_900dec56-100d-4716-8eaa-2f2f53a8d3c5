import { User as SupabaseUser, Session } from '@supabase/supabase-js';

export interface User extends SupabaseUser {
    // Additional user properties can be added here
}

export interface UserProfile {
    id: string;
    farm_location?: {
        latitude: number;
        longitude: number;
    };
    crop_types?: string[];
    experience_level?: 'beginner' | 'intermediate' | 'expert';
    preferred_language: string;
    voice_enabled: boolean;
    points: number;
    subscription_tier: string;
    subscription_expires_at?: string;
}

export interface LoginCredentials {
    email?: string;
    phone?: string;
    password: string;
}

export interface RegisterData {
    email?: string;
    phone?: string;
    password: string;
    firstName: string;
    lastName: string;
}

export interface AuthState {
    user: User | null;
    session: Session | null;
    isLoading: boolean;
    isAuthenticated: boolean;
    error: string | null;
}

export interface OTPVerification {
    phone: string;
    token: string;
}

export interface PasswordReset {
    email: string;
}

export interface PasswordUpdate {
    newPassword: string;
    confirmPassword: string;
}

export type AuthProvider = 'email' | 'phone' | 'google' | 'apple';

export interface SocialAuthConfig {
    provider: AuthProvider;
    redirectUrl?: string;
}

export interface AuthError {
    message: string;
    status?: number;
    code?: string;
}

export interface AuthResponse {
    success: boolean;
    user?: User;
    session?: Session;
    error?: AuthError;
}

// Form validation types
export interface LoginFormData {
    identifier: string; // email or phone
    password: string;
    rememberMe?: boolean;
}

export interface RegisterFormData {
    firstName: string;
    lastName: string;
    email?: string;
    phone?: string;
    password: string;
    confirmPassword: string;
    agreeToTerms: boolean;
}

export interface OTPFormData {
    code: string;
}

export interface ResetPasswordFormData {
    email: string;
}

export interface UpdatePasswordFormData {
    currentPassword: string;
    newPassword: string;
    confirmNewPassword: string;
}

// Auth context types
export interface AuthContextType {
    user: User | null;
    session: Session | null;
    isLoading: boolean;
    isAuthenticated: boolean;
    error: string | null;
    login: (credentials: LoginCredentials) => Promise<boolean>;
    register: (data: RegisterData) => Promise<boolean>;
    logout: () => Promise<void>;
    resetPassword: (email: string) => Promise<boolean>;
    updatePassword: (newPassword: string) => Promise<boolean>;
    verifyOTP: (phone: string, token: string) => Promise<boolean>;
    resendOTP: (phone: string) => Promise<boolean>;
    signInWithGoogle: () => Promise<boolean>;
    signInWithApple: () => Promise<boolean>;
    clearError: () => void;
}

// Session management types
export interface SessionConfig {
    autoRefresh: boolean;
    persistSession: boolean;
    detectSessionInUrl: boolean;
    storageKey: string;
}

export interface TokenInfo {
    accessToken: string;
    refreshToken: string;
    expiresAt: number;
    tokenType: string;
}

// Auth event types
export type AuthEvent =
    | 'SIGNED_IN'
    | 'SIGNED_OUT'
    | 'TOKEN_REFRESHED'
    | 'USER_UPDATED'
    | 'PASSWORD_RECOVERY';

export interface AuthEventHandler {
    (event: AuthEvent, session: Session | null): void;
}