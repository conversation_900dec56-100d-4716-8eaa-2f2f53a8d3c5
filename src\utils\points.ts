import { PointsTransaction, Achievement } from '../types/points';

/**
 * Format points amount for display
 */
export const formatPoints = (points: number): string => {
    if (points >= 1000000) {
        return `${(points / 1000000).toFixed(1)}M`;
    } else if (points >= 1000) {
        return `${(points / 1000).toFixed(1)}K`;
    }
    return points.toString();
};

/**
 * Get color for points transaction based on type
 */
export const getTransactionColor = (transaction: PointsTransaction): string => {
    switch (transaction.transaction_type) {
        case 'earned':
        case 'bonus':
            return '#22c55e'; // Green
        case 'spent':
            return '#ef4444'; // Red
        case 'refund':
            return '#3b82f6'; // Blue
        default:
            return '#6b7280'; // Gray
    }
};

/**
 * Get icon for points transaction source
 */
export const getTransactionIcon = (source: PointsTransaction['source']): string => {
    const iconMap: Record<PointsTransaction['source'], string> = {
        task_completion: '✅',
        daily_checkin: '📅',
        community_post: '📝',
        ai_consultation: '🤖',
        subscription_bonus: '⭐',
        achievement: '🏆',
        referral: '👥',
        admin_adjustment: '⚙️',
    };
    return iconMap[source] || '💰';
};

/**
 * Get human-readable description for transaction source
 */
export const getTransactionSourceLabel = (source: PointsTransaction['source']): string => {
    const labelMap: Record<PointsTransaction['source'], string> = {
        task_completion: 'Task Completion',
        daily_checkin: 'Daily Check-in',
        community_post: 'Community Post',
        ai_consultation: 'AI Consultation',
        subscription_bonus: 'Subscription Bonus',
        achievement: 'Achievement',
        referral: 'Referral Bonus',
        admin_adjustment: 'Admin Adjustment',
    };
    return labelMap[source] || 'Unknown';
};

/**
 * Calculate streak multiplier based on days
 */
export const calculateStreakMultiplier = (streakDays: number): number => {
    // Max 2.0x multiplier at 30+ days
    return Math.min(1.0 + (streakDays * 0.033), 2.0);
};

/**
 * Get achievement category color
 */
export const getAchievementCategoryColor = (category: Achievement['category']): string => {
    const colorMap: Record<Achievement['category'], string> = {
        farming: '#22c55e',     // Green
        community: '#3b82f6',   // Blue
        learning: '#8b5cf6',    // Purple
        consistency: '#f59e0b', // Amber
        milestone: '#ef4444',   // Red
    };
    return colorMap[category] || '#6b7280';
};

/**
 * Get achievement difficulty level based on points reward
 */
export const getAchievementDifficulty = (pointsReward: number): 'easy' | 'medium' | 'hard' | 'legendary' => {
    if (pointsReward <= 50) return 'easy';
    if (pointsReward <= 100) return 'medium';
    if (pointsReward <= 200) return 'hard';
    return 'legendary';
};

/**
 * Calculate progress towards achievement
 */
export const calculateAchievementProgress = (
    currentValue: number,
    targetValue: number
): { percentage: number; isComplete: boolean } => {
    const percentage = Math.min(100, (currentValue / targetValue) * 100);
    const isComplete = currentValue >= targetValue;

    return { percentage, isComplete };
};

/**
 * Get time-based greeting for daily check-in
 */
export const getDailyCheckinGreeting = (): string => {
    const hour = new Date().getHours();

    if (hour < 12) return 'Good morning, farmer!';
    if (hour < 17) return 'Good afternoon, farmer!';
    return 'Good evening, farmer!';
};

/**
 * Calculate points needed for next achievement milestone
 */
export const getNextMilestone = (currentPoints: number): { points: number; description: string } => {
    const milestones = [
        { points: 100, description: 'Getting Started' },
        { points: 500, description: 'Active Farmer' },
        { points: 1000, description: 'Dedicated Farmer' },
        { points: 2500, description: 'Expert Farmer' },
        { points: 5000, description: 'Master Farmer' },
        { points: 10000, description: 'Farming Legend' },
    ];

    const nextMilestone = milestones.find(m => m.points > currentPoints);
    return nextMilestone || { points: 10000, description: 'Farming Legend' };
};

/**
 * Format time ago for transactions
 */
export const formatTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;

    return date.toLocaleDateString();
};

/**
 * Validate points transaction
 */
export const validatePointsTransaction = (
    amount: number,
    source: PointsTransaction['source'],
    userBalance: number
): { isValid: boolean; error?: string } => {
    if (amount === 0) {
        return { isValid: false, error: 'Amount cannot be zero' };
    }

    if (amount < 0 && Math.abs(amount) > userBalance) {
        return { isValid: false, error: 'Insufficient points' };
    }

    if (!source) {
        return { isValid: false, error: 'Transaction source is required' };
    }

    return { isValid: true };
};

/**
 * Get leaderboard rank suffix
 */
export const getRankSuffix = (rank: number): string => {
    if (rank >= 11 && rank <= 13) return 'th';

    const lastDigit = rank % 10;
    switch (lastDigit) {
        case 1: return 'st';
        case 2: return 'nd';
        case 3: return 'rd';
        default: return 'th';
    }
};

/**
 * Calculate weekly points goal based on user activity
 */
export const calculateWeeklyPointsGoal = (
    averageDailyPoints: number,
    subscriptionTier: string
): number => {
    const baseGoal = averageDailyPoints * 7;
    const multiplier = subscriptionTier === 'free' ? 1 : 1.5;

    return Math.max(50, Math.floor(baseGoal * multiplier));
};

/**
 * Get points earning tips based on user activity
 */
export const getPointsEarningTips = (
    recentTransactions: PointsTransaction[]
): string[] => {
    const tips: string[] = [];
    const sources = recentTransactions.map(t => t.source);

    if (!sources.includes('daily_checkin')) {
        tips.push('Check in daily for easy points!');
    }

    if (!sources.includes('community_post')) {
        tips.push('Share your farming experience with the community');
    }

    if (!sources.includes('task_completion')) {
        tips.push('Complete farming tasks to earn points');
    }

    if (tips.length === 0) {
        tips.push('Keep up the great work!');
        tips.push('Try referring friends for bonus points');
    }

    return tips.slice(0, 3);
};