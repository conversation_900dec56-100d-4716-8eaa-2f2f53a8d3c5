#!/usr/bin/env node

/**
 * Direct Migration Script
 * This script applies migrations by reading SQL files and executing them directly
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.EXPO_PUBLIC_SUPABASE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function testConnection() {
  console.log('🔍 Testing database connection...');

  try {
    // Try a simple query to test connection
    const { data, error } = await supabase.from('users').select('count').limit(1);

    if (error && !error.message.includes('relation "users" does not exist')) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }

    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection exception:', error.message);
    return false;
  }
}

async function applyMigrationFile(filename) {
  console.log(`\n📄 Applying migration: ${filename}`);

  const filePath = path.join(__dirname, '..', 'supabase', 'migrations', filename);

  if (!fs.existsSync(filePath)) {
    console.log(`⏭️  Skipping missing file: ${filename}`);
    return true;
  }

  const sql = fs.readFileSync(filePath, 'utf8');

  try {
    // For now, we'll use a simple approach - execute the entire file as one query
    // This works for most migration files
    const { error } = await supabase.rpc('exec', { sql });

    if (error) {
      console.error(`❌ Error applying ${filename}:`, error.message);
      return false;
    }

    console.log(`✅ Successfully applied: ${filename}`);
    return true;
  } catch (error) {
    console.error(`❌ Exception applying ${filename}:`, error.message);
    return false;
  }
}

async function applyEnhancedSchema() {
  console.log('\n📄 Applying enhanced production schema...');

  const filePath = path.join(
    __dirname,
    '..',
    'supabase',
    'migrations',
    'production_enhanced_schema.sql'
  );

  if (!fs.existsSync(filePath)) {
    console.log('⏭️  Enhanced schema file not found, skipping...');
    return true;
  }

  const sql = fs.readFileSync(filePath, 'utf8');

  try {
    const { error } = await supabase.rpc('exec', { sql });

    if (error) {
      console.error('❌ Error applying enhanced schema:', error.message);
      return false;
    }

    console.log('✅ Successfully applied enhanced production schema');
    return true;
  } catch (error) {
    console.error('❌ Exception applying enhanced schema:', error.message);
    return false;
  }
}

async function verifyTables() {
  console.log('\n🔍 Verifying database tables...');

  const expectedTables = [
    'users',
    'user_profiles',
    'crop_plans',
    'tasks',
    'community_posts',
    'products',
    'orders',
  ];

  let allExist = true;

  for (const table of expectedTables) {
    try {
      const { data, error } = await supabase.from(table).select('*').limit(1);

      if (error && error.message.includes('does not exist')) {
        console.log(`❌ Table missing: ${table}`);
        allExist = false;
      } else {
        console.log(`✅ Table exists: ${table}`);
      }
    } catch (error) {
      console.log(`❌ Error checking table ${table}:`, error.message);
      allExist = false;
    }
  }

  return allExist;
}

async function main() {
  console.log('🚀 Starting direct database migration...\n');

  // Test connection
  const connectionOk = await testConnection();
  if (!connectionOk) {
    console.error('❌ Cannot proceed without database connection');
    process.exit(1);
  }

  // Apply key migration files in order
  const migrationFiles = [
    '001_initial_schema.sql',
    '002_rls_policies.sql',
    '20241216000001_points_and_subscriptions.sql',
    '20241216000001_create_notification_tables.sql',
  ];

  let successCount = 0;
  let failureCount = 0;

  for (const filename of migrationFiles) {
    const success = await applyMigrationFile(filename);
    if (success) {
      successCount++;
    } else {
      failureCount++;
    }
  }

  // Apply enhanced schema
  const enhancedSuccess = await applyEnhancedSchema();
  if (enhancedSuccess) {
    successCount++;
  } else {
    failureCount++;
  }

  console.log('\n📊 Migration Summary:');
  console.log(`   ✅ Successful: ${successCount}`);
  console.log(`   ❌ Failed: ${failureCount}`);

  // Verify results
  const tablesExist = await verifyTables();

  if (tablesExist && failureCount === 0) {
    console.log('\n🎉 Database migration completed successfully!');
    console.log('✅ All tables are ready for production use.');
  } else {
    console.log('\n⚠️  Migration completed with some issues.');
    console.log('❌ Please check the logs above for details.');
  }

  process.exit(tablesExist && failureCount === 0 ? 0 : 1);
}

// Run the migration
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  });
}
