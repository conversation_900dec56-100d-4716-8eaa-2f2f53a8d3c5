import * as SQLite from 'expo-sqlite';
import { OfflineAction, OfflineData, SyncQueueItem } from '../../types/offline';

export class OfflineDatabase {
    private db: SQLite.SQLiteDatabase | null = null;
    private readonly DB_NAME = 'farming_assistant_offline.db';
    private readonly DB_VERSION = 1;

    async initialize(): Promise<void> {
        try {
            this.db = await SQLite.openDatabaseAsync(this.DB_NAME);
            await this.createTables();
            console.log('Offline database initialized successfully');
        } catch (error) {
            console.error('Failed to initialize offline database:', error);
            throw error;
        }
    }

    private async createTables(): Promise<void> {
        if (!this.db) throw new Error('Database not initialized');

        const tables = [
            // Sync queue table
            `CREATE TABLE IF NOT EXISTS sync_queue (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        table_name TEXT NOT NULL,
        data TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        user_id TEXT,
        retry_count INTEGER DEFAULT 0,
        max_retries INTEGER DEFAULT 3,
        status TEXT DEFAULT 'pending',
        local_id TEXT,
        server_id TEXT,
        conflict_resolution TEXT,
        created_at INTEGER DEFAULT (strftime('%s', 'now'))
      )`,

            // User profiles cache
            `CREATE TABLE IF NOT EXISTS offline_user_profiles (
        id TEXT PRIMARY KEY,
        data TEXT NOT NULL,
        last_updated INTEGER NOT NULL,
        synced INTEGER DEFAULT 0
      )`,

            // Crop plans cache
            `CREATE TABLE IF NOT EXISTS offline_crop_plans (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        data TEXT NOT NULL,
        last_updated INTEGER NOT NULL,
        synced INTEGER DEFAULT 0
      )`,

            // Tasks cache
            `CREATE TABLE IF NOT EXISTS offline_tasks (
        id TEXT PRIMARY KEY,
        crop_plan_id TEXT NOT NULL,
        user_id TEXT NOT NULL,
        data TEXT NOT NULL,
        last_updated INTEGER NOT NULL,
        synced INTEGER DEFAULT 0
      )`,

            // Chat sessions cache
            `CREATE TABLE IF NOT EXISTS offline_chat_sessions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        data TEXT NOT NULL,
        last_updated INTEGER NOT NULL,
        synced INTEGER DEFAULT 0
      )`,

            // Chat messages cache
            `CREATE TABLE IF NOT EXISTS offline_chat_messages (
        id TEXT PRIMARY KEY,
        session_id TEXT NOT NULL,
        user_id TEXT NOT NULL,
        data TEXT NOT NULL,
        last_updated INTEGER NOT NULL,
        synced INTEGER DEFAULT 0
      )`,

            // Community posts cache
            `CREATE TABLE IF NOT EXISTS offline_community_posts (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        data TEXT NOT NULL,
        last_updated INTEGER NOT NULL,
        synced INTEGER DEFAULT 0
      )`,

            // Products cache (for offline browsing)
            `CREATE TABLE IF NOT EXISTS offline_products (
        id TEXT PRIMARY KEY,
        data TEXT NOT NULL,
        last_updated INTEGER NOT NULL,
        synced INTEGER DEFAULT 0
      )`,

            // Metadata table
            `CREATE TABLE IF NOT EXISTS offline_metadata (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at INTEGER DEFAULT (strftime('%s', 'now'))
      )`
        ];

        for (const tableSQL of tables) {
            await this.db.execAsync(tableSQL);
        }

        // Create indexes for better performance
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_sync_queue_status ON sync_queue(status)',
            'CREATE INDEX IF NOT EXISTS idx_sync_queue_timestamp ON sync_queue(timestamp)',
            'CREATE INDEX IF NOT EXISTS idx_sync_queue_user_id ON sync_queue(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_offline_crop_plans_user_id ON offline_crop_plans(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_offline_tasks_user_id ON offline_tasks(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_offline_tasks_crop_plan_id ON offline_tasks(crop_plan_id)',
            'CREATE INDEX IF NOT EXISTS idx_offline_chat_sessions_user_id ON offline_chat_sessions(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_offline_chat_messages_session_id ON offline_chat_messages(session_id)',
            'CREATE INDEX IF NOT EXISTS idx_offline_community_posts_user_id ON offline_community_posts(user_id)'
        ];

        for (const indexSQL of indexes) {
            await this.db.execAsync(indexSQL);
        }
    }

    // Sync Queue Operations
    async addToSyncQueue(action: OfflineAction): Promise<void> {
        if (!this.db) throw new Error('Database not initialized');

        await this.db.runAsync(
            `INSERT INTO sync_queue (
        id, type, table_name, data, timestamp, user_id, 
        retry_count, max_retries, status, local_id, server_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
                action.id,
                action.type,
                action.table,
                JSON.stringify(action.data),
                action.timestamp,
                action.userId || null,
                action.retryCount,
                action.maxRetries,
                action.status,
                (action as SyncQueueItem).localId || null,
                (action as SyncQueueItem).serverId || null
            ]
        );
    }

    async getSyncQueue(status?: string): Promise<SyncQueueItem[]> {
        if (!this.db) throw new Error('Database not initialized');

        const query = status
            ? 'SELECT * FROM sync_queue WHERE status = ? ORDER BY timestamp ASC'
            : 'SELECT * FROM sync_queue ORDER BY timestamp ASC';

        const params = status ? [status] : [];
        const result = await this.db.getAllAsync(query, params);

        return result.map(row => ({
            id: row.id as string,
            type: row.type as 'CREATE' | 'UPDATE' | 'DELETE',
            table: row.table_name as string,
            data: JSON.parse(row.data as string),
            timestamp: row.timestamp as number,
            userId: row.user_id as string,
            retryCount: row.retry_count as number,
            maxRetries: row.max_retries as number,
            status: row.status as 'pending' | 'syncing' | 'completed' | 'failed',
            localId: row.local_id as string,
            serverId: row.server_id as string,
            conflictResolution: row.conflict_resolution as 'client_wins' | 'server_wins' | 'merge'
        }));
    }

    async updateSyncQueueItem(id: string, updates: Partial<SyncQueueItem>): Promise<void> {
        if (!this.db) throw new Error('Database not initialized');

        const setClause = [];
        const values = [];

        if (updates.status !== undefined) {
            setClause.push('status = ?');
            values.push(updates.status);
        }
        if (updates.retryCount !== undefined) {
            setClause.push('retry_count = ?');
            values.push(updates.retryCount);
        }
        if (updates.serverId !== undefined) {
            setClause.push('server_id = ?');
            values.push(updates.serverId);
        }

        values.push(id);

        await this.db.runAsync(
            `UPDATE sync_queue SET ${setClause.join(', ')} WHERE id = ?`,
            values
        );
    }

    async removeSyncQueueItem(id: string): Promise<void> {
        if (!this.db) throw new Error('Database not initialized');
        await this.db.runAsync('DELETE FROM sync_queue WHERE id = ?', [id]);
    }

    async clearSyncQueue(): Promise<void> {
        if (!this.db) throw new Error('Database not initialized');
        await this.db.runAsync('DELETE FROM sync_queue');
    }

    // Generic cache operations
    async cacheData(table: string, id: string, data: any, userId?: string): Promise<void> {
        if (!this.db) throw new Error('Database not initialized');

        const timestamp = Date.now();
        const dataJson = JSON.stringify(data);

        const query = userId
            ? `INSERT OR REPLACE INTO ${table} (id, user_id, data, last_updated, synced) VALUES (?, ?, ?, ?, 1)`
            : `INSERT OR REPLACE INTO ${table} (id, data, last_updated, synced) VALUES (?, ?, ?, 1)`;

        const params = userId ? [id, userId, dataJson, timestamp] : [id, dataJson, timestamp];

        await this.db.runAsync(query, params);
    }

    async getCachedData(table: string, id?: string, userId?: string): Promise<any[]> {
        if (!this.db) throw new Error('Database not initialized');

        let query = `SELECT * FROM ${table}`;
        const params = [];

        if (id) {
            query += ' WHERE id = ?';
            params.push(id);
        } else if (userId) {
            query += ' WHERE user_id = ?';
            params.push(userId);
        }

        query += ' ORDER BY last_updated DESC';

        const result = await this.db.getAllAsync(query, params);
        return result.map(row => JSON.parse(row.data as string));
    }

    async removeCachedData(table: string, id: string): Promise<void> {
        if (!this.db) throw new Error('Database not initialized');
        await this.db.runAsync(`DELETE FROM ${table} WHERE id = ?`, [id]);
    }

    async clearCache(table?: string): Promise<void> {
        if (!this.db) throw new Error('Database not initialized');

        if (table) {
            await this.db.runAsync(`DELETE FROM ${table}`);
        } else {
            // Clear all cache tables
            const tables = [
                'offline_user_profiles',
                'offline_crop_plans',
                'offline_tasks',
                'offline_chat_sessions',
                'offline_chat_messages',
                'offline_community_posts',
                'offline_products'
            ];

            for (const tableName of tables) {
                await this.db.runAsync(`DELETE FROM ${tableName}`);
            }
        }
    }

    // Metadata operations
    async setMetadata(key: string, value: any): Promise<void> {
        if (!this.db) throw new Error('Database not initialized');

        await this.db.runAsync(
            'INSERT OR REPLACE INTO offline_metadata (key, value, updated_at) VALUES (?, ?, ?)',
            [key, JSON.stringify(value), Date.now()]
        );
    }

    async getMetadata(key: string): Promise<any> {
        if (!this.db) throw new Error('Database not initialized');

        const result = await this.db.getFirstAsync(
            'SELECT value FROM offline_metadata WHERE key = ?',
            [key]
        );

        return result ? JSON.parse(result.value as string) : null;
    }

    // Database maintenance
    async vacuum(): Promise<void> {
        if (!this.db) throw new Error('Database not initialized');
        await this.db.execAsync('VACUUM');
    }

    async getStorageSize(): Promise<number> {
        if (!this.db) throw new Error('Database not initialized');

        const result = await this.db.getFirstAsync('PRAGMA page_count');
        const pageCount = result?.page_count as number || 0;

        const pageSizeResult = await this.db.getFirstAsync('PRAGMA page_size');
        const pageSize = pageSizeResult?.page_size as number || 4096;

        return pageCount * pageSize;
    }

    async close(): Promise<void> {
        if (this.db) {
            await this.db.closeAsync();
            this.db = null;
        }
    }
}

export const offlineDatabase = new OfflineDatabase();