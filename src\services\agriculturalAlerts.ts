import weatherAlertService from './weatherAlerts';
import cropStageAlertService from './cropStageAlerts';
import aiAlertService from './aiAlerts';
import communityAlertService from './communityAlerts';
import notificationScheduler from './notificationScheduler';

export interface AlertCoordinatorConfig {
    weatherCheckInterval: number; // minutes
    cropStageCheckInterval: number; // minutes
    aiAnalysisProcessingEnabled: boolean;
    communityAlertsEnabled: boolean;
    emergencyAlertRadius: number; // km
}

export class AgriculturalAlertCoordinator {
    private static instance: AgriculturalAlertCoordinator;
    private config: AlertCoordinatorConfig;
    private intervalIds: Map<string, NodeJS.Timeout> = new Map();
    private isInitialized = false;

    static getInstance(): AgriculturalAlertCoordinator {
        if (!AgriculturalAlertCoordinator.instance) {
            AgriculturalAlertCoordinator.instance = new AgriculturalAlertCoordinator();
        }
        return AgriculturalAlertCoordinator.instance;
    }

    constructor() {
        this.config = {
            weatherCheckInterval: 30, // Check weather every 30 minutes
            cropStageCheckInterval: 60, // Check crop stages every hour
            aiAnalysisProcessingEnabled: true,
            communityAlertsEnabled: true,
            emergencyAlertRadius: 100, // 100km radius for emergency alerts
        };
    }

    async initialize(): Promise<void> {
        if (this.isInitialized) return;

        try {
            console.log('Initializing Agricultural Alert Coordinator...');

            // Initialize all alert services
            await weatherAlertService.initialize();
            await cropStageAlertService.initialize();
            await aiAlertService.initialize();
            await communityAlertService.initialize();

            // Start periodic checks
            this.startPeriodicChecks();

            this.isInitialized = true;
            console.log('Agricultural Alert Coordinator initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Agricultural Alert Coordinator:', error);
            throw error;
        }
    }

    private startPeriodicChecks(): void {
        // Weather alerts check
        const weatherInterval = setInterval(async () => {
            try {
                await this.checkWeatherAlerts();
            } catch (error) {
                console.error('Error in weather alerts check:', error);
            }
        }, this.config.weatherCheckInterval * 60 * 1000);

        this.intervalIds.set('weather', weatherInterval);

        // Crop stage alerts check
        const cropStageInterval = setInterval(async () => {
            try {
                await cropStageAlertService.checkCropStageUpdates();
            } catch (error) {
                console.error('Error in crop stage alerts check:', error);
            }
        }, this.config.cropStageCheckInterval * 60 * 1000);

        this.intervalIds.set('cropStage', cropStageInterval);

        console.log('Periodic alert checks started');
    }

    private async checkWeatherAlerts(): Promise<void> {
        try {
            // This would integrate with the weather service to get current conditions
            // For now, we'll use mock data
            const mockWeatherData = {
                location: { latitude: 31.9686, longitude: 35.2033, name: 'Jerusalem' },
                currentWeather: {
                    temperature: 15,
                    humidity: 65,
                    windSpeed: 25,
                    precipitation: 0,
                    pressure: 1013,
                    uvIndex: 3,
                    visibility: 10,
                    condition: 'partly_cloudy',
                    timestamp: new Date(),
                },
                forecast: [
                    {
                        date: new Date(Date.now() + 24 * 60 * 60 * 1000),
                        minTemp: 8,
                        maxTemp: 18,
                        condition: 'cloudy',
                        precipitationChance: 20,
                        windSpeed: 15,
                        humidity: 70,
                    },
                    {
                        date: new Date(Date.now() + 48 * 60 * 60 * 1000),
                        minTemp: 5,
                        maxTemp: 12,
                        condition: 'rain',
                        precipitationChance: 80,
                        windSpeed: 30,
                        humidity: 85,
                    },
                ],
            };

            await weatherAlertService.checkWeatherAlerts(
                mockWeatherData.location,
                mockWeatherData.currentWeather,
                mockWeatherData.forecast
            );
        } catch (error) {
            console.error('Failed to check weather alerts:', error);
        }
    }

    async processAIAnalysis(analysisResult: any): Promise<void> {
        if (!this.config.aiAnalysisProcessingEnabled) return;

        try {
            await aiAlertService.processAIAnalysis(analysisResult);
        } catch (error) {
            console.error('Failed to process AI analysis:', error);
        }
    }

    async handleCommunityEvent(event: any): Promise<void> {
        if (!this.config.communityAlertsEnabled) return;

        try {
            await communityAlertService.handleNewCommunityEvent(event);
            await communityAlertService.scheduleEventReminders(event);
        } catch (error) {
            console.error('Failed to handle community event:', error);
        }
    }

    async handleCommunityPost(post: any): Promise<void> {
        if (!this.config.communityAlertsEnabled) return;

        try {
            await communityAlertService.handleNewCommunityPost(post);
        } catch (error) {
            console.error('Failed to handle community post:', error);
        }
    }

    async sendEmergencyAlert(
        title: string,
        message: string,
        location: { latitude: number; longitude: number; name: string },
        customRadius?: number
    ): Promise<void> {
        try {
            const radius = customRadius || this.config.emergencyAlertRadius;

            // Send through community alert service
            await communityAlertService.sendEmergencyAlert(title, message, location, radius);

            // Also send weather alert if weather-related
            if (title.toLowerCase().includes('weather') || title.toLowerCase().includes('storm')) {
                await weatherAlertService.createCustomAlert(
                    'system', // System user ID
                    'severe_weather',
                    title,
                    message,
                    ['Seek shelter immediately', 'Follow local emergency procedures'],
                    'extreme'
                );
            }

            console.log(`Emergency alert sent: ${title}`);
        } catch (error) {
            console.error('Failed to send emergency alert:', error);
            throw error;
        }
    }

    async scheduleTaskReminder(
        taskId: string,
        taskTitle: string,
        dueDate: Date,
        cropPlanId?: string
    ): Promise<void> {
        try {
            await notificationScheduler.scheduleTaskReminder(taskId, taskTitle, dueDate, cropPlanId);
        } catch (error) {
            console.error('Failed to schedule task reminder:', error);
        }
    }

    async cancelTaskReminder(taskId: string): Promise<void> {
        try {
            await notificationScheduler.cancelTaskReminder(taskId);
        } catch (error) {
            console.error('Failed to cancel task reminder:', error);
        }
    }

    async createCustomAlert(
        type: 'weather' | 'crop' | 'ai' | 'community',
        alertData: any
    ): Promise<void> {
        try {
            switch (type) {
                case 'weather':
                    await weatherAlertService.createCustomAlert(
                        alertData.userId,
                        alertData.alertType,
                        alertData.title,
                        alertData.description,
                        alertData.recommendations,
                        alertData.severity
                    );
                    break;

                case 'crop':
                    await cropStageAlertService.createCustomCropStageAlert(
                        alertData.cropPlanId,
                        alertData.stageName,
                        alertData.recommendations,
                        alertData.criticalActions
                    );
                    break;

                case 'ai':
                    await aiAlertService.createCustomAIAlert(
                        alertData.userId,
                        alertData.insightType,
                        alertData.title,
                        alertData.description,
                        alertData.recommendations,
                        alertData.urgency,
                        alertData.imageUrl,
                        alertData.relatedCropPlan
                    );
                    break;

                case 'community':
                    await communityAlertService.sendEmergencyAlert(
                        alertData.title,
                        alertData.message,
                        alertData.location,
                        alertData.radius
                    );
                    break;

                default:
                    throw new Error(`Unknown alert type: ${type}`);
            }
        } catch (error) {
            console.error(`Failed to create custom ${type} alert:`, error);
            throw error;
        }
    }

    async getAlertHistory(
        userId: string,
        type?: 'weather' | 'crop' | 'ai' | 'community',
        limit: number = 20
    ): Promise<any[]> {
        try {
            switch (type) {
                case 'weather':
                    return await weatherAlertService.getAlertHistory(userId, limit);
                case 'ai':
                    return await aiAlertService.getAIInsightHistory(userId, limit);
                default:
                    // Return combined history from notification service
                    return [];
            }
        } catch (error) {
            console.error('Failed to get alert history:', error);
            return [];
        }
    }

    async updateConfiguration(newConfig: Partial<AlertCoordinatorConfig>): Promise<void> {
        try {
            this.config = { ...this.config, ...newConfig };

            // Restart periodic checks if intervals changed
            if (newConfig.weatherCheckInterval || newConfig.cropStageCheckInterval) {
                this.stopPeriodicChecks();
                this.startPeriodicChecks();
            }

            console.log('Alert coordinator configuration updated');
        } catch (error) {
            console.error('Failed to update configuration:', error);
            throw error;
        }
    }

    getConfiguration(): AlertCoordinatorConfig {
        return { ...this.config };
    }

    private stopPeriodicChecks(): void {
        this.intervalIds.forEach((intervalId, key) => {
            clearInterval(intervalId);
            console.log(`Stopped ${key} periodic check`);
        });
        this.intervalIds.clear();
    }

    async shutdown(): Promise<void> {
        try {
            this.stopPeriodicChecks();
            this.isInitialized = false;
            console.log('Agricultural Alert Coordinator shut down');
        } catch (error) {
            console.error('Error during shutdown:', error);
        }
    }

    // Health check method
    async healthCheck(): Promise<{
        status: 'healthy' | 'degraded' | 'unhealthy';
        services: Record<string, boolean>;
        lastChecks: Record<string, Date>;
    }> {
        const services = {
            weather: true, // Would check if weather service is responding
            cropStage: true, // Would check if crop stage service is working
            ai: this.config.aiAnalysisProcessingEnabled,
            community: this.config.communityAlertsEnabled,
            scheduler: true, // Would check if scheduler is working
        };

        const healthyServices = Object.values(services).filter(Boolean).length;
        const totalServices = Object.keys(services).length;

        let status: 'healthy' | 'degraded' | 'unhealthy';
        if (healthyServices === totalServices) {
            status = 'healthy';
        } else if (healthyServices > totalServices / 2) {
            status = 'degraded';
        } else {
            status = 'unhealthy';
        }

        return {
            status,
            services,
            lastChecks: {
                weather: new Date(),
                cropStage: new Date(),
                ai: new Date(),
                community: new Date(),
            },
        };
    }

    isReady(): boolean {
        return this.isInitialized;
    }
}

export const agriculturalAlertCoordinator = AgriculturalAlertCoordinator.getInstance();
export default agriculturalAlertCoordinator;