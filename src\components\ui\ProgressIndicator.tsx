import React from 'react';
import { View, Text } from 'react-native';

export interface ProgressIndicatorProps {
    currentStep: number;
    totalSteps: number;
    stepLabels?: string[];
    className?: string;
    showLabels?: boolean;
    isRTL?: boolean;
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
    currentStep,
    totalSteps,
    stepLabels = [],
    className = '',
    showLabels = true,
    isRTL = false,
}) => {
    const progressPercentage = (currentStep / totalSteps) * 100;

    return (
        <View className={`w-full ${className}`}>
            {/* Progress Bar */}
            <View className="mb-4">
                <View className="h-2 w-full rounded-full bg-earth-200">
                    <View
                        className="h-full rounded-full bg-primary-500 transition-all duration-300"
                        style={{ width: `${progressPercentage}%` }}
                    />
                </View>
            </View>

            {/* Step Indicators */}
            <View className={`flex-row justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                {Array.from({ length: totalSteps }, (_, index) => {
                    const stepNumber = index + 1;
                    const isCompleted = stepNumber < currentStep;
                    const isCurrent = stepNumber === currentStep;
                    const isPending = stepNumber > currentStep;

                    return (
                        <View key={stepNumber} className="items-center">
                            {/* Step Circle */}
                            <View
                                className={`mb-2 h-8 w-8 items-center justify-center rounded-full border-2 ${isCompleted
                                        ? 'border-primary-500 bg-primary-500'
                                        : isCurrent
                                            ? 'border-primary-500 bg-white'
                                            : 'border-earth-300 bg-white'
                                    }`}
                            >
                                {isCompleted ? (
                                    <Text className="text-sm font-bold text-white">✓</Text>
                                ) : (
                                    <Text
                                        className={`text-sm font-bold ${isCurrent ? 'text-primary-500' : 'text-earth-400'
                                            }`}
                                    >
                                        {stepNumber}
                                    </Text>
                                )}
                            </View>

                            {/* Step Label */}
                            {showLabels && stepLabels[index] && (
                                <Text
                                    className={`text-xs text-center ${isCurrent ? 'text-primary-600 font-medium' : 'text-earth-500'
                                        }`}
                                    numberOfLines={2}
                                >
                                    {stepLabels[index]}
                                </Text>
                            )}
                        </View>
                    );
                })}
            </View>

            {/* Current Step Text */}
            <Text className={`mt-4 text-center text-sm text-earth-600 ${isRTL ? 'text-right' : 'text-left'}`}>
                Step {currentStep} of {totalSteps}
            </Text>
        </View>
    );
};