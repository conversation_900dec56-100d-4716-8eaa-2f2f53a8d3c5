import { supabase } from './supabase/client';
import notificationService from './notifications';
import NotificationTemplateService from './notificationTemplates';
import { CropStageAlert } from '../types/notifications';

export interface CropStage {
    name: string;
    daysFromPlanting: number;
    description: string;
    criticalActions: string[];
    recommendations: string[];
    optimalConditions: {
        temperature: { min: number; max: number };
        humidity: { min: number; max: number };
        soilMoisture: string;
    };
    nextStage?: string;
}

export interface CropStageDefinition {
    cropType: string;
    stages: CropStage[];
    totalGrowingDays: number;
}

export class CropStageAlertService {
    private static instance: CropStageAlertService;
    private cropStageDefinitions: Map<string, CropStageDefinition> = new Map();

    static getInstance(): CropStageAlertService {
        if (!CropStageAlertService.instance) {
            CropStageAlertService.instance = new CropStageAlertService();
        }
        return CropStageAlertService.instance;
    }

    async initialize(): Promise<void> {
        try {
            await this.loadCropStageDefinitions();
            console.log('Crop stage alert service initialized');
        } catch (error) {
            console.error('Failed to initialize crop stage alert service:', error);
        }
    }

    private async loadCropStageDefinitions(): Promise<void> {
        // Define crop stage definitions
        const definitions: CropStageDefinition[] = [
            {
                cropType: 'tomatoes',
                totalGrowingDays: 120,
                stages: [
                    {
                        name: 'Germination',
                        daysFromPlanting: 0,
                        description: 'Seeds are sprouting and first leaves are emerging',
                        criticalActions: [
                            'Maintain consistent soil moisture',
                            'Provide adequate light (14-16 hours)',
                            'Keep temperature between 20-25°C',
                        ],
                        recommendations: [
                            'Use seed starting mix for better drainage',
                            'Cover seeds lightly with soil',
                            'Mist regularly to prevent drying out',
                        ],
                        optimalConditions: {
                            temperature: { min: 20, max: 25 },
                            humidity: { min: 70, max: 85 },
                            soilMoisture: 'consistently moist',
                        },
                        nextStage: 'Seedling',
                    },
                    {
                        name: 'Seedling',
                        daysFromPlanting: 14,
                        description: 'True leaves are developing, plants are establishing',
                        criticalActions: [
                            'Transplant to larger containers if needed',
                            'Begin light fertilization',
                            'Gradually introduce to outdoor conditions',
                        ],
                        recommendations: [
                            'Provide support for weak stems',
                            'Monitor for damping-off disease',
                            'Ensure good air circulation',
                        ],
                        optimalConditions: {
                            temperature: { min: 18, max: 24 },
                            humidity: { min: 60, max: 75 },
                            soilMoisture: 'moist but well-draining',
                        },
                        nextStage: 'Vegetative Growth',
                    },
                    {
                        name: 'Vegetative Growth',
                        daysFromPlanting: 35,
                        description: 'Rapid leaf and stem growth, plant is establishing size',
                        criticalActions: [
                            'Transplant to final location',
                            'Install support structures (cages/stakes)',
                            'Begin regular fertilization schedule',
                        ],
                        recommendations: [
                            'Pinch suckers for indeterminate varieties',
                            'Mulch around plants to retain moisture',
                            'Monitor for pests and diseases',
                        ],
                        optimalConditions: {
                            temperature: { min: 16, max: 29 },
                            humidity: { min: 50, max: 70 },
                            soilMoisture: 'consistently moist',
                        },
                        nextStage: 'Flowering',
                    },
                    {
                        name: 'Flowering',
                        daysFromPlanting: 60,
                        description: 'First flowers are appearing, pollination is critical',
                        criticalActions: [
                            'Ensure consistent watering (avoid water stress)',
                            'Monitor for blossom end rot',
                            'Support heavy branches',
                        ],
                        recommendations: [
                            'Hand pollinate if necessary',
                            'Avoid high nitrogen fertilizers',
                            'Maintain calcium levels in soil',
                        ],
                        optimalConditions: {
                            temperature: { min: 18, max: 27 },
                            humidity: { min: 45, max: 65 },
                            soilMoisture: 'consistently moist, avoid fluctuations',
                        },
                        nextStage: 'Fruit Development',
                    },
                    {
                        name: 'Fruit Development',
                        daysFromPlanting: 80,
                        description: 'Fruits are forming and growing, high nutrient demand',
                        criticalActions: [
                            'Increase watering frequency',
                            'Apply potassium-rich fertilizer',
                            'Prune lower leaves touching ground',
                        ],
                        recommendations: [
                            'Monitor for fruit cracking',
                            'Harvest regularly to encourage production',
                            'Watch for late blight and other diseases',
                        ],
                        optimalConditions: {
                            temperature: { min: 20, max: 26 },
                            humidity: { min: 50, max: 70 },
                            soilMoisture: 'deep, consistent watering',
                        },
                        nextStage: 'Ripening',
                    },
                    {
                        name: 'Ripening',
                        daysFromPlanting: 100,
                        description: 'Fruits are maturing and changing color',
                        criticalActions: [
                            'Reduce watering slightly to concentrate flavors',
                            'Harvest ripe fruits promptly',
                            'Protect from extreme weather',
                        ],
                        recommendations: [
                            'Pick fruits at optimal ripeness',
                            'Store properly to extend shelf life',
                            'Save seeds from best fruits',
                        ],
                        optimalConditions: {
                            temperature: { min: 18, max: 28 },
                            humidity: { min: 45, max: 65 },
                            soilMoisture: 'moderate, avoid overwatering',
                        },
                    },
                ],
            },
            {
                cropType: 'corn',
                totalGrowingDays: 100,
                stages: [
                    {
                        name: 'Germination',
                        daysFromPlanting: 0,
                        description: 'Seeds are sprouting, shoots emerging from soil',
                        criticalActions: [
                            'Ensure soil temperature is above 10°C',
                            'Maintain adequate soil moisture',
                            'Protect from birds and pests',
                        ],
                        recommendations: [
                            'Plant in blocks for better pollination',
                            'Use treated seeds in cool conditions',
                            'Monitor for corn rootworm',
                        ],
                        optimalConditions: {
                            temperature: { min: 15, max: 25 },
                            humidity: { min: 60, max: 80 },
                            soilMoisture: 'moist but not waterlogged',
                        },
                        nextStage: 'Vegetative Growth',
                    },
                    {
                        name: 'Vegetative Growth',
                        daysFromPlanting: 21,
                        description: 'Rapid leaf development, plant establishing height',
                        criticalActions: [
                            'Side-dress with nitrogen fertilizer',
                            'Control weeds aggressively',
                            'Monitor for corn borer',
                        ],
                        recommendations: [
                            'Hill soil around base of plants',
                            'Ensure adequate spacing for air circulation',
                            'Scout for armyworm damage',
                        ],
                        optimalConditions: {
                            temperature: { min: 18, max: 30 },
                            humidity: { min: 50, max: 70 },
                            soilMoisture: 'consistently moist',
                        },
                        nextStage: 'Tasseling',
                    },
                    {
                        name: 'Tasseling',
                        daysFromPlanting: 60,
                        description: 'Tassels are emerging, pollen production beginning',
                        criticalActions: [
                            'Ensure adequate water supply',
                            'Monitor for drought stress',
                            'Check for proper pollination',
                        ],
                        recommendations: [
                            'Avoid cultivation that damages roots',
                            'Watch for corn earworm',
                            'Ensure good air movement for pollination',
                        ],
                        optimalConditions: {
                            temperature: { min: 20, max: 32 },
                            humidity: { min: 45, max: 65 },
                            soilMoisture: 'deep, consistent watering',
                        },
                        nextStage: 'Grain Filling',
                    },
                    {
                        name: 'Grain Filling',
                        daysFromPlanting: 80,
                        description: 'Kernels are developing and filling with starch',
                        criticalActions: [
                            'Maintain consistent water supply',
                            'Monitor for late-season pests',
                            'Protect from strong winds',
                        ],
                        recommendations: [
                            'Avoid stress during this critical period',
                            'Scout for stalk rot diseases',
                            'Plan harvest timing',
                        ],
                        optimalConditions: {
                            temperature: { min: 18, max: 28 },
                            humidity: { min: 50, max: 70 },
                            soilMoisture: 'consistent, avoid water stress',
                        },
                        nextStage: 'Maturity',
                    },
                    {
                        name: 'Maturity',
                        daysFromPlanting: 100,
                        description: 'Kernels are fully developed, ready for harvest',
                        criticalActions: [
                            'Monitor moisture content for harvest timing',
                            'Prepare harvesting equipment',
                            'Plan storage and drying',
                        ],
                        recommendations: [
                            'Harvest at optimal moisture content',
                            'Dry properly to prevent mold',
                            'Store in pest-free environment',
                        ],
                        optimalConditions: {
                            temperature: { min: 15, max: 25 },
                            humidity: { min: 40, max: 60 },
                            soilMoisture: 'reduced watering before harvest',
                        },
                    },
                ],
            },
            // Add more crop definitions as needed
        ];

        // Store definitions in map
        definitions.forEach(def => {
            this.cropStageDefinitions.set(def.cropType, def);
        });
    }

    async checkCropStageUpdates(): Promise<void> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return;

            // Get active crop plans
            const { data: cropPlans } = await supabase
                .from('crop_plans')
                .select('*')
                .eq('user_id', user.id)
                .eq('status', 'active');

            if (!cropPlans) return;

            for (const plan of cropPlans) {
                const stageAlert = await this.calculateCropStageUpdate(plan);
                if (stageAlert) {
                    await this.sendCropStageAlert(stageAlert);
                }
            }
        } catch (error) {
            console.error('Failed to check crop stage updates:', error);
        }
    }

    private async calculateCropStageUpdate(cropPlan: any): Promise<CropStageAlert | null> {
        try {
            const cropDefinition = this.cropStageDefinitions.get(cropPlan.crop_type);
            if (!cropDefinition) return null;

            const plantingDate = new Date(cropPlan.planting_date);
            const currentDate = new Date();
            const daysFromPlanting = Math.floor((currentDate.getTime() - plantingDate.getTime()) / (1000 * 60 * 60 * 24));

            // Find current stage
            let currentStage: CropStage | null = null;
            let nextStage: CropStage | null = null;

            for (let i = 0; i < cropDefinition.stages.length; i++) {
                const stage = cropDefinition.stages[i];
                if (daysFromPlanting >= stage.daysFromPlanting) {
                    currentStage = stage;
                    nextStage = cropDefinition.stages[i + 1] || null;
                } else {
                    break;
                }
            }

            if (!currentStage || !nextStage) return null;

            // Check if we're approaching the next stage (within 3 days)
            const daysToNextStage = nextStage.daysFromPlanting - daysFromPlanting;
            if (daysToNextStage > 3) return null;

            // Check if we already sent an alert for this stage transition
            const alertKey = `${cropPlan.id}_${nextStage.name}`;
            const existingAlert = await this.checkExistingAlert(alertKey);
            if (existingAlert) return null;

            return {
                id: alertKey,
                cropPlanId: cropPlan.id,
                cropType: cropPlan.crop_type,
                currentStage: currentStage.name,
                nextStage: nextStage.name,
                daysToNextStage,
                recommendations: nextStage.recommendations,
                criticalActions: nextStage.criticalActions,
                optimalConditions: nextStage.optimalConditions,
            };
        } catch (error) {
            console.error('Failed to calculate crop stage update:', error);
            return null;
        }
    }

    private async checkExistingAlert(alertKey: string): Promise<boolean> {
        try {
            const { data } = await supabase
                .from('notifications')
                .select('id')
                .eq('data->alertKey', alertKey)
                .eq('type', 'crop_stage_update')
                .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()) // Within last 7 days
                .single();

            return !!data;
        } catch (error) {
            return false;
        }
    }

    private async sendCropStageAlert(alert: CropStageAlert): Promise<void> {
        try {
            const notification = NotificationTemplateService.createCropStageNotification(alert);

            await notificationService.sendLocalNotification(
                notification.title,
                notification.body,
                {
                    ...notification.data,
                    alertKey: alert.id,
                },
                { priority: notification.priority }
            );

            // Store notification in database
            const { data: { user } } = await supabase.auth.getUser();
            if (user) {
                await supabase
                    .from('notifications')
                    .insert({
                        user_id: user.id,
                        type: 'crop_stage_update',
                        title: notification.title,
                        body: notification.body,
                        data: {
                            ...notification.data,
                            alertKey: alert.id,
                        },
                        priority: notification.priority,
                        category: 'agricultural',
                    });
            }
        } catch (error) {
            console.error('Failed to send crop stage alert:', error);
        }
    }

    async getCropStageInfo(cropType: string, daysFromPlanting: number): Promise<{
        currentStage: CropStage | null;
        nextStage: CropStage | null;
        progress: number;
    }> {
        const cropDefinition = this.cropStageDefinitions.get(cropType);
        if (!cropDefinition) {
            return { currentStage: null, nextStage: null, progress: 0 };
        }

        let currentStage: CropStage | null = null;
        let nextStage: CropStage | null = null;

        for (let i = 0; i < cropDefinition.stages.length; i++) {
            const stage = cropDefinition.stages[i];
            if (daysFromPlanting >= stage.daysFromPlanting) {
                currentStage = stage;
                nextStage = cropDefinition.stages[i + 1] || null;
            } else {
                break;
            }
        }

        const progress = Math.min((daysFromPlanting / cropDefinition.totalGrowingDays) * 100, 100);

        return { currentStage, nextStage, progress };
    }

    async createCustomCropStageAlert(
        cropPlanId: string,
        stageName: string,
        recommendations: string[],
        criticalActions: string[]
    ): Promise<void> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return;

            const { data: cropPlan } = await supabase
                .from('crop_plans')
                .select('crop_type')
                .eq('id', cropPlanId)
                .single();

            if (!cropPlan) return;

            const alert: CropStageAlert = {
                id: `custom_${Date.now()}`,
                cropPlanId,
                cropType: cropPlan.crop_type,
                currentStage: 'Custom',
                nextStage: stageName,
                daysToNextStage: 0,
                recommendations,
                criticalActions,
                optimalConditions: {
                    temperature: { min: 15, max: 30 },
                    humidity: { min: 50, max: 70 },
                    soilMoisture: 'moderate',
                },
            };

            await this.sendCropStageAlert(alert);
        } catch (error) {
            console.error('Failed to create custom crop stage alert:', error);
            throw error;
        }
    }

    getCropStageDefinitions(): Map<string, CropStageDefinition> {
        return this.cropStageDefinitions;
    }

    getSupportedCrops(): string[] {
        return Array.from(this.cropStageDefinitions.keys());
    }
}

export const cropStageAlertService = CropStageAlertService.getInstance();
export default cropStageAlertService;