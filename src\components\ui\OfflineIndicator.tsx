import React from 'react';
import { View, Text, TouchableOpacity, Animated } from 'react-native';
import { useOfflineStore, useConnectivity, useSyncStatus } from '../../stores/offline';
import { offlineModeService } from '../../services/offline';

interface OfflineIndicatorProps {
    position?: 'top' | 'bottom';
    showDetails?: boolean;
    onPress?: () => void;
}

export const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({
    position = 'top',
    showDetails = false,
    onPress
}) => {
    const { isOnline, isOfflineMode } = useConnectivity();
    const { syncInProgress, pendingActions, failedActions } = useSyncStatus();
    const { forceSync } = useOfflineStore();

    const [fadeAnim] = React.useState(new Animated.Value(0));

    React.useEffect(() => {
        if (isOfflineMode || syncInProgress || pendingActions > 0 || failedActions > 0) {
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
            }).start();
        } else {
            Animated.timing(fadeAnim, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            }).start();
        }
    }, [isOfflineMode, syncInProgress, pendingActions, failedActions]);

    const getIndicatorColor = () => {
        if (failedActions > 0) return 'bg-red-500';
        if (isOfflineMode) return 'bg-orange-500';
        if (syncInProgress) return 'bg-blue-500';
        if (pendingActions > 0) return 'bg-yellow-500';
        return 'bg-green-500';
    };

    const getIndicatorText = () => {
        if (failedActions > 0) return `${failedActions} sync failed`;
        if (isOfflineMode) return offlineModeService.getOfflineStatusMessage();
        if (syncInProgress) return 'Syncing...';
        if (pendingActions > 0) return `${pendingActions} pending`;
        return 'Online';
    };

    const getDetailText = () => {
        if (!showDetails) return null;

        if (failedActions > 0) {
            return 'Tap to retry failed syncs';
        }
        if (isOfflineMode) {
            return offlineModeService.getNextRetryMessage();
        }
        if (pendingActions > 0) {
            return 'Changes will sync when online';
        }
        return null;
    };

    const handlePress = async () => {
        if (onPress) {
            onPress();
            return;
        }

        if (failedActions > 0) {
            await useOfflineStore.getState().retryFailedActions();
        } else if (pendingActions > 0 && isOnline) {
            await forceSync();
        } else if (isOfflineMode) {
            await offlineModeService.forceReconnectionAttempt();
        }
    };

    return (
        <Animated.View
            style={{
                opacity: fadeAnim,
                transform: [
                    {
                        translateY: fadeAnim.interpolate({
                            inputRange: [0, 1],
                            outputRange: position === 'top' ? [-50, 0] : [50, 0],
                        }),
                    },
                ],
            }}
            className={`absolute left-0 right-0 z-50 ${position === 'top' ? 'top-0' : 'bottom-0'
                }`}
        >
            <TouchableOpacity
                onPress={handlePress}
                className={`${getIndicatorColor()} px-4 py-2 mx-4 rounded-lg shadow-lg`}
                activeOpacity={0.8}
            >
                <View className="flex-row items-center justify-between">
                    <View className="flex-1">
                        <Text className="text-white font-medium text-sm">
                            {getIndicatorText()}
                        </Text>
                        {showDetails && getDetailText() && (
                            <Text className="text-white/80 text-xs mt-1">
                                {getDetailText()}
                            </Text>
                        )}
                    </View>

                    {syncInProgress && (
                        <View className="ml-2">
                            <SyncSpinner />
                        </View>
                    )}

                    {(failedActions > 0 || pendingActions > 0) && (
                        <View className="ml-2 bg-white/20 rounded-full px-2 py-1">
                            <Text className="text-white text-xs font-bold">
                                {failedActions > 0 ? '!' : pendingActions}
                            </Text>
                        </View>
                    )}
                </View>
            </TouchableOpacity>
        </Animated.View>
    );
};

const SyncSpinner: React.FC = () => {
    const [rotation] = React.useState(new Animated.Value(0));

    React.useEffect(() => {
        const animate = () => {
            Animated.timing(rotation, {
                toValue: 1,
                duration: 1000,
                useNativeDriver: true,
            }).start(() => {
                rotation.setValue(0);
                animate();
            });
        };
        animate();
    }, []);

    return (
        <Animated.View
            style={{
                transform: [
                    {
                        rotate: rotation.interpolate({
                            inputRange: [0, 1],
                            outputRange: ['0deg', '360deg'],
                        }),
                    },
                ],
            }}
            className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full"
        />
    );
};

export default OfflineIndicator;