/**
 * Accessibility Testing Utilities
 * Tools for validating and testing accessibility features
 */

import { AccessibilityInfo, Platform } from 'react-native';
import { colorContrast } from './accessibility';

export interface AccessibilityTestResult {
    passed: boolean;
    message: string;
    severity: 'error' | 'warning' | 'info';
    recommendation?: string;
}

export interface AccessibilityTestSuite {
    componentName: string;
    tests: AccessibilityTestResult[];
    overallScore: number;
}

/**
 * Accessibility Testing Class
 */
export class AccessibilityTester {
    private static instance: AccessibilityTester;

    static getInstance(): AccessibilityTester {
        if (!AccessibilityTester.instance) {
            AccessibilityTester.instance = new AccessibilityTester();
        }
        return AccessibilityTester.instance;
    }

    /**
     * Test touch target size
     */
    testTouchTargetSize(width: number, height: number, componentName: string): AccessibilityTestResult {
        const minSize = 44; // iOS/Android minimum
        const recommendedSize = 56; // Better for work gloves

        const size = Math.min(width, height);

        if (size < minSize) {
            return {
                passed: false,
                message: `Touch target too small: ${size}px (minimum: ${minSize}px)`,
                severity: 'error',
                recommendation: `Increase ${componentName} size to at least ${minSize}px`,
            };
        }

        if (size < recommendedSize) {
            return {
                passed: true,
                message: `Touch target meets minimum but could be larger: ${size}px`,
                severity: 'warning',
                recommendation: `Consider increasing to ${recommendedSize}px for better agricultural use`,
            };
        }

        return {
            passed: true,
            message: `Touch target size is good: ${size}px`,
            severity: 'info',
        };
    }

    /**
     * Test color contrast
     */
    testColorContrast(foreground: string, background: string, isLargeText: boolean = false): AccessibilityTestResult {
        const ratio = colorContrast.getContrastRatio(foreground, background);
        const requiredRatio = isLargeText ? 3.0 : 4.5;
        const aaRatio = isLargeText ? 4.5 : 7.0;

        if (ratio < requiredRatio) {
            return {
                passed: false,
                message: `Poor contrast ratio: ${ratio.toFixed(2)}:1 (required: ${requiredRatio}:1)`,
                severity: 'error',
                recommendation: 'Use colors with higher contrast for better readability',
            };
        }

        if (ratio < aaRatio) {
            return {
                passed: true,
                message: `Contrast meets AA standard: ${ratio.toFixed(2)}:1`,
                severity: 'warning',
                recommendation: `Consider improving to ${aaRatio}:1 for AAA compliance`,
            };
        }

        return {
            passed: true,
            message: `Excellent contrast ratio: ${ratio.toFixed(2)}:1`,
            severity: 'info',
        };
    }

    /**
     * Test accessibility labels
     */
    testAccessibilityLabels(props: any, componentName: string): AccessibilityTestResult {
        const hasLabel = props.accessibilityLabel || props.accessibilityLabelledBy;
        const hasRole = props.accessibilityRole;
        const hasHint = props.accessibilityHint;

        if (!hasLabel) {
            return {
                passed: false,
                message: 'Missing accessibility label',
                severity: 'error',
                recommendation: `Add accessibilityLabel prop to ${componentName}`,
            };
        }

        if (!hasRole) {
            return {
                passed: true,
                message: 'Has label but missing role',
                severity: 'warning',
                recommendation: `Add accessibilityRole prop to ${componentName}`,
            };
        }

        if (!hasHint) {
            return {
                passed: true,
                message: 'Has label and role but missing hint',
                severity: 'info',
                recommendation: `Consider adding accessibilityHint for better context`,
            };
        }

        return {
            passed: true,
            message: 'Complete accessibility labels',
            severity: 'info',
        };
    }

    /**
     * Test voice navigation support
     */
    testVoiceNavigation(componentProps: any, componentName: string): AccessibilityTestResult {
        const hasVoiceSupport = componentProps.voiceFeedbackEnabled || componentProps.onVoiceFeedback;
        const hasAccessibleText = componentProps.accessibilityLabel || componentProps.children;

        if (!hasAccessibleText) {
            return {
                passed: false,
                message: 'No text available for voice navigation',
                severity: 'error',
                recommendation: `Add text content or accessibility label to ${componentName}`,
            };
        }

        if (!hasVoiceSupport) {
            return {
                passed: true,
                message: 'Has text but no voice feedback',
                severity: 'warning',
                recommendation: `Consider adding voice feedback support to ${componentName}`,
            };
        }

        return {
            passed: true,
            message: 'Voice navigation supported',
            severity: 'info',
        };
    }

    /**
     * Test keyboard navigation
     */
    testKeyboardNavigation(componentProps: any, componentName: string): AccessibilityTestResult {
        const isInteractive = componentProps.onPress || componentProps.onFocus || componentProps.onBlur;
        const hasFocusSupport = componentProps.focusable !== false;
        const hasTabIndex = componentProps.tabIndex !== undefined;

        if (!isInteractive) {
            return {
                passed: true,
                message: 'Non-interactive component',
                severity: 'info',
            };
        }

        if (!hasFocusSupport) {
            return {
                passed: false,
                message: 'Interactive component not focusable',
                severity: 'error',
                recommendation: `Ensure ${componentName} can receive focus for keyboard navigation`,
            };
        }

        return {
            passed: true,
            message: 'Keyboard navigation supported',
            severity: 'info',
        };
    }

    /**
     * Test font scaling support
     */
    testFontScaling(fontSize: number, componentName: string): AccessibilityTestResult {
        const minFontSize = 12;
        const maxRecommendedSize = 24;

        if (fontSize < minFontSize) {
            return {
                passed: false,
                message: `Font too small: ${fontSize}px (minimum: ${minFontSize}px)`,
                severity: 'error',
                recommendation: `Increase font size in ${componentName}`,
            };
        }

        if (fontSize > maxRecommendedSize) {
            return {
                passed: true,
                message: `Large font size: ${fontSize}px`,
                severity: 'warning',
                recommendation: 'Ensure layout handles large text properly',
            };
        }

        return {
            passed: true,
            message: `Good font size: ${fontSize}px`,
            severity: 'info',
        };
    }

    /**
     * Run comprehensive accessibility test suite
     */
    runTestSuite(
        componentName: string,
        componentProps: any,
        dimensions?: { width: number; height: number },
        colors?: { foreground: string; background: string },
        fontSize?: number
    ): AccessibilityTestSuite {
        const tests: AccessibilityTestResult[] = [];

        // Test touch targets
        if (dimensions) {
            tests.push(this.testTouchTargetSize(dimensions.width, dimensions.height, componentName));
        }

        // Test color contrast
        if (colors) {
            tests.push(this.testColorContrast(colors.foreground, colors.background));
        }

        // Test accessibility labels
        tests.push(this.testAccessibilityLabels(componentProps, componentName));

        // Test voice navigation
        tests.push(this.testVoiceNavigation(componentProps, componentName));

        // Test keyboard navigation
        tests.push(this.testKeyboardNavigation(componentProps, componentName));

        // Test font scaling
        if (fontSize) {
            tests.push(this.testFontScaling(fontSize, componentName));
        }

        // Calculate overall score
        const passedTests = tests.filter(test => test.passed).length;
        const overallScore = Math.round((passedTests / tests.length) * 100);

        return {
            componentName,
            tests,
            overallScore,
        };
    }

    /**
     * Generate accessibility report
     */
    generateReport(testSuites: AccessibilityTestSuite[]): string {
        let report = '# Accessibility Test Report\n\n';

        const totalTests = testSuites.reduce((sum, suite) => sum + suite.tests.length, 0);
        const totalPassed = testSuites.reduce((sum, suite) =>
            sum + suite.tests.filter(test => test.passed).length, 0
        );
        const overallScore = Math.round((totalPassed / totalTests) * 100);

        report += `## Overall Score: ${overallScore}%\n`;
        report += `**Tests Passed:** ${totalPassed}/${totalTests}\n\n`;

        testSuites.forEach(suite => {
            report += `## ${suite.componentName} (${suite.overallScore}%)\n\n`;

            suite.tests.forEach(test => {
                const icon = test.passed ? '✅' : '❌';
                const severity = test.severity.toUpperCase();

                report += `${icon} **[${severity}]** ${test.message}\n`;
                if (test.recommendation) {
                    report += `   💡 *${test.recommendation}*\n`;
                }
                report += '\n';
            });
        });

        return report;
    }
}

/**
 * Accessibility testing hooks for React components
 */
export const useAccessibilityTesting = () => {
    const tester = AccessibilityTester.getInstance();

    const testComponent = (
        componentName: string,
        props: any,
        dimensions?: { width: number; height: number },
        colors?: { foreground: string; background: string },
        fontSize?: number
    ) => {
        return tester.runTestSuite(componentName, props, dimensions, colors, fontSize);
    };

    return {
        testComponent,
        generateReport: tester.generateReport.bind(tester),
    };
};

/**
 * Development-only accessibility validator
 */
export const validateAccessibility = (componentName: string, props: any) => {
    if (__DEV__) {
        const tester = AccessibilityTester.getInstance();
        const results = tester.runTestSuite(componentName, props);

        const errors = results.tests.filter(test => !test.passed && test.severity === 'error');
        const warnings = results.tests.filter(test => test.severity === 'warning');

        if (errors.length > 0) {
            console.error(`🚨 Accessibility Errors in ${componentName}:`, errors);
        }

        if (warnings.length > 0) {
            console.warn(`⚠️ Accessibility Warnings in ${componentName}:`, warnings);
        }

        if (results.overallScore < 80) {
            console.warn(`📊 Low accessibility score for ${componentName}: ${results.overallScore}%`);
        }
    }
};