import React from 'react';
import { View, Text, Pressable } from 'react-native';
import { Card } from '../ui';

export interface Task {
  id: string;
  title: string;
  description: string;
  dueDate: Date;
  type: 'watering' | 'fertilizing' | 'monitoring' | 'harvesting' | 'planting';
  completed: boolean;
  priority: 'low' | 'medium' | 'high';
  pointsReward: number;
}

export interface TaskCardProps {
  task: Task;
  onToggleComplete: (taskId: string) => void;
  onPress?: () => void;
  showPoints?: boolean;
}

export const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onToggleComplete,
  onPress,
  showPoints = true,
}) => {
  const getTaskIcon = (type: string) => {
    const icons: Record<string, string> = {
      watering: '💧',
      fertilizing: '🌱',
      monitoring: '👁️',
      harvesting: '🌾',
      planting: '🌰',
    };
    return icons[type] || '📋';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-red-600 bg-red-50';
      case 'medium':
        return 'text-orange-600 bg-orange-50';
      case 'low':
        return 'text-green-600 bg-green-50';
      default:
        return 'text-earth-600 bg-earth-50';
    }
  };

  const formatDueDate = (date: Date) => {
    const today = new Date();
    const diffTime = date.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays === -1) return 'Yesterday';
    if (diffDays < 0) return `${Math.abs(diffDays)} days overdue`;
    return `In ${diffDays} days`;
  };

  return (
    <Card
      onPress={onPress}
      variant="outlined"
      className={task.completed ? 'opacity-60' : ''}
      accessibilityLabel={`Task: ${task.title}, ${task.completed ? 'completed' : 'pending'}`}
      accessibilityHint="Tap to view task details">
      <View className="flex-row items-start justify-between">
        <View className="mr-3 flex-1">
          <View className="mb-2 flex-row items-center">
            <Text className="mr-2 text-2xl">{getTaskIcon(task.type)}</Text>
            <View className="flex-1">
              <Text
                className={`text-base font-semibold ${
                  task.completed ? 'text-earth-500 line-through' : 'text-earth-900'
                }`}>
                {task.title}
              </Text>
              <Text className="mt-1 text-sm text-earth-600">{task.description}</Text>
            </View>
          </View>

          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center gap-2">
              <Text
                className={`rounded-full px-2 py-1 text-xs font-medium ${getPriorityColor(task.priority)}`}>
                {task.priority.toUpperCase()}
              </Text>
              <Text className="text-xs text-earth-500">{formatDueDate(task.dueDate)}</Text>
            </View>

            {showPoints && (
              <Text className="text-xs font-medium text-primary-600">+{task.pointsReward} pts</Text>
            )}
          </View>
        </View>

        <Pressable
          onPress={() => onToggleComplete(task.id)}
          className={`
            h-6 w-6 items-center justify-center rounded-full border-2
            ${task.completed ? 'border-primary-600 bg-primary-600' : 'border-earth-300 bg-white'}
          `}
          accessibilityRole="checkbox"
          accessibilityState={{ checked: task.completed }}
          accessibilityLabel={`Mark task ${task.completed ? 'incomplete' : 'complete'}`}>
          {task.completed && <Text className="text-xs font-bold text-white">✓</Text>}
        </Pressable>
      </View>
    </Card>
  );
};
