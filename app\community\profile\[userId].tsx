import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    Alert,
    Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../../../src/design-system';
import { CommunityPost } from '../../../src/components/community/CommunityPost';
import { SocialService, UserProfile } from '../../../src/services/supabase/social';
import { useCommunityStore } from '../../../src/stores/community';
import { useVoiceStore } from '../../../src/stores/voice';

export default function UserProfileScreen() {
    const { userId } = useLocalSearchParams<{ userId: string }>();
    const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
    const [userPosts, setUserPosts] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [followLoading, setFollowLoading] = useState(false);

    const { likePost, sharePost } = useCommunityStore();
    const { speak, isVoiceEnabled } = useVoiceStore();

    useEffect(() => {
        if (userId) {
            loadUserProfile();
        }
    }, [userId]);

    const loadUserProfile = async () => {
        try {
            setLoading(true);

            // Mock user profile data
            const mockProfile: UserProfile = {
                id: userId,
                name: 'Ahmed Hassan',
                location: 'Cairo, Egypt',
                experienceLevel: 'intermediate',
                followersCount: 245,
                followingCount: 89,
                isFollowing: false,
            };

            // Mock user posts
            const mockPosts = [
                {
                    id: 'post1',
                    title: 'Great harvest this season! 🌽',
                    content: 'Just finished harvesting my corn field. The yield was amazing this year thanks to the new irrigation system.',
                    author: {
                        id: userId,
                        name: 'Ahmed Hassan',
                        location: 'Cairo, Egypt',
                        experienceLevel: 'intermediate',
                    },
                    images: ['https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=400'],
                    likes: 24,
                    comments: 8,
                    shares: 3,
                    isLiked: false,
                    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
                    updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
                },
                {
                    id: 'post2',
                    title: 'Organic fertilizer tips',
                    content: 'Here are some tips for making your own organic fertilizer from kitchen scraps...',
                    author: {
                        id: userId,
                        name: 'Ahmed Hassan',
                        location: 'Cairo, Egypt',
                        experienceLevel: 'intermediate',
                    },
                    likes: 18,
                    comments: 12,
                    shares: 5,
                    isLiked: true,
                    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
                    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
                },
            ];

            setUserProfile(mockProfile);
            setUserPosts(mockPosts);
        } catch (error) {
            Alert.alert('Error', 'Failed to load user profile');
        } finally {
            setLoading(false);
        }
    };

    const handleFollow = async () => {
        if (!userProfile) return;

        setFollowLoading(true);

        try {
            // In a real implementation, this would call SocialService.toggleFollow
            await new Promise(resolve => setTimeout(resolve, 1000));

            setUserProfile(prev => prev ? {
                ...prev,
                isFollowing: !prev.isFollowing,
                followersCount: prev.isFollowing
                    ? prev.followersCount - 1
                    : prev.followersCount + 1,
            } : null);

            if (isVoiceEnabled) {
                speak(userProfile.isFollowing ? 'Unfollowed user' : 'Following user');
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to update follow status');
        } finally {
            setFollowLoading(false);
        }
    };

    const handleMessage = () => {
        if (isVoiceEnabled) {
            speak('Opening message conversation');
        }
        router.push(`/community/chat/${userId}`);
    };

    const handlePostPress = (postId: string) => {
        router.push(`/community/post/${postId}`);
    };

    if (loading) {
        return (
            <SafeAreaView style={{ flex: 1, backgroundColor: 'white' }}>
                <View style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                }}>
                    <Text style={{ color: colors.earth[500] }}>Loading profile...</Text>
                </View>
            </SafeAreaView>
        );
    }

    if (!userProfile) {
        return (
            <SafeAreaView style={{ flex: 1, backgroundColor: 'white' }}>
                <View style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                    padding: 20,
                }}>
                    <Ionicons name="person-outline" size={64} color={colors.earth[300]} />
                    <Text style={{
                        fontSize: 18,
                        fontWeight: '600',
                        color: colors.earth[600],
                        marginTop: 16,
                        textAlign: 'center',
                    }}>
                        User not found
                    </Text>
                    <TouchableOpacity
                        onPress={() => router.back()}
                        style={{
                            backgroundColor: colors.primary[500],
                            paddingHorizontal: 24,
                            paddingVertical: 12,
                            borderRadius: 24,
                            marginTop: 20,
                        }}
                    >
                        <Text style={{ color: 'white', fontWeight: '600' }}>
                            Go Back
                        </Text>
                    </TouchableOpacity>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary[50] }}>
            {/* Header */}
            <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingHorizontal: 16,
                paddingVertical: 12,
                backgroundColor: 'white',
                borderBottomWidth: 1,
                borderBottomColor: colors.primary[100],
            }}>
                <TouchableOpacity
                    onPress={() => router.back()}
                    accessibilityLabel="Go back"
                >
                    <Ionicons name="arrow-back" size={24} color={colors.primary[900]} />
                </TouchableOpacity>

                <Text style={{
                    fontSize: 18,
                    fontWeight: '600',
                    color: colors.primary[900],
                }}>
                    Profile
                </Text>

                <TouchableOpacity
                    onPress={() => {
                        Alert.alert('Coming Soon', 'More options will be available soon');
                    }}
                    accessibilityLabel="More options"
                >
                    <Ionicons name="ellipsis-horizontal" size={24} color={colors.earth[500]} />
                </TouchableOpacity>
            </View>

            <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
                {/* Profile Header */}
                <View style={{
                    backgroundColor: 'white',
                    padding: 20,
                    alignItems: 'center',
                }}>
                    {/* Avatar */}
                    <View style={{
                        width: 100,
                        height: 100,
                        borderRadius: 50,
                        backgroundColor: colors.primary[100],
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginBottom: 16,
                    }}>
                        <Ionicons name="person" size={48} color={colors.primary[500]} />
                    </View>

                    {/* Name and Info */}
                    <Text style={{
                        fontSize: 24,
                        fontWeight: 'bold',
                        color: colors.primary[900],
                        marginBottom: 4,
                    }}>
                        {userProfile.name}
                    </Text>

                    <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginBottom: 8,
                        gap: 8,
                    }}>
                        {userProfile.experienceLevel && (
                            <>
                                <Text style={{
                                    fontSize: 14,
                                    color: colors.earth[500],
                                    textTransform: 'capitalize',
                                }}>
                                    {userProfile.experienceLevel} Farmer
                                </Text>
                                <Text style={{ color: colors.earth[300] }}>•</Text>
                            </>
                        )}
                        {userProfile.location && (
                            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4 }}>
                                <Ionicons name="location" size={14} color={colors.earth[500]} />
                                <Text style={{
                                    fontSize: 14,
                                    color: colors.earth[500],
                                }}>
                                    {userProfile.location}
                                </Text>
                            </View>
                        )}
                    </View>

                    {/* Stats */}
                    <View style={{
                        flexDirection: 'row',
                        justifyContent: 'center',
                        gap: 32,
                        marginBottom: 20,
                    }}>
                        <View style={{ alignItems: 'center' }}>
                            <Text style={{
                                fontSize: 20,
                                fontWeight: 'bold',
                                color: colors.primary[900],
                            }}>
                                {userPosts.length}
                            </Text>
                            <Text style={{
                                fontSize: 14,
                                color: colors.earth[500],
                            }}>
                                Posts
                            </Text>
                        </View>

                        <TouchableOpacity
                            style={{ alignItems: 'center' }}
                            onPress={() => {
                                Alert.alert('Coming Soon', 'Followers list will be available soon');
                            }}
                        >
                            <Text style={{
                                fontSize: 20,
                                fontWeight: 'bold',
                                color: colors.primary[900],
                            }}>
                                {userProfile.followersCount}
                            </Text>
                            <Text style={{
                                fontSize: 14,
                                color: colors.earth[500],
                            }}>
                                Followers
                            </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={{ alignItems: 'center' }}
                            onPress={() => {
                                Alert.alert('Coming Soon', 'Following list will be available soon');
                            }}
                        >
                            <Text style={{
                                fontSize: 20,
                                fontWeight: 'bold',
                                color: colors.primary[900],
                            }}>
                                {userProfile.followingCount}
                            </Text>
                            <Text style={{
                                fontSize: 14,
                                color: colors.earth[500],
                            }}>
                                Following
                            </Text>
                        </TouchableOpacity>
                    </View>

                    {/* Action Buttons */}
                    <View style={{
                        flexDirection: 'row',
                        gap: 12,
                        width: '100%',
                    }}>
                        <TouchableOpacity
                            onPress={handleFollow}
                            disabled={followLoading}
                            style={{
                                flex: 1,
                                backgroundColor: userProfile.isFollowing
                                    ? colors.earth[200]
                                    : colors.primary[500],
                                paddingVertical: 12,
                                borderRadius: 24,
                                alignItems: 'center',
                            }}
                            accessibilityLabel={userProfile.isFollowing ? 'Unfollow user' : 'Follow user'}
                        >
                            <Text style={{
                                color: userProfile.isFollowing ? colors.earth[700] : 'white',
                                fontWeight: '600',
                                fontSize: 16,
                            }}>
                                {followLoading
                                    ? 'Loading...'
                                    : userProfile.isFollowing
                                        ? 'Following'
                                        : 'Follow'
                                }
                            </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            onPress={handleMessage}
                            style={{
                                flex: 1,
                                backgroundColor: 'white',
                                borderWidth: 1,
                                borderColor: colors.primary[500],
                                paddingVertical: 12,
                                borderRadius: 24,
                                alignItems: 'center',
                            }}
                            accessibilityLabel="Send message"
                        >
                            <Text style={{
                                color: colors.primary[500],
                                fontWeight: '600',
                                fontSize: 16,
                            }}>
                                Message
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>

                {/* Posts Section */}
                <View style={{ marginTop: 8 }}>
                    <View style={{
                        backgroundColor: 'white',
                        paddingHorizontal: 16,
                        paddingVertical: 12,
                        borderBottomWidth: 1,
                        borderBottomColor: colors.primary[100],
                    }}>
                        <Text style={{
                            fontSize: 18,
                            fontWeight: '600',
                            color: colors.primary[900],
                        }}>
                            Posts ({userPosts.length})
                        </Text>
                    </View>

                    {userPosts.length === 0 ? (
                        <View style={{
                            backgroundColor: 'white',
                            padding: 40,
                            alignItems: 'center',
                        }}>
                            <Ionicons name="document-outline" size={48} color={colors.earth[300]} />
                            <Text style={{
                                fontSize: 16,
                                color: colors.earth[500],
                                marginTop: 12,
                                textAlign: 'center',
                            }}>
                                No posts yet
                            </Text>
                        </View>
                    ) : (
                        <View style={{ paddingBottom: 20 }}>
                            {userPosts.map((post) => (
                                <CommunityPost
                                    key={post.id}
                                    post={post}
                                    onPress={() => handlePostPress(post.id)}
                                    onLike={() => likePost(post.id)}
                                    onShare={() => sharePost(post.id)}
                                    onComment={() => handlePostPress(post.id)}
                                />
                            ))}
                        </View>
                    )}
                </View>
            </ScrollView>
        </SafeAreaView>
    );
}