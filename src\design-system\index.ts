/**
 * Design System for AI Farming Assistant
 * Comprehensive design tokens and utilities for agricultural-friendly UI
 */

export { colors, type ColorPalette } from './colors';
export { typography, textStyles, type TextStyle } from './typography';
export { spacing, touchTargets, spacingPatterns } from './spacing';
export { shadows, elevations } from './shadows';

// Design system configuration
export const designSystem = {
  // Accessibility settings
  accessibility: {
    minimumTouchTarget: 44,
    recommendedTouchTarget: 56,
    largeTextThreshold: 18,
    highContrastMode: false,
  },

  // Agricultural-specific settings
  agricultural: {
    outdoorVisibility: true,
    workGloveOptimized: true,
    voiceFirst: true,
    offlineCapable: true,
  },

  // Animation settings
  animations: {
    duration: {
      fast: 150,
      normal: 300,
      slow: 500,
    },
    easing: {
      ease: 'ease',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      easeInOut: 'ease-in-out',
    },
  },

  // Breakpoints for responsive design
  breakpoints: {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
  },
} as const;

export type DesignSystem = typeof designSystem;
