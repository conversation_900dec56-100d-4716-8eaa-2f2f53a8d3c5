#!/usr/bin/env node

/**
 * Database Setup Verification Script
 * This script verifies that all database tables, indexes, and policies are correctly set up
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.EXPO_PUBLIC_SUPABASE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

// Expected database structure
const expectedTables = [
  'users',
  'user_profiles',
  'crop_plans',
  'tasks',
  'chat_sessions',
  'chat_messages',
  'community_posts',
  'post_comments',
  'products',
  'orders',
  'order_items',
  'points_transactions',
  'points_balances',
  'achievements',
  'user_achievements',
  'subscription_plans',
  'user_subscriptions',
  'subscription_usage',
  'notifications',
  'push_tokens',
  'image_analyses',
  'performance_logs',
  'error_logs',
];

const expectedIndexes = [
  'idx_users_email',
  'idx_user_profiles_location',
  'idx_crop_plans_user_id',
  'idx_tasks_crop_plan_id',
  'idx_community_posts_location',
  'idx_points_transactions_user_id',
  'idx_notifications_user_id',
];

const expectedPolicies = [
  'Users can view their own profile',
  'Users can access their own profile',
  'Users can access their own crop plans',
  'Users can access tasks for their crop plans',
  'Community posts are publicly readable',
];

async function checkTableExists(tableName) {
  try {
    // Test by trying to query the table
    const { data, error } = await supabase.from(tableName).select('*').limit(1);

    if (error && error.message.includes('does not exist')) {
      return false;
    }

    return true;
  } catch (error) {
    console.error(`❌ Exception checking table ${tableName}:`, error.message);
    return false;
  }
}

async function checkIndexExists(indexName) {
  try {
    const { data, error } = await supabase.rpc('exec_sql', {
      sql_query: `
        SELECT indexname 
        FROM pg_indexes 
        WHERE schemaname = 'public' 
        AND indexname = '${indexName}';
      `,
    });

    if (error) {
      console.error(`❌ Error checking index ${indexName}:`, error.message);
      return false;
    }

    return data && data.length > 0;
  } catch (error) {
    console.error(`❌ Exception checking index ${indexName}:`, error.message);
    return false;
  }
}

async function checkRLSEnabled(tableName) {
  try {
    const { data, error } = await supabase.rpc('exec_sql', {
      sql_query: `
        SELECT relrowsecurity 
        FROM pg_class 
        WHERE relname = '${tableName}' 
        AND relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');
      `,
    });

    if (error) {
      console.error(`❌ Error checking RLS for ${tableName}:`, error.message);
      return false;
    }

    return data && data.length > 0 && data[0].relrowsecurity;
  } catch (error) {
    console.error(`❌ Exception checking RLS for ${tableName}:`, error.message);
    return false;
  }
}

async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...');

  try {
    // Test with a simple table query
    const { data, error } = await supabase.from('users').select('*').limit(1);

    if (error && !error.message.includes('does not exist')) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }

    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection exception:', error.message);
    return false;
  }
}

async function verifyTables() {
  console.log('\n📋 Verifying database tables...');

  let passCount = 0;
  let failCount = 0;

  for (const table of expectedTables) {
    const exists = await checkTableExists(table);
    if (exists) {
      console.log(`✅ Table exists: ${table}`);
      passCount++;
    } else {
      console.log(`❌ Table missing: ${table}`);
      failCount++;
    }
  }

  console.log(`\n📊 Tables Summary: ${passCount} passed, ${failCount} failed`);
  return failCount === 0;
}

async function verifyIndexes() {
  console.log('\n🔍 Verifying database indexes...');

  let passCount = 0;
  let failCount = 0;

  for (const index of expectedIndexes) {
    const exists = await checkIndexExists(index);
    if (exists) {
      console.log(`✅ Index exists: ${index}`);
      passCount++;
    } else {
      console.log(`❌ Index missing: ${index}`);
      failCount++;
    }
  }

  console.log(`\n📊 Indexes Summary: ${passCount} passed, ${failCount} failed`);
  return failCount === 0;
}

async function verifyRLS() {
  console.log('\n🔒 Verifying Row Level Security...');

  let passCount = 0;
  let failCount = 0;

  const criticalTables = ['users', 'user_profiles', 'crop_plans', 'tasks', 'notifications'];

  for (const table of criticalTables) {
    const enabled = await checkRLSEnabled(table);
    if (enabled) {
      console.log(`✅ RLS enabled: ${table}`);
      passCount++;
    } else {
      console.log(`❌ RLS not enabled: ${table}`);
      failCount++;
    }
  }

  console.log(`\n📊 RLS Summary: ${passCount} passed, ${failCount} failed`);
  return failCount === 0;
}

async function testBasicOperations() {
  console.log('\n🧪 Testing basic database operations...');

  try {
    // Test creating a test user profile (this will fail due to RLS, which is expected)
    const { error: insertError } = await supabase.from('user_profiles').insert({
      id: '00000000-0000-0000-0000-000000000000',
      experience_level: 'beginner',
    });

    // We expect this to fail due to RLS, which means RLS is working
    if (insertError && insertError.message.includes('policy')) {
      console.log('✅ RLS is properly blocking unauthorized inserts');
    } else {
      console.log('⚠️  RLS might not be working correctly');
    }

    // Test reading from a public table
    const { data: achievements, error: readError } = await supabase
      .from('achievements')
      .select('*')
      .limit(1);

    if (!readError) {
      console.log('✅ Public table read operations working');
    } else {
      console.log('❌ Public table read failed:', readError.message);
      return false;
    }

    return true;
  } catch (error) {
    console.error('❌ Basic operations test failed:', error.message);
    return false;
  }
}

async function runProductionVerification() {
  console.log('\n🔧 Running production verification function...');

  try {
    const { data, error } = await supabase.rpc('verify_production_setup');

    if (error) {
      console.error('❌ Production verification failed:', error.message);
      return false;
    }

    if (data && data.length > 0) {
      console.log('\n📋 Production Setup Verification Results:');
      data.forEach((result) => {
        const status = result.status === 'PASS' ? '✅' : '❌';
        console.log(`${status} ${result.check_name}: ${result.details}`);
      });

      const allPassed = data.every((result) => result.status === 'PASS');
      return allPassed;
    }

    return false;
  } catch (error) {
    console.error('❌ Production verification exception:', error.message);
    return false;
  }
}

async function generateHealthReport() {
  console.log('\n📊 Generating database health report...');

  try {
    // Get table sizes
    const { data: tableSizes, error: sizeError } = await supabase.rpc('exec_sql', {
      sql_query: `
        SELECT 
          schemaname,
          tablename,
          pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
        FROM pg_tables 
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
        LIMIT 10;
      `,
    });

    if (!sizeError && tableSizes) {
      console.log('\n📈 Top 10 Tables by Size:');
      tableSizes.forEach((table) => {
        console.log(`   ${table.tablename}: ${table.size}`);
      });
    }

    // Get connection info
    const { data: connections, error: connError } = await supabase.rpc('exec_sql', {
      sql_query: `
        SELECT 
          count(*) as total_connections,
          count(*) FILTER (WHERE state = 'active') as active_connections
        FROM pg_stat_activity;
      `,
    });

    if (!connError && connections && connections.length > 0) {
      console.log('\n🔗 Connection Statistics:');
      console.log(`   Total connections: ${connections[0].total_connections}`);
      console.log(`   Active connections: ${connections[0].active_connections}`);
    }

    return true;
  } catch (error) {
    console.error('❌ Health report generation failed:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting database verification...\n');

  // Test connection first
  const connectionOk = await testDatabaseConnection();
  if (!connectionOk) {
    console.error('❌ Cannot proceed without database connection');
    process.exit(1);
  }

  // Run all verification tests
  const tablesOk = await verifyTables();
  const indexesOk = await verifyIndexes();
  const rlsOk = await verifyRLS();
  const operationsOk = await testBasicOperations();
  const productionOk = await runProductionVerification();

  // Generate health report
  await generateHealthReport();

  // Final summary
  console.log('\n🎯 Final Verification Summary:');
  console.log(`   Database Connection: ${connectionOk ? '✅' : '❌'}`);
  console.log(`   Tables: ${tablesOk ? '✅' : '❌'}`);
  console.log(`   Indexes: ${indexesOk ? '✅' : '❌'}`);
  console.log(`   Row Level Security: ${rlsOk ? '✅' : '❌'}`);
  console.log(`   Basic Operations: ${operationsOk ? '✅' : '❌'}`);
  console.log(`   Production Setup: ${productionOk ? '✅' : '❌'}`);

  const allPassed = tablesOk && indexesOk && rlsOk && operationsOk && productionOk;

  if (allPassed) {
    console.log('\n🎉 All verification tests passed!');
    console.log('✅ Database is ready for production use.');
  } else {
    console.log('\n⚠️  Some verification tests failed.');
    console.log('❌ Please review the issues above before proceeding to production.');
  }

  process.exit(allPassed ? 0 : 1);
}

// Run the verification
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Verification failed with exception:', error);
    process.exit(1);
  });
}

module.exports = {
  testDatabaseConnection,
  verifyTables,
  verifyIndexes,
  verifyRLS,
  testBasicOperations,
};
