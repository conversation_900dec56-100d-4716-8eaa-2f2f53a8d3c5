#!/usr/bin/env node

/**
 * Simple Migration Script
 * This script applies the core database schema step by step
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.EXPO_PUBLIC_SUPABASE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

console.log('🔧 Initializing Supabase client...');
console.log(`📍 URL: ${SUPABASE_URL}`);

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function executeSQL(sql, description) {
  console.log(`🔄 ${description}...`);

  try {
    const { data, error } = await supabase.rpc('exec', { sql });

    if (error) {
      console.error(`❌ Error: ${error.message}`);
      return false;
    }

    console.log(`✅ Success: ${description}`);
    return true;
  } catch (error) {
    console.error(`❌ Exception: ${error.message}`);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting simple database migration...\n');

  // Step 1: Enable extensions
  console.log('📦 Step 1: Enabling extensions...');
  await executeSQL(
    `
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    CREATE EXTENSION IF NOT EXISTS "postgis";
  `,
    'Enable extensions'
  );

  // Step 2: Create users table
  console.log('\n👥 Step 2: Creating users table...');
  await executeSQL(
    `
    CREATE TABLE IF NOT EXISTS users (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      email VARCHAR UNIQUE NOT NULL,
      phone VARCHAR UNIQUE NOT NULL,
      first_name VARCHAR NOT NULL,
      last_name VARCHAR NOT NULL,
      created_at TIMESTAMP DEFAULT NOW(),
      updated_at TIMESTAMP DEFAULT NOW()
    );
  `,
    'Create users table'
  );

  // Step 3: Create user_profiles table
  console.log('\n📋 Step 3: Creating user_profiles table...');
  await executeSQL(
    `
    CREATE TABLE IF NOT EXISTS user_profiles (
      id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
      farm_location GEOGRAPHY(POINT, 4326),
      crop_types TEXT[],
      experience_level VARCHAR CHECK (experience_level IN ('beginner', 'intermediate', 'expert')),
      preferred_language VARCHAR DEFAULT 'ar',
      voice_enabled BOOLEAN DEFAULT false,
      points INTEGER DEFAULT 0,
      subscription_tier VARCHAR DEFAULT 'free',
      subscription_expires_at TIMESTAMP
    );
  `,
    'Create user_profiles table'
  );

  // Step 4: Create crop_plans table
  console.log('\n🌱 Step 4: Creating crop_plans table...');
  await executeSQL(
    `
    CREATE TABLE IF NOT EXISTS crop_plans (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID REFERENCES users(id) ON DELETE CASCADE,
      crop_type VARCHAR NOT NULL,
      planting_date DATE,
      harvest_date DATE,
      location GEOGRAPHY(POINT, 4326),
      status VARCHAR DEFAULT 'active',
      created_at TIMESTAMP DEFAULT NOW()
    );
  `,
    'Create crop_plans table'
  );

  // Step 5: Create tasks table
  console.log('\n✅ Step 5: Creating tasks table...');
  await executeSQL(
    `
    CREATE TABLE IF NOT EXISTS tasks (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      crop_plan_id UUID REFERENCES crop_plans(id) ON DELETE CASCADE,
      title VARCHAR NOT NULL,
      description TEXT,
      due_date TIMESTAMP,
      task_type VARCHAR,
      completed BOOLEAN DEFAULT false,
      points_reward INTEGER DEFAULT 0,
      created_at TIMESTAMP DEFAULT NOW()
    );
  `,
    'Create tasks table'
  );

  // Step 6: Create community_posts table
  console.log('\n🏘️ Step 6: Creating community_posts table...');
  await executeSQL(
    `
    CREATE TABLE IF NOT EXISTS community_posts (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID REFERENCES users(id) ON DELETE CASCADE,
      title VARCHAR NOT NULL,
      content TEXT,
      image_urls TEXT[],
      location GEOGRAPHY(POINT, 4326),
      likes_count INTEGER DEFAULT 0,
      comments_count INTEGER DEFAULT 0,
      created_at TIMESTAMP DEFAULT NOW()
    );
  `,
    'Create community_posts table'
  );

  // Step 7: Create basic indexes
  console.log('\n🔍 Step 7: Creating indexes...');
  await executeSQL(
    `
    CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(id);
    CREATE INDEX IF NOT EXISTS idx_crop_plans_user_id ON crop_plans(user_id);
    CREATE INDEX IF NOT EXISTS idx_tasks_crop_plan_id ON tasks(crop_plan_id);
    CREATE INDEX IF NOT EXISTS idx_community_posts_user_id ON community_posts(user_id);
  `,
    'Create basic indexes'
  );

  // Step 8: Enable RLS
  console.log('\n🔒 Step 8: Enabling Row Level Security...');
  await executeSQL(
    `
    ALTER TABLE users ENABLE ROW LEVEL SECURITY;
    ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
    ALTER TABLE crop_plans ENABLE ROW LEVEL SECURITY;
    ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
    ALTER TABLE community_posts ENABLE ROW LEVEL SECURITY;
  `,
    'Enable RLS'
  );

  // Step 9: Create basic RLS policies
  console.log('\n🛡️ Step 9: Creating RLS policies...');
  await executeSQL(
    `
    CREATE POLICY IF NOT EXISTS "Users can view their own profile" ON users
      FOR SELECT USING (auth.uid() = id);
    
    CREATE POLICY IF NOT EXISTS "Users can access their own profile" ON user_profiles
      FOR ALL USING (auth.uid() = id);
    
    CREATE POLICY IF NOT EXISTS "Users can access their own crop plans" ON crop_plans
      FOR ALL USING (auth.uid() = user_id);
    
    CREATE POLICY IF NOT EXISTS "Users can access tasks for their crop plans" ON tasks
      FOR ALL USING (
        EXISTS (
          SELECT 1 FROM crop_plans 
          WHERE crop_plans.id = tasks.crop_plan_id 
          AND crop_plans.user_id = auth.uid()
        )
      );
    
    CREATE POLICY IF NOT EXISTS "Community posts are publicly readable" ON community_posts
      FOR SELECT USING (true);
  `,
    'Create RLS policies'
  );

  // Step 10: Verify tables
  console.log('\n✅ Step 10: Verifying tables...');

  const tables = ['users', 'user_profiles', 'crop_plans', 'tasks', 'community_posts'];
  let allExist = true;

  for (const table of tables) {
    try {
      const { data, error } = await supabase.from(table).select('*').limit(1);

      if (error && error.message.includes('does not exist')) {
        console.log(`❌ Table missing: ${table}`);
        allExist = false;
      } else {
        console.log(`✅ Table verified: ${table}`);
      }
    } catch (error) {
      console.log(`❌ Error verifying table ${table}:`, error.message);
      allExist = false;
    }
  }

  if (allExist) {
    console.log('\n🎉 Core database migration completed successfully!');
    console.log('✅ All core tables are ready for production use.');
  } else {
    console.log('\n⚠️  Some tables are missing. Please check the logs above.');
  }

  process.exit(allExist ? 0 : 1);
}

// Run the migration
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  });
}
