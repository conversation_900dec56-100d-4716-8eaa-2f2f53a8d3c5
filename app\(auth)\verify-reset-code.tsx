import React, { useEffect, useState, useRef } from 'react';
import { View, Text, ScrollView, Pressable, Animated, Alert, TextInput } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Button } from '../../src/components/ui/Button';
import { AppProvider, useApp } from '../../src/contexts/AppContext';

const VerifyResetCodeContent: React.FC = () => {
    const { t, isRTL, isVoiceEnabled, speak } = useApp();
    const params = useLocalSearchParams<{ method: string; contact: string }>();
    const [fadeAnim] = useState(new Animated.Value(0));
    const [slideAnim] = useState(new Animated.Value(50));

    // Form state
    const [code, setCode] = useState(['', '', '', '', '', '']);
    const [isLoading, setIsLoading] = useState(false);
    const [isResending, setIsResending] = useState(false);
    const [countdown, setCountdown] = useState(60);
    const [canResend, setCanResend] = useState(false);

    // Refs for code inputs
    const inputRefs = useRef<(TextInput | null)[]>([]);

    // Error state
    const [error, setError] = useState<string>('');

    useEffect(() => {
        // Animate entrance
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 800,
                useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 800,
                useNativeDriver: true,
            }),
        ]).start();

        // Speak welcome message if voice is enabled
        if (isVoiceEnabled) {
            const contactInfo = params.method === 'email'
                ? t('verifyResetCode.sentToEmail', { email: params.contact })
                : t('verifyResetCode.sentToPhone', { phone: params.contact });
            speak(t('verifyResetCode.title') + '. ' + contactInfo);
        }

        // Start countdown timer
        const timer = setInterval(() => {
            setCountdown((prev) => {
                if (prev <= 1) {
                    setCanResend(true);
                    clearInterval(timer);
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, [isVoiceEnabled, params]);

    const handleCodeChange = (value: string, index: number) => {
        if (value.length > 1) {
            // Handle paste
            const pastedCode = value.slice(0, 6).split('');
            const newCode = [...code];
            pastedCode.forEach((digit, i) => {
                if (i < 6 && /^\d$/.test(digit)) {
                    newCode[i] = digit;
                }
            });
            setCode(newCode);
            setError('');

            // Focus last filled input or next empty
            const lastFilledIndex = newCode.findIndex(digit => digit === '');
            const focusIndex = lastFilledIndex === -1 ? 5 : Math.max(0, lastFilledIndex - 1);
            inputRefs.current[focusIndex]?.focus();
            return;
        }

        // Handle single digit input
        if (/^\d$/.test(value) || value === '') {
            const newCode = [...code];
            newCode[index] = value;
            setCode(newCode);
            setError('');

            // Auto-focus next input
            if (value && index < 5) {
                inputRefs.current[index + 1]?.focus();
            }
        }
    };

    const handleKeyPress = (key: string, index: number) => {
        if (key === 'Backspace' && !code[index] && index > 0) {
            inputRefs.current[index - 1]?.focus();
        }
    };

    const handleVerifyCode = async () => {
        const fullCode = code.join('');

        if (fullCode.length !== 6) {
            const errorMsg = t('verifyResetCode.errors.incompleteCode');
            setError(errorMsg);
            if (isVoiceEnabled) {
                speak(errorMsg);
            }
            return;
        }

        setIsLoading(true);

        try {
            // TODO: Implement actual code verification
            await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call

            if (isVoiceEnabled) {
                speak(t('verifyResetCode.codeVerified'));
            }

            // Navigate to new password screen
            router.push({
                pathname: '/(auth)/new-password',
                params: {
                    method: params.method,
                    contact: params.contact,
                    resetCode: fullCode
                }
            });
        } catch (error) {
            const errorMsg = t('verifyResetCode.errors.invalidCode');
            setError(errorMsg);
            if (isVoiceEnabled) {
                speak(errorMsg);
            }
            // Clear the code inputs
            setCode(['', '', '', '', '', '']);
            inputRefs.current[0]?.focus();
        } finally {
            setIsLoading(false);
        }
    };

    const handleResendCode = async () => {
        if (!canResend) return;

        setIsResending(true);
        setCanResend(false);
        setCountdown(60);

        try {
            // TODO: Implement actual resend logic
            await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call

            if (isVoiceEnabled) {
                speak(t('verifyResetCode.codeResent'));
            }

            // Restart countdown
            const timer = setInterval(() => {
                setCountdown((prev) => {
                    if (prev <= 1) {
                        setCanResend(true);
                        clearInterval(timer);
                        return 0;
                    }
                    return prev - 1;
                });
            }, 1000);

        } catch (error) {
            if (isVoiceEnabled) {
                speak(t('verifyResetCode.errors.resendFailed'));
            }
            Alert.alert(t('verifyResetCode.errors.title'), t('verifyResetCode.errors.resendFailed'));
            setCanResend(true);
        } finally {
            setIsResending(false);
        }
    };

    const handleBack = () => {
        if (isVoiceEnabled) {
            speak(t('common.back'));
        }
        router.back();
    };

    const formatCountdown = (seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    return (
        <SafeAreaView className={`flex-1 bg-gradient-to-b from-primary-50 to-white ${isRTL ? 'rtl' : 'ltr'}`}>
            <StatusBar style="dark" />

            <ScrollView
                className="flex-1"
                contentContainerStyle={{ flexGrow: 1 }}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
            >
                <Animated.View
                    className="flex-1 px-6 py-8"
                    style={{
                        opacity: fadeAnim,
                        transform: [{ translateY: slideAnim }],
                    }}
                >
                    {/* Back Button */}
                    <Pressable
                        onPress={handleBack}
                        className={`mb-6 flex-row items-center ${isRTL ? 'flex-row-reverse' : ''}`}
                        accessibilityRole="button"
                        accessibilityLabel={t('common.back')}
                    >
                        <Text className={`text-2xl text-earth-600 ${isRTL ? 'ml-2' : 'mr-2'}`}>
                            {isRTL ? '→' : '←'}
                        </Text>
                        <Text className="text-base font-medium text-earth-600">
                            {t('common.back')}
                        </Text>
                    </Pressable>

                    {/* Header */}
                    <View className="items-center mb-8">
                        <View className="mb-6 h-20 w-20 items-center justify-center rounded-full bg-secondary-100">
                            <Text className="text-3xl">📱</Text>
                        </View>

                        <Text className={`mb-2 text-center text-2xl font-bold text-earth-900 ${isRTL ? 'text-right' : 'text-left'}`}>
                            {t('verifyResetCode.title')}
                        </Text>

                        <Text className={`text-center text-base text-earth-600 mb-2 ${isRTL ? 'text-right' : 'text-left'}`}>
                            {params.method === 'email'
                                ? t('verifyResetCode.sentToEmail', { email: params.contact })
                                : t('verifyResetCode.sentToPhone', { phone: params.contact })
                            }
                        </Text>

                        <Text className={`text-center text-sm text-earth-500 ${isRTL ? 'text-right' : 'text-left'}`}>
                            {t('verifyResetCode.description')}
                        </Text>
                    </View>

                    {/* Code Input */}
                    <View className="mb-6">
                        <Text className={`mb-4 text-center text-lg font-semibold text-earth-800 ${isRTL ? 'text-right' : 'text-left'}`}>
                            {t('verifyResetCode.enterCode')}
                        </Text>

                        <View className={`flex-row justify-center gap-3 mb-4 ${isRTL ? 'flex-row-reverse gap-reverse' : ''}`}>
                            {code.map((digit, index) => (
                                <TextInput
                                    key={index}
                                    ref={(ref) => (inputRefs.current[index] = ref)}
                                    value={digit}
                                    onChangeText={(value) => handleCodeChange(value, index)}
                                    onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
                                    keyboardType="numeric"
                                    maxLength={6} // Allow paste
                                    selectTextOnFocus
                                    className={`h-14 w-12 rounded-xl border-2 text-center text-xl font-bold ${error
                                        ? 'border-red-500 bg-red-50'
                                        : digit
                                            ? 'border-primary-500 bg-primary-50'
                                            : 'border-earth-300 bg-white'
                                        } text-earth-900`}
                                    accessibilityLabel={t('verifyResetCode.codeDigit', { position: index + 1 })}
                                    accessibilityHint={t('verifyResetCode.codeDigitHint')}
                                />
                            ))}
                        </View>

                        {error && (
                            <Text className={`text-center text-sm text-red-500 ${isRTL ? 'text-right' : 'text-left'}`}>
                                {error}
                            </Text>
                        )}
                    </View>

                    {/* Resend Code */}
                    <View className="items-center mb-8">
                        {canResend ? (
                            <Pressable
                                onPress={handleResendCode}
                                disabled={isResending}
                                accessibilityRole="button"
                                accessibilityLabel={t('verifyResetCode.resendCode')}
                                accessibilityState={{ disabled: isResending }}
                            >
                                <Text className={`text-base font-medium ${isResending ? 'text-earth-400' : 'text-primary-600'
                                    }`}>
                                    {isResending ? t('verifyResetCode.resending') : t('verifyResetCode.resendCode')}
                                </Text>
                            </Pressable>
                        ) : (
                            <Text className="text-base text-earth-500">
                                {t('verifyResetCode.resendIn', { time: formatCountdown(countdown) })}
                            </Text>
                        )}
                    </View>

                    {/* Instructions */}
                    <View className="mb-8 rounded-xl bg-blue-50 p-4">
                        <View className={`flex-row items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <Text className={`text-2xl ${isRTL ? 'ml-3' : 'mr-3'}`}>💡</Text>
                            <View className="flex-1">
                                <Text className={`text-sm font-medium text-blue-800 mb-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                                    {t('verifyResetCode.tips.title')}
                                </Text>
                                <Text className={`text-sm text-blue-700 leading-relaxed ${isRTL ? 'text-right' : 'text-left'}`}>
                                    {t('verifyResetCode.tips.description')}
                                </Text>
                            </View>
                        </View>
                    </View>

                    {/* Spacer */}
                    <View className="flex-1" />

                    {/* Verify Button */}
                    <Button
                        title={t('verifyResetCode.verify')}
                        onPress={handleVerifyCode}
                        variant="primary"
                        size="large"
                        fullWidth
                        loading={isLoading}
                        disabled={isLoading || code.join('').length !== 6}
                        accessibilityLabel={t('verifyResetCode.verify')}
                        accessibilityHint={t('verifyResetCode.verifyHint')}
                        voiceFeedbackEnabled={isVoiceEnabled}
                        onVoiceFeedback={speak}
                    />
                </Animated.View>
            </ScrollView>
        </SafeAreaView>
    );
};

export default function VerifyResetCode() {
    return (
        <AppProvider>
            <VerifyResetCodeContent />
        </AppProvider>
    );
}