import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image } from 'react-native';
import { CropPlan } from '../../types/crops';

interface GrowthMilestone {
    id: string;
    title: string;
    titleAr: string;
    description: string;
    descriptionAr: string;
    expectedDate: Date;
    actualDate?: Date;
    completed: boolean;
    stage: 'germination' | 'seedling' | 'vegetative' | 'flowering' | 'fruiting' | 'harvest';
    icon: string;
    photos: string[];
    notes?: string;
}

interface CropGrowthTimelineProps {
    cropPlan: CropPlan;
    milestones: GrowthMilestone[];
    onMilestonePress: (milestone: GrowthMilestone) => void;
    onAddPhoto: (milestoneId: string) => void;
    voiceEnabled?: boolean;
    onVoiceCommand?: (command: string) => void;
}

export default function CropGrowthTimeline({
    cropPlan,
    milestones,
    onMilestonePress,
    onAddPhoto,
    voiceEnabled = false,
    onVoiceCommand,
}: CropGrowthTimelineProps) {
    const [selectedLanguage, setSelectedLanguage] = useState<'en' | 'ar'>('en');

    const getStageColor = (stage: GrowthMilestone['stage']): string => {
        const colors = {
            germination: 'bg-yellow-100 border-yellow-300 text-yellow-700',
            seedling: 'bg-green-100 border-green-300 text-green-700',
            vegetative: 'bg-blue-100 border-blue-300 text-blue-700',
            flowering: 'bg-purple-100 border-purple-300 text-purple-700',
            fruiting: 'bg-orange-100 border-orange-300 text-orange-700',
            harvest: 'bg-red-100 border-red-300 text-red-700',
        };
        return colors[stage] || 'bg-gray-100 border-gray-300 text-gray-700';
    };

    const getProgressPercentage = (): number => {
        const completedMilestones = milestones.filter(m => m.completed).length;
        return Math.round((completedMilestones / milestones.length) * 100);
    };

    const getCurrentStage = (): GrowthMilestone | null => {
        const incompleteMilestones = milestones.filter(m => !m.completed);
        return incompleteMilestones.length > 0 ? incompleteMilestones[0] : null;
    };

    const getDaysFromPlanting = (date: Date): number => {
        const plantingDate = new Date(cropPlan.plantingDate);
        const diffTime = date.getTime() - plantingDate.getTime();
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    };

    const formatDate = (date: Date): string => {
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
        });
    };

    const handleVoiceCommand = () => {
        if (onVoiceCommand) {
            onVoiceCommand('growth_timeline');
        }
    };

    const currentStage = getCurrentStage();
    const progressPercentage = getProgressPercentage();

    return (
        <View className="flex-1 bg-gray-50">
            {/* Header */}
            <View className="bg-white border-b border-gray-200 p-4">
                <View className="flex-row items-center justify-between mb-3">
                    <View className="flex-1">
                        <Text className="text-xl font-bold text-gray-900">
                            {cropPlan.cropName} Growth Timeline
                        </Text>
                        <Text className="text-sm text-gray-600">
                            Planted on {formatDate(new Date(cropPlan.plantingDate))}
                        </Text>
                    </View>

                    <View className="flex-row items-center gap-2">
                        <TouchableOpacity
                            onPress={() => setSelectedLanguage(selectedLanguage === 'en' ? 'ar' : 'en')}
                            className="px-3 py-1 bg-gray-100 rounded-full"
                            accessibilityLabel="Switch language"
                        >
                            <Text className="text-sm font-medium text-gray-700">
                                {selectedLanguage === 'en' ? 'العربية' : 'English'}
                            </Text>
                        </TouchableOpacity>

                        {voiceEnabled && (
                            <TouchableOpacity
                                onPress={handleVoiceCommand}
                                className="p-2 bg-green-500 rounded-full"
                                accessibilityLabel="Voice commands"
                            >
                                <Text className="text-white text-sm">🎤</Text>
                            </TouchableOpacity>
                        )}
                    </View>
                </View>

                {/* Progress Bar */}
                <View className="mb-3">
                    <View className="flex-row justify-between items-center mb-2">
                        <Text className="text-sm font-medium text-gray-700">
                            Overall Progress
                        </Text>
                        <Text className="text-sm font-bold text-green-600">
                            {progressPercentage}%
                        </Text>
                    </View>
                    <View className="bg-gray-200 rounded-full h-3">
                        <View
                            className="bg-green-500 h-3 rounded-full transition-all duration-300"
                            style={{ width: `${progressPercentage}%` }}
                        />
                    </View>
                </View>

                {/* Current Stage */}
                {currentStage && (
                    <View className={`p-3 rounded-lg border ${getStageColor(currentStage.stage)}`}>
                        <Text className="font-semibold mb-1">
                            Current Stage: {selectedLanguage === 'en' ? currentStage.title : currentStage.titleAr}
                        </Text>
                        <Text className="text-sm">
                            Expected: {formatDate(currentStage.expectedDate)}
                            ({getDaysFromPlanting(currentStage.expectedDate)} days from planting)
                        </Text>
                    </View>
                )}
            </View>

            {/* Timeline */}
            <ScrollView className="flex-1 p-4">
                <View className="relative">
                    {/* Timeline Line */}
                    <View className="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-300" />

                    {milestones.map((milestone, index) => {
                        const isLast = index === milestones.length - 1;
                        const daysFromPlanting = getDaysFromPlanting(milestone.expectedDate);
                        const isOverdue = !milestone.completed && new Date() > milestone.expectedDate;

                        return (
                            <TouchableOpacity
                                key={milestone.id}
                                onPress={() => onMilestonePress(milestone)}
                                className={`relative mb-6 ${!isLast ? 'pb-6' : ''}`}
                                accessibilityLabel={`Milestone: ${milestone.title}`}
                                accessibilityHint={`${milestone.completed ? 'Completed' : 'Pending'} milestone`}
                            >
                                {/* Timeline Node */}
                                <View className={`absolute left-6 w-4 h-4 rounded-full border-2 ${milestone.completed
                                    ? 'bg-green-500 border-green-500'
                                    : isOverdue
                                        ? 'bg-red-500 border-red-500'
                                        : 'bg-white border-gray-300'
                                    }`} />

                                {/* Milestone Card */}
                                <View className="ml-16 bg-white rounded-lg border border-gray-200 p-4">
                                    {/* Milestone Header */}
                                    <View className="flex-row items-start justify-between mb-3">
                                        <View className="flex-1">
                                            <View className="flex-row items-center mb-2">
                                                <Text className="text-2xl mr-2">{milestone.icon}</Text>
                                                <View className="flex-1">
                                                    <Text className="text-lg font-semibold text-gray-900">
                                                        {selectedLanguage === 'en' ? milestone.title : milestone.titleAr}
                                                    </Text>
                                                    <View className={`self-start px-2 py-1 rounded-full border ${getStageColor(milestone.stage)}`}>
                                                        <Text className="text-xs font-medium capitalize">
                                                            {milestone.stage}
                                                        </Text>
                                                    </View>
                                                </View>
                                            </View>

                                            <Text className="text-sm text-gray-600 mb-2">
                                                {selectedLanguage === 'en' ? milestone.description : milestone.descriptionAr}
                                            </Text>
                                        </View>

                                        {milestone.completed && (
                                            <View className="bg-green-100 rounded-full p-2">
                                                <Text className="text-green-600 text-lg">✓</Text>
                                            </View>
                                        )}
                                    </View>

                                    {/* Date Information */}
                                    <View className="flex-row justify-between items-center mb-3">
                                        <View>
                                            <Text className="text-xs text-gray-500">Expected Date</Text>
                                            <Text className="text-sm font-medium text-gray-900">
                                                {formatDate(milestone.expectedDate)} (Day {daysFromPlanting})
                                            </Text>
                                        </View>

                                        {milestone.actualDate && (
                                            <View>
                                                <Text className="text-xs text-gray-500">Actual Date</Text>
                                                <Text className="text-sm font-medium text-green-600">
                                                    {formatDate(milestone.actualDate)}
                                                </Text>
                                            </View>
                                        )}

                                        {isOverdue && (
                                            <View className="bg-red-50 px-2 py-1 rounded">
                                                <Text className="text-xs font-medium text-red-600">
                                                    Overdue
                                                </Text>
                                            </View>
                                        )}
                                    </View>

                                    {/* Photos */}
                                    {milestone.photos.length > 0 && (
                                        <View className="mb-3">
                                            <Text className="text-sm font-medium text-gray-700 mb-2">
                                                Progress Photos
                                            </Text>
                                            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                                                <View className="flex-row gap-2">
                                                    {milestone.photos.map((photo, photoIndex) => (
                                                        <Image
                                                            key={photoIndex}
                                                            source={{ uri: photo }}
                                                            className="w-16 h-16 rounded-lg"
                                                            resizeMode="cover"
                                                        />
                                                    ))}

                                                    <TouchableOpacity
                                                        onPress={() => onAddPhoto(milestone.id)}
                                                        className="w-16 h-16 bg-gray-100 rounded-lg items-center justify-center border-2 border-dashed border-gray-300"
                                                        accessibilityLabel="Add photo"
                                                    >
                                                        <Text className="text-2xl text-gray-400">+</Text>
                                                    </TouchableOpacity>
                                                </View>
                                            </ScrollView>
                                        </View>
                                    )}

                                    {/* Notes */}
                                    {milestone.notes && (
                                        <View className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                                            <Text className="text-sm font-medium text-yellow-800 mb-1">
                                                📝 Notes
                                            </Text>
                                            <Text className="text-sm text-yellow-700">
                                                {milestone.notes}
                                            </Text>
                                        </View>
                                    )}

                                    {/* Action Buttons */}
                                    <View className="flex-row justify-end mt-3 gap-2">
                                        {!milestone.completed && (
                                            <TouchableOpacity
                                                onPress={() => onMilestonePress(milestone)}
                                                className="px-3 py-2 bg-green-500 rounded-lg"
                                                accessibilityLabel="Mark as completed"
                                            >
                                                <Text className="text-white text-sm font-medium">
                                                    Mark Complete
                                                </Text>
                                            </TouchableOpacity>
                                        )}

                                        <TouchableOpacity
                                            onPress={() => onAddPhoto(milestone.id)}
                                            className="px-3 py-2 bg-blue-500 rounded-lg"
                                            accessibilityLabel="Add photo"
                                        >
                                            <Text className="text-white text-sm font-medium">
                                                📷 Add Photo
                                            </Text>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </TouchableOpacity>
                        );
                    })}
                </View>

                {/* Timeline Summary */}
                <View className="bg-white rounded-lg border border-gray-200 p-4 mt-4">
                    <Text className="text-lg font-semibold text-gray-900 mb-3">
                        📊 Growth Summary
                    </Text>

                    <View className="gap-2">
                        <View className="flex-row justify-between">
                            <Text className="text-sm text-gray-600">Total Milestones:</Text>
                            <Text className="text-sm font-medium text-gray-900">
                                {milestones.length}
                            </Text>
                        </View>

                        <View className="flex-row justify-between">
                            <Text className="text-sm text-gray-600">Completed:</Text>
                            <Text className="text-sm font-medium text-green-600">
                                {milestones.filter(m => m.completed).length}
                            </Text>
                        </View>

                        <View className="flex-row justify-between">
                            <Text className="text-sm text-gray-600">Days Since Planting:</Text>
                            <Text className="text-sm font-medium text-gray-900">
                                {getDaysFromPlanting(new Date())}
                            </Text>
                        </View>

                        <View className="flex-row justify-between">
                            <Text className="text-sm text-gray-600">Expected Harvest:</Text>
                            <Text className="text-sm font-medium text-orange-600">
                                {formatDate(new Date(cropPlan.harvestDate))}
                            </Text>
                        </View>
                    </View>
                </View>
            </ScrollView>

            {/* Voice Commands Help */}
            {voiceEnabled && (
                <View className="bg-blue-50 border-t border-blue-200 p-3">
                    <Text className="text-sm font-semibold text-blue-800 mb-1">
                        🗣️ Voice Commands
                    </Text>
                    <Text className="text-xs text-blue-700">
                        "Mark milestone complete" • "Add photo" • "Read progress" • "Next milestone"
                    </Text>
                </View>
            )}
        </View>
    );
}