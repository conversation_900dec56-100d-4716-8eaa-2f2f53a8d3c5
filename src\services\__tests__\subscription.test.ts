import { SubscriptionService } from '../supabase/subscription';
import { SUBSCRIPTION_PLANS, getFeatureAccess, canAccessFeature } from '../../types/subscription';

// Mock Supabase client
jest.mock('../supabase/client', () => ({
    supabase: {
        from: jest.fn(() => ({
            insert: jest.fn(() => ({ select: jest.fn(() => ({ single: jest.fn() })) })),
            select: jest.fn(() => ({
                eq: jest.fn(() => ({
                    single: jest.fn(),
                    order: jest.fn()
                }))
            })),
            update: jest.fn(() => ({ eq: jest.fn() })),
            upsert: jest.fn(),
        })),
        rpc: jest.fn(),
    },
}));

describe('SubscriptionService', () => {
    const mockUserId = 'test-user-id';
    const mockPlanId = 'basic';

    describe('createSubscription', () => {
        it('should create subscription for user', async () => {
            const result = await SubscriptionService.createSubscription(
                mockUserId,
                mockPlanId
            );

            // Since we're mocking, we expect the function to complete without error
            expect(result.error).toBeNull();
        });
    });

    describe('cancelSubscription', () => {
        it('should cancel user subscription', async () => {
            const result = await SubscriptionService.cancelSubscription(mockUserId);
            expect(result.error).toBeNull();
        });
    });

    describe('checkFeatureAccess', () => {
        it('should check feature access for user', async () => {
            const result = await SubscriptionService.checkFeatureAccess(
                mockUserId,
                'image_analysis'
            );

            expect(result.hasAccess).toBeDefined();
            expect(result.error).toBeNull();
        });
    });
});

describe('Subscription Configuration', () => {
    it('should have valid subscription plans', () => {
        expect(SUBSCRIPTION_PLANS).toBeDefined();
        expect(SUBSCRIPTION_PLANS.length).toBeGreaterThan(0);

        SUBSCRIPTION_PLANS.forEach(plan => {
            expect(plan.name).toBeDefined();
            expect(plan.description).toBeDefined();
            expect(plan.price).toBeGreaterThanOrEqual(0);
            expect(plan.currency).toBeDefined();
            expect(plan.billing_period).toMatch(/^(monthly|yearly)$/);
            expect(plan.features).toBeDefined();
            expect(Array.isArray(plan.features)).toBe(true);
            expect(plan.points_included).toBeGreaterThanOrEqual(0);
            expect(plan.ai_consultations_limit).toBeGreaterThanOrEqual(-1);
        });
    });

    it('should provide correct feature access for different tiers', () => {
        const freeAccess = getFeatureAccess('free');
        const basicAccess = getFeatureAccess('basic');
        const premiumAccess = getFeatureAccess('premium');

        // Free tier should have basic features
        expect(freeAccess.ai_consultations).toBe(true);
        expect(freeAccess.image_analysis).toBe(false);
        expect(freeAccess.advanced_analytics).toBe(false);

        // Basic tier should have more features
        expect(basicAccess.ai_consultations).toBe(true);
        expect(basicAccess.image_analysis).toBe(true);
        expect(basicAccess.offline_mode).toBe(true);

        // Premium tier should have advanced features
        expect(premiumAccess.advanced_analytics).toBe(true);
        expect(premiumAccess.api_access).toBe(true);
    });

    it('should correctly check feature access', () => {
        expect(canAccessFeature('free', 'ai_consultations')).toBe(true);
        expect(canAccessFeature('free', 'image_analysis')).toBe(false);
        expect(canAccessFeature('basic', 'image_analysis')).toBe(true);
        expect(canAccessFeature('premium', 'advanced_analytics')).toBe(true);
    });
});