import { supabase } from './client';
import { Database } from '../../types/database';

type UserFollow = Database['public']['Tables']['user_follows']['Row'];
type DirectMessage = Database['public']['Tables']['direct_messages']['Row'];
type CommunityEvent = Database['public']['Tables']['community_events']['Row'];
type EventRSVP = Database['public']['Tables']['event_rsvps']['Row'];

export interface UserProfile {
    id: string;
    name: string;
    avatar?: string;
    location?: string;
    experienceLevel?: 'beginner' | 'intermediate' | 'expert';
    followersCount: number;
    followingCount: number;
    isFollowing: boolean;
}

export interface Message {
    id: string;
    senderId: string;
    recipientId: string;
    content: string;
    readAt?: Date;
    createdAt: Date;
    sender: {
        id: string;
        name: string;
        avatar?: string;
    };
}

export interface Event {
    id: string;
    title: string;
    description: string;
    eventDate: Date;
    location?: {
        latitude: number;
        longitude: number;
        name?: string;
    };
    maxAttendees?: number;
    isPublic: boolean;
    status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
    organizer: {
        id: string;
        name: string;
        avatar?: string;
    };
    attendeesCount: number;
    userRSVP?: 'going' | 'maybe' | 'not_going';
}

export class SocialService {
    /**
     * Follow or unfollow a user
     */
    static async toggleFollow(userId: string): Promise<{
        isFollowing: boolean;
        error: string | null;
    }> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                return { isFollowing: false, error: 'User not authenticated' };
            }

            if (user.id === userId) {
                return { isFollowing: false, error: 'Cannot follow yourself' };
            }

            // Check if already following
            const { data: existingFollow } = await supabase
                .from('user_follows')
                .select('id')
                .eq('follower_id', user.id)
                .eq('following_id', userId)
                .single();

            let isFollowing: boolean;

            if (existingFollow) {
                // Unfollow
                const { error } = await supabase
                    .from('user_follows')
                    .delete()
                    .eq('follower_id', user.id)
                    .eq('following_id', userId);

                if (error) {
                    return { isFollowing: false, error: error.message };
                }

                isFollowing = false;
            } else {
                // Follow
                const { error } = await supabase
                    .from('user_follows')
                    .insert({
                        follower_id: user.id,
                        following_id: userId,
                    });

                if (error) {
                    return { isFollowing: false, error: error.message };
                }

                isFollowing = true;
            }

            return { isFollowing, error: null };
        } catch (error) {
            return { isFollowing: false, error: (error as Error).message };
        }
    }

    /**
     * Get user's followers
     */
    static async getFollowers(userId: string): Promise<{
        followers: UserProfile[];
        error: string | null;
    }> {
        try {
            const { data, error } = await supabase
                .from('user_follows')
                .select(`
                    follower_id,
                    users!user_follows_follower_id_fkey (
                        id,
                        first_name,
                        last_name
                    ),
                    user_profiles!user_follows_follower_id_fkey (
                        farm_location,
                        experience_level
                    )
                `)
                .eq('following_id', userId);

            if (error) {
                return { followers: [], error: error.message };
            }

            const followers: UserProfile[] = (data || []).map((follow: any) => ({
                id: follow.users.id,
                name: `${follow.users.first_name} ${follow.users.last_name}`,
                location: follow.user_profiles?.farm_location,
                experienceLevel: follow.user_profiles?.experience_level,
                followersCount: 0, // Would need additional query
                followingCount: 0, // Would need additional query
                isFollowing: false, // Would need to check current user's follows
            }));

            return { followers, error: null };
        } catch (error) {
            return { followers: [], error: (error as Error).message };
        }
    }

    /**
     * Get users that a user is following
     */
    static async getFollowing(userId: string): Promise<{
        following: UserProfile[];
        error: string | null;
    }> {
        try {
            const { data, error } = await supabase
                .from('user_follows')
                .select(`
                    following_id,
                    users!user_follows_following_id_fkey (
                        id,
                        first_name,
                        last_name
                    ),
                    user_profiles!user_follows_following_id_fkey (
                        farm_location,
                        experience_level
                    )
                `)
                .eq('follower_id', userId);

            if (error) {
                return { following: [], error: error.message };
            }

            const following: UserProfile[] = (data || []).map((follow: any) => ({
                id: follow.users.id,
                name: `${follow.users.first_name} ${follow.users.last_name}`,
                location: follow.user_profiles?.farm_location,
                experienceLevel: follow.user_profiles?.experience_level,
                followersCount: 0, // Would need additional query
                followingCount: 0, // Would need additional query
                isFollowing: true, // Already following since this is the following list
            }));

            return { following, error: null };
        } catch (error) {
            return { following: [], error: (error as Error).message };
        }
    }

    /**
     * Send a direct message
     */
    static async sendMessage(recipientId: string, content: string): Promise<{
        message: Message | null;
        error: string | null;
    }> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                return { message: null, error: 'User not authenticated' };
            }

            const { data, error } = await supabase
                .from('direct_messages')
                .insert({
                    sender_id: user.id,
                    recipient_id: recipientId,
                    content,
                })
                .select(`
                    *,
                    users!direct_messages_sender_id_fkey (
                        id,
                        first_name,
                        last_name
                    )
                `)
                .single();

            if (error) {
                return { message: null, error: error.message };
            }

            const message: Message = {
                id: data.id,
                senderId: data.sender_id,
                recipientId: data.recipient_id,
                content: data.content,
                readAt: data.read_at ? new Date(data.read_at) : undefined,
                createdAt: new Date(data.created_at),
                sender: {
                    id: data.users.id,
                    name: `${data.users.first_name} ${data.users.last_name}`,
                },
            };

            return { message, error: null };
        } catch (error) {
            return { message: null, error: (error as Error).message };
        }
    }

    /**
     * Get conversation between two users
     */
    static async getConversation(otherUserId: string): Promise<{
        messages: Message[];
        error: string | null;
    }> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                return { messages: [], error: 'User not authenticated' };
            }

            const { data, error } = await supabase
                .from('direct_messages')
                .select(`
                    *,
                    users!direct_messages_sender_id_fkey (
                        id,
                        first_name,
                        last_name
                    )
                `)
                .or(`and(sender_id.eq.${user.id},recipient_id.eq.${otherUserId}),and(sender_id.eq.${otherUserId},recipient_id.eq.${user.id})`)
                .order('created_at', { ascending: true });

            if (error) {
                return { messages: [], error: error.message };
            }

            const messages: Message[] = (data || []).map((msg: any) => ({
                id: msg.id,
                senderId: msg.sender_id,
                recipientId: msg.recipient_id,
                content: msg.content,
                readAt: msg.read_at ? new Date(msg.read_at) : undefined,
                createdAt: new Date(msg.created_at),
                sender: {
                    id: msg.users.id,
                    name: `${msg.users.first_name} ${msg.users.last_name}`,
                },
            }));

            return { messages, error: null };
        } catch (error) {
            return { messages: [], error: (error as Error).message };
        }
    }

    /**
     * Mark messages as read
     */
    static async markMessagesAsRead(messageIds: string[]): Promise<{
        success: boolean;
        error: string | null;
    }> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                return { success: false, error: 'User not authenticated' };
            }

            const { error } = await supabase
                .from('direct_messages')
                .update({ read_at: new Date().toISOString() })
                .in('id', messageIds)
                .eq('recipient_id', user.id);

            if (error) {
                return { success: false, error: error.message };
            }

            return { success: true, error: null };
        } catch (error) {
            return { success: false, error: (error as Error).message };
        }
    }

    /**
     * Create a community event
     */
    static async createEvent(eventData: {
        title: string;
        description: string;
        eventDate: Date;
        location?: { latitude: number; longitude: number; name?: string };
        maxAttendees?: number;
        isPublic?: boolean;
    }): Promise<{
        event: Event | null;
        error: string | null;
    }> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                return { event: null, error: 'User not authenticated' };
            }

            // Format location as PostGIS point if provided
            let locationPoint: string | null = null;
            if (eventData.location) {
                locationPoint = `POINT(${eventData.location.longitude} ${eventData.location.latitude})`;
            }

            const { data, error } = await supabase
                .from('community_events')
                .insert({
                    organizer_id: user.id,
                    title: eventData.title,
                    description: eventData.description,
                    event_date: eventData.eventDate.toISOString(),
                    location: locationPoint,
                    location_name: eventData.location?.name,
                    max_attendees: eventData.maxAttendees,
                    is_public: eventData.isPublic ?? true,
                })
                .select(`
                    *,
                    users!community_events_organizer_id_fkey (
                        id,
                        first_name,
                        last_name
                    )
                `)
                .single();

            if (error) {
                return { event: null, error: error.message };
            }

            const event: Event = {
                id: data.id,
                title: data.title,
                description: data.description,
                eventDate: new Date(data.event_date),
                location: eventData.location,
                maxAttendees: data.max_attendees,
                isPublic: data.is_public,
                status: data.status as any,
                organizer: {
                    id: data.users.id,
                    name: `${data.users.first_name} ${data.users.last_name}`,
                },
                attendeesCount: 0,
            };

            return { event, error: null };
        } catch (error) {
            return { event: null, error: (error as Error).message };
        }
    }

    /**
     * RSVP to an event
     */
    static async rsvpToEvent(eventId: string, status: 'going' | 'maybe' | 'not_going'): Promise<{
        success: boolean;
        error: string | null;
    }> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                return { success: false, error: 'User not authenticated' };
            }

            const { error } = await supabase
                .from('event_rsvps')
                .upsert({
                    event_id: eventId,
                    user_id: user.id,
                    status,
                });

            if (error) {
                return { success: false, error: error.message };
            }

            return { success: true, error: null };
        } catch (error) {
            return { success: false, error: (error as Error).message };
        }
    }

    /**
     * Get community events
     */
    static async getEvents(filters: {
        upcoming?: boolean;
        location?: { latitude: number; longitude: number; radius?: number };
        limit?: number;
    } = {}): Promise<{
        events: Event[];
        error: string | null;
    }> {
        try {
            let query = supabase
                .from('community_events')
                .select(`
                    *,
                    users!community_events_organizer_id_fkey (
                        id,
                        first_name,
                        last_name
                    )
                `)
                .eq('is_public', true)
                .order('event_date', { ascending: true });

            if (filters.upcoming) {
                query = query.gte('event_date', new Date().toISOString());
            }

            if (filters.limit) {
                query = query.limit(filters.limit);
            }

            const { data, error } = await query;

            if (error) {
                return { events: [], error: error.message };
            }

            const events: Event[] = (data || []).map((event: any) => ({
                id: event.id,
                title: event.title,
                description: event.description,
                eventDate: new Date(event.event_date),
                location: event.location ? {
                    // Would need to parse PostGIS POINT format
                    latitude: 0,
                    longitude: 0,
                    name: event.location_name,
                } : undefined,
                maxAttendees: event.max_attendees,
                isPublic: event.is_public,
                status: event.status,
                organizer: {
                    id: event.users.id,
                    name: `${event.users.first_name} ${event.users.last_name}`,
                },
                attendeesCount: 0, // Would need additional query
            }));

            return { events, error: null };
        } catch (error) {
            return { events: [], error: (error as Error).message };
        }
    }
}