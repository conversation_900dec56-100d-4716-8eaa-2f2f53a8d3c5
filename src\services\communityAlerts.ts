import { supabase } from './supabase/client';
import notificationService from './notifications';
import NotificationTemplateService from './notificationTemplates';

export interface CommunityEvent {
    id: string;
    title: string;
    description: string;
    eventType: 'workshop' | 'market_day' | 'field_visit' | 'meeting' | 'emergency' | 'celebration';
    location: {
        name: string;
        latitude: number;
        longitude: number;
        address?: string;
    };
    startTime: Date;
    endTime: Date;
    organizer: {
        id: string;
        name: string;
        avatar?: string;
    };
    maxParticipants?: number;
    currentParticipants: number;
    tags: string[];
    isPublic: boolean;
    requiresRSVP: boolean;
    cost?: {
        amount: number;
        currency: string;
        description?: string;
    };
    materials?: string[];
    prerequisites?: string[];
}

export interface CommunityPost {
    id: string;
    authorId: string;
    authorName: string;
    authorAvatar?: string;
    title: string;
    content: string;
    imageUrls?: string[];
    location?: {
        name: string;
        latitude: number;
        longitude: number;
    };
    tags: string[];
    category: 'question' | 'tip' | 'success_story' | 'problem' | 'market_info' | 'general';
    urgency: 'low' | 'normal' | 'high' | 'urgent';
    likesCount: number;
    commentsCount: number;
    sharesCount: number;
    createdAt: Date;
    updatedAt: Date;
}

export interface CommunityAlertPreferences {
    userId: string;
    nearbyEvents: boolean;
    eventReminders: boolean;
    newPosts: boolean;
    postReplies: boolean;
    mentions: boolean;
    urgentPosts: boolean;
    followedUsers: boolean;
    marketUpdates: boolean;
    emergencyAlerts: boolean;
    maxDistanceKm: number;
    interestedTags: string[];
}

export class CommunityAlertService {
    private static instance: CommunityAlertService;
    private userPreferences: Map<string, CommunityAlertPreferences> = new Map();

    static getInstance(): CommunityAlertService {
        if (!CommunityAlertService.instance) {
            CommunityAlertService.instance = new CommunityAlertService();
        }
        return CommunityAlertService.instance;
    }

    async initialize(): Promise<void> {
        try {
            await this.loadUserPreferences();
            console.log('Community alert service initialized');
        } catch (error) {
            console.error('Failed to initialize community alert service:', error);
        }
    }

    private async loadUserPreferences(): Promise<void> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return;

            const { data: preferences } = await supabase
                .from('community_alert_preferences')
                .select('*')
                .eq('user_id', user.id)
                .single();

            if (preferences) {
                this.userPreferences.set(user.id, preferences);
            } else {
                // Create default preferences
                await this.createDefaultPreferences(user.id);
            }
        } catch (error) {
            console.error('Failed to load user preferences:', error);
        }
    }

    private async createDefaultPreferences(userId: string): Promise<void> {
        const defaultPreferences: CommunityAlertPreferences = {
            userId,
            nearbyEvents: true,
            eventReminders: true,
            newPosts: false,
            postReplies: true,
            mentions: true,
            urgentPosts: true,
            followedUsers: true,
            marketUpdates: true,
            emergencyAlerts: true,
            maxDistanceKm: 50,
            interestedTags: ['farming', 'crops', 'weather', 'market'],
        };

        try {
            await supabase
                .from('community_alert_preferences')
                .insert(defaultPreferences);

            this.userPreferences.set(userId, defaultPreferences);
        } catch (error) {
            console.error('Failed to create default preferences:', error);
        }
    }

    async handleNewCommunityPost(post: CommunityPost): Promise<void> {
        try {
            // Get users who should be notified about this post
            const notificationTargets = await this.getPostNotificationTargets(post);

            for (const target of notificationTargets) {
                await this.sendPostNotification(target.userId, post, target.reason);
            }
        } catch (error) {
            console.error('Failed to handle new community post:', error);
        }
    }

    async handleNewCommunityEvent(event: CommunityEvent): Promise<void> {
        try {
            // Get users who should be notified about this event
            const notificationTargets = await this.getEventNotificationTargets(event);

            for (const target of notificationTargets) {
                await this.sendEventNotification(target.userId, event, target.reason);
            }
        } catch (error) {
            console.error('Failed to handle new community event:', error);
        }
    }

    async handlePostReply(postId: string, replyAuthorId: string, replyContent: string): Promise<void> {
        try {
            // Get the original post
            const { data: post } = await supabase
                .from('community_posts')
                .select(`
          *,
          profiles:author_id (
            id,
            first_name,
            last_name,
            avatar_url
          )
        `)
                .eq('id', postId)
                .single();

            if (!post) return;

            // Notify the post author (if not the same as reply author)
            if (post.author_id !== replyAuthorId) {
                const preferences = this.userPreferences.get(post.author_id);
                if (preferences?.postReplies) {
                    await this.sendReplyNotification(post.author_id, post, replyContent);
                }
            }

            // Notify other users who have commented on this post
            await this.notifyOtherCommenters(postId, replyAuthorId, post, replyContent);
        } catch (error) {
            console.error('Failed to handle post reply:', error);
        }
    }

    async handleUserMention(mentionedUserId: string, postId: string, mentionerName: string): Promise<void> {
        try {
            const preferences = this.userPreferences.get(mentionedUserId);
            if (!preferences?.mentions) return;

            const { data: post } = await supabase
                .from('community_posts')
                .select('title, content')
                .eq('id', postId)
                .single();

            if (!post) return;

            const notification = NotificationTemplateService.createCommunityNotification(
                mentionerName,
                `mentioned you in: ${post.title}`,
                postId
            );

            await notificationService.sendLocalNotification(
                notification.title,
                notification.body,
                notification.data,
                { priority: notification.priority }
            );
        } catch (error) {
            console.error('Failed to handle user mention:', error);
        }
    }

    async scheduleEventReminders(event: CommunityEvent): Promise<void> {
        try {
            // Get users who RSVP'd to the event
            const { data: attendees } = await supabase
                .from('event_attendees')
                .select('user_id')
                .eq('event_id', event.id)
                .eq('status', 'attending');

            if (!attendees) return;

            // Schedule reminders for different time intervals
            const reminderIntervals = [
                { hours: 24, message: 'tomorrow' },
                { hours: 2, message: 'in 2 hours' },
                { hours: 0.5, message: 'in 30 minutes' },
            ];

            for (const attendee of attendees) {
                const preferences = this.userPreferences.get(attendee.user_id);
                if (!preferences?.eventReminders) continue;

                for (const interval of reminderIntervals) {
                    const reminderTime = new Date(event.startTime.getTime() - interval.hours * 60 * 60 * 1000);

                    if (reminderTime > new Date()) {
                        await notificationService.scheduleNotification({
                            id: `event_reminder_${event.id}_${attendee.user_id}_${interval.hours}h`,
                            identifier: '',
                            content: {
                                title: `📅 Event Reminder: ${event.title}`,
                                body: `Your event "${event.title}" starts ${interval.message} at ${event.location.name}`,
                                data: {
                                    type: 'community_event_reminder',
                                    eventId: event.id,
                                    userId: attendee.user_id,
                                },
                            },
                            trigger: {
                                type: 'date',
                                date: reminderTime,
                            },
                        });
                    }
                }
            }
        } catch (error) {
            console.error('Failed to schedule event reminders:', error);
        }
    }

    async sendEmergencyAlert(
        title: string,
        message: string,
        location: { latitude: number; longitude: number; name: string },
        radiusKm: number = 100
    ): Promise<void> {
        try {
            // Get all users within the specified radius
            const affectedUsers = await this.getUsersInRadius(location, radiusKm);

            for (const user of affectedUsers) {
                const preferences = this.userPreferences.get(user.id);
                if (!preferences?.emergencyAlerts) continue;

                await notificationService.sendLocalNotification(
                    `🚨 EMERGENCY: ${title}`,
                    message,
                    {
                        type: 'emergency_alert',
                        location,
                        radiusKm,
                    },
                    {
                        priority: 'critical',
                        sound: 'emergency_alert.wav',
                    }
                );

                // Store emergency alert
                await supabase
                    .from('notifications')
                    .insert({
                        user_id: user.id,
                        type: 'emergency_alert',
                        title: `🚨 EMERGENCY: ${title}`,
                        body: message,
                        data: {
                            type: 'emergency_alert',
                            location,
                            radiusKm,
                        },
                        priority: 'critical',
                        category: 'community',
                    });
            }
        } catch (error) {
            console.error('Failed to send emergency alert:', error);
        }
    }

    private async getPostNotificationTargets(post: CommunityPost): Promise<Array<{ userId: string; reason: string }>> {
        const targets: Array<{ userId: string; reason: string }> = [];

        try {
            // Get users following the post author
            if (post.urgency === 'urgent' || post.urgency === 'high') {
                const { data: followers } = await supabase
                    .from('user_follows')
                    .select('follower_id')
                    .eq('following_id', post.authorId);

                if (followers) {
                    for (const follower of followers) {
                        const preferences = this.userPreferences.get(follower.follower_id);
                        if (preferences?.urgentPosts || preferences?.followedUsers) {
                            targets.push({
                                userId: follower.follower_id,
                                reason: post.urgency === 'urgent' ? 'urgent_post' : 'followed_user',
                            });
                        }
                    }
                }
            }

            // Get users interested in the post's tags
            if (post.tags.length > 0) {
                const interestedUsers = await this.getUsersInterestedInTags(post.tags);
                for (const userId of interestedUsers) {
                    const preferences = this.userPreferences.get(userId);
                    if (preferences?.newPosts) {
                        targets.push({ userId, reason: 'interested_tags' });
                    }
                }
            }

            // Get users in the same location (if post has location)
            if (post.location) {
                const nearbyUsers = await this.getUsersInRadius(post.location, 25); // 25km radius
                for (const user of nearbyUsers) {
                    const preferences = this.userPreferences.get(user.id);
                    if (preferences?.newPosts) {
                        targets.push({ userId: user.id, reason: 'nearby_post' });
                    }
                }
            }

            // Remove duplicates and the post author
            const uniqueTargets = targets.filter((target, index, self) =>
                target.userId !== post.authorId &&
                index === self.findIndex(t => t.userId === target.userId)
            );

            return uniqueTargets;
        } catch (error) {
            console.error('Failed to get post notification targets:', error);
            return [];
        }
    }

    private async getEventNotificationTargets(event: CommunityEvent): Promise<Array<{ userId: string; reason: string }>> {
        const targets: Array<{ userId: string; reason: string }> = [];

        try {
            // Get users within the event's radius
            const nearbyUsers = await this.getUsersInRadius(event.location, 50); // 50km radius

            for (const user of nearbyUsers) {
                const preferences = this.userPreferences.get(user.id);
                if (preferences?.nearbyEvents) {
                    targets.push({ userId: user.id, reason: 'nearby_event' });
                }
            }

            // Get users interested in event tags
            if (event.tags.length > 0) {
                const interestedUsers = await this.getUsersInterestedInTags(event.tags);
                for (const userId of interestedUsers) {
                    targets.push({ userId, reason: 'interested_tags' });
                }
            }

            // Remove duplicates and the event organizer
            const uniqueTargets = targets.filter((target, index, self) =>
                target.userId !== event.organizer.id &&
                index === self.findIndex(t => t.userId === target.userId)
            );

            return uniqueTargets;
        } catch (error) {
            console.error('Failed to get event notification targets:', error);
            return [];
        }
    }

    private async sendPostNotification(userId: string, post: CommunityPost, reason: string): Promise<void> {
        try {
            const notification = NotificationTemplateService.createCommunityNotification(
                post.authorName,
                post.title,
                post.id
            );

            await notificationService.sendLocalNotification(
                notification.title,
                notification.body,
                {
                    ...notification.data,
                    reason,
                    postCategory: post.category,
                    urgency: post.urgency,
                },
                { priority: notification.priority }
            );
        } catch (error) {
            console.error('Failed to send post notification:', error);
        }
    }

    private async sendEventNotification(userId: string, event: CommunityEvent, reason: string): Promise<void> {
        try {
            await notificationService.sendLocalNotification(
                `📅 New Event: ${event.title}`,
                `${event.organizer.name} is organizing "${event.title}" at ${event.location.name}`,
                {
                    type: 'community_event',
                    eventId: event.id,
                    reason,
                    eventType: event.eventType,
                },
                { priority: 'normal' }
            );
        } catch (error) {
            console.error('Failed to send event notification:', error);
        }
    }

    private async sendReplyNotification(userId: string, post: CommunityPost, replyContent: string): Promise<void> {
        try {
            await notificationService.sendLocalNotification(
                `💬 New Reply to Your Post`,
                `Someone replied to "${post.title}": ${replyContent.substring(0, 100)}...`,
                {
                    type: 'community_reply',
                    postId: post.id,
                },
                { priority: 'normal' }
            );
        } catch (error) {
            console.error('Failed to send reply notification:', error);
        }
    }

    private async notifyOtherCommenters(postId: string, replyAuthorId: string, post: CommunityPost, replyContent: string): Promise<void> {
        try {
            // Get users who have commented on this post
            const { data: commenters } = await supabase
                .from('post_comments')
                .select('user_id')
                .eq('post_id', postId)
                .neq('user_id', replyAuthorId)
                .neq('user_id', post.authorId);

            if (!commenters) return;

            const uniqueCommenters = [...new Set(commenters.map(c => c.user_id))];

            for (const commenterId of uniqueCommenters) {
                const preferences = this.userPreferences.get(commenterId);
                if (preferences?.postReplies) {
                    await notificationService.sendLocalNotification(
                        `💬 New Reply in Discussion`,
                        `New reply in "${post.title}": ${replyContent.substring(0, 100)}...`,
                        {
                            type: 'community_discussion',
                            postId: post.id,
                        },
                        { priority: 'low' }
                    );
                }
            }
        } catch (error) {
            console.error('Failed to notify other commenters:', error);
        }
    }

    private async getUsersInterestedInTags(tags: string[]): Promise<string[]> {
        try {
            const users: string[] = [];

            for (const [userId, preferences] of this.userPreferences.entries()) {
                const hasMatchingTag = tags.some(tag =>
                    preferences.interestedTags.includes(tag.toLowerCase())
                );

                if (hasMatchingTag) {
                    users.push(userId);
                }
            }

            return users;
        } catch (error) {
            console.error('Failed to get users interested in tags:', error);
            return [];
        }
    }

    private async getUsersInRadius(
        location: { latitude: number; longitude: number },
        radiusKm: number
    ): Promise<Array<{ id: string }>> {
        try {
            // This would use PostGIS functions in a real implementation
            // For now, return a placeholder
            const { data: users } = await supabase
                .from('user_profiles')
                .select('id')
                .not('farm_location', 'is', null);

            return users || [];
        } catch (error) {
            console.error('Failed to get users in radius:', error);
            return [];
        }
    }

    async updateUserPreferences(userId: string, preferences: Partial<CommunityAlertPreferences>): Promise<void> {
        try {
            const currentPreferences = this.userPreferences.get(userId);
            const updatedPreferences = { ...currentPreferences, ...preferences };

            await supabase
                .from('community_alert_preferences')
                .upsert(updatedPreferences);

            this.userPreferences.set(userId, updatedPreferences as CommunityAlertPreferences);
        } catch (error) {
            console.error('Failed to update user preferences:', error);
            throw error;
        }
    }

    async getUserPreferences(userId: string): Promise<CommunityAlertPreferences | null> {
        return this.userPreferences.get(userId) || null;
    }
}

export const communityAlertService = CommunityAlertService.getInstance();
export default communityAlertService;