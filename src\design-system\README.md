# AI Farming Assistant Design System

A comprehensive design system built for agricultural applications with accessibility and outdoor usability in mind.

## Overview

This design system provides a complete set of design tokens, components, and utilities specifically crafted for farming applications. It prioritizes:

- **Accessibility**: Voice navigation, screen reader support, and large touch targets
- **Outdoor Visibility**: High contrast colors and clear typography
- **Agricultural Context**: Farming-specific components and terminology
- **Multi-language Support**: RTL layout support for Arabic language
- **Offline Capability**: Optimized for rural connectivity scenarios

## Design Tokens

### Colors

Our color palette is inspired by agricultural environments:

- **Primary (Green)**: Represents growth, health, and nature
- **Secondary (Yellow)**: Represents sunshine, energy, and warnings
- **Earth (Brown)**: Represents soil, stability, and grounding
- **Status Colors**: Success, warning, error, and info states

```typescript
import { colors } from './colors';

// Usage
const primaryColor = colors.primary[500]; // #22c55e
const earthTone = colors.earth[600]; // #57534e
```

### Typography

Voice-friendly typography with clear hierarchy:

```typescript
import { textStyles } from './typography';

// Usage in components
<Text className={textStyles.h1}>Main Heading</Text>
<Text className={textStyles.body}>Body text</Text>
<Text className={textStyles.voiceLarge}>Voice-optimized text</Text>
```

### Spacing

Consistent spacing system with touch-friendly targets:

```typescript
import { spacing, touchTargets } from './spacing';

// Minimum touch target for accessibility
const buttonHeight = touchTargets.minimum; // 44px

// Comfortable touch target for work gloves
const largeButtonHeight = touchTargets.comfortable; // 56px
```

## Components

### Base UI Components

#### Button

Accessible button component with multiple variants:

```tsx
<Button
  title="Water Plants"
  onPress={handleWatering}
  variant="primary"
  size="large"
  accessibilityLabel="Water all tomato plants"
  accessibilityHint="Starts the watering task for tomato plants"
/>
```

#### Input

Form input with voice support:

```tsx
<Input
  label="Farm Name"
  value={farmName}
  onChangeText={setFarmName}
  voiceInputEnabled
  onVoiceInput={handleVoiceInput}
  accessibilityLabel="Enter your farm name"
/>
```

#### Card

Flexible card container:

```tsx
<Card variant="elevated" onPress={handleCardPress} accessibilityLabel="Weather information card">
  <WeatherWidget weather={weatherData} />
</Card>
```

#### Modal

Accessible modal dialog:

```tsx
<Modal visible={isVisible} onClose={handleClose} title="Task Details" size="medium">
  <TaskDetails task={selectedTask} />
</Modal>
```

### Agricultural Components

#### WeatherWidget

Displays current weather conditions:

```tsx
<WeatherWidget weather={weatherData} location="Springfield Farm" onPress={showDetailedWeather} />
```

#### TaskCard

Farming task management:

```tsx
<TaskCard
  task={wateringTask}
  onToggleComplete={handleTaskComplete}
  onPress={showTaskDetails}
  showPoints={true}
/>
```

## Accessibility Features

### Voice Navigation

- Text-to-speech support for all interface elements
- Voice input capabilities on form fields
- Voice command recognition for common actions

### Screen Reader Support

- Semantic HTML/React Native elements
- Descriptive accessibility labels
- Proper focus management

### Touch Targets

- Minimum 44px touch targets (iOS/Android guidelines)
- Comfortable 56px targets for work gloves
- Large 72px targets for critical actions

### High Contrast

- WCAG AA compliant color combinations
- High contrast mode support
- Outdoor visibility optimizations

## Usage Guidelines

### Do's

✅ Use semantic color names (primary, secondary, earth)
✅ Provide accessibility labels for all interactive elements
✅ Use consistent spacing from the design system
✅ Test with voice navigation enabled
✅ Consider outdoor lighting conditions

### Don'ts

❌ Use hardcoded colors or spacing values
❌ Create touch targets smaller than 44px
❌ Forget accessibility labels
❌ Use low contrast color combinations
❌ Ignore RTL layout requirements

## Customization

### Theme Configuration

```typescript
import { designSystem } from './design-system';

// Enable high contrast mode
designSystem.accessibility.highContrastMode = true;

// Enable voice mode
designSystem.accessibility.voiceEnabled = true;
```

### Custom Colors

```typescript
// Extend the color palette
const customColors = {
  ...colors,
  brand: {
    500: '#your-brand-color',
  },
};
```

## Testing

### Accessibility Testing

- Test with screen readers (TalkBack/VoiceOver)
- Verify keyboard navigation
- Check color contrast ratios
- Test with large text sizes

### Agricultural Context Testing

- Test in bright sunlight conditions
- Verify with work gloves on
- Test voice commands in noisy environments
- Validate offline functionality

## Contributing

When adding new components:

1. Follow accessibility guidelines
2. Include proper TypeScript types
3. Add comprehensive documentation
4. Test with voice navigation
5. Ensure outdoor visibility
6. Support RTL layouts

## Resources

- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [React Native Accessibility](https://reactnative.dev/docs/accessibility)
- [Agricultural UI Patterns](https://example.com/agricultural-ui)
- [Voice Interface Design](https://example.com/voice-ui)
