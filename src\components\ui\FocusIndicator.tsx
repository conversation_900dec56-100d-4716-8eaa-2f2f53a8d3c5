/**
 * Focus Indicator Component
 * Visual focus indicator for keyboard navigation
 */

import React from 'react';
import { View, ViewStyle } from 'react-native';
import { useAccessibility } from '../../hooks/useAccessibility';

interface FocusIndicatorProps {
    visible: boolean;
    style?: ViewStyle;
    children: React.ReactNode;
}

export const FocusIndicator: React.FC<FocusIndicatorProps> = ({
    visible,
    style,
    children,
}) => {
    const { getHighContrastColors, isKeyboardNavigationEnabled } = useAccessibility();

    const highContrastColors = getHighContrastColors();

    const getFocusStyles = (): ViewStyle => {
        if (!visible || !isKeyboardNavigationEnabled) {
            return {};
        }

        const focusColor = highContrastColors?.focusRing || '#007AFF';
        const focusBackground = highContrastColors?.focusBackground || 'rgba(0, 122, 255, 0.1)';

        return {
            borderWidth: 3,
            borderColor: focusColor,
            borderRadius: 8,
            backgroundColor: focusBackground,
            shadowColor: focusColor,
            shadowOffset: { width: 0, height: 0 },
            shadowOpacity: 0.5,
            shadowRadius: 4,
            elevation: 4,
        };
    };

    return (
        <View style={[getFocusStyles(), style]}>
            {children}
        </View>
    );
};