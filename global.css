@tailwind base;
@tailwind components;
@tailwind utilities;

/* Agricultural-friendly base styles */
@layer base {
  /* High contrast mode support */
  .high-contrast {
    --primary-color: #000000;
    --background-color: #ffffff;
    --text-color: #000000;
    --border-color: #000000;
  }

  /* Voice mode indicators */
  .voice-enabled {
    border: 2px solid #22c55e;
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
  }

  /* Focus styles for keyboard navigation */
  .focus-visible {
    outline: 2px solid #22c55e;
    outline-offset: 2px;
  }
}

@layer components {
  /* Agricultural button variants */
  .btn-agricultural {
    @apply bg-primary-600 rounded-xl px-6 py-3 font-semibold text-white shadow-md;
    @apply active:bg-primary-700 active:shadow-lg;
    @apply disabled:cursor-not-allowed disabled:opacity-50;
    min-height: 48px; /* Minimum touch target */
  }

  .btn-agricultural-outline {
    @apply border-primary-600 text-primary-600 rounded-xl border-2 bg-transparent px-6 py-3 font-semibold;
    @apply active:bg-primary-50;
    min-height: 48px;
  }

  /* Agricultural card styles */
  .card-agricultural {
    @apply border-earth-100 rounded-2xl border bg-white p-4 shadow-md;
  }

  .card-agricultural-elevated {
    @apply rounded-2xl bg-white p-4 shadow-lg;
  }

  /* Form styles optimized for outdoor use */
  .input-agricultural {
    @apply border-earth-300 rounded-xl border-2 bg-white px-4 py-3 text-base;
    @apply focus:border-primary-500 focus:ring-primary-200 focus:ring-2;
    min-height: 48px;
  }

  /* Weather widget styles */
  .weather-widget {
    @apply rounded-2xl bg-gradient-to-br from-blue-50 to-green-50 p-4 shadow-md;
  }

  /* Task completion animations */
  .task-complete {
    @apply transition-all duration-300 ease-in-out;
  }

  /* Voice feedback indicators */
  .voice-listening {
    @apply border-primary-500 animate-pulse border-2;
  }

  .voice-speaking {
    @apply border-secondary-500 animate-pulse border-2;
  }
}

@layer utilities {
  /* Touch target utilities */
  .touch-target-min {
    min-width: 44px;
    min-height: 44px;
  }

  .touch-target-comfortable {
    min-width: 56px;
    min-height: 56px;
  }

  .touch-target-large {
    min-width: 72px;
    min-height: 72px;
  }

  /* Outdoor visibility utilities */
  .outdoor-visible {
    @apply border-2 shadow-lg;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  /* RTL support utilities */
  .rtl {
    direction: rtl;
  }

  .ltr {
    direction: ltr;
  }

  /* Voice mode utilities */
  .voice-text-large {
    @apply text-xl leading-relaxed;
  }

  .voice-text-xlarge {
    @apply text-2xl leading-relaxed;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .btn-agricultural {
    @apply border-2 border-black;
  }

  .card-agricultural {
    @apply border-2 border-black;
  }

  .input-agricultural {
    @apply border-2 border-black;
  }
}
