import React from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    Alert,
    ActionSheetIOS,
    Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../../design-system';
import { useVoiceStore } from '../../stores/voice';

interface PostActionsProps {
    postId: string;
    isOwnPost: boolean;
    onEdit?: () => void;
    onDelete?: () => void;
    onReport?: (reason: string, description?: string) => void;
    onShare?: () => void;
}

export function PostActions({
    postId,
    isOwnPost,
    onEdit,
    onDelete,
    onReport,
    onShare,
}: PostActionsProps) {
    const { speak, isVoiceEnabled } = useVoiceStore();

    const showActionSheet = () => {
        const options = [];
        const actions: (() => void)[] = [];

        if (onShare) {
            options.push('Share Post');
            actions.push(() => {
                onShare();
                if (isVoiceEnabled) speak('Post shared');
            });
        }

        if (isOwnPost) {
            if (onEdit) {
                options.push('Edit Post');
                actions.push(() => {
                    onEdit();
                    if (isVoiceEnabled) speak('Opening post editor');
                });
            }

            if (onDelete) {
                options.push('Delete Post');
                actions.push(() => {
                    Alert.alert(
                        'Delete Post',
                        'Are you sure you want to delete this post? This action cannot be undone.',
                        [
                            { text: 'Cancel', style: 'cancel' },
                            {
                                text: 'Delete',
                                style: 'destructive',
                                onPress: () => {
                                    onDelete();
                                    if (isVoiceEnabled) speak('Post deleted');
                                },
                            },
                        ]
                    );
                });
            }
        } else {
            if (onReport) {
                options.push('Report Post');
                actions.push(() => showReportOptions());
            }
        }

        options.push('Cancel');

        if (Platform.OS === 'ios') {
            ActionSheetIOS.showActionSheetWithOptions(
                {
                    options,
                    cancelButtonIndex: options.length - 1,
                    destructiveButtonIndex: isOwnPost && onDelete ? options.indexOf('Delete Post') : undefined,
                },
                (buttonIndex) => {
                    if (buttonIndex < actions.length) {
                        actions[buttonIndex]();
                    }
                }
            );
        } else {
            // For Android, show a custom alert
            Alert.alert(
                'Post Actions',
                'Choose an action',
                [
                    ...options.slice(0, -1).map((option, index) => ({
                        text: option,
                        onPress: actions[index],
                        style: option === 'Delete Post' ? 'destructive' as const : 'default' as const,
                    })),
                    { text: 'Cancel', style: 'cancel' as const },
                ]
            );
        }
    };

    const showReportOptions = () => {
        if (!onReport) return;

        Alert.alert(
            'Report Post',
            'Why are you reporting this post?',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Spam',
                    onPress: () => {
                        onReport('spam', 'This post appears to be spam');
                        if (isVoiceEnabled) speak('Post reported as spam');
                    },
                },
                {
                    text: 'Inappropriate Content',
                    onPress: () => {
                        onReport('inappropriate', 'This post contains inappropriate content');
                        if (isVoiceEnabled) speak('Post reported as inappropriate');
                    },
                },
                {
                    text: 'Misinformation',
                    onPress: () => {
                        onReport('misinformation', 'This post contains false or misleading information');
                        if (isVoiceEnabled) speak('Post reported as misinformation');
                    },
                },
                {
                    text: 'Harassment',
                    onPress: () => {
                        onReport('harassment', 'This post contains harassment or bullying');
                        if (isVoiceEnabled) speak('Post reported for harassment');
                    },
                },
                {
                    text: 'Other',
                    onPress: () => {
                        onReport('other', 'Other reason');
                        if (isVoiceEnabled) speak('Post reported');
                    },
                },
            ]
        );
    };

    return (
        <TouchableOpacity
            onPress={showActionSheet}
            style={{
                padding: 8,
            }}
            accessibilityLabel="Post actions menu"
        >
            <Ionicons name="ellipsis-horizontal" size={20} color={colors.earth[500]} />
        </TouchableOpacity>
    );
}

// Component for displaying post engagement metrics
export function PostEngagement({
    views,
    likes,
    comments,
    shares,
}: {
    views: number;
    likes: number;
    comments: number;
    shares: number;
}) {
    const formatCount = (count: number) => {
        if (count < 1000) return count.toString();
        if (count < 1000000) return `${(count / 1000).toFixed(1)}K`;
        return `${(count / 1000000).toFixed(1)}M`;
    };

    return (
        <View style={{
            flexDirection: 'row',
            justifyContent: 'space-around',
            paddingVertical: 8,
            paddingHorizontal: 16,
            backgroundColor: colors.primary[50],
            borderRadius: 8,
            marginTop: 8,
        }}>
            {views > 0 && (
                <View style={{ alignItems: 'center' }}>
                    <Ionicons name="eye-outline" size={16} color={colors.earth[500]} />
                    <Text style={{
                        fontSize: 12,
                        color: colors.earth[500],
                        marginTop: 2,
                    }}>
                        {formatCount(views)}
                    </Text>
                </View>
            )}

            <View style={{ alignItems: 'center' }}>
                <Ionicons name="heart-outline" size={16} color={colors.earth[500]} />
                <Text style={{
                    fontSize: 12,
                    color: colors.earth[500],
                    marginTop: 2,
                }}>
                    {formatCount(likes)}
                </Text>
            </View>

            <View style={{ alignItems: 'center' }}>
                <Ionicons name="chatbubble-outline" size={16} color={colors.earth[500]} />
                <Text style={{
                    fontSize: 12,
                    color: colors.earth[500],
                    marginTop: 2,
                }}>
                    {formatCount(comments)}
                </Text>
            </View>

            <View style={{ alignItems: 'center' }}>
                <Ionicons name="share-outline" size={16} color={colors.earth[500]} />
                <Text style={{
                    fontSize: 12,
                    color: colors.earth[500],
                    marginTop: 2,
                }}>
                    {formatCount(shares)}
                </Text>
            </View>
        </View>
    );
}