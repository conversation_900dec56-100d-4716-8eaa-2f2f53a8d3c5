import { ChatMessage, ChatContext, AIService } from '../../types/ai';
import { GoogleGenerativeAI } from '@google/generative-ai';

export class MockChatService implements AIService {
    async sendMessage(message: string, context?: ChatContext): Promise<string> {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Generate contextual response based on category and message
        return this.generateResponse(message, context);
    }

    async analyzeImage(imageUri: string, category: string): Promise<any> {
        // This would be handled by the image analysis service
        throw new Error('Use ImageAnalysisService for image analysis');
    }

    private generateResponse(message: string, context?: ChatContext): string {
        const category = context?.category;
        const lowerMessage = message.toLowerCase();

        // Category-specific responses
        if (category === 'plant-health') {
            if (lowerMessage.includes('أصفر') || lowerMessage.includes('yellow')) {
                return 'اصفرار الأوراق قد يكون بسبب عدة عوامل:\n\n• نقص النيتروجين - الأوراق السفلية تصفر أولاً\n• زيادة الري - يؤدي إلى تعفن الجذور\n• نقص الحديد - اصفرار بين العروق\n• الأمراض الفطرية\n\nهل يمكنك وصف موقع الاصفرار بدقة أكثر؟';
            }
            if (lowerMessage.includes('بقع') || lowerMessage.includes('spots')) {
                return 'البقع على الأوراق تشير عادة إلى:\n\n• الأمراض الفطرية مثل اللفحة المبكرة\n• الأمراض البكتيرية\n• حروق الشمس\n• رش المبيدات في الوقت الخاطئ\n\nلون البقع وشكلها مهم للتشخيص الدقيق. هل يمكنك التقاط صورة للتشخيص الدقيق؟';
            }
            if (lowerMessage.includes('ذابل') || lowerMessage.includes('wilted')) {
                return 'الذبول قد يكون بسبب:\n\n• نقص الماء - تحقق من رطوبة التربة\n• زيادة الماء - يؤدي إلى تعفن الجذور\n• الحرارة العالية\n• الأمراض الجذرية\n• نقل النبات حديثاً\n\nتحقق من رطوبة التربة أولاً وأخبرني بالنتيجة.';
            }
        }

        if (category === 'soil') {
            if (lowerMessage.includes('حموضة') || lowerMessage.includes('ph')) {
                return 'لاختبار حموضة التربة:\n\n• استخدم جهاز قياس pH الرقمي\n• أو شرائط اختبار pH\n• أو خذ عينة لمختبر زراعي\n\nالمستوى المثالي لمعظم النباتات: 6.0-7.0\n\n• أقل من 6.0: تربة حمضية - أضف الجير\n• أكثر من 7.5: تربة قلوية - أضف الكبريت\n\nهل تعرف نوع النباتات التي تزرعها؟';
            }
            if (lowerMessage.includes('صلبة') || lowerMessage.includes('hard')) {
                return 'لتحسين التربة الصلبة:\n\n• أضف الكومبوست العضوي\n• اخلط رمل خشن (ليس ناعم)\n• أضف البيرلايت أو الفيرميكوليت\n• احرث التربة بعمق 20-30 سم\n• تجنب المشي على التربة المبللة\n\nالتحسين يحتاج وقت، ابدأ بإضافة 5-10 سم من الكومبوست سنوياً.';
            }
        }

        if (category === 'fertilizer') {
            if (lowerMessage.includes('متى') || lowerMessage.includes('when')) {
                return 'توقيت التسميد يعتمد على:\n\n• نوع النبات والموسم\n• مرحلة النمو\n• نوع السماد\n\nالقواعد العامة:\n• الربيع: سماد نيتروجيني للنمو\n• الصيف: سماد متوازن\n• الخريف: سماد فوسفوري وبوتاسي\n• تجنب التسميد في الشتاء\n\nما نوع النباتات التي تريد تسميدها؟';
            }
            if (lowerMessage.includes('طماطم') || lowerMessage.includes('tomato')) {
                return 'تسميد الطماطم:\n\n• البداية: سماد عالي النيتروجين (NPK 10-5-5)\n• الإزهار: سماد متوازن (NPK 10-10-10)\n• الإثمار: سماد عالي البوتاسيوم (NPK 5-10-15)\n\nالجدولة:\n• كل أسبوعين في موسم النمو\n• أضف الكالسيوم لمنع تعفن الطرف القمي\n• تجنب الإفراط في النيتروجين أثناء الإثمار';
            }
        }

        if (category === 'pests') {
            if (lowerMessage.includes('طبيعي') || lowerMessage.includes('natural')) {
                return 'طرق مكافحة الآفات الطبيعية:\n\n• الصابون المبيد: ملعقة صابون + لتر ماء\n• زيت النيم: مبيد طبيعي فعال\n• الثوم والفلفل: طارد طبيعي\n• النباتات المصاحبة: الريحان، النعناع\n• الحشرات المفيدة: أبو العيد، العناكب\n\nالوقاية أفضل من العلاج - حافظ على نظافة الحديقة وتهوية جيدة.';
            }
        }

        // General responses
        if (lowerMessage.includes('شكر') || lowerMessage.includes('thank')) {
            return 'عفواً! أنا هنا لمساعدتك في رحلتك الزراعية. لا تتردد في السؤال عن أي شيء متعلق بالزراعة. 🌱';
        }

        // Default response
        return `شكراً لسؤالك عن "${message}". بناءً على خبرتي الزراعية، إليك نصيحتي:\n\n${this.getGeneralAdvice(lowerMessage)}\n\nهل تحتاج لمزيد من التفاصيل حول هذا الموضوع؟ أو يمكنك التقاط صورة للحصول على تشخيص أكثر دقة.`;
    }

    private getGeneralAdvice(message: string): string {
        if (message.includes('ماء') || message.includes('ري')) {
            return 'الري المناسب أساسي للنباتات الصحية. تحقق من رطوبة التربة بإدخال إصبعك 2-3 سم في التربة. إذا كانت جافة، فقد حان وقت الري.';
        }
        if (message.includes('سماد')) {
            return 'التسميد المتوازن مهم للنمو الصحي. استخدم سماد NPK متوازن واتبع التعليمات على العبوة. لا تفرط في التسميد.';
        }
        if (message.includes('مرض') || message.includes('آفة')) {
            return 'الوقاية خير من العلاج. حافظ على نظافة النباتات، وفر تهوية جيدة، وتجنب الري على الأوراق.';
        }
        return 'هذا سؤال مهم في الزراعة. للحصول على إجابة أكثر دقة، حدد نوع النبات والمشكلة بوضوح أكثر.';
    }
}

// OpenAI Chat API integration (placeholder for future implementation)
export class OpenAIChatService implements AIService {
    private apiKey: string;

    constructor(apiKey: string) {
        this.apiKey = apiKey;
    }

    async sendMessage(message: string, context?: ChatContext): Promise<string> {
        try {
            // TODO: Implement actual OpenAI Chat API integration
            // For now, fall back to mock service
            const mockService = new MockChatService();
            return mockService.sendMessage(message, context);
        } catch (error) {
            console.error('OpenAI service error:', error);
            throw error;
        }
    }

    async analyzeImage(imageUri: string, category: string): Promise<any> {
        throw new Error('Use ImageAnalysisService for image analysis');
    }
}

// Gemini Chat API integration with full implementation
export class GeminiChatService implements AIService {
    private genAI: GoogleGenerativeAI;
    private model: any;

    constructor(apiKey: string) {
        this.genAI = new GoogleGenerativeAI(apiKey);
        this.model = this.genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    }

    async sendMessage(message: string, context?: ChatContext): Promise<string> {
        try {
            const agriculturalPrompt = this.buildAgriculturalPrompt(message, context);

            const result = await this.model.generateContent(agriculturalPrompt);
            const response = await result.response;
            const text = response.text();

            return this.formatResponse(text);
        } catch (error) {
            console.error('Gemini service error:', error);
            // Fallback to mock service on error
            const mockService = new MockChatService();
            return mockService.sendMessage(message, context);
        }
    }

    async analyzeImage(imageUri: string, category: string): Promise<any> {
        throw new Error('Use ImageAnalysisService for image analysis');
    }

    private buildAgriculturalPrompt(message: string, context?: ChatContext): string {
        const basePrompt = `أنت مساعد زراعي خبير متخصص في الزراعة في المنطقة العربية والشرق الأوسط. 
        تقدم نصائح عملية ومفيدة للمزارعين باللغة العربية.
        
        المعلومات السياقية:`;

        let contextInfo = '';
        if (context?.category) {
            const categoryMap: { [key: string]: string } = {
                'plant-health': 'صحة النباتات والأمراض النباتية',
                'soil': 'تحليل التربة وتحسينها',
                'fertilizer': 'التسميد والعناصر الغذائية',
                'pests': 'مكافحة الآفات والحشرات'
            };
            contextInfo += `\n- التخصص: ${categoryMap[context.category] || 'استشارة زراعية عامة'}`;
        }

        if (context?.userProfile) {
            if (context.userProfile.cropTypes?.length > 0) {
                contextInfo += `\n- المحاصيل: ${context.userProfile.cropTypes.join(', ')}`;
            }
            if (context.userProfile.location) {
                contextInfo += `\n- الموقع: ${context.userProfile.location}`;
            }
            if (context.userProfile.experienceLevel) {
                const levelMap: { [key: string]: string } = {
                    'beginner': 'مبتدئ',
                    'intermediate': 'متوسط',
                    'expert': 'خبير'
                };
                contextInfo += `\n- مستوى الخبرة: ${levelMap[context.userProfile.experienceLevel]}`;
            }
        }

        const instructions = `
        
        تعليمات الإجابة:
        1. اجب باللغة العربية فقط
        2. قدم نصائح عملية وقابلة للتطبيق
        3. اذكر أسماء المنتجات أو الأدوية الزراعية المحددة عند الحاجة
        4. اعتبر المناخ والظروف البيئية في المنطقة العربية
        5. قدم خطوات واضحة ومرقمة عند الحاجة
        6. اذكر التوقيت المناسب للتطبيق
        7. حذر من المخاطر إن وجدت
        
        السؤال: ${message}`;

        return basePrompt + contextInfo + instructions;
    }

    private formatResponse(text: string): string {
        // Clean up and format the response
        return text
            .trim()
            .replace(/\*\*(.*?)\*\*/g, '$1') // Remove markdown bold
            .replace(/\*(.*?)\*/g, '$1') // Remove markdown italic
            .replace(/\n{3,}/g, '\n\n'); // Limit consecutive newlines
    }
}

// High-availability AI service with automatic fallback
export class HighAvailabilityAIService implements AIService {
    private primaryService: AIService;
    private fallbackService: AIService;
    private mockService: AIService;

    constructor(
        primaryService: AIService,
        fallbackService: AIService
    ) {
        this.primaryService = primaryService;
        this.fallbackService = fallbackService;
        this.mockService = new MockChatService();
    }

    async sendMessage(message: string, context?: ChatContext): Promise<string> {
        // Try primary service first
        try {
            const response = await this.primaryService.sendMessage(message, context);
            return this.addConfidenceScore(response, 'primary');
        } catch (primaryError) {
            console.warn('Primary AI service failed, trying fallback:', primaryError);

            // Try fallback service
            try {
                const response = await this.fallbackService.sendMessage(message, context);
                return this.addConfidenceScore(response, 'fallback');
            } catch (fallbackError) {
                console.warn('Fallback AI service failed, using mock:', fallbackError);

                // Use mock service as last resort
                const response = await this.mockService.sendMessage(message, context);
                return this.addConfidenceScore(response, 'mock');
            }
        }
    }

    async analyzeImage(imageUri: string, category: string): Promise<any> {
        throw new Error('Use ImageAnalysisService for image analysis');
    }

    private addConfidenceScore(response: string, serviceType: 'primary' | 'fallback' | 'mock'): string {
        const confidenceMap = {
            primary: 'عالية',
            fallback: 'جيدة',
            mock: 'محدودة'
        };

        // Don't add confidence score to mock responses as they already have contextual info
        if (serviceType === 'mock') {
            return response;
        }

        return response + `\n\n📊 مستوى الثقة في الإجابة: ${confidenceMap[serviceType]}`;
    }
}

// Factory function to create the appropriate service with fallback logic
export function createChatService(): AIService {
    const openaiKey = process.env.EXPO_PUBLIC_OPENAI_API_KEY;
    const geminiKey = process.env.EXPO_PUBLIC_GEMINI_API_KEY;
    const mockMode = process.env.EXPO_PUBLIC_MOCK_SERVICES === 'true';

    // In mock mode, return mock service
    if (mockMode) {
        return new MockChatService();
    }

    // If both keys available, use high availability service
    if (openaiKey && geminiKey) {
        const primaryService = new OpenAIChatService(openaiKey);
        const fallbackService = new GeminiChatService(geminiKey);
        return new HighAvailabilityAIService(primaryService, fallbackService);
    }

    // If only Gemini key available, use Gemini
    if (geminiKey) {
        return new GeminiChatService(geminiKey);
    }

    // If only OpenAI key available, use OpenAI
    if (openaiKey) {
        return new OpenAIChatService(openaiKey);
    }

    // Fallback to mock service if no keys
    console.warn('No AI API keys found, using mock service');
    return new MockChatService();
}