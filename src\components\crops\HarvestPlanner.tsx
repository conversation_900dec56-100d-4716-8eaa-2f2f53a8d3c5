import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { CropPlan } from '../../types/crops';

interface HarvestWindow {
    start: Date;
    end: Date;
    optimal: Date;
    quality: 'peak' | 'good' | 'acceptable';
    description: string;
    descriptionAr: string;
}

interface HarvestPlannerProps {
    cropPlan: CropPlan;
    harvestWindows: HarvestWindow[];
    weatherForecast: {
        date: Date;
        condition: 'sunny' | 'cloudy' | 'rainy' | 'stormy';
        temperature: number;
        humidity: number;
        suitable: boolean;
    }[];
    onScheduleHarvest: (date: Date) => void;
    onHarvestComplete: (actualYield: number, quality: string, notes: string) => void;
    voiceEnabled?: boolean;
    onVoiceCommand?: (command: string) => void;
}

export default function HarvestPlanner({
    cropPlan,
    harvestWindows,
    weatherForecast,
    onScheduleHarvest,
    onHarvestComplete,
    voiceEnabled = false,
    onVoiceCommand,
}: HarvestPlannerProps) {
    const [selectedDate, setSelectedDate] = useState<Date | null>(null);
    const [viewMode, setViewMode] = useState<'calendar' | 'weather' | 'tips'>('calendar');

    const getQualityColor = (quality: HarvestWindow['quality']): string => {
        switch (quality) {
            case 'peak': return 'text-green-600 bg-green-50 border-green-200';
            case 'good': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
            case 'acceptable': return 'text-orange-600 bg-orange-50 border-orange-200';
            default: return 'text-gray-600 bg-gray-50 border-gray-200';
        }
    };

    const getWeatherIcon = (condition: string): string => {
        const icons = {
            sunny: '☀️',
            cloudy: '☁️',
            rainy: '🌧️',
            stormy: '⛈️',
        };
        return icons[condition as keyof typeof icons] || '🌤️';
    };

    const getDaysUntilHarvest = (date: Date): number => {
        const today = new Date();
        const diffTime = date.getTime() - today.getTime();
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    };

    const isDateInWindow = (date: Date, window: HarvestWindow): boolean => {
        return date >= window.start && date <= window.end;
    };

    const getOptimalHarvestDays = (): Date[] => {
        const optimalDays: Date[] = [];
        const today = new Date();

        for (let i = 0; i < 14; i++) {
            const checkDate = new Date(today);
            checkDate.setDate(today.getDate() + i);

            const inWindow = harvestWindows.some(window => isDateInWindow(checkDate, window));
            const goodWeather = weatherForecast.find(w =>
                w.date.toDateString() === checkDate.toDateString()
            )?.suitable;

            if (inWindow && goodWeather) {
                optimalDays.push(checkDate);
            }
        }

        return optimalDays;
    };

    const handleVoiceCommand = () => {
        if (onVoiceCommand) {
            onVoiceCommand('harvest_planning');
        }
    };

    const handleScheduleHarvest = () => {
        if (!selectedDate) {
            Alert.alert('No Date Selected', 'Please select a harvest date first.');
            return;
        }

        Alert.alert(
            'Schedule Harvest',
            `Schedule harvest for ${selectedDate.toLocaleDateString()}?`,
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Schedule',
                    onPress: () => onScheduleHarvest(selectedDate),
                },
            ]
        );
    };

    const renderCalendarView = () => {
        const optimalDays = getOptimalHarvestDays();

        return (
            <View className="gap-4">
                {/* Harvest Windows */}
                <View className="bg-white rounded-lg border border-gray-200 p-4">
                    <Text className="text-lg font-semibold text-gray-900 mb-3">
                        🗓️ Harvest Windows
                    </Text>

                    {harvestWindows.map((window, index) => {
                        const daysUntil = getDaysUntilHarvest(window.start);
                        const isActive = new Date() >= window.start && new Date() <= window.end;

                        return (
                            <View
                                key={index}
                                className={`p-3 rounded-lg border mb-3 ${isActive ? 'border-green-300 bg-green-50' : 'border-gray-200'
                                    }`}
                            >
                                <View className="flex-row items-center justify-between mb-2">
                                    <View className={`px-2 py-1 rounded-full border ${getQualityColor(window.quality)}`}>
                                        <Text className="text-xs font-medium capitalize">
                                            {window.quality} Quality
                                        </Text>
                                    </View>

                                    {isActive && (
                                        <View className="bg-green-500 px-2 py-1 rounded-full">
                                            <Text className="text-white text-xs font-medium">
                                                Active Now
                                            </Text>
                                        </View>
                                    )}
                                </View>

                                <Text className="text-sm text-gray-700 mb-2">
                                    {window.description}
                                </Text>

                                <View className="flex-row justify-between text-sm">
                                    <Text className="text-gray-600">
                                        Start: {window.start.toLocaleDateString()}
                                    </Text>
                                    <Text className="text-gray-600">
                                        End: {window.end.toLocaleDateString()}
                                    </Text>
                                </View>

                                <Text className="text-sm font-medium text-green-600 mt-1">
                                    Optimal: {window.optimal.toLocaleDateString()}
                                    {daysUntil > 0 && ` (in ${daysUntil} days)`}
                                </Text>
                            </View>
                        );
                    })}
                </View>

                {/* Optimal Days */}
                {optimalDays.length > 0 && (
                    <View className="bg-white rounded-lg border border-gray-200 p-4">
                        <Text className="text-lg font-semibold text-gray-900 mb-3">
                            ⭐ Optimal Harvest Days
                        </Text>

                        <View className="flex-row flex-wrap gap-2">
                            {optimalDays.map((day, index) => (
                                <TouchableOpacity
                                    key={index}
                                    onPress={() => setSelectedDate(day)}
                                    className={`px-3 py-2 rounded-lg border ${selectedDate?.toDateString() === day.toDateString()
                                        ? 'border-green-500 bg-green-100'
                                        : 'border-green-200 bg-green-50'
                                        }`}
                                    accessibilityLabel={`Select ${day.toLocaleDateString()} for harvest`}
                                >
                                    <Text className={`text-sm font-medium ${selectedDate?.toDateString() === day.toDateString()
                                        ? 'text-green-700'
                                        : 'text-green-600'
                                        }`}>
                                        {day.toLocaleDateString('en-US', {
                                            month: 'short',
                                            day: 'numeric',
                                        })}
                                    </Text>
                                </TouchableOpacity>
                            ))}
                        </View>

                        <Text className="text-sm text-gray-600 mt-3">
                            These days combine optimal harvest timing with favorable weather conditions.
                        </Text>
                    </View>
                )}

                {/* Schedule Button */}
                {selectedDate && (
                    <View className="bg-white rounded-lg border border-gray-200 p-4">
                        <Text className="text-lg font-semibold text-gray-900 mb-2">
                            Selected Date
                        </Text>
                        <Text className="text-base text-gray-700 mb-3">
                            {selectedDate.toLocaleDateString('en-US', {
                                weekday: 'long',
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                            })}
                        </Text>

                        <TouchableOpacity
                            onPress={handleScheduleHarvest}
                            className="bg-green-500 py-3 px-4 rounded-lg"
                            accessibilityLabel="Schedule harvest"
                        >
                            <Text className="text-white text-center font-medium">
                                📅 Schedule Harvest
                            </Text>
                        </TouchableOpacity>
                    </View>
                )}
            </View>
        );
    };

    const renderWeatherView = () => (
        <View className="bg-white rounded-lg border border-gray-200 p-4">
            <Text className="text-lg font-semibold text-gray-900 mb-3">
                🌤️ Weather Forecast
            </Text>

            <View className="gap-3">
                {weatherForecast.slice(0, 7).map((forecast, index) => (
                    <View
                        key={index}
                        className={`flex-row items-center justify-between p-3 rounded-lg border ${forecast.suitable
                            ? 'border-green-200 bg-green-50'
                            : 'border-red-200 bg-red-50'
                            }`}
                    >
                        <View className="flex-row items-center flex-1">
                            <Text className="text-2xl mr-3">
                                {getWeatherIcon(forecast.condition)}
                            </Text>
                            <View>
                                <Text className="font-medium text-gray-900">
                                    {forecast.date.toLocaleDateString('en-US', {
                                        weekday: 'short',
                                        month: 'short',
                                        day: 'numeric',
                                    })}
                                </Text>
                                <Text className="text-sm text-gray-600 capitalize">
                                    {forecast.condition}
                                </Text>
                            </View>
                        </View>

                        <View className="items-end">
                            <Text className="font-medium text-gray-900">
                                {forecast.temperature}°C
                            </Text>
                            <Text className="text-sm text-gray-600">
                                {forecast.humidity}% humidity
                            </Text>
                        </View>

                        <View className="ml-3">
                            {forecast.suitable ? (
                                <View className="bg-green-500 rounded-full p-1">
                                    <Text className="text-white text-xs">✓</Text>
                                </View>
                            ) : (
                                <View className="bg-red-500 rounded-full p-1">
                                    <Text className="text-white text-xs">✗</Text>
                                </View>
                            )}
                        </View>
                    </View>
                ))}
            </View>

            <View className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <Text className="text-sm font-medium text-blue-800 mb-1">
                    Weather Guidelines
                </Text>
                <Text className="text-sm text-blue-700">
                    • Avoid harvesting during or right after rain
                </Text>
                <Text className="text-sm text-blue-700">
                    • Best conditions: sunny, low humidity, mild temperature
                </Text>
                <Text className="text-sm text-blue-700">
                    • Early morning harvest often provides best quality
                </Text>
            </View>
        </View>
    );

    const renderTipsView = () => (
        <View className="gap-4">
            <View className="bg-white rounded-lg border border-gray-200 p-4">
                <Text className="text-lg font-semibold text-gray-900 mb-3">
                    💡 Harvest Tips
                </Text>

                <View className="gap-3">
                    <View className="flex-row items-start">
                        <Text className="text-2xl mr-3">⏰</Text>
                        <View className="flex-1">
                            <Text className="font-medium text-gray-900 mb-1">
                                Best Time of Day
                            </Text>
                            <Text className="text-sm text-gray-600">
                                Harvest in early morning when temperatures are cool and plants are well-hydrated.
                            </Text>
                        </View>
                    </View>

                    <View className="flex-row items-start">
                        <Text className="text-2xl mr-3">🔪</Text>
                        <View className="flex-1">
                            <Text className="font-medium text-gray-900 mb-1">
                                Proper Tools
                            </Text>
                            <Text className="text-sm text-gray-600">
                                Use clean, sharp tools to avoid damaging plants and prevent disease spread.
                            </Text>
                        </View>
                    </View>

                    <View className="flex-row items-start">
                        <Text className="text-2xl mr-3">🧊</Text>
                        <View className="flex-1">
                            <Text className="font-medium text-gray-900 mb-1">
                                Post-Harvest Handling
                            </Text>
                            <Text className="text-sm text-gray-600">
                                Cool harvested produce quickly and store in appropriate conditions to maintain quality.
                            </Text>
                        </View>
                    </View>

                    <View className="flex-row items-start">
                        <Text className="text-2xl mr-3">📊</Text>
                        <View className="flex-1">
                            <Text className="font-medium text-gray-900 mb-1">
                                Record Keeping
                            </Text>
                            <Text className="text-sm text-gray-600">
                                Document harvest dates, yields, and quality to improve future planning.
                            </Text>
                        </View>
                    </View>
                </View>
            </View>

            {/* Crop-Specific Tips */}
            <View className="bg-white rounded-lg border border-gray-200 p-4">
                <Text className="text-lg font-semibold text-gray-900 mb-3">
                    🌱 {cropPlan.cropName} Specific Tips
                </Text>

                <View className="gap-2">
                    {/* These would be dynamically generated based on crop type */}
                    <Text className="text-sm text-gray-700">
                        • Check for proper ripeness indicators before harvesting
                    </Text>
                    <Text className="text-sm text-gray-700">
                        • Handle produce gently to avoid bruising
                    </Text>
                    <Text className="text-sm text-gray-700">
                        • Harvest regularly to encourage continued production
                    </Text>
                    <Text className="text-sm text-gray-700">
                        • Store at optimal temperature and humidity levels
                    </Text>
                </View>
            </View>

            {/* Quality Indicators */}
            <View className="bg-white rounded-lg border border-gray-200 p-4">
                <Text className="text-lg font-semibold text-gray-900 mb-3">
                    🎯 Quality Indicators
                </Text>

                <View className="gap-3">
                    <View className="flex-row items-center">
                        <View className="w-3 h-3 bg-green-500 rounded-full mr-3" />
                        <Text className="text-sm text-gray-700">
                            Peak Quality: Optimal color, size, and firmness
                        </Text>
                    </View>
                    <View className="flex-row items-center">
                        <View className="w-3 h-3 bg-yellow-500 rounded-full mr-3" />
                        <Text className="text-sm text-gray-700">
                            Good Quality: Slight variations but still marketable
                        </Text>
                    </View>
                    <View className="flex-row items-center">
                        <View className="w-3 h-3 bg-orange-500 rounded-full mr-3" />
                        <Text className="text-sm text-gray-700">
                            Acceptable: May have minor defects, suitable for processing
                        </Text>
                    </View>
                </View>
            </View>
        </View>
    );

    return (
        <View className="flex-1 bg-gray-50">
            {/* Header */}
            <View className="bg-white border-b border-gray-200 p-4">
                <View className="flex-row items-center justify-between mb-3">
                    <Text className="text-xl font-bold text-gray-900">
                        Harvest Planning
                    </Text>

                    {voiceEnabled && (
                        <TouchableOpacity
                            onPress={handleVoiceCommand}
                            className="p-2 bg-green-500 rounded-full"
                            accessibilityLabel="Voice commands"
                        >
                            <Text className="text-white text-sm">🎤</Text>
                        </TouchableOpacity>
                    )}
                </View>

                {/* View Mode Toggle */}
                <View className="flex-row bg-gray-100 rounded-lg p-1">
                    {[
                        { key: 'calendar', label: 'Calendar', icon: '📅' },
                        { key: 'weather', label: 'Weather', icon: '🌤️' },
                        { key: 'tips', label: 'Tips', icon: '💡' },
                    ].map(({ key, label, icon }) => (
                        <TouchableOpacity
                            key={key}
                            onPress={() => setViewMode(key as any)}
                            className={`flex-1 px-3 py-2 rounded-md ${viewMode === key ? 'bg-white shadow-sm' : ''
                                }`}
                            accessibilityLabel={`${label} view`}
                        >
                            <Text className={`text-sm font-medium text-center ${viewMode === key ? 'text-gray-900' : 'text-gray-600'
                                }`}>
                                {icon} {label}
                            </Text>
                        </TouchableOpacity>
                    ))}
                </View>
            </View>

            <ScrollView className="flex-1 p-4">
                {viewMode === 'calendar' && renderCalendarView()}
                {viewMode === 'weather' && renderWeatherView()}
                {viewMode === 'tips' && renderTipsView()}
            </ScrollView>

            {/* Voice Commands Help */}
            {voiceEnabled && (
                <View className="bg-blue-50 border-t border-blue-200 p-3">
                    <Text className="text-sm font-semibold text-blue-800 mb-1">
                        🗣️ Voice Commands
                    </Text>
                    <Text className="text-xs text-blue-700">
                        "Schedule harvest" • "Check weather" • "Show tips" • "Optimal days"
                    </Text>
                </View>
            )}
        </View>
    );
}