import { Task, TaskCompletion, DailyTaskSummary, TaskFilter, TaskSort } from '../types/tasks';
import { CropPlan } from '../types/crops';
import { WeatherService, WeatherData } from './weather';
import { LocationCoordinates } from './location';

export class TaskService {
    private static instance: TaskService;
    private tasks: Task[] = [];
    private weatherService = WeatherService.getInstance();

    static getInstance(): TaskService {
        if (!TaskService.instance) {
            TaskService.instance = new TaskService();
        }
        return TaskService.instance;
    }

    constructor() {
        // Initialize with sample tasks for development
        this.initializeSampleTasks();
    }

    private initializeSampleTasks(): void {
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        this.tasks = [
            {
                id: '1',
                title: 'Water tomato plants',
                description: 'Check soil moisture and water tomato plants in greenhouse section A',
                dueDate: today,
                type: 'watering',
                completed: false,
                pointsReward: 10,
                priority: 'high',
                estimatedDuration: 30,
                instructions: [
                    'Check soil moisture with finger test',
                    'Water slowly at base of plants',
                    'Avoid getting leaves wet',
                    'Apply 2-3 liters per plant'
                ],
            },
            {
                id: '2',
                title: 'Monitor corn growth',
                description: 'Check corn plants for signs of pests or disease',
                dueDate: today,
                type: 'monitoring',
                completed: false,
                pointsReward: 15,
                priority: 'medium',
                estimatedDuration: 45,
                instructions: [
                    'Inspect leaves for discoloration',
                    'Check for pest damage',
                    'Measure plant height',
                    'Take photos if issues found'
                ],
            },
            {
                id: '3',
                title: 'Apply fertilizer to peppers',
                description: 'Apply organic fertilizer to pepper plants',
                dueDate: today,
                type: 'fertilizing',
                completed: true,
                pointsReward: 20,
                priority: 'medium',
                estimatedDuration: 60,
                completedAt: new Date(today.getTime() - 2 * 60 * 60 * 1000), // 2 hours ago
                instructions: [
                    'Use organic compost fertilizer',
                    'Apply around base of plants',
                    'Water lightly after application',
                    'Record application date'
                ],
            },
            {
                id: '4',
                title: 'Harvest ripe tomatoes',
                description: 'Collect ripe tomatoes from plants in section B',
                dueDate: tomorrow,
                type: 'harvesting',
                completed: false,
                pointsReward: 25,
                priority: 'high',
                estimatedDuration: 90,
                instructions: [
                    'Pick only fully red tomatoes',
                    'Handle gently to avoid bruising',
                    'Sort by size and quality',
                    'Store in cool, dry place'
                ],
            },
        ];
    }

    async getTodaysTasks(): Promise<Task[]> {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        return this.tasks.filter(task => {
            const taskDate = new Date(task.dueDate);
            taskDate.setHours(0, 0, 0, 0);
            return taskDate.getTime() === today.getTime();
        });
    }

    async getTaskById(id: string): Promise<Task | null> {
        return this.tasks.find(task => task.id === id) || null;
    }

    async completeTask(taskId: string, completion: Partial<TaskCompletion>): Promise<boolean> {
        const taskIndex = this.tasks.findIndex(task => task.id === taskId);
        if (taskIndex === -1) return false;

        this.tasks[taskIndex] = {
            ...this.tasks[taskIndex],
            completed: true,
            completedAt: completion.completedAt || new Date(),
        };

        return true;
    }

    async uncompleteTask(taskId: string): Promise<boolean> {
        const taskIndex = this.tasks.findIndex(task => task.id === taskId);
        if (taskIndex === -1) return false;

        this.tasks[taskIndex] = {
            ...this.tasks[taskIndex],
            completed: false,
            completedAt: undefined,
        };

        return true;
    }

    async getDailyTaskSummary(date: Date = new Date()): Promise<DailyTaskSummary> {
        const targetDate = new Date(date);
        targetDate.setHours(0, 0, 0, 0);

        const nextDay = new Date(targetDate);
        nextDay.setDate(nextDay.getDate() + 1);

        const dayTasks = this.tasks.filter(task => {
            const taskDate = new Date(task.dueDate);
            taskDate.setHours(0, 0, 0, 0);
            return taskDate.getTime() === targetDate.getTime();
        });

        const completedTasks = dayTasks.filter(task => task.completed);
        const pointsEarned = completedTasks.reduce((sum, task) => sum + task.pointsReward, 0);

        return {
            date: targetDate,
            totalTasks: dayTasks.length,
            completedTasks: completedTasks.length,
            pendingTasks: dayTasks.length - completedTasks.length,
            pointsEarned,
            tasks: dayTasks,
        };
    }

    getTaskIcon(type: Task['type']): string {
        const icons = {
            watering: '💧',
            fertilizing: '🌱',
            monitoring: '👁️',
            harvesting: '🌾',
            planting: '🌱',
            pest_control: '🐛',
        };
        return icons[type] || '📋';
    }

    getTaskPriorityColor(priority: Task['priority']): string {
        const colors = {
            low: 'text-green-600',
            medium: 'text-yellow-600',
            high: 'text-red-600',
        };
        return colors[priority];
    }

    formatDuration(minutes: number): string {
        if (minutes < 60) {
            return `${minutes}m`;
        }
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
    }

    isTaskOverdue(task: Task): boolean {
        if (task.completed) return false;
        const now = new Date();
        return task.dueDate < now;
    }

    getTaskStatusText(task: Task): string {
        if (task.completed) return 'Completed';
        if (this.isTaskOverdue(task)) return 'Overdue';
        return 'Pending';
    }

    /**
     * Generate daily tasks based on crop plans and weather conditions
     */
    async generateDailyTasks(
        cropPlans: CropPlan[],
        location: LocationCoordinates,
        targetDate: Date = new Date()
    ): Promise<Task[]> {
        const weatherData = await this.weatherService.getCompleteWeatherData(location);
        const generatedTasks: Task[] = [];

        for (const cropPlan of cropPlans) {
            if (cropPlan.status !== 'active') continue;

            const cropTasks = await this.generateTasksForCropAndDate(
                cropPlan,
                targetDate,
                weatherData
            );
            generatedTasks.push(...cropTasks);
        }

        // Add weather-responsive adjustments
        const adjustedTasks = this.adjustTasksForWeather(generatedTasks, weatherData);

        // Add to internal tasks array
        adjustedTasks.forEach(task => {
            if (!this.tasks.find(t => t.id === task.id)) {
                this.tasks.push(task);
            }
        });

        return adjustedTasks;
    }

    /**
     * Generate tasks for a specific crop plan and date
     */
    private async generateTasksForCropAndDate(
        cropPlan: CropPlan,
        targetDate: Date,
        weatherData?: WeatherData | null
    ): Promise<Task[]> {
        const tasks: Task[] = [];
        const plantingDate = new Date(cropPlan.plantingDate);
        const harvestDate = new Date(cropPlan.harvestDate);
        const daysSincePlanting = Math.floor(
            (targetDate.getTime() - plantingDate.getTime()) / (1000 * 60 * 60 * 24)
        );

        if (daysSincePlanting < 0 || targetDate > harvestDate) {
            return tasks; // Outside growing period
        }

        // Determine growth stage
        const growthStage = this.determineGrowthStage(daysSincePlanting, plantingDate, harvestDate);

        // Generate stage-appropriate tasks
        switch (growthStage) {
            case 'germination':
                tasks.push(...this.generateGerminationTasks(cropPlan, targetDate, weatherData));
                break;
            case 'vegetative':
                tasks.push(...this.generateVegetativeTasks(cropPlan, targetDate, weatherData));
                break;
            case 'flowering':
                tasks.push(...this.generateFloweringTasks(cropPlan, targetDate, weatherData));
                break;
            case 'maturation':
                tasks.push(...this.generateMaturationTasks(cropPlan, targetDate, weatherData));
                break;
        }

        // Add routine maintenance tasks
        tasks.push(...this.generateMaintenanceTasks(cropPlan, targetDate, weatherData));

        return tasks;
    }

    /**
     * Determine growth stage based on days since planting
     */
    private determineGrowthStage(
        daysSincePlanting: number,
        plantingDate: Date,
        harvestDate: Date
    ): 'germination' | 'vegetative' | 'flowering' | 'maturation' {
        const totalDays = Math.floor(
            (harvestDate.getTime() - plantingDate.getTime()) / (1000 * 60 * 60 * 24)
        );

        const progress = daysSincePlanting / totalDays;

        if (progress < 0.15) return 'germination';
        if (progress < 0.5) return 'vegetative';
        if (progress < 0.8) return 'flowering';
        return 'maturation';
    }

    /**
     * Generate germination stage tasks
     */
    private generateGerminationTasks(
        cropPlan: CropPlan,
        targetDate: Date,
        weatherData?: WeatherData | null
    ): Task[] {
        const tasks: Task[] = [];

        // Daily monitoring during germination
        tasks.push({
            id: `${cropPlan.id}-germination-monitor-${targetDate.toISOString().split('T')[0]}`,
            title: `Monitor ${cropPlan.cropName} Germination`,
            description: 'Check for signs of germination and seedling emergence',
            dueDate: targetDate,
            type: 'monitoring',
            completed: false,
            pointsReward: 8,
            priority: 'high',
            cropPlanId: cropPlan.id,
            estimatedDuration: 15,
            instructions: [
                'Check soil surface for emerging seedlings',
                'Look for signs of seed germination',
                'Monitor soil moisture levels',
                'Check for pest activity around seeds',
            ],
        });

        // Gentle watering if needed
        if (this.shouldWaterBasedOnWeather(weatherData, 'germination')) {
            tasks.push({
                id: `${cropPlan.id}-germination-water-${targetDate.toISOString().split('T')[0]}`,
                title: `Gentle Watering for ${cropPlan.cropName}`,
                description: 'Provide gentle watering to maintain soil moisture for germination',
                dueDate: targetDate,
                type: 'watering',
                completed: false,
                pointsReward: 10,
                priority: 'medium',
                cropPlanId: cropPlan.id,
                estimatedDuration: 20,
                instructions: [
                    'Use fine spray or misting to avoid disturbing seeds',
                    'Keep soil consistently moist but not waterlogged',
                    'Water early morning or late evening',
                ],
            });
        }

        return tasks;
    }

    /**
     * Generate vegetative growth stage tasks
     */
    private generateVegetativeTasks(
        cropPlan: CropPlan,
        targetDate: Date,
        weatherData?: WeatherData | null
    ): Task[] {
        const tasks: Task[] = [];

        // Weekly fertilizing during vegetative growth
        const daysSincePlanting = Math.floor(
            (targetDate.getTime() - new Date(cropPlan.plantingDate).getTime()) / (1000 * 60 * 60 * 24)
        );

        if (daysSincePlanting % 14 === 0) { // Every 2 weeks
            tasks.push({
                id: `${cropPlan.id}-vegetative-fertilize-${targetDate.toISOString().split('T')[0]}`,
                title: `Fertilize ${cropPlan.cropName}`,
                description: 'Apply nitrogen-rich fertilizer to promote vegetative growth',
                dueDate: targetDate,
                type: 'fertilizing',
                completed: false,
                pointsReward: 15,
                priority: 'medium',
                cropPlanId: cropPlan.id,
                estimatedDuration: 30,
                instructions: [
                    'Apply balanced NPK fertilizer around plant base',
                    'Water lightly after fertilizer application',
                    'Avoid getting fertilizer on leaves',
                ],
            });
        }

        // Thinning if needed (once during vegetative stage)
        if (daysSincePlanting === 21) { // 3 weeks after planting
            tasks.push({
                id: `${cropPlan.id}-vegetative-thin-${targetDate.toISOString().split('T')[0]}`,
                title: `Thin ${cropPlan.cropName} Seedlings`,
                description: 'Remove weaker seedlings to give stronger ones more space',
                dueDate: targetDate,
                type: 'monitoring',
                completed: false,
                pointsReward: 12,
                priority: 'medium',
                cropPlanId: cropPlan.id,
                estimatedDuration: 45,
                instructions: [
                    'Identify strongest, healthiest seedlings',
                    'Remove weaker seedlings carefully',
                    'Maintain recommended spacing between plants',
                ],
            });
        }

        return tasks;
    }

    /**
     * Generate flowering stage tasks
     */
    private generateFloweringTasks(
        cropPlan: CropPlan,
        targetDate: Date,
        weatherData?: WeatherData | null
    ): Task[] {
        const tasks: Task[] = [];

        // Support structures for heavy fruiting plants
        tasks.push({
            id: `${cropPlan.id}-flowering-support-${targetDate.toISOString().split('T')[0]}`,
            title: `Support ${cropPlan.cropName} Plants`,
            description: 'Install or check support structures for flowering plants',
            dueDate: targetDate,
            type: 'monitoring',
            completed: false,
            pointsReward: 18,
            priority: 'medium',
            cropPlanId: cropPlan.id,
            estimatedDuration: 40,
            instructions: [
                'Install stakes or cages for tall plants',
                'Tie branches gently to supports',
                'Check existing supports for stability',
            ],
        });

        // Pollination assistance if needed
        if (['tomatoes', 'peppers', 'watermelon'].includes(cropPlan.cropType)) {
            tasks.push({
                id: `${cropPlan.id}-flowering-pollinate-${targetDate.toISOString().split('T')[0]}`,
                title: `Assist ${cropPlan.cropName} Pollination`,
                description: 'Help with pollination to ensure good fruit set',
                dueDate: targetDate,
                type: 'monitoring',
                completed: false,
                pointsReward: 15,
                priority: 'low',
                cropPlanId: cropPlan.id,
                estimatedDuration: 25,
                instructions: [
                    'Gently shake flowering branches',
                    'Use small brush for hand pollination if needed',
                    'Encourage beneficial insects',
                ],
            });
        }

        return tasks;
    }

    /**
     * Generate maturation stage tasks
     */
    private generateMaturationTasks(
        cropPlan: CropPlan,
        targetDate: Date,
        weatherData?: WeatherData | null
    ): Task[] {
        const tasks: Task[] = [];

        // Daily harvest readiness checks
        tasks.push({
            id: `${cropPlan.id}-maturation-check-${targetDate.toISOString().split('T')[0]}`,
            title: `Check ${cropPlan.cropName} Ripeness`,
            description: 'Monitor crops for harvest readiness',
            dueDate: targetDate,
            type: 'monitoring',
            completed: false,
            pointsReward: 20,
            priority: 'high',
            cropPlanId: cropPlan.id,
            estimatedDuration: 20,
            instructions: [
                'Check color, size, and firmness',
                'Look for harvest indicators specific to crop',
                'Test a sample if unsure about ripeness',
            ],
        });

        // Reduce watering as harvest approaches
        if (this.shouldReduceWatering(cropPlan, targetDate)) {
            tasks.push({
                id: `${cropPlan.id}-maturation-reduce-water-${targetDate.toISOString().split('T')[0]}`,
                title: `Reduce Watering for ${cropPlan.cropName}`,
                description: 'Reduce watering frequency as harvest approaches',
                dueDate: targetDate,
                type: 'watering',
                completed: false,
                pointsReward: 10,
                priority: 'low',
                cropPlanId: cropPlan.id,
                estimatedDuration: 15,
                instructions: [
                    'Water only if soil is very dry',
                    'Focus on deep, infrequent watering',
                    'Stop watering 3-5 days before harvest',
                ],
            });
        }

        return tasks;
    }

    /**
     * Generate routine maintenance tasks
     */
    private generateMaintenanceTasks(
        cropPlan: CropPlan,
        targetDate: Date,
        weatherData?: WeatherData | null
    ): Task[] {
        const tasks: Task[] = [];

        // Weekly pest and disease monitoring
        const dayOfWeek = targetDate.getDay();
        if (dayOfWeek === 1) { // Monday
            tasks.push({
                id: `${cropPlan.id}-maintenance-pest-${targetDate.toISOString().split('T')[0]}`,
                title: `Pest & Disease Check for ${cropPlan.cropName}`,
                description: 'Weekly inspection for pests and diseases',
                dueDate: targetDate,
                type: 'monitoring',
                completed: false,
                pointsReward: 12,
                priority: 'medium',
                cropPlanId: cropPlan.id,
                estimatedDuration: 30,
                instructions: [
                    'Inspect leaves for discoloration or spots',
                    'Check for insect damage or presence',
                    'Look for signs of fungal infections',
                    'Take photos of any issues found',
                ],
            });
        }

        // Weeding tasks
        if (dayOfWeek === 3) { // Wednesday
            tasks.push({
                id: `${cropPlan.id}-maintenance-weed-${targetDate.toISOString().split('T')[0]}`,
                title: `Weed Control for ${cropPlan.cropName}`,
                description: 'Remove weeds around crop plants',
                dueDate: targetDate,
                type: 'monitoring',
                completed: false,
                pointsReward: 8,
                priority: 'low',
                cropPlanId: cropPlan.id,
                estimatedDuration: 25,
                instructions: [
                    'Remove weeds by hand or with tools',
                    'Be careful not to damage crop roots',
                    'Apply mulch to prevent weed regrowth',
                ],
            });
        }

        return tasks;
    }

    /**
     * Adjust tasks based on weather conditions
     */
    private adjustTasksForWeather(tasks: Task[], weatherData?: WeatherData | null): Task[] {
        if (!weatherData?.current) return tasks;

        const weather = weatherData.current;
        const adjustedTasks = tasks.map(task => {
            const adjustedTask = { ...task };

            // Temperature adjustments
            if (weather.temperature > 35) {
                if (task.type === 'watering') {
                    adjustedTask.priority = 'high';
                    adjustedTask.pointsReward += 5;
                    adjustedTask.instructions = [
                        ...(task.instructions || []),
                        'Water early morning or late evening due to extreme heat',
                        'Provide shade protection if possible',
                    ];
                }
            } else if (weather.temperature < 10) {
                if (task.type === 'watering') {
                    adjustedTask.priority = 'low';
                    adjustedTask.instructions = [
                        ...(task.instructions || []),
                        'Water during warmer parts of the day',
                        'Reduce watering frequency in cold weather',
                    ];
                }
            }

            // Rain adjustments
            if (weatherData.forecast) {
                const rainExpected = weatherData.forecast
                    .slice(0, 2)
                    .some(day => day.precipitation > 5);

                if (rainExpected && task.type === 'watering') {
                    adjustedTask.priority = 'low';
                    adjustedTask.instructions = [
                        ...(task.instructions || []),
                        'Skip watering if heavy rain is expected within 24 hours',
                    ];
                }
            }

            // Wind adjustments
            if (weather.windSpeed > 20) {
                if (task.type === 'monitoring') {
                    adjustedTask.instructions = [
                        ...(task.instructions || []),
                        'Check plant supports due to strong winds',
                        'Secure loose materials',
                    ];
                }
            }

            return adjustedTask;
        });

        return adjustedTasks;
    }

    /**
     * Determine if watering is needed based on weather
     */
    private shouldWaterBasedOnWeather(
        weatherData?: WeatherData | null,
        growthStage?: string
    ): boolean {
        if (!weatherData?.current) return true; // Default to watering if no weather data

        const weather = weatherData.current;

        // Don't water if recent rain
        if (weatherData.forecast) {
            const recentRain = weatherData.forecast
                .slice(0, 2)
                .some(day => day.precipitation > 3);
            if (recentRain) return false;
        }

        // Water more frequently in hot, dry conditions
        if (weather.temperature > 30 && weather.humidity < 50) {
            return true;
        }

        // Water less frequently in cool, humid conditions
        if (weather.temperature < 20 && weather.humidity > 70) {
            return false;
        }

        return true; // Default to watering
    }

    /**
     * Determine if watering should be reduced
     */
    private shouldReduceWatering(cropPlan: CropPlan, targetDate: Date): boolean {
        const harvestDate = new Date(cropPlan.harvestDate);
        const daysToHarvest = Math.floor(
            (harvestDate.getTime() - targetDate.getTime()) / (1000 * 60 * 60 * 24)
        );

        return daysToHarvest <= 7; // Reduce watering in final week
    }

    /**
     * Complete task with photo evidence and point rewards
     */
    async completeTaskWithEvidence(
        taskId: string,
        completion: TaskCompletion & { imageEvidence?: string }
    ): Promise<{ success: boolean; pointsEarned: number }> {
        const taskIndex = this.tasks.findIndex(task => task.id === taskId);
        if (taskIndex === -1) {
            return { success: false, pointsEarned: 0 };
        }

        const task = this.tasks[taskIndex];
        let pointsEarned = task.pointsReward;

        // Bonus points for photo evidence
        if (completion.imageEvidence) {
            pointsEarned += 5;
        }

        // Bonus points for early completion
        const now = new Date();
        if (now < task.dueDate) {
            const hoursEarly = Math.floor((task.dueDate.getTime() - now.getTime()) / (1000 * 60 * 60));
            if (hoursEarly >= 24) {
                pointsEarned += 3; // Bonus for completing a day early
            }
        }

        this.tasks[taskIndex] = {
            ...task,
            completed: true,
            completedAt: completion.completedAt || new Date(),
            imageUrl: completion.imageEvidence,
        };

        return { success: true, pointsEarned };
    }

    /**
     * Get weekly task summary with points breakdown
     */
    async getWeeklyTaskSummary(startDate: Date): Promise<{
        totalTasks: number;
        completedTasks: number;
        totalPointsEarned: number;
        tasksByType: Record<string, number>;
        dailySummaries: DailyTaskSummary[];
    }> {
        const endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + 7);

        const weekTasks = this.tasks.filter(task => {
            const taskDate = new Date(task.dueDate);
            return taskDate >= startDate && taskDate < endDate;
        });

        const completedTasks = weekTasks.filter(task => task.completed);
        const totalPointsEarned = completedTasks.reduce((sum, task) => sum + task.pointsReward, 0);

        const tasksByType = weekTasks.reduce((acc, task) => {
            acc[task.type] = (acc[task.type] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        const dailySummaries: DailyTaskSummary[] = [];
        for (let i = 0; i < 7; i++) {
            const date = new Date(startDate);
            date.setDate(date.getDate() + i);
            const summary = await this.getDailyTaskSummary(date);
            dailySummaries.push(summary);
        }

        return {
            totalTasks: weekTasks.length,
            completedTasks: completedTasks.length,
            totalPointsEarned,
            tasksByType,
            dailySummaries,
        };
    }
}