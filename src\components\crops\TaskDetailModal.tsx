import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Modal, TextInput, Image, Alert } from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { Task, TaskCompletion } from '../../types/tasks';
import { Button } from '../ui';

interface TaskDetailModalProps {
    task: Task | null;
    visible: boolean;
    onClose: () => void;
    onTaskComplete: (completion: TaskCompletion) => void;
    onTaskUpdate: (taskId: string, updates: Partial<Task>) => void;
    voiceEnabled?: boolean;
    onVoiceCommand?: (command: string) => void;
}

export default function TaskDetailModal({
    task,
    visible,
    onClose,
    onTaskComplete,
    onTaskUpdate,
    voiceEnabled = false,
    onVoiceCommand,
}: TaskDetailModalProps) {
    const [completionNotes, setCompletionNotes] = useState('');
    const [evidenceImage, setEvidenceImage] = useState<string | null>(null);
    const [loading, setLoading] = useState(false);

    if (!task) return null;

    const getTaskTypeIcon = (type: Task['type']): string => {
        const icons = {
            watering: '💧',
            fertilizing: '🌱',
            monitoring: '👁️',
            harvesting: '🌾',
            planting: '🌱',
            pest_control: '🐛',
        };
        return icons[type] || '📋';
    };

    const getTaskTypeColor = (type: Task['type']): string => {
        const colors = {
            watering: 'text-blue-600 bg-blue-50 border-blue-200',
            fertilizing: 'text-green-600 bg-green-50 border-green-200',
            monitoring: 'text-purple-600 bg-purple-50 border-purple-200',
            harvesting: 'text-yellow-600 bg-yellow-50 border-yellow-200',
            planting: 'text-green-600 bg-green-50 border-green-200',
            pest_control: 'text-red-600 bg-red-50 border-red-200',
        };
        return colors[type] || 'text-gray-600 bg-gray-50 border-gray-200';
    };

    const getPriorityColor = (priority: Task['priority']): string => {
        switch (priority) {
            case 'high': return 'text-red-600 bg-red-50 border-red-200';
            case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
            case 'low': return 'text-green-600 bg-green-50 border-green-200';
            default: return 'text-gray-600 bg-gray-50 border-gray-200';
        }
    };

    const formatDueDate = (date: Date): string => {
        const now = new Date();
        const diffTime = date.getTime() - now.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 0) return 'Due today';
        if (diffDays === 1) return 'Due tomorrow';
        if (diffDays === -1) return 'Due yesterday';
        if (diffDays < 0) return `Overdue by ${Math.abs(diffDays)} days`;
        return `Due in ${diffDays} days`;
    };

    const handleTakePhoto = async () => {
        try {
            const { status } = await ImagePicker.requestCameraPermissionsAsync();
            if (status !== 'granted') {
                Alert.alert('Permission needed', 'Camera permission is required to take photos.');
                return;
            }

            const result = await ImagePicker.launchCameraAsync({
                mediaTypes: ImagePicker.MediaTypeOptions.Images,
                allowsEditing: true,
                aspect: [4, 3],
                quality: 0.8,
            });

            if (!result.canceled && result.assets[0]) {
                setEvidenceImage(result.assets[0].uri);
            }
        } catch (error) {
            console.error('Error taking photo:', error);
            Alert.alert('Error', 'Failed to take photo. Please try again.');
        }
    };

    const handleSelectPhoto = async () => {
        try {
            const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
            if (status !== 'granted') {
                Alert.alert('Permission needed', 'Photo library permission is required to select photos.');
                return;
            }

            const result = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ImagePicker.MediaTypeOptions.Images,
                allowsEditing: true,
                aspect: [4, 3],
                quality: 0.8,
            });

            if (!result.canceled && result.assets[0]) {
                setEvidenceImage(result.assets[0].uri);
            }
        } catch (error) {
            console.error('Error selecting photo:', error);
            Alert.alert('Error', 'Failed to select photo. Please try again.');
        }
    };

    const handleCompleteTask = async () => {
        setLoading(true);
        try {
            const completion: TaskCompletion = {
                taskId: task.id,
                completedAt: new Date(),
                notes: completionNotes.trim() || undefined,
                imageEvidence: evidenceImage || undefined,
                pointsEarned: task.pointsReward,
            };

            await onTaskComplete(completion);

            // Reset form
            setCompletionNotes('');
            setEvidenceImage(null);
            onClose();
        } catch (error) {
            console.error('Error completing task:', error);
            Alert.alert('Error', 'Failed to complete task. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleVoiceCommand = () => {
        if (onVoiceCommand) {
            onVoiceCommand('task_details');
        }
    };

    return (
        <Modal
            visible={visible}
            animationType="slide"
            presentationStyle="pageSheet"
            onRequestClose={onClose}
        >
            <View className="flex-1 bg-gray-50">
                {/* Header */}
                <View className="bg-white border-b border-gray-200 px-4 py-3 pt-12">
                    <View className="flex-row items-center justify-between">
                        <TouchableOpacity
                            onPress={onClose}
                            className="p-2"
                            accessibilityLabel="Close task details"
                        >
                            <Text className="text-2xl text-gray-600">×</Text>
                        </TouchableOpacity>

                        <Text className="text-lg font-semibold text-gray-900">
                            Task Details
                        </Text>

                        {voiceEnabled && (
                            <TouchableOpacity
                                onPress={handleVoiceCommand}
                                className="p-2 bg-green-500 rounded-full"
                                accessibilityLabel="Voice commands"
                            >
                                <Text className="text-white text-sm">🎤</Text>
                            </TouchableOpacity>
                        )}
                    </View>
                </View>

                <ScrollView className="flex-1 p-4">
                    {/* Task Header */}
                    <View className="bg-white rounded-lg p-4 border border-gray-200 mb-4">
                        <View className="flex-row items-start mb-3">
                            <Text className="text-4xl mr-3">
                                {getTaskTypeIcon(task.type)}
                            </Text>

                            <View className="flex-1">
                                <Text className="text-xl font-bold text-gray-900 mb-2">
                                    {task.title}
                                </Text>

                                <View className="flex-row items-center gap-2 mb-2">
                                    <View className={`px-2 py-1 rounded-full border ${getTaskTypeColor(task.type)}`}>
                                        <Text className="text-xs font-medium capitalize">
                                            {task.type.replace('_', ' ')}
                                        </Text>
                                    </View>

                                    <View className={`px-2 py-1 rounded-full border ${getPriorityColor(task.priority)}`}>
                                        <Text className="text-xs font-medium capitalize">
                                            {task.priority} Priority
                                        </Text>
                                    </View>
                                </View>

                                <Text className="text-sm text-gray-600">
                                    {formatDueDate(new Date(task.dueDate))}
                                </Text>
                            </View>
                        </View>

                        {task.completed && (
                            <View className="bg-green-50 border border-green-200 rounded-lg p-3 mb-3">
                                <Text className="text-green-800 font-medium">
                                    ✅ Task Completed
                                </Text>
                                {task.completedAt && (
                                    <Text className="text-sm text-green-600">
                                        Completed on {new Date(task.completedAt).toLocaleDateString()}
                                    </Text>
                                )}
                            </View>
                        )}
                    </View>

                    {/* Task Description */}
                    <View className="bg-white rounded-lg p-4 border border-gray-200 mb-4">
                        <Text className="text-lg font-semibold text-gray-900 mb-2">
                            Description
                        </Text>
                        <Text className="text-gray-700 leading-6">
                            {task.description}
                        </Text>
                    </View>

                    {/* Task Instructions */}
                    {task.instructions && task.instructions.length > 0 && (
                        <View className="bg-white rounded-lg p-4 border border-gray-200 mb-4">
                            <Text className="text-lg font-semibold text-gray-900 mb-3">
                                📋 Instructions
                            </Text>
                            <View className="gap-2">
                                {task.instructions.map((instruction, index) => (
                                    <View key={index} className="flex-row items-start">
                                        <Text className="text-green-600 font-bold mr-2">
                                            {index + 1}.
                                        </Text>
                                        <Text className="flex-1 text-gray-700">
                                            {instruction}
                                        </Text>
                                    </View>
                                ))}
                            </View>
                        </View>
                    )}

                    {/* Task Info */}
                    <View className="bg-white rounded-lg p-4 border border-gray-200 mb-4">
                        <Text className="text-lg font-semibold text-gray-900 mb-3">
                            Task Information
                        </Text>
                        <View className="gap-3">
                            <View className="flex-row justify-between">
                                <Text className="text-gray-600">Estimated Duration:</Text>
                                <Text className="font-medium text-gray-900">
                                    {task.estimatedDuration} minutes
                                </Text>
                            </View>
                            <View className="flex-row justify-between">
                                <Text className="text-gray-600">Points Reward:</Text>
                                <Text className="font-medium text-green-600">
                                    🏆 {task.pointsReward} points
                                </Text>
                            </View>
                            <View className="flex-row justify-between">
                                <Text className="text-gray-600">Due Date:</Text>
                                <Text className="font-medium text-gray-900">
                                    {new Date(task.dueDate).toLocaleDateString('en-US', {
                                        weekday: 'short',
                                        month: 'short',
                                        day: 'numeric',
                                        hour: '2-digit',
                                        minute: '2-digit',
                                    })}
                                </Text>
                            </View>
                        </View>
                    </View>

                    {/* Task Completion Section */}
                    {!task.completed && (
                        <View className="bg-white rounded-lg p-4 border border-gray-200 mb-4">
                            <Text className="text-lg font-semibold text-gray-900 mb-3">
                                Complete Task
                            </Text>

                            {/* Completion Notes */}
                            <View className="mb-4">
                                <Text className="text-sm font-medium text-gray-700 mb-2">
                                    Notes (Optional)
                                </Text>
                                <TextInput
                                    className="border border-gray-300 rounded-lg p-3 text-gray-900 min-h-[80px]"
                                    placeholder="Add any notes about completing this task..."
                                    placeholderTextColor="#9CA3AF"
                                    value={completionNotes}
                                    onChangeText={setCompletionNotes}
                                    multiline
                                    textAlignVertical="top"
                                    accessibilityLabel="Task completion notes"
                                />
                            </View>

                            {/* Photo Evidence */}
                            <View className="mb-4">
                                <Text className="text-sm font-medium text-gray-700 mb-2">
                                    Photo Evidence (Optional)
                                </Text>

                                {evidenceImage ? (
                                    <View className="relative">
                                        <Image
                                            source={{ uri: evidenceImage }}
                                            className="w-full h-48 rounded-lg"
                                            resizeMode="cover"
                                        />
                                        <TouchableOpacity
                                            onPress={() => setEvidenceImage(null)}
                                            className="absolute top-2 right-2 bg-red-500 rounded-full p-1"
                                            accessibilityLabel="Remove photo"
                                        >
                                            <Text className="text-white text-xs">×</Text>
                                        </TouchableOpacity>
                                    </View>
                                ) : (
                                    <View className="flex-row gap-2">
                                        <Button
                                            title="📷 Take Photo"
                                            onPress={handleTakePhoto}
                                            variant="outline"
                                            size="sm"
                                            className="flex-1"
                                        />
                                        <Button
                                            title="🖼️ Choose Photo"
                                            onPress={handleSelectPhoto}
                                            variant="outline"
                                            size="sm"
                                            className="flex-1"
                                        />
                                    </View>
                                )}
                            </View>

                            {/* Complete Button */}
                            <Button
                                title={`Complete Task (+${task.pointsReward} points)`}
                                onPress={handleCompleteTask}
                                loading={loading}
                                className="w-full"
                                accessibilityLabel="Complete task and earn points"
                            />
                        </View>
                    )}

                    {/* Voice Commands Help */}
                    {voiceEnabled && (
                        <View className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                            <Text className="text-lg font-semibold text-blue-800 mb-2">
                                🗣️ Voice Commands
                            </Text>
                            <Text className="text-sm text-blue-700 mb-2">
                                Try saying:
                            </Text>
                            <View className="gap-1">
                                <Text className="text-sm text-blue-600">• "Complete this task"</Text>
                                <Text className="text-sm text-blue-600">• "Take a photo"</Text>
                                <Text className="text-sm text-blue-600">• "Add notes"</Text>
                                <Text className="text-sm text-blue-600">• "Read instructions"</Text>
                            </View>
                        </View>
                    )}
                </ScrollView>
            </View>
        </Modal>
    );
}