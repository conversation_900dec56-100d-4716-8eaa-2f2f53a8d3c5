import {
    NotificationTemplate,
    NotificationType,
    NotificationPriority,
    NotificationCategory,
    WeatherAlert,
    CropStageAlert,
    AIInsightNotification,
} from '../types/notifications';

export class NotificationTemplateService {
    private static templates: Record<NotificationType, NotificationTemplate> = {
        weather_alert: {
            id: 'weather_alert',
            type: 'weather_alert',
            title: '🌦️ Weather Alert: {alertType}',
            body: '{description} Expected from {startTime} to {endTime}. {recommendations}',
            variables: ['alertType', 'description', 'startTime', 'endTime', 'recommendations'],
            priority: 'high',
            category: 'weather',
            soundFile: 'weather_alert.wav',
        },
        task_reminder: {
            id: 'task_reminder',
            type: 'task_reminder',
            title: '🌱 Farm Task Reminder',
            body: "Don't forget: {taskTitle}. Due {dueTime}",
            variables: ['taskTitle', 'dueTime'],
            priority: 'normal',
            category: 'agricultural',
        },
        crop_stage_update: {
            id: 'crop_stage_update',
            type: 'crop_stage_update',
            title: '🌾 {cropType} Growth Update',
            body: 'Your {cropType} is entering {nextStage} stage. {recommendations}',
            variables: ['cropType', 'nextStage', 'recommendations'],
            priority: 'normal',
            category: 'agricultural',
        },
        community_post: {
            id: 'community_post',
            type: 'community_post',
            title: '👥 New Community Post',
            body: '{authorName} shared: {postTitle}',
            variables: ['authorName', 'postTitle'],
            priority: 'low',
            category: 'community',
        },
        emergency_alert: {
            id: 'emergency_alert',
            type: 'emergency_alert',
            title: '🚨 EMERGENCY: {alertType}',
            body: '{description} Take immediate action: {actions}',
            variables: ['alertType', 'description', 'actions'],
            priority: 'critical',
            category: 'weather',
            soundFile: 'emergency_alert.wav',
        },
        ai_insight: {
            id: 'ai_insight',
            type: 'ai_insight',
            title: '🤖 AI Detected: {insightType}',
            body: '{description} Confidence: {confidence}%. {recommendations}',
            variables: ['insightType', 'description', 'confidence', 'recommendations'],
            priority: 'high',
            category: 'agricultural',
        },
        subscription_reminder: {
            id: 'subscription_reminder',
            type: 'subscription_reminder',
            title: '💎 Subscription Reminder',
            body: 'Your {planName} subscription {action} in {days} days.',
            variables: ['planName', 'action', 'days'],
            priority: 'normal',
            category: 'commercial',
        },
        points_earned: {
            id: 'points_earned',
            type: 'points_earned',
            title: '⭐ Points Earned!',
            body: 'You earned {points} points for {action}. Total: {totalPoints}',
            variables: ['points', 'action', 'totalPoints'],
            priority: 'low',
            category: 'system',
        },
        achievement_unlocked: {
            id: 'achievement_unlocked',
            type: 'achievement_unlocked',
            title: '🏆 Achievement Unlocked!',
            body: 'Congratulations! You unlocked "{achievementName}". {description}',
            variables: ['achievementName', 'description'],
            priority: 'normal',
            category: 'system',
        },
        system_maintenance: {
            id: 'system_maintenance',
            type: 'system_maintenance',
            title: '🔧 System Maintenance',
            body: 'Scheduled maintenance {when}. Expected duration: {duration}',
            variables: ['when', 'duration'],
            priority: 'low',
            category: 'system',
        },
    };

    static getTemplate(type: NotificationType): NotificationTemplate {
        return this.templates[type];
    }

    static renderTemplate(
        type: NotificationType,
        variables: Record<string, string>
    ): { title: string; body: string } {
        const template = this.getTemplate(type);
        if (!template) {
            throw new Error(`Template not found for notification type: ${type}`);
        }

        let title = template.title;
        let body = template.body;

        // Replace variables in title and body
        Object.entries(variables).forEach(([key, value]) => {
            const placeholder = `{${key}}`;
            title = title.replace(new RegExp(placeholder, 'g'), value);
            body = body.replace(new RegExp(placeholder, 'g'), value);
        });

        return { title, body };
    }

    static createWeatherAlertNotification(alert: WeatherAlert): {
        title: string;
        body: string;
        data: Record<string, any>;
        priority: NotificationPriority;
        sound?: string;
    } {
        const template = this.getTemplate('weather_alert');
        const { title, body } = this.renderTemplate('weather_alert', {
            alertType: this.formatAlertType(alert.alertType),
            description: alert.description,
            startTime: alert.startTime.toLocaleTimeString(),
            endTime: alert.endTime.toLocaleTimeString(),
            recommendations: alert.recommendations.join('. '),
        });

        return {
            title,
            body,
            data: {
                type: 'weather_alert',
                alertId: alert.id,
                severity: alert.severity,
                location: alert.location,
                affectedCrops: alert.affectedCrops,
            },
            priority: this.mapSeverityToPriority(alert.severity),
            sound: template.soundFile,
        };
    }

    static createCropStageNotification(alert: CropStageAlert): {
        title: string;
        body: string;
        data: Record<string, any>;
        priority: NotificationPriority;
    } {
        const { title, body } = this.renderTemplate('crop_stage_update', {
            cropType: alert.cropType,
            nextStage: alert.nextStage,
            recommendations: alert.recommendations.join('. '),
        });

        return {
            title,
            body,
            data: {
                type: 'crop_stage_update',
                cropPlanId: alert.cropPlanId,
                currentStage: alert.currentStage,
                nextStage: alert.nextStage,
                daysToNextStage: alert.daysToNextStage.toString(),
                criticalActions: alert.criticalActions,
            },
            priority: 'normal',
        };
    }

    static createAIInsightNotification(insight: AIInsightNotification): {
        title: string;
        body: string;
        data: Record<string, any>;
        priority: NotificationPriority;
    } {
        const { title, body } = this.renderTemplate('ai_insight', {
            insightType: this.formatInsightType(insight.insightType),
            description: insight.description,
            confidence: Math.round(insight.confidence * 100).toString(),
            recommendations: insight.recommendations.join('. '),
        });

        return {
            title,
            body,
            data: {
                type: 'ai_insight',
                insightId: insight.id,
                insightType: insight.insightType,
                confidence: insight.confidence,
                urgency: insight.urgency,
                imageUrl: insight.imageUrl,
                relatedCropPlan: insight.relatedCropPlan,
                actionRequired: insight.actionRequired,
            },
            priority: this.mapUrgencyToPriority(insight.urgency),
        };
    }

    static createTaskReminderNotification(
        taskTitle: string,
        dueTime: string,
        taskId: string,
        cropPlanId?: string
    ): {
        title: string;
        body: string;
        data: Record<string, any>;
        priority: NotificationPriority;
    } {
        const { title, body } = this.renderTemplate('task_reminder', {
            taskTitle,
            dueTime,
        });

        return {
            title,
            body,
            data: {
                type: 'task_reminder',
                taskId,
                cropPlanId,
            },
            priority: 'normal',
        };
    }

    static createCommunityNotification(
        authorName: string,
        postTitle: string,
        postId: string
    ): {
        title: string;
        body: string;
        data: Record<string, any>;
        priority: NotificationPriority;
    } {
        const { title, body } = this.renderTemplate('community_post', {
            authorName,
            postTitle,
        });

        return {
            title,
            body,
            data: {
                type: 'community_post',
                postId,
                authorName,
            },
            priority: 'low',
        };
    }

    static createPointsEarnedNotification(
        points: number,
        action: string,
        totalPoints: number
    ): {
        title: string;
        body: string;
        data: Record<string, any>;
        priority: NotificationPriority;
    } {
        const { title, body } = this.renderTemplate('points_earned', {
            points: points.toString(),
            action,
            totalPoints: totalPoints.toString(),
        });

        return {
            title,
            body,
            data: {
                type: 'points_earned',
                pointsEarned: points,
                action,
                totalPoints,
            },
            priority: 'low',
        };
    }

    static createAchievementNotification(
        achievementName: string,
        description: string,
        achievementId: string
    ): {
        title: string;
        body: string;
        data: Record<string, any>;
        priority: NotificationPriority;
    } {
        const { title, body } = this.renderTemplate('achievement_unlocked', {
            achievementName,
            description,
        });

        return {
            title,
            body,
            data: {
                type: 'achievement_unlocked',
                achievementId,
                achievementName,
            },
            priority: 'normal',
        };
    }

    static createSubscriptionReminderNotification(
        planName: string,
        action: 'expires' | 'renews',
        days: number
    ): {
        title: string;
        body: string;
        data: Record<string, any>;
        priority: NotificationPriority;
    } {
        const { title, body } = this.renderTemplate('subscription_reminder', {
            planName,
            action,
            days: days.toString(),
        });

        return {
            title,
            body,
            data: {
                type: 'subscription_reminder',
                planName,
                action,
                daysRemaining: days,
            },
            priority: 'normal',
        };
    }

    private static formatAlertType(alertType: WeatherAlert['alertType']): string {
        const typeMap: Record<WeatherAlert['alertType'], string> = {
            severe_weather: 'Severe Weather',
            frost_warning: 'Frost Warning',
            drought_alert: 'Drought Alert',
            flood_warning: 'Flood Warning',
            heat_wave: 'Heat Wave',
        };
        return typeMap[alertType] || alertType;
    }

    private static formatInsightType(insightType: AIInsightNotification['insightType']): string {
        const typeMap: Record<AIInsightNotification['insightType'], string> = {
            disease_detected: 'Disease Detected',
            nutrient_deficiency: 'Nutrient Deficiency',
            pest_warning: 'Pest Warning',
            optimization_tip: 'Optimization Tip',
        };
        return typeMap[insightType] || insightType;
    }

    private static mapSeverityToPriority(severity: WeatherAlert['severity']): NotificationPriority {
        const severityMap: Record<WeatherAlert['severity'], NotificationPriority> = {
            minor: 'low',
            moderate: 'normal',
            severe: 'high',
            extreme: 'critical',
        };
        return severityMap[severity] || 'normal';
    }

    private static mapUrgencyToPriority(urgency: AIInsightNotification['urgency']): NotificationPriority {
        const urgencyMap: Record<AIInsightNotification['urgency'], NotificationPriority> = {
            low: 'low',
            medium: 'normal',
            high: 'high',
            critical: 'critical',
        };
        return urgencyMap[urgency] || 'normal';
    }
}

export default NotificationTemplateService;