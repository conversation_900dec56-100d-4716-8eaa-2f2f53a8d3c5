/**
 * Shopping Cart and Checkout Service
 * Handles cart persistence, synchronization, and secure checkout process
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from './supabase/client';
import {
    ShoppingCart,
    CartItem,
    Order,
    OrderStatus,
    ShippingAddress,
    PaymentMethod,
} from '../types/product';
import { Database } from '../types/database';

type CartRow = Database['public']['Tables']['shopping_carts']['Row'];
type CartItemRow = Database['public']['Tables']['cart_items']['Row'];
type OrderRow = Database['public']['Tables']['orders']['Row'];
type OrderItemRow = Database['public']['Tables']['order_items']['Row'];

const STORAGE_KEYS = {
    CART: '@farming_app_cart',
    SHIPPING_ADDRESS: '@farming_app_shipping_address',
    PAYMENT_METHODS: '@farming_app_payment_methods',
};

export class ShoppingCartService {
    private localCart: ShoppingCart | null = null;
    private syncInProgress = false;

    /**
     * Initialize cart - load from local storage and sync with server
     */
    async initializeCart(userId?: string): Promise<ShoppingCart> {
        try {
            // Load from local storage first for immediate UI response
            const localCartData = await AsyncStorage.getItem(STORAGE_KEYS.CART);
            if (localCartData) {
                this.localCart = JSON.parse(localCartData);
            }

            // If user is logged in, sync with server
            if (userId) {
                await this.syncCartWithServer(userId);
            }

            // Return local cart or create empty one
            return this.localCart || this.createEmptyCart();
        } catch (error) {
            console.error('Error initializing cart:', error);
            return this.createEmptyCart();
        }
    }

    /**
     * Add item to cart
     */
    async addToCart(
        productId: string,
        quantity: number = 1,
        variantId?: string,
        userId?: string
    ): Promise<ShoppingCart> {
        try {
            // Get product details
            const { data: product, error: productError } = await supabase
                .from('products')
                .select('*')
                .eq('id', productId)
                .single();

            if (productError || !product) {
                throw new Error('Product not found');
            }

            // Check stock availability
            if (product.stock_quantity < quantity) {
                throw new Error('Insufficient stock');
            }

            // Initialize cart if not exists
            if (!this.localCart) {
                this.localCart = this.createEmptyCart();
            }

            // Check if item already exists in cart
            const existingItemIndex = this.localCart.items.findIndex(
                item => item.product.id === productId &&
                    (item.selectedVariant?.id || null) === (variantId || null)
            );

            const unitPrice = variantId ?
                await this.getVariantPrice(variantId) :
                product.price;

            if (existingItemIndex >= 0) {
                // Update existing item
                const newQuantity = this.localCart.items[existingItemIndex].quantity + quantity;

                // Check total stock
                if (product.stock_quantity < newQuantity) {
                    throw new Error('Insufficient stock for requested quantity');
                }

                this.localCart.items[existingItemIndex].quantity = newQuantity;
            } else {
                // Add new item
                const cartItem: CartItem = {
                    id: `${productId}_${variantId || 'default'}_${Date.now()}`,
                    product: {
                        id: product.id,
                        name: product.name,
                        description: product.description || '',
                        category: product.category as any,
                        price: product.price,
                        originalPrice: product.original_price || undefined,
                        currency: product.currency,
                        imageUrls: product.image_urls,
                        specifications: product.specifications as any,
                        stockQuantity: product.stock_quantity,
                        rating: product.rating,
                        reviewCount: product.review_count,
                        tags: product.tags,
                        brand: product.brand || undefined,
                        isRecommended: product.is_recommended,
                        isFeatured: product.is_featured,
                        createdAt: new Date(product.created_at),
                        updatedAt: new Date(product.updated_at),
                    },
                    quantity,
                    selectedVariant: variantId ? await this.getVariant(variantId) : undefined,
                };

                this.localCart.items.push(cartItem);
            }

            // Recalculate totals
            this.recalculateCart();

            // Save to local storage
            await this.saveCartLocally();

            // Sync with server if user is logged in
            if (userId) {
                await this.syncCartWithServer(userId);
            }

            return this.localCart;
        } catch (error) {
            console.error('Error adding to cart:', error);
            throw error;
        }
    }

    /**
     * Remove item from cart
     */
    async removeFromCart(itemId: string, userId?: string): Promise<ShoppingCart> {
        try {
            if (!this.localCart) {
                this.localCart = this.createEmptyCart();
                return this.localCart;
            }

            this.localCart.items = this.localCart.items.filter(item => item.id !== itemId);
            this.recalculateCart();

            await this.saveCartLocally();

            if (userId) {
                await this.syncCartWithServer(userId);
            }

            return this.localCart;
        } catch (error) {
            console.error('Error removing from cart:', error);
            throw error;
        }
    }

    /**
     * Update item quantity
     */
    async updateItemQuantity(
        itemId: string,
        quantity: number,
        userId?: string
    ): Promise<ShoppingCart> {
        try {
            if (!this.localCart) {
                throw new Error('Cart not initialized');
            }

            if (quantity <= 0) {
                return this.removeFromCart(itemId, userId);
            }

            const itemIndex = this.localCart.items.findIndex(item => item.id === itemId);
            if (itemIndex === -1) {
                throw new Error('Item not found in cart');
            }

            const item = this.localCart.items[itemIndex];

            // Check stock availability
            if (item.product.stockQuantity < quantity) {
                throw new Error('Insufficient stock');
            }

            this.localCart.items[itemIndex].quantity = quantity;
            this.recalculateCart();

            await this.saveCartLocally();

            if (userId) {
                await this.syncCartWithServer(userId);
            }

            return this.localCart;
        } catch (error) {
            console.error('Error updating item quantity:', error);
            throw error;
        }
    }

    /**
     * Get current cart
     */
    async getCart(userId?: string): Promise<ShoppingCart> {
        if (!this.localCart) {
            return this.initializeCart(userId);
        }
        return this.localCart;
    }

    /**
     * Clear cart
     */
    async clearCart(userId?: string): Promise<ShoppingCart> {
        try {
            this.localCart = this.createEmptyCart();
            await this.saveCartLocally();

            if (userId) {
                // Clear server cart
                const { data: serverCart } = await supabase
                    .from('shopping_carts')
                    .select('id')
                    .eq('user_id', userId)
                    .single();

                if (serverCart) {
                    await supabase
                        .from('cart_items')
                        .delete()
                        .eq('cart_id', serverCart.id);
                }
            }

            return this.localCart;
        } catch (error) {
            console.error('Error clearing cart:', error);
            throw error;
        }
    }

    /**
     * Calculate shipping cost
     */
    async calculateShipping(
        cartItems: CartItem[],
        shippingAddress: ShippingAddress
    ): Promise<number> {
        try {
            // Simple shipping calculation - in production, integrate with shipping API
            const totalWeight = cartItems.reduce((weight, item) => {
                // Assume 1kg per item for simplicity
                return weight + (item.quantity * 1);
            }, 0);

            const baseShipping = 5.00; // Base shipping cost
            const weightCost = totalWeight * 0.5; // $0.50 per kg

            // Free shipping for orders over $100
            const cartTotal = cartItems.reduce((total, item) => {
                const price = item.selectedVariant?.price || item.product.price;
                return total + (price * item.quantity);
            }, 0);

            if (cartTotal >= 100) {
                return 0;
            }

            return Math.round((baseShipping + weightCost) * 100) / 100;
        } catch (error) {
            console.error('Error calculating shipping:', error);
            return 10.00; // Default shipping cost
        }
    }

    /**
     * Calculate tax
     */
    async calculateTax(
        cartItems: CartItem[],
        shippingAddress: ShippingAddress
    ): Promise<number> {
        try {
            const subtotal = cartItems.reduce((total, item) => {
                const price = item.selectedVariant?.price || item.product.price;
                return total + (price * item.quantity);
            }, 0);

            // Simple tax calculation - 8% for most states
            const taxRate = 0.08;
            return Math.round(subtotal * taxRate * 100) / 100;
        } catch (error) {
            console.error('Error calculating tax:', error);
            return 0;
        }
    }

    /**
     * Create order from cart
     */
    async createOrder(
        userId: string,
        shippingAddress: ShippingAddress,
        paymentMethod: PaymentMethod,
        billingAddress?: ShippingAddress
    ): Promise<Order> {
        try {
            if (!this.localCart || this.localCart.items.length === 0) {
                throw new Error('Cart is empty');
            }

            // Calculate totals
            const subtotal = this.localCart.totalAmount;
            const shippingAmount = await this.calculateShipping(this.localCart.items, shippingAddress);
            const taxAmount = await this.calculateTax(this.localCart.items, shippingAddress);
            const totalAmount = subtotal + shippingAmount + taxAmount;

            // Create order in database
            const { data: order, error: orderError } = await supabase
                .from('orders')
                .insert({
                    user_id: userId,
                    total_amount: totalAmount,
                    tax_amount: taxAmount,
                    shipping_amount: shippingAmount,
                    discount_amount: 0,
                    status: 'pending',
                    payment_status: 'pending',
                    payment_method: paymentMethod as any,
                    shipping_address: shippingAddress as any,
                    billing_address: billingAddress as any,
                    estimated_delivery: this.calculateEstimatedDelivery(),
                })
                .select()
                .single();

            if (orderError) {
                throw new Error(`Failed to create order: ${orderError.message}`);
            }

            // Create order items
            const orderItems = this.localCart.items.map(item => ({
                order_id: order.id,
                product_id: item.product.id,
                variant_id: item.selectedVariant?.id || null,
                quantity: item.quantity,
                unit_price: item.selectedVariant?.price || item.product.price,
                total_price: (item.selectedVariant?.price || item.product.price) * item.quantity,
                product_snapshot: {
                    name: item.product.name,
                    description: item.product.description,
                    imageUrls: item.product.imageUrls,
                    specifications: item.product.specifications,
                } as any,
            }));

            const { error: itemsError } = await supabase
                .from('order_items')
                .insert(orderItems);

            if (itemsError) {
                throw new Error(`Failed to create order items: ${itemsError.message}`);
            }

            // Update inventory
            await this.updateInventoryForOrder(this.localCart.items);

            // Clear cart after successful order
            await this.clearCart(userId);

            // Return order object
            const orderObj: Order = {
                id: order.id,
                userId: order.user_id,
                items: this.localCart.items,
                totalAmount: order.total_amount,
                status: order.status as OrderStatus,
                shippingAddress: order.shipping_address as ShippingAddress,
                paymentMethod: order.payment_method as PaymentMethod,
                trackingNumber: order.tracking_number || undefined,
                createdAt: new Date(order.created_at),
                updatedAt: new Date(order.updated_at),
                estimatedDelivery: order.estimated_delivery ? new Date(order.estimated_delivery) : undefined,
            };

            return orderObj;
        } catch (error) {
            console.error('Error creating order:', error);
            throw error;
        }
    }

    /**
     * Get user's order history
     */
    async getOrderHistory(userId: string, limit: number = 20): Promise<Order[]> {
        try {
            const { data: orders, error } = await supabase
                .from('orders')
                .select(`
                    *,
                    order_items (
                        *,
                        product:product_id (*)
                    )
                `)
                .eq('user_id', userId)
                .order('created_at', { ascending: false })
                .limit(limit);

            if (error) {
                throw new Error(`Failed to get order history: ${error.message}`);
            }

            return orders?.map(order => ({
                id: order.id,
                userId: order.user_id,
                items: order.order_items?.map(item => ({
                    id: item.id,
                    product: this.mapProductFromOrderItem(item.product as any),
                    quantity: item.quantity,
                    selectedVariant: item.variant_id ? { id: item.variant_id } as any : undefined,
                })) || [],
                totalAmount: order.total_amount,
                status: order.status as OrderStatus,
                shippingAddress: order.shipping_address as ShippingAddress,
                paymentMethod: order.payment_method as PaymentMethod,
                trackingNumber: order.tracking_number || undefined,
                createdAt: new Date(order.created_at),
                updatedAt: new Date(order.updated_at),
                estimatedDelivery: order.estimated_delivery ? new Date(order.estimated_delivery) : undefined,
            })) || [];
        } catch (error) {
            console.error('Error getting order history:', error);
            return [];
        }
    }

    /**
     * Track order status
     */
    async trackOrder(orderId: string): Promise<Order | null> {
        try {
            const { data: order, error } = await supabase
                .from('orders')
                .select(`
                    *,
                    order_items (
                        *,
                        product:product_id (*)
                    )
                `)
                .eq('id', orderId)
                .single();

            if (error) {
                if (error.code === 'PGRST116') {
                    return null;
                }
                throw new Error(`Failed to track order: ${error.message}`);
            }

            return {
                id: order.id,
                userId: order.user_id,
                items: order.order_items?.map(item => ({
                    id: item.id,
                    product: this.mapProductFromOrderItem(item.product as any),
                    quantity: item.quantity,
                    selectedVariant: item.variant_id ? { id: item.variant_id } as any : undefined,
                })) || [],
                totalAmount: order.total_amount,
                status: order.status as OrderStatus,
                shippingAddress: order.shipping_address as ShippingAddress,
                paymentMethod: order.payment_method as PaymentMethod,
                trackingNumber: order.tracking_number || undefined,
                createdAt: new Date(order.created_at),
                updatedAt: new Date(order.updated_at),
                estimatedDelivery: order.estimated_delivery ? new Date(order.estimated_delivery) : undefined,
            };
        } catch (error) {
            console.error('Error tracking order:', error);
            return null;
        }
    }

    // Private helper methods

    private createEmptyCart(): ShoppingCart {
        return {
            id: 'local',
            items: [],
            totalAmount: 0,
            itemCount: 0,
            updatedAt: new Date(),
        };
    }

    private recalculateCart(): void {
        if (!this.localCart) return;

        this.localCart.totalAmount = this.localCart.items.reduce((total, item) => {
            const price = item.selectedVariant?.price || item.product.price;
            return total + (price * item.quantity);
        }, 0);

        this.localCart.itemCount = this.localCart.items.reduce(
            (count, item) => count + item.quantity,
            0
        );

        this.localCart.updatedAt = new Date();
    }

    private async saveCartLocally(): Promise<void> {
        if (this.localCart) {
            await AsyncStorage.setItem(STORAGE_KEYS.CART, JSON.stringify(this.localCart));
        }
    }

    private async syncCartWithServer(userId: string): Promise<void> {
        if (this.syncInProgress) return;
        this.syncInProgress = true;

        try {
            // Get or create server cart
            let { data: serverCart } = await supabase
                .from('shopping_carts')
                .select('id')
                .eq('user_id', userId)
                .single();

            if (!serverCart) {
                const { data: newCart, error } = await supabase
                    .from('shopping_carts')
                    .insert({ user_id: userId })
                    .select()
                    .single();

                if (error) {
                    throw new Error(`Failed to create server cart: ${error.message}`);
                }
                serverCart = newCart;
            }

            // Clear existing server cart items
            await supabase
                .from('cart_items')
                .delete()
                .eq('cart_id', serverCart.id);

            // Add local cart items to server
            if (this.localCart && this.localCart.items.length > 0) {
                const cartItems = this.localCart.items.map(item => ({
                    cart_id: serverCart.id,
                    product_id: item.product.id,
                    variant_id: item.selectedVariant?.id || null,
                    quantity: item.quantity,
                    unit_price: item.selectedVariant?.price || item.product.price,
                }));

                const { error } = await supabase
                    .from('cart_items')
                    .insert(cartItems);

                if (error) {
                    console.error('Failed to sync cart items:', error);
                }
            }
        } catch (error) {
            console.error('Error syncing cart with server:', error);
        } finally {
            this.syncInProgress = false;
        }
    }

    private async getVariant(variantId: string): Promise<any> {
        const { data } = await supabase
            .from('product_variants')
            .select('*')
            .eq('id', variantId)
            .single();

        return data;
    }

    private async getVariantPrice(variantId: string): Promise<number> {
        const variant = await this.getVariant(variantId);
        return variant?.price || 0;
    }

    private calculateEstimatedDelivery(): string {
        const deliveryDate = new Date();
        deliveryDate.setDate(deliveryDate.getDate() + 7); // 7 days from now
        return deliveryDate.toISOString().split('T')[0];
    }

    private async updateInventoryForOrder(items: CartItem[]): Promise<void> {
        for (const item of items) {
            const { error } = await supabase
                .from('products')
                .update({
                    stock_quantity: item.product.stockQuantity - item.quantity
                })
                .eq('id', item.product.id);

            if (error) {
                console.error(`Failed to update inventory for product ${item.product.id}:`, error);
            }
        }
    }

    private mapProductFromOrderItem(productData: any): any {
        return {
            id: productData.id,
            name: productData.name,
            description: productData.description || '',
            category: productData.category,
            price: productData.price,
            originalPrice: productData.original_price,
            currency: productData.currency,
            imageUrls: productData.image_urls,
            specifications: productData.specifications,
            stockQuantity: productData.stock_quantity,
            rating: productData.rating,
            reviewCount: productData.review_count,
            tags: productData.tags,
            brand: productData.brand,
            isRecommended: productData.is_recommended,
            isFeatured: productData.is_featured,
            createdAt: new Date(productData.created_at),
            updatedAt: new Date(productData.updated_at),
        };
    }
}

export const shoppingCartService = new ShoppingCartService();