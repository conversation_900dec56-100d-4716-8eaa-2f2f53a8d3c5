import React, { useEffect } from 'react';
import { Modal as RNModal, View, Pressable, Text } from 'react-native';

export interface ModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'small' | 'medium' | 'large' | 'fullscreen';
  showCloseButton?: boolean;
  closeOnBackdropPress?: boolean;
  accessibilityLabel?: string;
  testID?: string;
  // Enhanced features for agricultural use
  type?: 'confirmation' | 'detail' | 'form' | 'alert';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  voiceFeedbackEnabled?: boolean;
  onVoiceFeedback?: (text: string) => void;
  autoAnnounce?: boolean;
}

export const Modal: React.FC<ModalProps> = ({
  visible,
  onClose,
  title,
  children,
  size = 'medium',
  showCloseButton = true,
  closeOnBackdropPress = true,
  accessibilityLabel,
  testID,
  type = 'detail',
  priority = 'medium',
  voiceFeedbackEnabled = false,
  onVoiceFeedback,
  autoAnnounce = false,
}) => {
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return 'max-w-sm mx-4';
      case 'medium':
        return 'max-w-md mx-4';
      case 'large':
        return 'max-w-lg mx-4';
      case 'fullscreen':
        return 'flex-1 m-0';
      default:
        return 'max-w-md mx-4';
    }
  };

  const getTypeStyles = () => {
    switch (type) {
      case 'confirmation':
        return 'border-t-4 border-t-yellow-500';
      case 'alert':
        return priority === 'urgent'
          ? 'border-t-4 border-t-red-500'
          : 'border-t-4 border-t-orange-500';
      case 'form':
        return 'border-t-4 border-t-blue-500';
      default:
        return 'border-t-4 border-t-green-500';
    }
  };

  const handleClose = () => {
    if (voiceFeedbackEnabled && onVoiceFeedback) {
      onVoiceFeedback('Modal closed');
    }
    onClose();
  };

  // Auto-announce modal content when it opens
  useEffect(() => {
    if (visible && autoAnnounce && voiceFeedbackEnabled && onVoiceFeedback && title) {
      const announcement = `${type} modal opened: ${title}`;
      onVoiceFeedback(announcement);
    }
  }, [visible, autoAnnounce, voiceFeedbackEnabled, onVoiceFeedback, title, type]);

  return (
    <RNModal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
      accessibilityViewIsModal
      testID={testID}>
      <View className="flex-1 items-center justify-center bg-black/50">
        <Pressable
          className="absolute inset-0"
          onPress={closeOnBackdropPress ? handleClose : undefined}
          accessibilityLabel="Close modal"
        />

        <View
          className={`
            ${getSizeStyles()}
            ${getTypeStyles()}
            rounded-3xl bg-white shadow-2xl
            ${size === 'fullscreen' ? 'rounded-none' : ''}
          `}
          accessibilityLabel={accessibilityLabel || title}>
          {/* Header */}
          {(title || showCloseButton) && (
            <View className="flex-row items-center justify-between border-b border-earth-100 p-6">
              {title && (
                <Text className="flex-1 text-xl font-semibold text-earth-900">{title}</Text>
              )}

              {showCloseButton && (
                <Pressable
                  onPress={handleClose}
                  className="ml-4 min-h-[44px] min-w-[44px] items-center justify-center rounded-full bg-earth-100 p-3 active:bg-earth-200"
                  accessibilityRole="button"
                  accessibilityLabel="Close modal"
                  accessibilityHint="Closes the modal dialog">
                  <Text className="text-xl font-bold text-earth-600">×</Text>
                </Pressable>
              )}
            </View>
          )}

          {/* Content */}
          <View
            className={`
            ${title || showCloseButton ? 'p-6 pt-4' : 'p-6'}
            ${size === 'fullscreen' ? 'flex-1' : ''}
          `}>
            {children}
          </View>
        </View>
      </View>
    </RNModal>
  );
};
