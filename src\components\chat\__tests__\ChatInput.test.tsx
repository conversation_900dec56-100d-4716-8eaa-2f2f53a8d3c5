import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { ChatInput } from '../ChatInput';

describe('ChatInput', () => {
    const mockProps = {
        onSendMessage: jest.fn(),
        onSendImage: jest.fn(),
        onVoiceInput: jest.fn(),
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('renders correctly', () => {
        const { getByPlaceholderText } = render(<ChatInput {...mockProps} />);
        expect(getByPlaceholderText('Ask about your crops, soil, or farming...')).toBeTruthy();
    });

    it('calls onSendMessage when send button is pressed with text', () => {
        const { getByPlaceholderText, getByLabelText } = render(<ChatInput {...mockProps} />);

        const input = getByPlaceholderText('Ask about your crops, soil, or farming...');
        const sendButton = getByLabelText('Send message');

        fireEvent.changeText(input, 'Test message');
        fireEvent.press(sendButton);

        expect(mockProps.onSendMessage).toHaveBeenCalledWith('Test message');
    });

    it('calls onVoiceInput when voice button is pressed', () => {
        const { getByLabelText } = render(<ChatInput {...mockProps} voiceEnabled={true} />);

        const voiceButton = getByLabelText('Start voice input');
        fireEvent.press(voiceButton);

        expect(mockProps.onVoiceInput).toHaveBeenCalled();
    });

    it('disables send button when no text is entered', () => {
        const { getByLabelText } = render(<ChatInput {...mockProps} />);

        const sendButton = getByLabelText('Send message');
        expect(sendButton.props.accessibilityState?.disabled).toBe(true);
    });
});