import { supabase } from './client';
import { Database } from '../../types/database';

type CommunityPost = Database['public']['Tables']['community_posts']['Row'];
type PostComment = Database['public']['Tables']['post_comments']['Row'];
type CreatePostData = Database['public']['Tables']['community_posts']['Insert'];
type CreateCommentData = Database['public']['Tables']['post_comments']['Insert'];

export interface PostWithAuthor extends CommunityPost {
    author: {
        id: string;
        name: string;
        avatar?: string;
        location?: string;
        experienceLevel?: 'beginner' | 'intermediate' | 'expert';
    };
}

export interface CommentWithAuthor extends PostComment {
    author: {
        id: string;
        name: string;
        avatar?: string;
    };
}

export interface PostFilters {
    location?: {
        latitude: number;
        longitude: number;
        radius?: number; // in kilometers
    };
    following?: string[]; // user IDs
    category?: string;
    limit?: number;
    offset?: number;
}

export class CommunityService {
    /**
     * Fetch community posts with filters
     */
    static async fetchPosts(filters: PostFilters = {}): Promise<{
        posts: PostWithAuthor[];
        error: string | null;
    }> {
        try {
            let query = supabase
                .from('community_posts')
                .select(`
                    *,
                    users!community_posts_user_id_fkey (
                        id,
                        first_name,
                        last_name
                    ),
                    user_profiles!community_posts_user_id_fkey (
                        farm_location,
                        experience_level
                    )
                `)
                .order('created_at', { ascending: false });

            // Apply filters
            if (filters.limit) {
                query = query.limit(filters.limit);
            }

            if (filters.offset) {
                query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
            }

            // Location-based filtering
            if (filters.location) {
                // This would require PostGIS extension for proper geo queries
                // For now, we'll implement basic filtering
                query = query.not('location', 'is', null);
            }

            // Following filter
            if (filters.following && filters.following.length > 0) {
                query = query.in('user_id', filters.following);
            }

            const { data, error } = await query;

            if (error) {
                return { posts: [], error: error.message };
            }

            // Transform data to match interface
            const posts: PostWithAuthor[] = (data || []).map((post: any) => ({
                ...post,
                author: {
                    id: post.users.id,
                    name: `${post.users.first_name} ${post.users.last_name}`,
                    location: post.user_profiles?.farm_location || undefined,
                    experienceLevel: post.user_profiles?.experience_level || undefined,
                },
            }));

            return { posts, error: null };
        } catch (error) {
            return { posts: [], error: (error as Error).message };
        }
    }

    /**
     * Create a new community post
     */
    static async createPost(postData: {
        title: string;
        content: string;
        images?: string[];
        location?: { latitude: number; longitude: number };
    }): Promise<{
        post: PostWithAuthor | null;
        error: string | null;
    }> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                return { post: null, error: 'User not authenticated' };
            }

            // Upload images to Supabase Storage if provided
            let imageUrls: string[] = [];
            if (postData.images && postData.images.length > 0) {
                imageUrls = await this.uploadImages(postData.images);
            }

            // Format location as PostGIS point if provided
            let locationPoint: string | null = null;
            if (postData.location) {
                locationPoint = `POINT(${postData.location.longitude} ${postData.location.latitude})`;
            }

            const createData: CreatePostData = {
                user_id: user.id,
                title: postData.title,
                content: postData.content,
                image_urls: imageUrls.length > 0 ? imageUrls : null,
                location: locationPoint,
            };

            const { data, error } = await supabase
                .from('community_posts')
                .insert(createData)
                .select(`
                    *,
                    users!community_posts_user_id_fkey (
                        id,
                        first_name,
                        last_name
                    ),
                    user_profiles!community_posts_user_id_fkey (
                        farm_location,
                        experience_level
                    )
                `)
                .single();

            if (error) {
                return { post: null, error: error.message };
            }

            // Transform data to match interface
            const post: PostWithAuthor = {
                ...data,
                author: {
                    id: data.users.id,
                    name: `${data.users.first_name} ${data.users.last_name}`,
                    location: data.user_profiles?.farm_location || undefined,
                    experienceLevel: data.user_profiles?.experience_level || undefined,
                },
            };

            return { post, error: null };
        } catch (error) {
            return { post: null, error: (error as Error).message };
        }
    }

    /**
     * Update a community post
     */
    static async updatePost(
        postId: string,
        updates: {
            title?: string;
            content?: string;
            images?: string[];
        }
    ): Promise<{
        post: PostWithAuthor | null;
        error: string | null;
    }> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                return { post: null, error: 'User not authenticated' };
            }

            // Upload new images if provided
            let imageUrls: string[] | undefined;
            if (updates.images) {
                imageUrls = await this.uploadImages(updates.images);
            }

            const updateData: any = {};
            if (updates.title) updateData.title = updates.title;
            if (updates.content) updateData.content = updates.content;
            if (imageUrls) updateData.image_urls = imageUrls;

            const { data, error } = await supabase
                .from('community_posts')
                .update(updateData)
                .eq('id', postId)
                .eq('user_id', user.id) // Ensure user can only update their own posts
                .select(`
                    *,
                    users!community_posts_user_id_fkey (
                        id,
                        first_name,
                        last_name
                    ),
                    user_profiles!community_posts_user_id_fkey (
                        farm_location,
                        experience_level
                    )
                `)
                .single();

            if (error) {
                return { post: null, error: error.message };
            }

            // Transform data to match interface
            const post: PostWithAuthor = {
                ...data,
                author: {
                    id: data.users.id,
                    name: `${data.users.first_name} ${data.users.last_name}`,
                    location: data.user_profiles?.farm_location || undefined,
                    experienceLevel: data.user_profiles?.experience_level || undefined,
                },
            };

            return { post, error: null };
        } catch (error) {
            return { post: null, error: (error as Error).message };
        }
    }

    /**
     * Delete a community post
     */
    static async deletePost(postId: string): Promise<{
        success: boolean;
        error: string | null;
    }> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                return { success: false, error: 'User not authenticated' };
            }

            // First, get the post to check ownership and get image URLs for cleanup
            const { data: post, error: fetchError } = await supabase
                .from('community_posts')
                .select('user_id, image_urls')
                .eq('id', postId)
                .single();

            if (fetchError) {
                return { success: false, error: fetchError.message };
            }

            if (post.user_id !== user.id) {
                return { success: false, error: 'Unauthorized to delete this post' };
            }

            // Delete associated images from storage
            if (post.image_urls && post.image_urls.length > 0) {
                await this.deleteImages(post.image_urls);
            }

            // Delete the post (comments will be cascade deleted)
            const { error } = await supabase
                .from('community_posts')
                .delete()
                .eq('id', postId);

            if (error) {
                return { success: false, error: error.message };
            }

            return { success: true, error: null };
        } catch (error) {
            return { success: false, error: (error as Error).message };
        }
    }

    /**
     * Like or unlike a post
     */
    static async togglePostLike(postId: string): Promise<{
        liked: boolean;
        likesCount: number;
        error: string | null;
    }> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                return { liked: false, likesCount: 0, error: 'User not authenticated' };
            }

            // Check if user already liked the post
            const { data: existingLike } = await supabase
                .from('post_likes')
                .select('id')
                .eq('post_id', postId)
                .eq('user_id', user.id)
                .single();

            let liked: boolean;
            let likesCountChange: number;

            if (existingLike) {
                // Unlike the post
                await supabase
                    .from('post_likes')
                    .delete()
                    .eq('post_id', postId)
                    .eq('user_id', user.id);

                liked = false;
                likesCountChange = -1;
            } else {
                // Like the post
                await supabase
                    .from('post_likes')
                    .insert({
                        post_id: postId,
                        user_id: user.id,
                    });

                liked = true;
                likesCountChange = 1;
            }

            // Update likes count in the post
            const { data: updatedPost, error } = await supabase
                .from('community_posts')
                .update({
                    likes_count: supabase.raw(`likes_count + ${likesCountChange}`)
                })
                .eq('id', postId)
                .select('likes_count')
                .single();

            if (error) {
                return { liked: false, likesCount: 0, error: error.message };
            }

            return { liked, likesCount: updatedPost.likes_count, error: null };
        } catch (error) {
            return { liked: false, likesCount: 0, error: (error as Error).message };
        }
    }

    /**
     * Fetch comments for a post
     */
    static async fetchComments(postId: string): Promise<{
        comments: CommentWithAuthor[];
        error: string | null;
    }> {
        try {
            const { data, error } = await supabase
                .from('post_comments')
                .select(`
                    *,
                    users!post_comments_user_id_fkey (
                        id,
                        first_name,
                        last_name
                    )
                `)
                .eq('post_id', postId)
                .order('created_at', { ascending: true });

            if (error) {
                return { comments: [], error: error.message };
            }

            // Transform data to match interface
            const comments: CommentWithAuthor[] = (data || []).map((comment: any) => ({
                ...comment,
                author: {
                    id: comment.users.id,
                    name: `${comment.users.first_name} ${comment.users.last_name}`,
                },
            }));

            return { comments, error: null };
        } catch (error) {
            return { comments: [], error: (error as Error).message };
        }
    }

    /**
     * Add a comment to a post
     */
    static async addComment(
        postId: string,
        content: string
    ): Promise<{
        comment: CommentWithAuthor | null;
        error: string | null;
    }> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                return { comment: null, error: 'User not authenticated' };
            }

            const createData: CreateCommentData = {
                post_id: postId,
                user_id: user.id,
                content,
            };

            const { data, error } = await supabase
                .from('post_comments')
                .insert(createData)
                .select(`
                    *,
                    users!post_comments_user_id_fkey (
                        id,
                        first_name,
                        last_name
                    )
                `)
                .single();

            if (error) {
                return { comment: null, error: error.message };
            }

            // Update comments count in the post
            await supabase
                .from('community_posts')
                .update({
                    comments_count: supabase.raw('comments_count + 1')
                })
                .eq('id', postId);

            // Transform data to match interface
            const comment: CommentWithAuthor = {
                ...data,
                author: {
                    id: data.users.id,
                    name: `${data.users.first_name} ${data.users.last_name}`,
                },
            };

            return { comment, error: null };
        } catch (error) {
            return { comment: null, error: (error as Error).message };
        }
    }

    /**
     * Report a post for moderation
     */
    static async reportPost(
        postId: string,
        reason: string,
        description?: string
    ): Promise<{
        success: boolean;
        error: string | null;
    }> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                return { success: false, error: 'User not authenticated' };
            }

            const { error } = await supabase
                .from('post_reports')
                .insert({
                    post_id: postId,
                    reporter_id: user.id,
                    reason,
                    description,
                });

            if (error) {
                return { success: false, error: error.message };
            }

            return { success: true, error: null };
        } catch (error) {
            return { success: false, error: (error as Error).message };
        }
    }

    /**
     * Get post engagement metrics
     */
    static async getPostEngagement(postId: string): Promise<{
        views: number;
        likes: number;
        comments: number;
        shares: number;
        error: string | null;
    }> {
        try {
            const { data, error } = await supabase
                .from('community_posts')
                .select('likes_count, comments_count')
                .eq('id', postId)
                .single();

            if (error) {
                return { views: 0, likes: 0, comments: 0, shares: 0, error: error.message };
            }

            // For now, we'll return the basic metrics
            // Views and shares would require additional tracking tables
            return {
                views: 0, // Would need post_views table
                likes: data.likes_count,
                comments: data.comments_count,
                shares: 0, // Would need post_shares table
                error: null,
            };
        } catch (error) {
            return { views: 0, likes: 0, comments: 0, shares: 0, error: (error as Error).message };
        }
    }

    /**
     * Upload images to Supabase Storage
     */
    private static async uploadImages(imageUris: string[]): Promise<string[]> {
        const uploadPromises = imageUris.map(async (uri, index) => {
            try {
                // Convert URI to blob for upload
                const response = await fetch(uri);
                const blob = await response.blob();

                const fileName = `community-post-${Date.now()}-${index}.jpg`;
                const filePath = `community-posts/${fileName}`;

                const { data, error } = await supabase.storage
                    .from('images')
                    .upload(filePath, blob, {
                        contentType: 'image/jpeg',
                        upsert: false,
                    });

                if (error) {
                    console.error('Image upload error:', error);
                    return null;
                }

                // Get public URL
                const { data: { publicUrl } } = supabase.storage
                    .from('images')
                    .getPublicUrl(data.path);

                return publicUrl;
            } catch (error) {
                console.error('Image processing error:', error);
                return null;
            }
        });

        const results = await Promise.all(uploadPromises);
        return results.filter((url): url is string => url !== null);
    }

    /**
     * Delete images from Supabase Storage
     */
    private static async deleteImages(imageUrls: string[]): Promise<void> {
        try {
            const filePaths = imageUrls.map(url => {
                // Extract file path from public URL
                const urlParts = url.split('/');
                return urlParts.slice(-2).join('/'); // Get last two parts (folder/filename)
            });

            await supabase.storage
                .from('images')
                .remove(filePaths);
        } catch (error) {
            console.error('Image deletion error:', error);
        }
    }
}