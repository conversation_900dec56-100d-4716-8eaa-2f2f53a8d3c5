import { create } from 'zustand';
import { User, Session } from '@supabase/supabase-js';
import { AuthService, LoginCredentials, RegisterData } from '../services/supabase/auth';

interface AuthState {
    user: User | null;
    session: Session | null;
    isLoading: boolean;
    isAuthenticated: boolean;
    error: string | null;

    // Actions
    login: (credentials: LoginCredentials) => Promise<boolean>;
    register: (data: RegisterData) => Promise<boolean>;
    logout: () => Promise<void>;
    resetPassword: (email: string) => Promise<boolean>;
    updatePassword: (newPassword: string) => Promise<boolean>;
    verifyOTP: (phone: string, token: string) => Promise<boolean>;
    resendOTP: (phone: string) => Promise<boolean>;
    signInWithGoogle: () => Promise<boolean>;
    signInWithApple: () => Promise<boolean>;
    refreshSession: () => Promise<void>;
    clearError: () => void;
    initialize: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
    user: null,
    session: null,
    isLoading: false,
    isAuthenticated: false,
    error: null,

    login: async (credentials: LoginCredentials) => {
        set({ isLoading: true, error: null });

        try {
            const { user, session, error } = await AuthService.login(credentials);

            if (error) {
                set({ error: error.message, isLoading: false });
                return false;
            }

            set({
                user,
                session,
                isAuthenticated: !!user,
                isLoading: false,
                error: null,
            });

            return true;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Login failed',
                isLoading: false,
            });
            return false;
        }
    },

    register: async (data: RegisterData) => {
        set({ isLoading: true, error: null });

        try {
            const { user, session, error } = await AuthService.register(data);

            if (error) {
                set({ error: error.message, isLoading: false });
                return false;
            }

            set({
                user,
                session,
                isAuthenticated: !!user,
                isLoading: false,
                error: null,
            });

            return true;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Registration failed',
                isLoading: false,
            });
            return false;
        }
    },

    logout: async () => {
        set({ isLoading: true });

        try {
            const { error } = await AuthService.logout();

            if (error) {
                set({ error: error.message, isLoading: false });
                return;
            }

            set({
                user: null,
                session: null,
                isAuthenticated: false,
                isLoading: false,
                error: null,
            });
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Logout failed',
                isLoading: false,
            });
        }
    },

    resetPassword: async (email: string) => {
        set({ isLoading: true, error: null });

        try {
            const { error } = await AuthService.resetPassword(email);

            if (error) {
                set({ error: error.message, isLoading: false });
                return false;
            }

            set({ isLoading: false, error: null });
            return true;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Password reset failed',
                isLoading: false,
            });
            return false;
        }
    },

    updatePassword: async (newPassword: string) => {
        set({ isLoading: true, error: null });

        try {
            const { user, session, error } = await AuthService.updatePassword(newPassword);

            if (error) {
                set({ error: error.message, isLoading: false });
                return false;
            }

            set({
                user,
                session,
                isLoading: false,
                error: null,
            });

            return true;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Password update failed',
                isLoading: false,
            });
            return false;
        }
    },

    verifyOTP: async (phone: string, token: string) => {
        set({ isLoading: true, error: null });

        try {
            const { user, session, error } = await AuthService.verifyOTP(phone, token);

            if (error) {
                set({ error: error.message, isLoading: false });
                return false;
            }

            set({
                user,
                session,
                isAuthenticated: !!user,
                isLoading: false,
                error: null,
            });

            return true;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'OTP verification failed',
                isLoading: false,
            });
            return false;
        }
    },

    resendOTP: async (phone: string) => {
        set({ isLoading: true, error: null });

        try {
            const { error } = await AuthService.resendOTP(phone);

            if (error) {
                set({ error: error.message, isLoading: false });
                return false;
            }

            set({ isLoading: false, error: null });
            return true;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Resend OTP failed',
                isLoading: false,
            });
            return false;
        }
    },

    signInWithGoogle: async () => {
        set({ isLoading: true, error: null });

        try {
            const { error } = await AuthService.signInWithGoogle();

            if (error) {
                set({ error: error.message, isLoading: false });
                return false;
            }

            // OAuth flow will handle the session update via auth state change listener
            set({ isLoading: false, error: null });
            return true;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Google sign-in failed',
                isLoading: false,
            });
            return false;
        }
    },

    signInWithApple: async () => {
        set({ isLoading: true, error: null });

        try {
            const { error } = await AuthService.signInWithApple();

            if (error) {
                set({ error: error.message, isLoading: false });
                return false;
            }

            // OAuth flow will handle the session update via auth state change listener
            set({ isLoading: false, error: null });
            return true;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Apple sign-in failed',
                isLoading: false,
            });
            return false;
        }
    },

    refreshSession: async () => {
        try {
            const { user, session, error } = await AuthService.refreshSession();

            if (error) {
                console.error('Session refresh failed:', error.message);
                return;
            }

            set({
                user,
                session,
                isAuthenticated: !!user,
            });
        } catch (error) {
            console.error('Session refresh error:', error);
        }
    },

    clearError: () => {
        set({ error: null });
    },

    initialize: async () => {
        set({ isLoading: true });

        try {
            // Get current session
            const { session, error } = await AuthService.getSession();

            if (error) {
                console.error('Session initialization error:', error.message);
                set({ isLoading: false });
                return;
            }

            if (session) {
                set({
                    user: session.user,
                    session,
                    isAuthenticated: true,
                    isLoading: false,
                });
            } else {
                set({
                    user: null,
                    session: null,
                    isAuthenticated: false,
                    isLoading: false,
                });
            }

            // Set up auth state change listener
            AuthService.onAuthStateChange((event, session) => {
                if (event === 'SIGNED_IN' && session) {
                    set({
                        user: session.user,
                        session,
                        isAuthenticated: true,
                        error: null,
                    });
                } else if (event === 'SIGNED_OUT') {
                    set({
                        user: null,
                        session: null,
                        isAuthenticated: false,
                        error: null,
                    });
                } else if (event === 'TOKEN_REFRESHED' && session) {
                    set({
                        user: session.user,
                        session,
                        isAuthenticated: true,
                    });
                }
            });
        } catch (error) {
            console.error('Auth initialization error:', error);
            set({ isLoading: false });
        }
    },
}));