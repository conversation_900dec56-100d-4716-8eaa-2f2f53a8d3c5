-- Points and Subscription System Migration

-- Create points_transactions table
CREATE TABLE IF NOT EXISTS points_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    amount INTEGER NOT NULL,
    transaction_type VARCHAR(10) CHECK (transaction_type IN ('earned', 'spent', 'bonus', 'refund')),
    source VARCHAR(50) CHECK (source IN ('task_completion', 'daily_checkin', 'community_post', 'ai_consultation', 'subscription_bonus', 'achievement', 'referral', 'admin_adjustment')),
    description TEXT NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create points_balances table
CREATE TABLE IF NOT EXISTS points_balances (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    total_points INTEGER DEFAULT 0,
    available_points INTEGER DEFAULT 0,
    pending_points INTEGER DEFAULT 0,
    lifetime_earned INTEGER DEFAULT 0,
    lifetime_spent INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create achievements table
CREATE TABLE IF NOT EXISTS achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    icon VARCHAR(10) NOT NULL,
    category VARCHAR(20) CHECK (category IN ('farming', 'community', 'learning', 'consistency', 'milestone')),
    points_reward INTEGER NOT NULL,
    requirements JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_achievements table
CREATE TABLE IF NOT EXISTS user_achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    achievement_id UUID REFERENCES achievements(id) ON DELETE CASCADE,
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    points_awarded INTEGER NOT NULL,
    UNIQUE(user_id, achievement_id)
);

-- Create subscription_plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    billing_period VARCHAR(10) CHECK (billing_period IN ('monthly', 'yearly')),
    features JSONB NOT NULL,
    points_included INTEGER DEFAULT 0,
    ai_consultations_limit INTEGER DEFAULT 5, -- -1 for unlimited
    priority_support BOOLEAN DEFAULT false,
    offline_access BOOLEAN DEFAULT false,
    advanced_analytics BOOLEAN DEFAULT false,
    api_access BOOLEAN DEFAULT false,
    multi_farm_support BOOLEAN DEFAULT false,
    team_collaboration BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_subscriptions table
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    plan_id UUID REFERENCES subscription_plans(id),
    status VARCHAR(20) CHECK (status IN ('active', 'cancelled', 'expired', 'past_due', 'trialing')),
    current_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    current_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    cancel_at_period_end BOOLEAN DEFAULT false,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    trial_start TIMESTAMP WITH TIME ZONE,
    trial_end TIMESTAMP WITH TIME ZONE,
    payment_method_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create subscription_usage table
CREATE TABLE IF NOT EXISTS subscription_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    subscription_id UUID REFERENCES user_subscriptions(id) ON DELETE CASCADE,
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    ai_consultations_used INTEGER DEFAULT 0,
    ai_consultations_limit INTEGER NOT NULL,
    image_analyses_used INTEGER DEFAULT 0,
    storage_used_mb INTEGER DEFAULT 0,
    api_calls_used INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create daily_streaks table
CREATE TABLE IF NOT EXISTS daily_streaks (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    last_activity_date TIMESTAMP WITH TIME ZONE NOT NULL,
    streak_multiplier DECIMAL(3,2) DEFAULT 1.0
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_points_transactions_user_id ON points_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_points_transactions_created_at ON points_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_points_transactions_source ON points_transactions(source);

CREATE INDEX IF NOT EXISTS idx_user_achievements_user_id ON user_achievements(user_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_achievement_id ON user_achievements(achievement_id);

CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_period_end ON user_subscriptions(current_period_end);

CREATE INDEX IF NOT EXISTS idx_subscription_usage_user_id ON subscription_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_subscription_usage_subscription_id ON subscription_usage(subscription_id);

-- Create function to update points balance
CREATE OR REPLACE FUNCTION update_points_balance(
    p_user_id UUID,
    p_amount INTEGER,
    p_transaction_type VARCHAR
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO points_balances (user_id, total_points, available_points, lifetime_earned, lifetime_spent, last_updated)
    VALUES (p_user_id, 0, 0, 0, 0, NOW())
    ON CONFLICT (user_id) DO NOTHING;

    IF p_transaction_type = 'earned' THEN
        UPDATE points_balances 
        SET 
            total_points = total_points + p_amount,
            available_points = available_points + p_amount,
            lifetime_earned = lifetime_earned + p_amount,
            last_updated = NOW()
        WHERE user_id = p_user_id;
    ELSIF p_transaction_type = 'spent' THEN
        UPDATE points_balances 
        SET 
            total_points = total_points + p_amount, -- p_amount is negative for spending
            available_points = available_points + p_amount,
            lifetime_spent = lifetime_spent + ABS(p_amount),
            last_updated = NOW()
        WHERE user_id = p_user_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create function to increment usage
CREATE OR REPLACE FUNCTION increment_usage(
    p_user_id UUID,
    p_subscription_id UUID,
    p_field VARCHAR,
    p_increment INTEGER
)
RETURNS VOID AS $$
DECLARE
    sql_query TEXT;
BEGIN
    sql_query := format('
        UPDATE subscription_usage 
        SET %I = %I + $1, updated_at = NOW()
        WHERE user_id = $2 AND subscription_id = $3
    ', p_field, p_field);
    
    EXECUTE sql_query USING p_increment, p_user_id, p_subscription_id;
END;
$$ LANGUAGE plpgsql;

-- Insert default subscription plans
INSERT INTO subscription_plans (id, name, description, price, currency, billing_period, features, points_included, ai_consultations_limit, priority_support, offline_access, advanced_analytics, api_access, multi_farm_support, team_collaboration, is_active, sort_order)
VALUES 
    ('free', 'Free', 'Perfect for getting started with smart farming', 0, 'USD', 'monthly', 
     '[{"id": "basic_crop_planning", "name": "Basic Crop Planning", "description": "Create and manage basic crop plans", "category": "core", "is_premium": false}, {"id": "weather_alerts", "name": "Weather Alerts", "description": "Receive weather notifications", "category": "core", "is_premium": false}, {"id": "community_access", "name": "Community Access", "description": "Join farmer community discussions", "category": "core", "is_premium": false}, {"id": "limited_ai", "name": "Limited AI Consultations", "description": "5 AI consultations per month", "category": "ai", "is_premium": false}]'::jsonb,
     100, 5, false, false, false, false, false, false, true, 1),
    
    ('basic', 'Basic', 'Enhanced features for serious farmers', 9.99, 'USD', 'monthly',
     '[{"id": "advanced_crop_planning", "name": "Advanced Crop Planning", "description": "Detailed crop planning with AI recommendations", "category": "core", "is_premium": true}, {"id": "image_analysis", "name": "Image Analysis", "description": "AI-powered plant disease detection", "category": "ai", "is_premium": true}, {"id": "offline_access", "name": "Offline Access", "description": "Access core features without internet", "category": "core", "is_premium": true}, {"id": "priority_support", "name": "Priority Support", "description": "Faster response times for support", "category": "support", "is_premium": true}, {"id": "extended_ai", "name": "Extended AI Consultations", "description": "25 AI consultations per month", "category": "ai", "is_premium": true}]'::jsonb,
     500, 25, true, true, false, false, false, false, true, 2),
    
    ('premium', 'Premium', 'Complete farming solution with unlimited AI', 19.99, 'USD', 'monthly',
     '[{"id": "unlimited_ai", "name": "Unlimited AI Consultations", "description": "No limits on AI consultations", "category": "ai", "is_premium": true}, {"id": "advanced_analytics", "name": "Advanced Analytics", "description": "Detailed insights and reports", "category": "analytics", "is_premium": true}, {"id": "custom_recommendations", "name": "Custom Recommendations", "description": "Personalized farming recommendations", "category": "ai", "is_premium": true}, {"id": "api_access", "name": "API Access", "description": "Integrate with third-party tools", "category": "integration", "is_premium": true}, {"id": "export_data", "name": "Data Export", "description": "Export your farming data", "category": "core", "is_premium": true}]'::jsonb,
     1000, -1, true, true, true, true, false, false, true, 3),
    
    ('pro', 'Pro', 'Enterprise solution for large operations', 49.99, 'USD', 'monthly',
     '[{"id": "multi_farm_management", "name": "Multi-Farm Management", "description": "Manage multiple farm locations", "category": "core", "is_premium": true}, {"id": "team_collaboration", "name": "Team Collaboration", "description": "Share access with team members", "category": "collaboration", "is_premium": true}, {"id": "white_label", "name": "White Label Options", "description": "Custom branding options", "category": "integration", "is_premium": true}, {"id": "priority_development", "name": "Priority Development", "description": "Influence feature development", "category": "support", "is_premium": true}, {"id": "dedicated_support", "name": "Dedicated Support", "description": "Personal account manager", "category": "support", "is_premium": true}]'::jsonb,
     2500, -1, true, true, true, true, true, true, true, 4)
ON CONFLICT (name) DO NOTHING;

-- Insert default achievements
INSERT INTO achievements (id, name, description, icon, category, points_reward, requirements, is_active)
VALUES 
    ('first_steps', 'First Steps', 'Complete your first farming task', '🌱', 'farming', 25, '{"type": "task_count", "target_value": 1}', true),
    ('dedicated_farmer', 'Dedicated Farmer', 'Complete 10 farming tasks', '🚜', 'farming', 100, '{"type": "task_count", "target_value": 10}', true),
    ('community_helper', 'Community Helper', 'Create 5 community posts', '🤝', 'community', 75, '{"type": "community_posts", "target_value": 5}', true),
    ('ai_explorer', 'AI Explorer', 'Use AI consultation 10 times', '🤖', 'learning', 50, '{"type": "ai_consultations", "target_value": 10}', true),
    ('consistent_farmer', 'Consistent Farmer', 'Stay active for 7 consecutive days', '📅', 'consistency', 150, '{"type": "days_active", "target_value": 7, "timeframe": "daily"}', true),
    ('points_collector', 'Points Collector', 'Earn 1000 total points', '💎', 'milestone', 200, '{"type": "points_earned", "target_value": 1000, "timeframe": "all_time"}', true)
ON CONFLICT (name) DO NOTHING;

-- Enable Row Level Security
ALTER TABLE points_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE points_balances ENABLE ROW LEVEL SECURITY;
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_streaks ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Points transactions: users can only see their own
CREATE POLICY "Users can view their own points transactions" ON points_transactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own points transactions" ON points_transactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Points balances: users can only see their own
CREATE POLICY "Users can view their own points balance" ON points_balances
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own points balance" ON points_balances
    FOR ALL USING (auth.uid() = user_id);

-- Achievements: publicly readable
CREATE POLICY "Achievements are publicly readable" ON achievements
    FOR SELECT USING (true);

-- User achievements: users can only see their own
CREATE POLICY "Users can view their own achievements" ON user_achievements
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own achievements" ON user_achievements
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Subscription plans: publicly readable
CREATE POLICY "Subscription plans are publicly readable" ON subscription_plans
    FOR SELECT USING (true);

-- User subscriptions: users can only see their own
CREATE POLICY "Users can view their own subscriptions" ON user_subscriptions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own subscriptions" ON user_subscriptions
    FOR ALL USING (auth.uid() = user_id);

-- Subscription usage: users can only see their own
CREATE POLICY "Users can view their own subscription usage" ON subscription_usage
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own subscription usage" ON subscription_usage
    FOR ALL USING (auth.uid() = user_id);

-- Daily streaks: users can only see their own
CREATE POLICY "Users can view their own daily streaks" ON daily_streaks
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own daily streaks" ON daily_streaks
    FOR ALL USING (auth.uid() = user_id);