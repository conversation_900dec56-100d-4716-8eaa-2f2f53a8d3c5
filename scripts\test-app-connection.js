#!/usr/bin/env node

/**
 * Application Connection Test
 * This script tests the database connection as the app would use it
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

console.log('🔧 Testing Application Database Connection');
console.log(`📍 URL: ${SUPABASE_URL}`);

// Initialize Supabase client with anon key (as the app would)
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testPublicTables() {
  console.log('\n📋 Testing public table access...');

  // Test achievements table (should be publicly readable)
  try {
    const { data, error } = await supabase
      .from('achievements')
      .select('name, description, points_reward')
      .limit(3);

    if (error) {
      console.log('❌ Achievements table error:', error.message);
      return false;
    }

    console.log(`✅ Achievements table accessible (${data?.length || 0} records)`);
    if (data && data.length > 0) {
      data.forEach((achievement) => {
        console.log(`   - ${achievement.name}: ${achievement.points_reward} points`);
      });
    }
    return true;
  } catch (error) {
    console.log('❌ Achievements table exception:', error.message);
    return false;
  }
}

async function testSubscriptionPlans() {
  console.log('\n💳 Testing subscription plans...');

  try {
    const { data, error } = await supabase
      .from('subscription_plans')
      .select('name, description, price, currency')
      .eq('is_active', true);

    if (error) {
      console.log('❌ Subscription plans error:', error.message);
      return false;
    }

    console.log(`✅ Subscription plans accessible (${data?.length || 0} plans)`);
    if (data && data.length > 0) {
      data.forEach((plan) => {
        console.log(
          `   - ${plan.name}: ${plan.price} ${plan.currency}/${plan.billing_period || 'month'}`
        );
      });
    }
    return true;
  } catch (error) {
    console.log('❌ Subscription plans exception:', error.message);
    return false;
  }
}

async function testProtectedTables() {
  console.log('\n🔒 Testing protected table access (should be blocked)...');

  // Test user_profiles table (should be protected by RLS)
  try {
    const { data, error } = await supabase.from('user_profiles').select('*').limit(1);

    if (error) {
      if (error.message.includes('policy') || error.message.includes('permission')) {
        console.log('✅ User profiles properly protected by RLS');
        return true;
      } else {
        console.log('❌ User profiles error (unexpected):', error.message);
        return false;
      }
    }

    console.log('⚠️  User profiles accessible without auth (potential security issue)');
    return false;
  } catch (error) {
    console.log('❌ User profiles exception:', error.message);
    return false;
  }
}

async function testAuthSignup() {
  console.log('\n👤 Testing auth signup capability...');

  try {
    // Try to sign up with a test email (this should work but not actually create a user)
    const { data, error } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'testpassword123',
      options: {
        data: {
          first_name: 'Test',
          last_name: 'User',
        },
      },
    });

    if (error) {
      if (error.message.includes('email') || error.message.includes('signup')) {
        console.log('✅ Auth signup configured (may require email confirmation)');
        return true;
      } else {
        console.log('❌ Auth signup error:', error.message);
        return false;
      }
    }

    console.log('✅ Auth signup working');
    return true;
  } catch (error) {
    console.log('❌ Auth signup exception:', error.message);
    return false;
  }
}

async function testStorageBuckets() {
  console.log('\n📦 Testing storage buckets...');

  try {
    const { data, error } = await supabase.storage.listBuckets();

    if (error) {
      console.log('❌ Storage buckets error:', error.message);
      return false;
    }

    console.log(`✅ Storage accessible (${data?.length || 0} buckets)`);
    if (data && data.length > 0) {
      data.forEach((bucket) => {
        console.log(`   - ${bucket.name} (${bucket.public ? 'public' : 'private'})`);
      });
    } else {
      console.log('   ℹ️  No storage buckets configured yet');
    }
    return true;
  } catch (error) {
    console.log('❌ Storage buckets exception:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting application connection test...\n');

  const tests = [
    { name: 'Public Tables', test: testPublicTables },
    { name: 'Subscription Plans', test: testSubscriptionPlans },
    { name: 'Protected Tables', test: testProtectedTables },
    { name: 'Auth Signup', test: testAuthSignup },
    { name: 'Storage Buckets', test: testStorageBuckets },
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const { name, test } of tests) {
    const result = await test();
    if (result) {
      passedTests++;
    }
  }

  console.log('\n📊 Test Summary:');
  console.log(`   ✅ Passed: ${passedTests}/${totalTests}`);
  console.log(`   📈 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

  if (passedTests >= totalTests * 0.8) {
    console.log('\n🎉 Application connection test successful!');
    console.log('✅ Database is ready for production use.');
    console.log('\n📝 Next Steps:');
    console.log('   1. Create storage buckets for image uploads');
    console.log('   2. Configure email templates for auth');
    console.log('   3. Set up monitoring and alerts');
    console.log('   4. Test the mobile app connection');
  } else {
    console.log('\n⚠️  Some connection tests failed.');
    console.log('❌ Please review the issues above.');
  }

  process.exit(passedTests >= totalTests * 0.8 ? 0 : 1);
}

// Run the test
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Connection test failed:', error);
    process.exit(1);
  });
}
