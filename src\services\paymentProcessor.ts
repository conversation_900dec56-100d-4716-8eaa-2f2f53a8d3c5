/**
 * Payment Processing Service
 * Handles secure payment processing with multiple payment methods
 * Integrates with payment gateways and manages payment flows
 */

import { PaymentMethod, ShippingAddress } from '../types/product';

export interface PaymentRequest {
    amount: number;
    currency: string;
    paymentMethod: PaymentMethod;
    billingAddress?: ShippingAddress;
    orderId: string;
    userId: string;
    description?: string;
}

export interface PaymentResult {
    success: boolean;
    transactionId?: string;
    paymentIntentId?: string;
    error?: string;
    requiresAction?: boolean;
    actionUrl?: string;
}

export interface PaymentMethodDetails {
    id: string;
    type: 'credit_card' | 'debit_card' | 'mobile_money' | 'cash_on_delivery';
    last4?: string;
    brand?: string;
    expiryMonth?: number;
    expiryYear?: number;
    isDefault: boolean;
    billingAddress?: ShippingAddress;
}

export class PaymentProcessorService {
    private readonly STRIPE_PUBLISHABLE_KEY = process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY;
    private readonly PAYPAL_CLIENT_ID = process.env.EXPO_PUBLIC_PAYPAL_CLIENT_ID;

    /**
     * Process payment with the selected payment method
     */
    async processPayment(paymentRequest: PaymentRequest): Promise<PaymentResult> {
        try {
            switch (paymentRequest.paymentMethod.type) {
                case 'credit_card':
                case 'debit_card':
                    return this.processCardPayment(paymentRequest);
                case 'mobile_money':
                    return this.processMobileMoneyPayment(paymentRequest);
                case 'cash_on_delivery':
                    return this.processCashOnDeliveryPayment(paymentRequest);
                default:
                    throw new Error(`Unsupported payment method: ${paymentRequest.paymentMethod.type}`);
            }
        } catch (error) {
            console.error('Payment processing error:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Payment processing failed',
            };
        }
    }

    /**
     * Validate payment method details
     */
    async validatePaymentMethod(paymentMethod: PaymentMethod): Promise<{
        isValid: boolean;
        errors: string[];
    }> {
        const errors: string[] = [];

        switch (paymentMethod.type) {
            case 'credit_card':
            case 'debit_card':
                errors.push(...this.validateCardDetails(paymentMethod.details));
                break;
            case 'mobile_money':
                errors.push(...this.validateMobileMoneyDetails(paymentMethod.details));
                break;
            case 'cash_on_delivery':
                // No validation needed for COD
                break;
            default:
                errors.push('Invalid payment method type');
        }

        return {
            isValid: errors.length === 0,
            errors,
        };
    }

    /**
     * Calculate payment processing fees
     */
    calculateProcessingFee(amount: number, paymentMethod: PaymentMethod): number {
        switch (paymentMethod.type) {
            case 'credit_card':
                // 2.9% + $0.30 for credit cards
                return Math.round((amount * 0.029 + 0.30) * 100) / 100;
            case 'debit_card':
                // 1.5% + $0.30 for debit cards
                return Math.round((amount * 0.015 + 0.30) * 100) / 100;
            case 'mobile_money':
                // 1.5% for mobile money
                return Math.round(amount * 0.015 * 100) / 100;
            case 'cash_on_delivery':
                // Fixed fee for COD
                return 2.00;
            default:
                return 0;
        }
    }

    /**
     * Get supported payment methods for the user's region
     */
    getSupportedPaymentMethods(countryCode: string): Array<{
        type: PaymentMethod['type'];
        name: string;
        icon: string;
        description: string;
        processingFee: string;
    }> {
        const methods = [
            {
                type: 'credit_card' as const,
                name: 'Credit Card',
                icon: '💳',
                description: 'Visa, Mastercard, American Express',
                processingFee: '2.9% + $0.30',
            },
            {
                type: 'debit_card' as const,
                name: 'Debit Card',
                icon: '💳',
                description: 'Bank debit cards',
                processingFee: '1.5% + $0.30',
            },
            {
                type: 'cash_on_delivery' as const,
                name: 'Cash on Delivery',
                icon: '💵',
                description: 'Pay when your order arrives',
                processingFee: '$2.00 flat fee',
            },
        ];

        // Add region-specific payment methods
        if (countryCode === 'KE' || countryCode === 'UG' || countryCode === 'TZ') {
            methods.push({
                type: 'mobile_money' as const,
                name: 'Mobile Money',
                icon: '📱',
                description: 'M-Pesa, Airtel Money, MTN Mobile Money',
                processingFee: '1.5%',
            });
        }

        return methods;
    }

    /**
     * Create payment intent for card payments
     */
    async createPaymentIntent(
        amount: number,
        currency: string,
        orderId: string
    ): Promise<{
        clientSecret: string;
        paymentIntentId: string;
    }> {
        try {
            // In a real implementation, this would call your backend API
            // which would create a payment intent with Stripe
            const response = await fetch('/api/create-payment-intent', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    amount: Math.round(amount * 100), // Convert to cents
                    currency: currency.toLowerCase(),
                    metadata: {
                        orderId,
                    },
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to create payment intent');
            }

            const data = await response.json();
            return {
                clientSecret: data.client_secret,
                paymentIntentId: data.id,
            };
        } catch (error) {
            console.error('Error creating payment intent:', error);
            throw new Error('Failed to initialize payment');
        }
    }

    /**
     * Confirm payment with payment method
     */
    async confirmPayment(
        clientSecret: string,
        paymentMethodId: string
    ): Promise<PaymentResult> {
        try {
            // In a real implementation, this would use Stripe's confirmPayment
            // For now, we'll simulate the payment confirmation
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Simulate success/failure
            const success = Math.random() > 0.1; // 90% success rate

            if (success) {
                return {
                    success: true,
                    transactionId: `txn_${Date.now()}`,
                    paymentIntentId: clientSecret.split('_secret_')[0],
                };
            } else {
                return {
                    success: false,
                    error: 'Payment was declined by your bank',
                };
            }
        } catch (error) {
            console.error('Error confirming payment:', error);
            return {
                success: false,
                error: 'Payment confirmation failed',
            };
        }
    }

    /**
     * Process refund for a payment
     */
    async processRefund(
        transactionId: string,
        amount?: number,
        reason?: string
    ): Promise<{
        success: boolean;
        refundId?: string;
        error?: string;
    }> {
        try {
            // In a real implementation, this would call your backend API
            // to process the refund through the payment gateway
            const response = await fetch('/api/process-refund', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    transactionId,
                    amount: amount ? Math.round(amount * 100) : undefined,
                    reason,
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to process refund');
            }

            const data = await response.json();
            return {
                success: true,
                refundId: data.id,
            };
        } catch (error) {
            console.error('Error processing refund:', error);
            return {
                success: false,
                error: 'Refund processing failed',
            };
        }
    }

    // Private helper methods

    private async processCardPayment(paymentRequest: PaymentRequest): Promise<PaymentResult> {
        try {
            // Create payment intent
            const { clientSecret, paymentIntentId } = await this.createPaymentIntent(
                paymentRequest.amount,
                paymentRequest.currency,
                paymentRequest.orderId
            );

            // In a real implementation, you would use Stripe's payment sheet
            // or card element to collect payment details securely
            const paymentMethodId = paymentRequest.paymentMethod.details.paymentMethodId;

            if (!paymentMethodId) {
                throw new Error('Payment method ID is required for card payments');
            }

            // Confirm payment
            const result = await this.confirmPayment(clientSecret, paymentMethodId);

            return {
                ...result,
                paymentIntentId,
            };
        } catch (error) {
            console.error('Card payment error:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Card payment failed',
            };
        }
    }

    private async processMobileMoneyPayment(paymentRequest: PaymentRequest): Promise<PaymentResult> {
        try {
            const { phoneNumber, provider } = paymentRequest.paymentMethod.details;

            if (!phoneNumber || !provider) {
                throw new Error('Phone number and provider are required for mobile money');
            }

            // Simulate mobile money payment processing
            await new Promise(resolve => setTimeout(resolve, 3000));

            // In a real implementation, this would integrate with mobile money APIs
            // like M-Pesa, Airtel Money, etc.
            const success = Math.random() > 0.05; // 95% success rate

            if (success) {
                return {
                    success: true,
                    transactionId: `mm_${Date.now()}`,
                };
            } else {
                return {
                    success: false,
                    error: 'Mobile money payment failed. Please check your balance and try again.',
                };
            }
        } catch (error) {
            console.error('Mobile money payment error:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Mobile money payment failed',
            };
        }
    }

    private async processCashOnDeliveryPayment(paymentRequest: PaymentRequest): Promise<PaymentResult> {
        // COD doesn't require actual payment processing
        // Just record the payment method for order fulfillment
        return {
            success: true,
            transactionId: `cod_${paymentRequest.orderId}`,
        };
    }

    private validateCardDetails(details: any): string[] {
        const errors: string[] = [];

        if (!details.cardNumber || details.cardNumber.length < 13) {
            errors.push('Invalid card number');
        }

        if (!details.expiryMonth || details.expiryMonth < 1 || details.expiryMonth > 12) {
            errors.push('Invalid expiry month');
        }

        if (!details.expiryYear || details.expiryYear < new Date().getFullYear()) {
            errors.push('Invalid expiry year');
        }

        if (!details.cvv || details.cvv.length < 3) {
            errors.push('Invalid CVV');
        }

        if (!details.cardholderName || details.cardholderName.trim().length < 2) {
            errors.push('Cardholder name is required');
        }

        return errors;
    }

    private validateMobileMoneyDetails(details: any): string[] {
        const errors: string[] = [];

        if (!details.phoneNumber || !/^\+?[1-9]\d{1,14}$/.test(details.phoneNumber)) {
            errors.push('Invalid phone number');
        }

        if (!details.provider || !['mpesa', 'airtel', 'mtn'].includes(details.provider)) {
            errors.push('Invalid mobile money provider');
        }

        return errors;
    }
}

export const paymentProcessorService = new PaymentProcessorService();