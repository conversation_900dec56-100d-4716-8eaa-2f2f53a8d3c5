import { supabase } from './client';

interface UploadResult {
  publicUrl: string | null;
  error: string | null;
}

export class StorageService {
  /**
   * Uploads a file to a specified Supabase storage bucket.
   * @param bucketName The name of the storage bucket.
   * @param filePath The path where the file will be stored in the bucket (e.g., 'users/user1/avatar.jpg').
   * @param file The file data (Blob, File, or Buffer).
   * @param contentType The MIME type of the file (e.g., 'image/jpeg').
   * @param upsert Whether to upsert the file if it already exists.
   * @returns An object containing the public URL of the uploaded file or an error message.
   */
  static async uploadFile(
    bucketName: string,
    filePath: string,
    file: Blob | File | Buffer,
    contentType: string,
    upsert: boolean = true
  ): Promise<UploadResult> {
    try {
      const { data, error } = await supabase.storage
        .from(bucketName)
        .upload(filePath, file, {
          contentType,
          upsert,
        });

      if (error) {
        console.error(`Error uploading file to ${bucketName}/${filePath}:`, error);
        return { publicUrl: null, error: error.message };
      }

      const { data: publicUrlData } = supabase.storage
        .from(bucketName)
        .getPublicUrl(data.path);

      return { publicUrl: publicUrlData.publicUrl, error: null };
    } catch (error) {
      console.error(`Unexpected error during file upload to ${bucketName}/${filePath}:`, error);
      return { publicUrl: null, error: (error as Error).message };
    }
  }

  /**
   * Deletes files from a specified Supabase storage bucket.
   * @param bucketName The name of the storage bucket.
   * @param filePaths An array of file paths to delete from the bucket.
   * @returns An object indicating success or an error message.
   */
  static async deleteFiles(
    bucketName: string,
    filePaths: string[]
  ): Promise<{ success: boolean; error: string | null }> {
    try {
      const { error } = await supabase.storage
        .from(bucketName)
        .remove(filePaths);

      if (error) {
        console.error(`Error deleting files from ${bucketName}:`, error);
        return { success: false, error: error.message };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error(`Unexpected error during file deletion from ${bucketName}:`, error);
      return { success: false, error: (error as Error).message };
    }
  }
}