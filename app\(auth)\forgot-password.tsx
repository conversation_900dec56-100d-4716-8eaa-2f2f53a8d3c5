import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, Pressable, Animated, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Button } from '../../src/components/ui/Button';
import { Input } from '../../src/components/ui/Input';
import { AppProvider, useApp } from '../../src/contexts/AppContext';

const ForgotPasswordContent: React.FC = () => {
    const { t, isRTL, isVoiceEnabled, speak } = useApp();
    const [fadeAnim] = useState(new Animated.Value(0));
    const [slideAnim] = useState(new Animated.Value(50));

    // Form state
    const [resetMethod, setResetMethod] = useState<'email' | 'phone'>('email');
    const [email, setEmail] = useState('');
    const [phone, setPhone] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    // Form validation
    const [errors, setErrors] = useState<{
        email?: string;
        phone?: string;
    }>({});

    useEffect(() => {
        // Animate entrance
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 800,
                useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 800,
                useNativeDriver: true,
            }),
        ]).start();

        // Speak welcome message if voice is enabled
        if (isVoiceEnabled) {
            speak(t('forgotPassword.title') + '. ' + t('forgotPassword.description'));
        }
    }, [isVoiceEnabled]);

    const validateForm = () => {
        const newErrors: typeof errors = {};

        if (resetMethod === 'email') {
            if (!email.trim()) {
                newErrors.email = t('forgotPassword.errors.emailRequired');
            } else if (!/\S+@\S+\.\S+/.test(email)) {
                newErrors.email = t('forgotPassword.errors.emailInvalid');
            }
        } else {
            if (!phone.trim()) {
                newErrors.phone = t('forgotPassword.errors.phoneRequired');
            } else if (!/^\+?[\d\s-()]+$/.test(phone)) {
                newErrors.phone = t('forgotPassword.errors.phoneInvalid');
            }
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSendResetCode = async () => {
        if (!validateForm()) {
            if (isVoiceEnabled) {
                const errorMessages = Object.values(errors).join('. ');
                speak(t('forgotPassword.errors.validationFailed') + '. ' + errorMessages);
            }
            return;
        }

        setIsLoading(true);

        try {
            // TODO: Implement actual password reset request
            await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call

            if (isVoiceEnabled) {
                speak(t('forgotPassword.codeSent'));
            }

            // Navigate to verification screen with the contact method
            const contactInfo = resetMethod === 'email' ? email : phone;
            router.push({
                pathname: '/(auth)/verify-reset-code',
                params: { method: resetMethod, contact: contactInfo }
            });
        } catch (error) {
            if (isVoiceEnabled) {
                speak(t('forgotPassword.errors.sendFailed'));
            }
            Alert.alert(t('forgotPassword.errors.title'), t('forgotPassword.errors.sendFailed'));
        } finally {
            setIsLoading(false);
        }
    };

    const handleBackToLogin = () => {
        if (isVoiceEnabled) {
            speak(t('forgotPassword.backToLogin'));
        }
        router.back();
    };

    const toggleResetMethod = (method: 'email' | 'phone') => {
        setResetMethod(method);
        setErrors({});
        if (isVoiceEnabled) {
            speak(t('forgotPassword.switchTo.' + method));
        }
    };

    return (
        <SafeAreaView className={`flex-1 bg-gradient-to-b from-primary-50 to-white ${isRTL ? 'rtl' : 'ltr'}`}>
            <StatusBar style="dark" />

            <ScrollView
                className="flex-1"
                contentContainerStyle={{ flexGrow: 1 }}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
            >
                <Animated.View
                    className="flex-1 px-6 py-8"
                    style={{
                        opacity: fadeAnim,
                        transform: [{ translateY: slideAnim }],
                    }}
                >
                    {/* Back Button */}
                    <Pressable
                        onPress={handleBackToLogin}
                        className={`mb-6 flex-row items-center ${isRTL ? 'flex-row-reverse' : ''}`}
                        accessibilityRole="button"
                        accessibilityLabel={t('common.back')}
                    >
                        <Text className={`text-2xl text-earth-600 ${isRTL ? 'ml-2' : 'mr-2'}`}>
                            {isRTL ? '→' : '←'}
                        </Text>
                        <Text className="text-base font-medium text-earth-600">
                            {t('common.back')}
                        </Text>
                    </Pressable>

                    {/* Header */}
                    <View className="items-center mb-8">
                        <View className="mb-6 h-20 w-20 items-center justify-center rounded-full bg-secondary-100">
                            <Text className="text-3xl">🔑</Text>
                        </View>

                        <Text className={`mb-2 text-center text-2xl font-bold text-earth-900 ${isRTL ? 'text-right' : 'text-left'}`}>
                            {t('forgotPassword.title')}
                        </Text>

                        <Text className={`text-center text-base text-earth-600 ${isRTL ? 'text-right' : 'text-left'}`}>
                            {t('forgotPassword.description')}
                        </Text>
                    </View>

                    {/* Reset Method Toggle */}
                    <View className="mb-6 rounded-xl bg-white p-1 shadow-sm">
                        <View className={`flex-row ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <Pressable
                                onPress={() => toggleResetMethod('email')}
                                className={`flex-1 rounded-lg py-3 ${resetMethod === 'email'
                                        ? 'bg-primary-100'
                                        : 'bg-transparent'
                                    }`}
                                accessibilityRole="button"
                                accessibilityLabel={t('forgotPassword.resetViaEmail')}
                                accessibilityState={{ selected: resetMethod === 'email' }}
                            >
                                <Text className={`text-center font-semibold ${resetMethod === 'email'
                                        ? 'text-primary-700'
                                        : 'text-earth-600'
                                    }`}>
                                    📧 {t('forgotPassword.email')}
                                </Text>
                            </Pressable>

                            <Pressable
                                onPress={() => toggleResetMethod('phone')}
                                className={`flex-1 rounded-lg py-3 ${resetMethod === 'phone'
                                        ? 'bg-primary-100'
                                        : 'bg-transparent'
                                    }`}
                                accessibilityRole="button"
                                accessibilityLabel={t('forgotPassword.resetViaPhone')}
                                accessibilityState={{ selected: resetMethod === 'phone' }}
                            >
                                <Text className={`text-center font-semibold ${resetMethod === 'phone'
                                        ? 'text-primary-700'
                                        : 'text-earth-600'
                                    }`}>
                                    📱 {t('forgotPassword.phone')}
                                </Text>
                            </Pressable>
                        </View>
                    </View>

                    {/* Reset Form */}
                    <View className="mb-8">
                        {resetMethod === 'email' ? (
                            <Input
                                label={t('forgotPassword.emailLabel')}
                                placeholder={t('forgotPassword.emailPlaceholder')}
                                value={email}
                                onChangeText={setEmail}
                                error={errors.email}
                                keyboardType="email-address"
                                autoCapitalize="none"
                                leftIcon={<Text className="text-lg text-earth-500">📧</Text>}
                                voiceInputEnabled={isVoiceEnabled}
                                voiceFeedbackEnabled={isVoiceEnabled}
                                onVoiceFeedback={speak}
                                accessibilityLabel={t('forgotPassword.emailLabel')}
                                accessibilityHint={t('forgotPassword.emailHint')}
                            />
                        ) : (
                            <Input
                                label={t('forgotPassword.phoneLabel')}
                                placeholder={t('forgotPassword.phonePlaceholder')}
                                value={phone}
                                onChangeText={setPhone}
                                error={errors.phone}
                                keyboardType="phone-pad"
                                leftIcon={<Text className="text-lg text-earth-500">📱</Text>}
                                voiceInputEnabled={isVoiceEnabled}
                                voiceFeedbackEnabled={isVoiceEnabled}
                                onVoiceFeedback={speak}
                                accessibilityLabel={t('forgotPassword.phoneLabel')}
                                accessibilityHint={t('forgotPassword.phoneHint')}
                            />
                        )}
                    </View>

                    {/* Instructions */}
                    <View className="mb-8 rounded-xl bg-blue-50 p-4">
                        <View className={`flex-row items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <Text className={`text-2xl ${isRTL ? 'ml-3' : 'mr-3'}`}>ℹ️</Text>
                            <View className="flex-1">
                                <Text className={`text-sm font-medium text-blue-800 mb-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                                    {t('forgotPassword.instructions.title')}
                                </Text>
                                <Text className={`text-sm text-blue-700 leading-relaxed ${isRTL ? 'text-right' : 'text-left'}`}>
                                    {resetMethod === 'email'
                                        ? t('forgotPassword.instructions.email')
                                        : t('forgotPassword.instructions.phone')
                                    }
                                </Text>
                            </View>
                        </View>
                    </View>

                    {/* Spacer */}
                    <View className="flex-1" />

                    {/* Send Code Button */}
                    <Button
                        title={t('forgotPassword.sendCode')}
                        onPress={handleSendResetCode}
                        variant="primary"
                        size="large"
                        fullWidth
                        loading={isLoading}
                        disabled={isLoading}
                        className="mb-4"
                        accessibilityLabel={t('forgotPassword.sendCode')}
                        accessibilityHint={t('forgotPassword.sendCodeHint')}
                        voiceFeedbackEnabled={isVoiceEnabled}
                        onVoiceFeedback={speak}
                    />

                    {/* Back to Login */}
                    <Button
                        title={t('forgotPassword.backToLogin')}
                        onPress={handleBackToLogin}
                        variant="ghost"
                        size="large"
                        fullWidth
                        accessibilityLabel={t('forgotPassword.backToLogin')}
                        accessibilityHint={t('forgotPassword.backToLoginHint')}
                        voiceFeedbackEnabled={isVoiceEnabled}
                        onVoiceFeedback={speak}
                    />
                </Animated.View>
            </ScrollView>
        </SafeAreaView>
    );
};

export default function ForgotPassword() {
    return (
        <AppProvider>
            <ForgotPasswordContent />
        </AppProvider>
    );
}