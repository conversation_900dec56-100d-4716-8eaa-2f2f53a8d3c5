import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { CropPlan } from '../../../src/types/crops';
import CropGrowthTimeline from '../../../src/components/crops/CropGrowthTimeline';
import YieldPredictionChart from '../../../src/components/crops/YieldPredictionChart';
import HarvestPlanner from '../../../src/components/crops/HarvestPlanner';

// Mock data - in real app this would come from state management/API
const mockCropPlan: CropPlan = {
    id: 'plan-1',
    cropType: 'tomatoes',
    cropName: 'Tomatoes',
    plantingDate: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000), // 45 days ago
    harvestDate: new Date(Date.now() + 35 * 24 * 60 * 60 * 1000), // 35 days from now
    location: {
        latitude: 40.7128,
        longitude: -74.0060,
        address: 'New York, NY',
    },
    status: 'active',
    tasks: [],
    weatherAlerts: [],
    notes: 'Cherry tomatoes planted in greenhouse',
    expectedYield: 25,
    createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(),
};

const mockMilestones = [
    {
        id: 'milestone-1',
        title: 'Germination',
        titleAr: 'الإنبات',
        description: 'Seeds have sprouted and first leaves are visible',
        descriptionAr: 'البذور نبتت والأوراق الأولى ظاهرة',
        expectedDate: new Date(Date.now() - 38 * 24 * 60 * 60 * 1000),
        actualDate: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000),
        completed: true,
        stage: 'germination' as const,
        icon: '🌱',
        photos: ['https://example.com/germination.jpg'],
        notes: 'Germination was faster than expected!',
    },
    {
        id: 'milestone-2',
        title: 'Seedling Development',
        titleAr: 'تطور الشتلة',
        description: 'True leaves have developed and plants are establishing',
        descriptionAr: 'الأوراق الحقيقية تطورت والنباتات تترسخ',
        expectedDate: new Date(Date.now() - 28 * 24 * 60 * 60 * 1000),
        actualDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        completed: true,
        stage: 'seedling' as const,
        icon: '🌿',
        photos: ['https://example.com/seedling.jpg'],
    },
    {
        id: 'milestone-3',
        title: 'Vegetative Growth',
        titleAr: 'النمو الخضري',
        description: 'Plants are growing rapidly and developing strong stems',
        descriptionAr: 'النباتات تنمو بسرعة وتطور سيقان قوية',
        expectedDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
        actualDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
        completed: true,
        stage: 'vegetative' as const,
        icon: '🌱',
        photos: ['https://example.com/vegetative.jpg'],
    },
    {
        id: 'milestone-4',
        title: 'Flowering',
        titleAr: 'الإزهار',
        description: 'First flowers are appearing and pollination begins',
        descriptionAr: 'الأزهار الأولى تظهر والتلقيح يبدأ',
        expectedDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        completed: false,
        stage: 'flowering' as const,
        icon: '🌸',
        photos: [],
    },
    {
        id: 'milestone-5',
        title: 'Fruit Development',
        titleAr: 'تطور الثمار',
        description: 'Small fruits are forming and growing',
        descriptionAr: 'الثمار الصغيرة تتشكل وتنمو',
        expectedDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        completed: false,
        stage: 'fruiting' as const,
        icon: '🍅',
        photos: [],
    },
    {
        id: 'milestone-6',
        title: 'Harvest Ready',
        titleAr: 'جاهز للحصاد',
        description: 'Fruits are mature and ready for harvest',
        descriptionAr: 'الثمار ناضجة وجاهزة للحصاد',
        expectedDate: new Date(Date.now() + 35 * 24 * 60 * 60 * 1000),
        completed: false,
        stage: 'harvest' as const,
        icon: '🌾',
        photos: [],
    },
];

const mockYieldData = [
    { week: 1, predictedYield: 2, confidence: 85, factors: { weather: 90, soil: 85, care: 80, pests: 95 } },
    { week: 2, predictedYield: 4, confidence: 88, factors: { weather: 85, soil: 90, care: 85, pests: 90 } },
    { week: 3, predictedYield: 6, actualYield: 5.5, confidence: 90, factors: { weather: 95, soil: 85, care: 90, pests: 85 } },
    { week: 4, predictedYield: 8, actualYield: 7.8, confidence: 92, factors: { weather: 90, soil: 90, care: 95, pests: 90 } },
    { week: 5, predictedYield: 10, confidence: 88, factors: { weather: 80, soil: 90, care: 90, pests: 85 } },
    { week: 6, predictedYield: 12, confidence: 85, factors: { weather: 85, soil: 85, care: 85, pests: 80 } },
];

const mockHarvestWindows = [
    {
        start: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        end: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000),
        optimal: new Date(Date.now() + 35 * 24 * 60 * 60 * 1000),
        quality: 'peak' as const,
        description: 'Peak harvest window for maximum flavor and quality',
        descriptionAr: 'نافذة الحصاد المثلى للحصول على أقصى نكهة وجودة',
    },
    {
        start: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000),
        end: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
        optimal: new Date(Date.now() + 50 * 24 * 60 * 60 * 1000),
        quality: 'good' as const,
        description: 'Extended harvest window with good quality',
        descriptionAr: 'نافذة حصاد ممتدة بجودة جيدة',
    },
];

const mockWeatherForecast = Array.from({ length: 7 }, (_, i) => ({
    date: new Date(Date.now() + i * 24 * 60 * 60 * 1000),
    condition: ['sunny', 'cloudy', 'rainy', 'sunny', 'cloudy', 'sunny', 'stormy'][i] as any,
    temperature: 20 + Math.random() * 10,
    humidity: 40 + Math.random() * 30,
    suitable: !['rainy', 'stormy'].includes(['sunny', 'cloudy', 'rainy', 'sunny', 'cloudy', 'sunny', 'stormy'][i]),
}));

export default function CropProgressScreen() {
    const { planId } = useLocalSearchParams<{ planId: string }>();
    const [cropPlan] = useState<CropPlan>(mockCropPlan);
    const [milestones, setMilestones] = useState(mockMilestones);
    const [yieldData] = useState(mockYieldData);
    const [harvestWindows] = useState(mockHarvestWindows);
    const [weatherForecast] = useState(mockWeatherForecast);
    const [currentTab, setCurrentTab] = useState<'timeline' | 'yield' | 'harvest'>('timeline');
    const [voiceEnabled, setVoiceEnabled] = useState(false);

    const handleMilestonePress = (milestone: any) => {
        if (!milestone.completed) {
            Alert.alert(
                'Mark Milestone Complete',
                `Mark "${milestone.title}" as completed?`,
                [
                    { text: 'Cancel', style: 'cancel' },
                    {
                        text: 'Complete',
                        onPress: () => {
                            setMilestones(prev =>
                                prev.map(m =>
                                    m.id === milestone.id
                                        ? { ...m, completed: true, actualDate: new Date() }
                                        : m
                                )
                            );
                        },
                    },
                ]
            );
        }
    };

    const handleAddPhoto = (milestoneId: string) => {
        // TODO: Implement photo capture/selection
        Alert.alert('Add Photo', 'Photo capture functionality will be implemented here.');
    };

    const handleUpdatePrediction = () => {
        // TODO: Implement prediction update
        Alert.alert('Update Prediction', 'AI will analyze current conditions and update yield predictions.');
    };

    const handleScheduleHarvest = (date: Date) => {
        // TODO: Implement harvest scheduling
        Alert.alert(
            'Harvest Scheduled',
            `Harvest has been scheduled for ${date.toLocaleDateString()}. You will receive reminders.`
        );
    };

    const handleHarvestComplete = (actualYield: number, quality: string, notes: string) => {
        // TODO: Implement harvest completion
        Alert.alert('Harvest Complete', 'Harvest data has been recorded successfully.');
    };

    const handleVoiceCommand = (command: string) => {
        // TODO: Implement voice commands
        console.log('Voice command:', command);
    };

    const getTabIcon = (tab: string): string => {
        const icons = {
            timeline: '📈',
            yield: '📊',
            harvest: '🌾',
        };
        return icons[tab as keyof typeof icons] || '📋';
    };

    return (
        <SafeAreaView className="flex-1 bg-gray-50">
            {/* Header */}
            <View className="bg-white border-b border-gray-200 px-4 py-3">
                <View className="flex-row items-center justify-between">
                    <TouchableOpacity
                        onPress={() => router.back()}
                        className="p-2"
                        accessibilityLabel="Go back"
                    >
                        <Text className="text-2xl">←</Text>
                    </TouchableOpacity>

                    <View className="flex-1 mx-4">
                        <Text className="text-lg font-semibold text-gray-900 text-center">
                            {cropPlan.cropName} Progress
                        </Text>
                        <Text className="text-sm text-gray-500 text-center">
                            Planted {new Date(cropPlan.plantingDate).toLocaleDateString()}
                        </Text>
                    </View>

                    <TouchableOpacity
                        onPress={() => setVoiceEnabled(!voiceEnabled)}
                        className={`p-2 rounded-full ${voiceEnabled ? 'bg-green-500' : 'bg-gray-200'}`}
                        accessibilityLabel="Toggle voice mode"
                    >
                        <Text className={`text-sm ${voiceEnabled ? 'text-white' : 'text-gray-600'}`}>
                            🎤
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>

            {/* Tab Navigation */}
            <View className="bg-white border-b border-gray-200 px-4 py-2">
                <View className="flex-row bg-gray-100 rounded-lg p-1">
                    {[
                        { key: 'timeline', label: 'Timeline' },
                        { key: 'yield', label: 'Yield' },
                        { key: 'harvest', label: 'Harvest' },
                    ].map(({ key, label }) => (
                        <TouchableOpacity
                            key={key}
                            onPress={() => setCurrentTab(key as any)}
                            className={`flex-1 px-3 py-2 rounded-md ${currentTab === key ? 'bg-white shadow-sm' : ''
                                }`}
                            accessibilityLabel={`${label} tab`}
                        >
                            <Text className={`text-sm font-medium text-center ${currentTab === key ? 'text-gray-900' : 'text-gray-600'
                                }`}>
                                {getTabIcon(key)} {label}
                            </Text>
                        </TouchableOpacity>
                    ))}
                </View>
            </View>

            {/* Tab Content */}
            {currentTab === 'timeline' && (
                <CropGrowthTimeline
                    cropPlan={cropPlan}
                    milestones={milestones}
                    onMilestonePress={handleMilestonePress}
                    onAddPhoto={handleAddPhoto}
                    voiceEnabled={voiceEnabled}
                    onVoiceCommand={handleVoiceCommand}
                />
            )}

            {currentTab === 'yield' && (
                <YieldPredictionChart
                    cropPlan={cropPlan}
                    yieldData={yieldData}
                    onUpdatePrediction={handleUpdatePrediction}
                    voiceEnabled={voiceEnabled}
                    onVoiceCommand={handleVoiceCommand}
                />
            )}

            {currentTab === 'harvest' && (
                <HarvestPlanner
                    cropPlan={cropPlan}
                    harvestWindows={harvestWindows}
                    weatherForecast={weatherForecast}
                    onScheduleHarvest={handleScheduleHarvest}
                    onHarvestComplete={handleHarvestComplete}
                    voiceEnabled={voiceEnabled}
                    onVoiceCommand={handleVoiceCommand}
                />
            )}
        </SafeAreaView>
    );
}