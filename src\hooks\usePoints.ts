import { useEffect } from 'react';
import { usePointsStore } from '../stores/points';
import { useAuthStore } from '../stores/auth';
import { PointsTransaction } from '../types/points';

export const usePoints = () => {
    const { user } = useAuthStore();
    const {
        balance,
        transactions,
        achievements,
        availableAchievements,
        leaderboard,
        isLoading,
        error,
        loadBalance,
        loadTransactions,
        loadAchievements,
        loadAvailableAchievements,
        loadLeaderboard,
        awardPoints,
        spendPoints,
        redeemPoints,
        checkDailyCheckin,
        clearError,
    } = usePointsStore();

    // Load user data when user changes
    useEffect(() => {
        if (user?.id) {
            loadBalance(user.id);
            loadTransactions(user.id);
            loadAchievements(user.id);
            loadAvailableAchievements();
        }
    }, [user?.id]);

    // Helper functions
    const canAfford = (amount: number): boolean => {
        return (balance?.available_points || 0) >= amount;
    };

    const getPointsForAction = (action: PointsTransaction['source']): number => {
        const pointsMap: Record<PointsTransaction['source'], number> = {
            task_completion: 10,
            daily_checkin: 5,
            community_post: 15,
            ai_consultation: -20,
            subscription_bonus: 100,
            achievement: 50,
            referral: 100,
            admin_adjustment: 0,
        };
        return pointsMap[action] || 0;
    };

    const awardTaskCompletion = async (taskId: string, taskType: string) => {
        if (!user?.id) return false;

        const basePoints = getPointsForAction('task_completion');
        const multiplier = taskType === 'harvesting' ? 2 : 1; // Bonus for harvesting
        const points = basePoints * multiplier;

        return await awardPoints(
            user.id,
            points,
            'task_completion',
            `Task completed: ${taskType}`,
            { task_id: taskId, task_type: taskType }
        );
    };

    const awardCommunityPost = async (postId: string) => {
        if (!user?.id) return false;

        return await awardPoints(
            user.id,
            getPointsForAction('community_post'),
            'community_post',
            'Community post created',
            { post_id: postId }
        );
    };

    const spendForAIConsultation = async () => {
        if (!user?.id) return false;

        const cost = Math.abs(getPointsForAction('ai_consultation'));
        return await spendPoints(
            user.id,
            cost,
            'ai_consultation',
            'AI consultation used'
        );
    };

    const performDailyCheckin = async () => {
        if (!user?.id) return false;
        return await checkDailyCheckin(user.id);
    };

    const redeemAIConsultation = async () => {
        if (!user?.id) return false;
        return await redeemPoints(user.id, 'ai_consultation', 20);
    };

    const refreshLeaderboard = async (timeframe: 'weekly' | 'monthly' | 'all_time' = 'all_time') => {
        await loadLeaderboard(50, timeframe);
    };

    return {
        // State
        balance,
        transactions,
        achievements,
        availableAchievements,
        leaderboard,
        isLoading,
        error,

        // Helper functions
        canAfford,
        getPointsForAction,

        // Action functions
        awardTaskCompletion,
        awardCommunityPost,
        spendForAIConsultation,
        performDailyCheckin,
        redeemAIConsultation,
        refreshLeaderboard,
        clearError,

        // Raw store functions
        awardPoints,
        spendPoints,
        redeemPoints,
        loadBalance: () => user?.id && loadBalance(user.id),
        loadTransactions: () => user?.id && loadTransactions(user.id),
        loadAchievements: () => user?.id && loadAchievements(user.id),
    };
};

export default usePoints;