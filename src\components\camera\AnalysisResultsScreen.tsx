import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, Image, ScrollView, Alert, Share } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ImageAnalysisResult, Issue, Recommendation } from '../../types/camera';
import { VoiceService } from '../../services/voice';

interface AnalysisResultsScreenProps {
    result: ImageAnalysisResult;
    onClose: () => void;
    onSave: (result: ImageAnalysisResult) => void;
    onBuyTreatment: (recommendations: Recommendation[]) => void;
    onRetake: () => void;
}

export function AnalysisResultsScreen({
    result,
    onClose,
    onSave,
    onBuyTreatment,
    onRetake
}: AnalysisResultsScreenProps) {
    const [isReading, setIsReading] = useState(false);

    useEffect(() => {
        // Announce results when screen loads
        announceResults();
    }, []);

    const announceResults = async () => {
        const announcement = `Analysis complete. Overall health: ${result.overallHealth}. ${result.summary}`;
        await VoiceService.speak(announcement);
    };

    const readFullResults = async () => {
        if (isReading) return;

        setIsReading(true);

        try {
            // Read summary
            await VoiceService.speak(`Analysis Results: ${result.summary}`);

            // Read issues
            if (result.issues.length > 0) {
                await VoiceService.speak(`Found ${result.issues.length} issue${result.issues.length > 1 ? 's' : ''}`);

                for (const issue of result.issues) {
                    await VoiceService.speak(`${issue.name}: ${issue.description}. Severity: ${issue.severity}. Confidence: ${Math.round(issue.confidence * 100)} percent.`);
                }
            }

            // Read recommendations
            if (result.recommendations.length > 0) {
                await VoiceService.speak(`${result.recommendations.length} recommendation${result.recommendations.length > 1 ? 's' : ''} available`);

                for (const rec of result.recommendations) {
                    await VoiceService.speak(`${rec.title}: ${rec.description}. Priority: ${rec.priority}.`);
                }
            }
        } finally {
            setIsReading(false);
        }
    };

    const handleSave = async () => {
        onSave(result);
        await VoiceService.speak('Results saved successfully');
    };

    const handleShare = async () => {
        try {
            const shareText = `AI Farming Analysis Results\n\nOverall Health: ${result.overallHealth}\nConfidence: ${Math.round(result.confidence * 100)}%\n\nSummary: ${result.summary}\n\nIssues Found: ${result.issues.length}\nRecommendations: ${result.recommendations.length}`;

            await Share.share({
                message: shareText,
                title: 'Crop Analysis Results'
            });

            await VoiceService.speak('Results shared');
        } catch (error) {
            console.error('Error sharing:', error);
            Alert.alert('Error', 'Failed to share results');
        }
    };

    const handleBuyTreatment = () => {
        const treatmentRecs = result.recommendations.filter(r => r.actionType === 'treatment');
        if (treatmentRecs.length > 0) {
            onBuyTreatment(treatmentRecs);
        } else {
            Alert.alert('No Treatments', 'No treatment products are recommended for this analysis.');
        }
    };

    const getHealthColor = (health: string): string => {
        switch (health) {
            case 'excellent': return 'text-green-600';
            case 'good': return 'text-green-500';
            case 'fair': return 'text-yellow-500';
            case 'poor': return 'text-orange-500';
            case 'critical': return 'text-red-600';
            default: return 'text-gray-500';
        }
    };

    const getHealthIcon = (health: string): string => {
        switch (health) {
            case 'excellent': return 'checkmark-circle';
            case 'good': return 'checkmark-circle-outline';
            case 'fair': return 'warning-outline';
            case 'poor': return 'alert-circle-outline';
            case 'critical': return 'alert-circle';
            default: return 'help-circle-outline';
        }
    };

    const getSeverityColor = (severity: string): string => {
        switch (severity) {
            case 'low': return 'bg-green-100 text-green-800';
            case 'medium': return 'bg-yellow-100 text-yellow-800';
            case 'high': return 'bg-orange-100 text-orange-800';
            case 'critical': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getPriorityColor = (priority: string): string => {
        switch (priority) {
            case 'low': return 'bg-blue-100 text-blue-800';
            case 'medium': return 'bg-yellow-100 text-yellow-800';
            case 'high': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <View className="flex-1 bg-gray-50">
            {/* Header */}
            <View className="bg-white shadow-sm">
                <View className="flex-row justify-between items-center p-4 pt-12">
                    <TouchableOpacity
                        onPress={onClose}
                        className="p-2"
                        accessibilityLabel="Close results"
                    >
                        <Ionicons name="arrow-back" size={24} color="#374151" />
                    </TouchableOpacity>

                    <Text className="text-lg font-semibold text-gray-900 capitalize">
                        {result.mode} Analysis Results
                    </Text>

                    <TouchableOpacity
                        onPress={handleShare}
                        className="p-2"
                        accessibilityLabel="Share results"
                    >
                        <Ionicons name="share-outline" size={24} color="#374151" />
                    </TouchableOpacity>
                </View>
            </View>

            <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
                {/* Image and Overall Health */}
                <View className="bg-white m-4 rounded-xl shadow-sm overflow-hidden">
                    <Image
                        source={{ uri: result.imageUri }}
                        className="w-full h-48"
                        resizeMode="cover"
                    />

                    <View className="p-4">
                        <View className="flex-row items-center justify-between mb-2">
                            <View className="flex-row items-center">
                                <Ionicons
                                    name={getHealthIcon(result.overallHealth) as any}
                                    size={24}
                                    color={result.overallHealth === 'excellent' || result.overallHealth === 'good' ? '#059669' :
                                        result.overallHealth === 'fair' ? '#d97706' : '#dc2626'}
                                />
                                <Text className={`ml-2 text-lg font-semibold capitalize ${getHealthColor(result.overallHealth)}`}>
                                    {result.overallHealth}
                                </Text>
                            </View>

                            <View className="bg-gray-100 px-3 py-1 rounded-full">
                                <Text className="text-gray-700 font-medium">
                                    {Math.round(result.confidence * 100)}% Confidence
                                </Text>
                            </View>
                        </View>

                        <Text className="text-gray-700 text-base leading-relaxed">
                            {result.summary}
                        </Text>
                    </View>
                </View>

                {/* Issues Found */}
                {result.issues.length > 0 && (
                    <View className="bg-white m-4 rounded-xl shadow-sm p-4">
                        <View className="flex-row items-center mb-3">
                            <Ionicons name="warning" size={20} color="#dc2626" />
                            <Text className="ml-2 text-lg font-semibold text-gray-900">
                                Issues Found ({result.issues.length})
                            </Text>
                        </View>

                        {result.issues.map((issue: Issue, index: number) => (
                            <View key={issue.id} className="mb-3 last:mb-0">
                                <View className="flex-row items-center justify-between mb-2">
                                    <Text className="text-base font-medium text-gray-900 flex-1">
                                        {issue.name}
                                    </Text>
                                    <View className={`px-2 py-1 rounded-full ${getSeverityColor(issue.severity)}`}>
                                        <Text className="text-xs font-medium capitalize">
                                            {issue.severity}
                                        </Text>
                                    </View>
                                </View>

                                <Text className="text-gray-600 text-sm mb-2">
                                    {issue.description}
                                </Text>

                                <Text className="text-gray-500 text-xs">
                                    Confidence: {Math.round(issue.confidence * 100)}%
                                </Text>

                                {index < result.issues.length - 1 && (
                                    <View className="border-b border-gray-200 mt-3" />
                                )}
                            </View>
                        ))}
                    </View>
                )}

                {/* Recommendations */}
                {result.recommendations.length > 0 && (
                    <View className="bg-white m-4 rounded-xl shadow-sm p-4">
                        <View className="flex-row items-center mb-3">
                            <Ionicons name="bulb" size={20} color="#059669" />
                            <Text className="ml-2 text-lg font-semibold text-gray-900">
                                Recommendations ({result.recommendations.length})
                            </Text>
                        </View>

                        {result.recommendations.map((rec: Recommendation, index: number) => (
                            <View key={rec.id} className="mb-4 last:mb-0">
                                <View className="flex-row items-center justify-between mb-2">
                                    <Text className="text-base font-medium text-gray-900 flex-1">
                                        {rec.title}
                                    </Text>
                                    <View className={`px-2 py-1 rounded-full ${getPriorityColor(rec.priority)}`}>
                                        <Text className="text-xs font-medium capitalize">
                                            {rec.priority}
                                        </Text>
                                    </View>
                                </View>

                                <Text className="text-gray-600 text-sm mb-2">
                                    {rec.description}
                                </Text>

                                {rec.estimatedCost && (
                                    <Text className="text-green-600 text-sm font-medium mb-2">
                                        Estimated Cost: ${rec.estimatedCost}
                                    </Text>
                                )}

                                {rec.productRecommendations && rec.productRecommendations.length > 0 && (
                                    <View className="mt-2">
                                        <Text className="text-gray-700 text-sm font-medium mb-1">
                                            Recommended Products:
                                        </Text>
                                        {rec.productRecommendations.map((product, productIndex) => (
                                            <Text key={productIndex} className="text-gray-600 text-sm ml-2">
                                                • {product}
                                            </Text>
                                        ))}
                                    </View>
                                )}

                                {index < result.recommendations.length - 1 && (
                                    <View className="border-b border-gray-200 mt-3" />
                                )}
                            </View>
                        ))}
                    </View>
                )}

                {/* Action Buttons */}
                <View className="p-4 gap-3">
                    {/* Voice Reading */}
                    <TouchableOpacity
                        onPress={readFullResults}
                        disabled={isReading}
                        className={`flex-row items-center justify-center py-4 px-6 rounded-xl ${isReading ? 'bg-gray-400' : 'bg-blue-600'
                            }`}
                        accessibilityLabel="Read full results aloud"
                    >
                        <Ionicons
                            name={isReading ? "hourglass" : "volume-high"}
                            size={20}
                            color="white"
                        />
                        <Text className="text-white font-semibold ml-2">
                            {isReading ? 'Reading Results...' : 'Read Results Aloud'}
                        </Text>
                    </TouchableOpacity>

                    {/* Primary Actions */}
                    <View className="flex-row gap-3">
                        <TouchableOpacity
                            onPress={handleBuyTreatment}
                            className="flex-1 bg-green-600 py-4 px-6 rounded-xl"
                            accessibilityLabel="Buy recommended treatments"
                        >
                            <View className="flex-row items-center justify-center">
                                <Ionicons name="storefront" size={20} color="white" />
                                <Text className="text-white font-semibold ml-2">
                                    Buy Treatment
                                </Text>
                            </View>
                        </TouchableOpacity>

                        <TouchableOpacity
                            onPress={handleSave}
                            className="flex-1 bg-gray-600 py-4 px-6 rounded-xl"
                            accessibilityLabel="Save results"
                        >
                            <View className="flex-row items-center justify-center">
                                <Ionicons name="bookmark" size={20} color="white" />
                                <Text className="text-white font-semibold ml-2">
                                    Save Results
                                </Text>
                            </View>
                        </TouchableOpacity>
                    </View>

                    {/* Secondary Action */}
                    <TouchableOpacity
                        onPress={onRetake}
                        className="bg-gray-200 py-4 px-6 rounded-xl"
                        accessibilityLabel="Take another photo"
                    >
                        <View className="flex-row items-center justify-center">
                            <Ionicons name="camera" size={20} color="#374151" />
                            <Text className="text-gray-700 font-semibold ml-2">
                                Take Another Photo
                            </Text>
                        </View>
                    </TouchableOpacity>
                </View>

                {/* Bottom Spacing */}
                <View className="h-6" />
            </ScrollView>
        </View>
    );
}