import { View, Text, Pressable } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';

export default function AIScreen() {
    const aiFeatures = [
        {
            id: 'chat',
            title: 'محادثة الذكاء الاصطناعي',
            description: 'تحدث مع المساعد الذكي واحصل على نصائح زراعية مخصصة',
            icon: '💬',
            color: 'bg-primary-500',
            route: '/ai-chat',
        },
        {
            id: 'diagnosis',
            title: 'تشخيص الصور',
            description: 'التقط صور للنباتات والتربة واحصل على تشخيص فوري',
            icon: '📷',
            color: 'bg-secondary-500',
            route: '/ai-diagnosis',
        },
    ];

    const quickActions = [
        {
            id: 'plant-health',
            title: 'صحة النباتات',
            icon: '🌱',
            description: 'تشخيص أمراض النباتات',
        },
        {
            id: 'soil-analysis',
            title: 'تحليل التربة',
            icon: '🌾',
            description: 'فحص جودة التربة',
        },
        {
            id: 'fertilizer',
            title: 'الأسمدة',
            icon: '💧',
            description: 'نصائح حول الأسمدة',
        },
        {
            id: 'pest-control',
            title: 'مكافحة الآفات',
            icon: '🐛',
            description: 'حلول للآفات الزراعية',
        },
    ];

    return (
        <SafeAreaView className="flex-1 bg-earth-50">
            {/* Header */}
            <View className="bg-white border-b border-earth-200 px-4 py-4 shadow-sm">
                <View className="flex-row items-center">
                    <View className="w-12 h-12 bg-primary-100 rounded-full items-center justify-center mr-3">
                        <Text className="text-primary-600 text-xl">🤖</Text>
                    </View>
                    <View className="flex-1">
                        <Text className="text-xl font-bold text-earth-900">مساعد الذكاء الاصطناعي</Text>
                        <Text className="text-sm text-earth-600">احصل على نصائح زراعية ذكية ومخصصة</Text>
                    </View>
                </View>
            </View>

            <View className="flex-1 p-4">
                {/* Main AI Features */}
                <View className="mb-6">
                    <Text className="text-lg font-semibold text-earth-900 mb-4">الميزات الرئيسية</Text>
                    <View className="space-y-3">
                        {aiFeatures.map((feature) => (
                            <Pressable
                                key={feature.id}
                                onPress={() => router.push(feature.route as any)}
                                className="bg-white rounded-xl p-4 shadow-sm border border-earth-200 active:bg-earth-50"
                                accessibilityRole="button"
                                accessibilityLabel={feature.title}>
                                <View className="flex-row items-center">
                                    <View className={`w-12 h-12 ${feature.color} rounded-xl items-center justify-center mr-4`}>
                                        <Text className="text-white text-xl">{feature.icon}</Text>
                                    </View>
                                    <View className="flex-1">
                                        <Text className="text-lg font-semibold text-earth-900 mb-1">
                                            {feature.title}
                                        </Text>
                                        <Text className="text-earth-600 leading-5">
                                            {feature.description}
                                        </Text>
                                    </View>
                                    <Text className="text-earth-400 text-xl">←</Text>
                                </View>
                            </Pressable>
                        ))}
                    </View>
                </View>

                {/* Quick Actions */}
                <View>
                    <Text className="text-lg font-semibold text-earth-900 mb-4">إجراءات سريعة</Text>
                    <View className="flex-row flex-wrap justify-between">
                        {quickActions.map((action) => (
                            <Pressable
                                key={action.id}
                                onPress={() => router.push('/ai-chat')}
                                className="w-[48%] bg-white rounded-xl p-4 mb-3 shadow-sm border border-earth-200 active:bg-earth-50"
                                accessibilityRole="button"
                                accessibilityLabel={action.title}>
                                <View className="items-center">
                                    <Text className="text-3xl mb-2">{action.icon}</Text>
                                    <Text className="text-base font-semibold text-earth-900 text-center mb-1">
                                        {action.title}
                                    </Text>
                                    <Text className="text-xs text-earth-600 text-center">
                                        {action.description}
                                    </Text>
                                </View>
                            </Pressable>
                        ))}
                    </View>
                </View>

                {/* Tips Section */}
                <View className="mt-6 bg-primary-50 rounded-xl p-4 border border-primary-200">
                    <View className="flex-row items-center mb-2">
                        <Text className="text-primary-600 text-lg mr-2">💡</Text>
                        <Text className="text-base font-semibold text-primary-800">نصائح للاستخدام</Text>
                    </View>
                    <Text className="text-primary-700 leading-5">
                        • استخدم المحادثة للحصول على نصائح مخصصة{'\n'}
                        • التقط صور واضحة للحصول على تشخيص دقيق{'\n'}
                        • اختر الفئة المناسبة لأفضل النتائج
                    </Text>
                </View>
            </View>
        </SafeAreaView>
    );
}