import NetInfo, { NetInfoState } from '@react-native-community/netinfo';

export interface ConnectivityState {
    isConnected: boolean;
    isInternetReachable: boolean;
    type: string;
    isWifiEnabled: boolean;
    isCellularEnabled: boolean;
    strength: number | null;
}

export class ConnectivityService {
    private listeners: ((state: ConnectivityState) => void)[] = [];
    private currentState: ConnectivityState = {
        isConnected: false,
        isInternetReachable: false,
        type: 'unknown',
        isWifiEnabled: false,
        isCellularEnabled: false,
        strength: null
    };

    private unsubscribe: (() => void) | null = null;

    async initialize(): Promise<void> {
        // Get initial state
        const state = await NetInfo.fetch();
        this.updateState(state);

        // Subscribe to network state changes
        this.unsubscribe = NetInfo.addEventListener((state) => {
            this.updateState(state);
        });

        console.log('Connectivity service initialized');
    }

    private updateState(netInfoState: NetInfoState): void {
        const newState: ConnectivityState = {
            isConnected: netInfoState.isConnected ?? false,
            isInternetReachable: netInfoState.isInternetReachable ?? false,
            type: netInfoState.type,
            isWifiEnabled: netInfoState.type === 'wifi' && (netInfoState.isConnected ?? false),
            isCellularEnabled: netInfoState.type === 'cellular' && (netInfoState.isConnected ?? false),
            strength: this.getSignalStrength(netInfoState)
        };

        const wasConnected = this.currentState.isConnected;
        const isNowConnected = newState.isConnected;

        this.currentState = newState;

        // Notify listeners
        this.listeners.forEach(listener => listener(newState));

        // Log connectivity changes
        if (wasConnected !== isNowConnected) {
            console.log(`Connectivity changed: ${isNowConnected ? 'Online' : 'Offline'}`);
        }
    }

    private getSignalStrength(state: NetInfoState): number | null {
        if (state.type === 'wifi' && state.details) {
            return (state.details as any).strength || null;
        }
        if (state.type === 'cellular' && state.details) {
            return (state.details as any).cellularGeneration || null;
        }
        return null;
    }

    getCurrentState(): ConnectivityState {
        return { ...this.currentState };
    }

    isOnline(): boolean {
        return this.currentState.isConnected && this.currentState.isInternetReachable;
    }

    isOffline(): boolean {
        return !this.isOnline();
    }

    getConnectionType(): string {
        return this.currentState.type;
    }

    isWifiConnection(): boolean {
        return this.currentState.isWifiEnabled;
    }

    isCellularConnection(): boolean {
        return this.currentState.isCellularEnabled;
    }

    hasStrongConnection(): boolean {
        if (!this.isOnline()) return false;

        // Consider WiFi as strong connection
        if (this.isWifiConnection()) return true;

        // For cellular, check if we have 4G or better
        if (this.isCellularConnection() && this.currentState.strength) {
            return this.currentState.strength >= 4; // 4G or 5G
        }

        return false;
    }

    addListener(listener: (state: ConnectivityState) => void): () => void {
        this.listeners.push(listener);

        // Return unsubscribe function
        return () => {
            const index = this.listeners.indexOf(listener);
            if (index > -1) {
                this.listeners.splice(index, 1);
            }
        };
    }

    removeAllListeners(): void {
        this.listeners = [];
    }

    async testInternetConnection(): Promise<boolean> {
        try {
            // Test with a lightweight endpoint
            const response = await fetch('https://www.google.com/generate_204', {
                method: 'HEAD',
                cache: 'no-cache',
                timeout: 5000
            });
            return response.ok;
        } catch (error) {
            console.log('Internet connection test failed:', error);
            return false;
        }
    }

    async waitForConnection(timeout: number = 30000): Promise<boolean> {
        if (this.isOnline()) return true;

        return new Promise((resolve) => {
            const timeoutId = setTimeout(() => {
                unsubscribe();
                resolve(false);
            }, timeout);

            const unsubscribe = this.addListener((state) => {
                if (state.isConnected && state.isInternetReachable) {
                    clearTimeout(timeoutId);
                    unsubscribe();
                    resolve(true);
                }
            });
        });
    }

    destroy(): void {
        if (this.unsubscribe) {
            this.unsubscribe();
            this.unsubscribe = null;
        }
        this.removeAllListeners();
    }
}

export const connectivityService = new ConnectivityService();