#!/usr/bin/env node

/**
 * Production Supabase Setup Script
 * This script configures a production Supabase project for the AI Farming Assistant app
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

class SupabaseProductionSetup {
  constructor() {
    this.supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
    this.supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
    this.supabaseServiceKey = process.env.EXPO_PUBLIC_SUPABASE_ROLE_KEY;

    if (!this.supabaseUrl || !this.supabaseAnonKey) {
      throw new Error('Missing required Supabase environment variables');
    }

    // Create client with service role for admin operations
    this.supabase = createClient(this.supabaseUrl, this.supabaseServiceKey || this.supabaseAnonKey);

    console.log('🚀 Initializing Production Supabase Setup');
    console.log('📍 Project URL:', this.supabaseUrl);
    console.log('🔑 Using service role key:', !!this.supabaseServiceKey);
  }

  async validateProject() {
    console.log('\n📋 Step 1: Validating Supabase Project Configuration');

    try {
      // Test basic connection by trying to access auth users (this should work with service role)
      const { data, error } = await this.supabase.auth.admin.listUsers({ page: 1, perPage: 1 });

      if (error) {
        // If auth admin doesn't work, try a simple query
        const { error: queryError } = await this.supabase
          .from('information_schema.tables')
          .select('table_name')
          .limit(1);

        if (queryError && !queryError.message.includes('permission denied')) {
          throw new Error(`Connection failed: ${queryError.message}`);
        }
      }

      console.log('✅ Connection to Supabase project successful');

      // Extract project details from URL
      const projectId = this.supabaseUrl.match(/https:\/\/([^.]+)\.supabase\.co/)?.[1];
      if (projectId) {
        console.log('🆔 Project ID:', projectId);
      }

      // Check project region (this would typically be done via Supabase CLI or dashboard)
      console.log('🌍 Region: Configured via Supabase Dashboard (recommended: closest to users)');
      console.log('💰 Tier: Pro recommended for production (configure via Dashboard)');

      return true;
    } catch (error) {
      console.error('❌ Project validation failed:', error.message);
      return false;
    }
  }

  async checkDatabaseStatus() {
    console.log('\n📋 Step 2: Checking Database Status');

    try {
      // Check if any tables exist
      const { data: tables, error } = await this.supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .limit(5);

      if (error) {
        console.log('⚠️ Could not query information_schema, database might be empty');
      } else {
        console.log(`📊 Found ${tables?.length || 0} existing tables in public schema`);
        if (tables && tables.length > 0) {
          console.log('📋 Existing tables:', tables.map((t) => t.table_name).join(', '));
        }
      }

      return true;
    } catch (error) {
      console.log('⚠️ Database status check completed with warnings');
      return true;
    }
  }

  async validateEnvironmentVariables() {
    console.log('\n📋 Step 3: Validating Environment Variables');

    const requiredVars = ['EXPO_PUBLIC_SUPABASE_URL', 'EXPO_PUBLIC_SUPABASE_ANON_KEY'];

    const optionalVars = [
      'EXPO_PUBLIC_SUPABASE_ROLE_KEY',
      'EXPO_PUBLIC_OPENAI_API_KEY',
      'EXPO_PUBLIC_GEMINI_API_KEY',
      'EXPO_PUBLIC_OPENWEATHER_API_KEY',
    ];

    let allValid = true;

    console.log('🔍 Required variables:');
    requiredVars.forEach((varName) => {
      const value = process.env[varName];
      if (value) {
        console.log(`  ✅ ${varName}: Present`);
      } else {
        console.log(`  ❌ ${varName}: Missing`);
        allValid = false;
      }
    });

    console.log('🔍 Optional variables:');
    optionalVars.forEach((varName) => {
      const value = process.env[varName];
      console.log(`  ${value ? '✅' : '⚠️'} ${varName}: ${value ? 'Present' : 'Missing'}`);
    });

    return allValid;
  }

  async generateProjectSummary() {
    console.log('\n📋 Step 4: Generating Project Summary');

    const projectId = this.supabaseUrl.match(/https:\/\/([^.]+)\.supabase\.co/)?.[1];

    const summary = {
      projectInfo: {
        url: this.supabaseUrl,
        projectId: projectId,
        region: 'Configure via Dashboard',
        tier: 'Configure via Dashboard (Pro recommended)',
        setupDate: new Date().toISOString(),
      },
      environmentVariables: {
        EXPO_PUBLIC_SUPABASE_URL: this.supabaseUrl,
        EXPO_PUBLIC_SUPABASE_ANON_KEY: this.supabaseAnonKey ? 'Configured' : 'Missing',
        EXPO_PUBLIC_SUPABASE_ROLE_KEY: this.supabaseServiceKey ? 'Configured' : 'Missing',
      },
      nextSteps: [
        'Run database migrations (Task 2)',
        'Configure Row Level Security policies',
        'Set up Storage buckets',
        'Configure authentication providers',
        'Set up monitoring and alerts',
      ],
      dashboardUrl: `https://supabase.com/dashboard/project/${projectId}`,
      documentation: 'https://supabase.com/docs',
    };

    // Save summary to file
    const summaryPath = path.join(process.cwd(), 'supabase-production-setup.json');
    fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));

    console.log('📄 Project summary saved to:', summaryPath);
    console.log('🌐 Dashboard URL:', summary.dashboardUrl);

    return summary;
  }

  async updateEnvironmentFile() {
    console.log('\n📋 Step 5: Updating Environment Configuration');

    const envPath = path.join(process.cwd(), '.env');
    const envExamplePath = path.join(process.cwd(), '.env.example');

    // Update .env.example with production template
    const envExampleContent = `# AI Services Configuration
EXPO_PUBLIC_OPENAI_API_KEY=your_openai_api_key_here
EXPO_PUBLIC_GEMINI_API_KEY=your_gemini_api_key_here

# Production Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=${this.supabaseUrl}
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
EXPO_PUBLIC_SUPABASE_ROLE_KEY=your_supabase_service_role_key_here

# Weather API
EXPO_PUBLIC_OPENWEATHER_API_KEY=your_weather_api_key_here

# Cloudinary Configuration
EXPO_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
EXPO_PUBLIC_CLOUDINARY_API_KEY=your_cloudinary_api_key
EXPO_PUBLIC_CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# AdMob Configuration
EXPO_PUBLIC_ADMOB_APP_ID=your_admob_app_id
EXPO_PUBLIC_ADMOB_BANNER_ID=your_admob_banner_id
EXPO_PUBLIC_ADMOB_INTERSTITIAL_ID=your_admob_interstitial_id
EXPO_PUBLIC_ADMOB_REWARDED_ID=your_admob_rewarded_id

# Payment Gateways
EXPO_PUBLIC_FAWRY_MERCHANT_CODE=your_fawry_merchant_code
EXPO_PUBLIC_PAYMOB_API_KEY=your_paymob_api_key

# Production Settings
EXPO_PUBLIC_DEV_MODE=false
EXPO_PUBLIC_MOCK_SERVICES=false
EXPO_PUBLIC_APP_VERSION=1.0.0
EXPO_PUBLIC_APP_URL=https://your-app-domain.com
`;

    fs.writeFileSync(envExamplePath, envExampleContent);
    console.log('✅ Updated .env.example with production template');

    // Verify current .env has required variables
    if (fs.existsSync(envPath)) {
      console.log('✅ Current .env file exists and contains production credentials');
    } else {
      console.log('⚠️ No .env file found - copy from .env.example and configure');
    }
  }

  async run() {
    try {
      console.log('🎯 Starting Production Supabase Setup for AI Farming Assistant');
      console.log('='.repeat(60));

      const isValid = await this.validateProject();
      if (!isValid) {
        throw new Error('Project validation failed');
      }

      await this.checkDatabaseStatus();

      const envValid = await this.validateEnvironmentVariables();
      if (!envValid) {
        console.log('⚠️ Some environment variables are missing - please configure them');
      }

      await this.updateEnvironmentFile();
      const summary = await this.generateProjectSummary();

      console.log('\n🎉 Production Supabase Setup Complete!');
      console.log('='.repeat(60));
      console.log('✅ Project validated and configured');
      console.log('✅ Environment variables checked');
      console.log('✅ Configuration files updated');
      console.log('✅ Project summary generated');

      console.log('\n📋 Next Steps:');
      console.log('1. 🗄️ Run database migrations (Task 2)');
      console.log('2. 🔐 Configure authentication settings');
      console.log('3. 📁 Set up storage buckets');
      console.log('4. 🔒 Apply Row Level Security policies');
      console.log('5. 📊 Set up monitoring and alerts');

      console.log(`\n🌐 Manage your project: ${summary.dashboardUrl}`);

      return true;
    } catch (error) {
      console.error('\n❌ Setup failed:', error.message);
      console.log('\n🔧 Troubleshooting:');
      console.log('1. Verify Supabase project exists and is accessible');
      console.log('2. Check environment variables in .env file');
      console.log('3. Ensure you have proper permissions');
      console.log('4. Visit https://supabase.com/dashboard to manage your project');

      return false;
    }
  }
}

// Run the setup if called directly
if (require.main === module) {
  const setup = new SupabaseProductionSetup();
  setup.run().then((success) => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = SupabaseProductionSetup;
