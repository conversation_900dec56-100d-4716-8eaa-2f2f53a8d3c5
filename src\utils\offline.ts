import { offlineService, connectivityService, offlineModeService } from '../services/offline';
import { OfflineAction } from '../types/offline';

/**
 * Utility functions for offline functionality
 */

export const OfflineUtils = {
    /**
     * Check if the app is currently in offline mode
     */
    isOffline(): boolean {
        return offlineModeService.isOfflineMode();
    },

    /**
     * Check if the device has network connectivity
     */
    isConnected(): boolean {
        return connectivityService.isOnline();
    },

    /**
     * Get a human-readable status message
     */
    getStatusMessage(): string {
        if (offlineModeService.isOfflineMode()) {
            return offlineModeService.getOfflineStatusMessage();
        }

        if (connectivityService.isOnline()) {
            return 'Online';
        }

        return 'Checking connection...';
    },

    /**
     * Create an offline action for later synchronization
     */
    createOfflineAction(
        type: 'CREATE' | 'UPDATE' | 'DELETE',
        table: string,
        data: any,
        userId?: string
    ): OfflineAction {
        return {
            id: `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type,
            table,
            data,
            timestamp: Date.now(),
            userId,
            retryCount: 0,
            maxRetries: 3,
            status: 'pending'
        };
    },

    /**
     * Format bytes to human readable format
     */
    formatBytes(bytes: number): string {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * Format duration to human readable format
     */
    formatDuration(milliseconds: number): string {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) {
            return `${days}d ${hours % 24}h`;
        } else if (hours > 0) {
            return `${hours}h ${minutes % 60}m`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        } else {
            return `${seconds}s`;
        }
    },

    /**
     * Get connection quality description
     */
    getConnectionQuality(): 'excellent' | 'good' | 'poor' | 'offline' {
        if (!connectivityService.isOnline()) {
            return 'offline';
        }

        if (connectivityService.hasStrongConnection()) {
            return 'excellent';
        }

        if (connectivityService.isWifiConnection()) {
            return 'good';
        }

        return 'poor';
    },

    /**
     * Get connection type icon name
     */
    getConnectionIcon(): string {
        const state = connectivityService.getCurrentState();

        if (!state.isConnected) {
            return 'wifi-off';
        }

        switch (state.type) {
            case 'wifi':
                return 'wifi';
            case 'cellular':
                return 'signal-cellular-4-bar';
            case 'ethernet':
                return 'ethernet';
            default:
                return 'device-unknown';
        }
    },

    /**
     * Determine if an operation should be allowed offline
     */
    canPerformOffline(operation: string): boolean {
        const allowedOfflineOperations = [
            'view_tasks',
            'complete_task',
            'create_note',
            'view_crop_plans',
            'view_chat_history',
            'take_photo',
            'record_voice_note'
        ];

        return allowedOfflineOperations.includes(operation);
    },

    /**
     * Get recommended sync strategy based on data type
     */
    getRecommendedSyncStrategy(dataType: string): 'immediate' | 'batch' | 'background' {
        const immediateSync = ['user_profile', 'critical_alerts'];
        const batchSync = ['tasks', 'crop_plans', 'chat_messages'];

        if (immediateSync.includes(dataType)) {
            return 'immediate';
        } else if (batchSync.includes(dataType)) {
            return 'batch';
        } else {
            return 'background';
        }
    },

    /**
     * Check if data is stale and needs refresh
     */
    isDataStale(timestamp: number, maxAge: number = 24 * 60 * 60 * 1000): boolean {
        return Date.now() - timestamp > maxAge;
    },

    /**
     * Generate a unique offline ID
     */
    generateOfflineId(prefix: string = 'offline'): string {
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    },

    /**
     * Check if an ID is an offline-generated ID
     */
    isOfflineId(id: string): boolean {
        return id.startsWith('offline_');
    },

    /**
     * Estimate data size for caching decisions
     */
    estimateDataSize(data: any): number {
        try {
            return JSON.stringify(data).length * 2; // UTF-16 estimation
        } catch {
            return 1024; // Default size if estimation fails
        }
    },

    /**
     * Prioritize data for offline caching
     */
    getDataPriority(dataType: string): 'high' | 'medium' | 'low' {
        const highPriority = ['user_profile', 'active_tasks', 'current_crop_plans'];
        const mediumPriority = ['chat_sessions', 'recent_messages', 'weather_data'];

        if (highPriority.includes(dataType)) {
            return 'high';
        } else if (mediumPriority.includes(dataType)) {
            return 'medium';
        } else {
            return 'low';
        }
    },

    /**
     * Create a debounced sync function
     */
    createDebouncedSync(delay: number = 2000) {
        let timeoutId: NodeJS.Timeout;

        return () => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(async () => {
                if (connectivityService.isOnline()) {
                    await offlineService.processSyncQueue();
                }
            }, delay);
        };
    },

    /**
     * Validate data before offline storage
     */
    validateOfflineData(data: any): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        if (!data) {
            errors.push('Data is null or undefined');
        }

        if (typeof data === 'object' && !data.id) {
            errors.push('Data must have an ID field');
        }

        const size = this.estimateDataSize(data);
        if (size > 10 * 1024 * 1024) { // 10MB limit
            errors.push('Data size exceeds maximum limit');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    },

    /**
     * Get offline storage statistics
     */
    async getStorageStats() {
        try {
            return await offlineService.getStorageInfo();
        } catch (error) {
            console.error('Failed to get storage stats:', error);
            return {
                databaseSize: 0,
                cacheStats: {},
                syncQueueStats: {}
            };
        }
    },

    /**
     * Clean up old offline data
     */
    async cleanupOldData(maxAge: number = 7 * 24 * 60 * 60 * 1000) {
        try {
            // This would implement cleanup logic based on age
            console.log(`Cleaning up data older than ${this.formatDuration(maxAge)}`);
            await offlineService.performMaintenance();
        } catch (error) {
            console.error('Failed to cleanup old data:', error);
        }
    }
};

/**
 * Hook-like utilities for React components
 */
export const OfflineHooks = {
    /**
     * Create a function that handles offline-aware operations
     */
    createOfflineHandler<T extends any[], R>(
        onlineHandler: (...args: T) => Promise<R>,
        offlineHandler: (...args: T) => Promise<R>,
        fallbackHandler?: (...args: T) => Promise<R>
    ) {
        return async (...args: T): Promise<R> => {
            try {
                if (connectivityService.isOnline()) {
                    return await onlineHandler(...args);
                } else {
                    return await offlineHandler(...args);
                }
            } catch (error) {
                console.error('Operation failed:', error);
                if (fallbackHandler) {
                    return await fallbackHandler(...args);
                }
                throw error;
            }
        };
    },

    /**
     * Create a retry mechanism for failed operations
     */
    createRetryHandler<T extends any[], R>(
        handler: (...args: T) => Promise<R>,
        maxRetries: number = 3,
        delay: number = 1000
    ) {
        return async (...args: T): Promise<R> => {
            let lastError: Error;

            for (let attempt = 0; attempt <= maxRetries; attempt++) {
                try {
                    return await handler(...args);
                } catch (error) {
                    lastError = error as Error;

                    if (attempt < maxRetries) {
                        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt)));
                    }
                }
            }

            throw lastError!;
        };
    }
};

export default OfflineUtils;