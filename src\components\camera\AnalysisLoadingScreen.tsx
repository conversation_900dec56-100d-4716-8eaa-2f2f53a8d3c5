import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Image, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { CaptureMode, AnalysisProgress } from '../../types/camera';
import { CameraService } from '../../services/camera';
import { VoiceService } from '../../services/voice';

interface AnalysisLoadingScreenProps {
    imageUri: string;
    mode: CaptureMode;
    onCancel: () => void;
    onComplete: () => void;
}

export function AnalysisLoadingScreen({
    imageUri,
    mode,
    onCancel,
    onComplete
}: AnalysisLoadingScreenProps) {
    const [currentStep, setCurrentStep] = useState(0);
    const [progress, setProgress] = useState(0);
    const [currentMessage, setCurrentMessage] = useState('');
    const [animatedValue] = useState(new Animated.Value(0));
    const [pulseAnimation] = useState(new Animated.Value(1));

    const progressSteps = CameraService.getAnalysisProgressSteps(mode);

    useEffect(() => {
        startAnalysis();
        startPulseAnimation();
        return () => {
            // Cleanup animations
            animatedValue.removeAllListeners();
            pulseAnimation.removeAllListeners();
        };
    }, []);

    const startPulseAnimation = () => {
        const pulse = () => {
            Animated.sequence([
                Animated.timing(pulseAnimation, {
                    toValue: 1.2,
                    duration: 1000,
                    useNativeDriver: true,
                }),
                Animated.timing(pulseAnimation, {
                    toValue: 1,
                    duration: 1000,
                    useNativeDriver: true,
                }),
            ]).start(() => pulse());
        };
        pulse();
    };

    const startAnalysis = async () => {
        for (let i = 0; i < progressSteps.length; i++) {
            const step = progressSteps[i];

            // Update UI
            setCurrentStep(i);
            setCurrentMessage(step.message);

            // Animate progress bar
            Animated.timing(animatedValue, {
                toValue: step.percentage / 100,
                duration: 800,
                useNativeDriver: false,
            }).start();

            setProgress(step.percentage);

            // Voice announcement
            await VoiceService.speak(step.voiceMessage);

            // Wait before next step (except for the last step)
            if (i < progressSteps.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 1500));
            }
        }

        // Complete analysis
        setTimeout(() => {
            onComplete();
        }, 1000);
    };

    const handleCancel = async () => {
        await VoiceService.speak('Analysis cancelled');
        onCancel();
    };

    const getModeIcon = (mode: CaptureMode): string => {
        switch (mode) {
            case 'plant': return 'leaf';
            case 'soil': return 'earth';
            case 'fertilizer': return 'water';
            default: return 'camera';
        }
    };

    const getModeColor = (mode: CaptureMode): string => {
        switch (mode) {
            case 'plant': return 'bg-green-600';
            case 'soil': return 'bg-amber-600';
            case 'fertilizer': return 'bg-blue-600';
            default: return 'bg-gray-600';
        }
    };

    return (
        <View className="flex-1 bg-gray-900">
            {/* Header */}
            <View className="flex-row justify-between items-center p-4 pt-12">
                <View className="flex-row items-center">
                    <View className={`p-2 rounded-full ${getModeColor(mode)} mr-3`}>
                        <Ionicons name={getModeIcon(mode) as any} size={24} color="white" />
                    </View>
                    <Text className="text-white text-lg font-semibold capitalize">
                        {mode} Analysis
                    </Text>
                </View>

                <TouchableOpacity
                    onPress={handleCancel}
                    className="bg-red-600 px-4 py-2 rounded-lg"
                    accessibilityLabel="Cancel analysis"
                >
                    <Text className="text-white font-medium">Cancel</Text>
                </TouchableOpacity>
            </View>

            {/* Image Preview */}
            <View className="items-center mt-8 mb-8">
                <Animated.View
                    style={{
                        transform: [{ scale: pulseAnimation }],
                    }}
                    className="relative"
                >
                    <Image
                        source={{ uri: imageUri }}
                        className="w-64 h-64 rounded-lg"
                        resizeMode="cover"
                    />

                    {/* Analysis Overlay */}
                    <View className="absolute inset-0 bg-black/30 rounded-lg justify-center items-center">
                        <View className="bg-white/90 p-4 rounded-full">
                            <Ionicons name="analytics" size={32} color="#059669" />
                        </View>
                    </View>
                </Animated.View>
            </View>

            {/* Progress Section */}
            <View className="px-6 mb-8">
                {/* Progress Bar */}
                <View className="bg-gray-700 h-3 rounded-full mb-4 overflow-hidden">
                    <Animated.View
                        className="bg-green-500 h-full rounded-full"
                        style={{
                            width: animatedValue.interpolate({
                                inputRange: [0, 1],
                                outputRange: ['0%', '100%'],
                            }),
                        }}
                    />
                </View>

                {/* Progress Text */}
                <View className="flex-row justify-between items-center mb-2">
                    <Text className="text-white text-lg font-medium">
                        {progress}% Complete
                    </Text>
                    <Text className="text-gray-300 text-sm">
                        Step {currentStep + 1} of {progressSteps.length}
                    </Text>
                </View>

                {/* Current Message */}
                <Text className="text-gray-300 text-base text-center">
                    {currentMessage}
                </Text>
            </View>

            {/* AI Processing Animation */}
            <View className="flex-1 justify-center items-center px-6">
                <View className="bg-gray-800 p-6 rounded-xl w-full max-w-sm">
                    <View className="flex-row items-center justify-center mb-4">
                        <Ionicons name="brain" size={24} color="#10b981" />
                        <Text className="text-white text-lg font-semibold ml-2">
                            AI Processing
                        </Text>
                    </View>

                    {/* Processing Steps */}
                    <View className="gap-2">
                        {progressSteps.map((step, index) => (
                            <View key={index} className="flex-row items-center">
                                <View
                                    className={`w-3 h-3 rounded-full mr-3 ${index < currentStep
                                        ? 'bg-green-500'
                                        : index === currentStep
                                            ? 'bg-yellow-500'
                                            : 'bg-gray-600'
                                        }`}
                                />
                                <Text
                                    className={`text-sm ${index <= currentStep ? 'text-white' : 'text-gray-500'
                                        }`}
                                >
                                    {step.message}
                                </Text>
                            </View>
                        ))}
                    </View>
                </View>

                {/* Voice Feedback Button */}
                <TouchableOpacity
                    onPress={() => VoiceService.speak(currentMessage)}
                    className="mt-6 bg-blue-600 py-3 px-6 rounded-lg"
                    accessibilityLabel="Repeat current status"
                >
                    <View className="flex-row items-center">
                        <Ionicons name="volume-high" size={20} color="white" />
                        <Text className="text-white font-medium ml-2">
                            Repeat Status
                        </Text>
                    </View>
                </TouchableOpacity>
            </View>

            {/* Tips Section */}
            <View className="bg-gray-800 p-4 mx-4 mb-6 rounded-lg">
                <Text className="text-white font-semibold mb-2">
                    💡 Did you know?
                </Text>
                <Text className="text-gray-300 text-sm">
                    {mode === 'plant' && 'Early detection of plant diseases can save up to 80% of your crop yield.'}
                    {mode === 'soil' && 'Healthy soil contains billions of microorganisms that help plants grow.'}
                    {mode === 'fertilizer' && 'Proper fertilizer timing can increase crop yield by 20-30%.'}
                </Text>
            </View>
        </View>
    );
}