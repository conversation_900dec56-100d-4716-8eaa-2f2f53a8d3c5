import { Redirect } from 'expo-router';
import { useEffect } from 'react';
import { useAuthStore } from '../src/stores/auth';

export default function Index() {
  const { isAuthenticated, isLoading, initialize } = useAuthStore();

  useEffect(() => {
    initialize();
  }, [initialize]);

  if (isLoading) return null; // شاشة تحميل لاحقًا

  return <Redirect href={isAuthenticated ? '/(tabs)/home' : '/(auth)/register'} />;
}
