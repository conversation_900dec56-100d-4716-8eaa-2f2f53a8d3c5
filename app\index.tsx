import { Redirect, useRootNavigationState } from 'expo-router';
import { useEffect, useState } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { useAuthStore } from '../src/stores/auth';

export default function Index() {
  const rootNavigationState = useRootNavigationState();
  const { isAuthenticated, isLoading, initialize } = useAuthStore();
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const initializeAuth = async () => {
      await initialize();
      setIsInitialized(true);
    };
    initializeAuth();
  }, [initialize]);

  // انتظار جاهزية نظام الملاحة والتهيئة قبل أي توجيه
  if (!rootNavigationState?.key || !isInitialized || isLoading) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: '#f8f9fa',
        }}>
        <ActivityIndicator size="large" color="#22c55e" />
        <Text style={{ marginTop: 16, fontSize: 16, color: '#6b7280' }}>جاري التحميل...</Text>
      </View>
    );
  }

  return <Redirect href={isAuthenticated ? '/(tabs)/home' : '/(auth)/register'} />;
}
