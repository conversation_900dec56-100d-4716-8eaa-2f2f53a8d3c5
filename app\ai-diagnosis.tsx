import React, { useState, useRef } from 'react';
import { View, Text, ScrollView, Pressable, Image, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import { Camera } from 'expo-camera';
import { createImageAnalysisService } from '../src/services/ai/imageAnalysis';
import { ImageAnalysisResult, Issue, Recommendation } from '../src/types/ai';



type DiagnosisCategory = 'plant' | 'soil' | 'fertilizer';

export default function AIDiagnosisScreen() {
    const [selectedImage, setSelectedImage] = useState<string | null>(null);
    const [selectedCategory, setSelectedCategory] = useState<DiagnosisCategory>('plant');
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [analysisResult, setAnalysisResult] = useState<ImageAnalysisResult | null>(null);
    const [analysisHistory, setAnalysisHistory] = useState<ImageAnalysisResult[]>([]);
    const imageAnalysisService = createImageAnalysisService();
    const [showHistory, setShowHistory] = useState(false);

    const categories = [
        {
            id: 'plant' as DiagnosisCategory,
            name: 'صحة النباتات',
            icon: '🌱',
            description: 'تشخيص أمراض النباتات والآفات',
            color: 'text-green-600',
            bgColor: 'bg-green-100',
        },
        {
            id: 'soil' as DiagnosisCategory,
            name: 'تحليل التربة',
            icon: '🌾',
            description: 'فحص جودة ونوع التربة',
            color: 'text-amber-600',
            bgColor: 'bg-amber-100',
        },
        {
            id: 'fertilizer' as DiagnosisCategory,
            name: 'مشاكل الأسمدة',
            icon: '💧',
            description: 'تحديد نقص أو زيادة الأسمدة',
            color: 'text-blue-600',
            bgColor: 'bg-blue-100',
        },
    ];

    const requestCameraPermission = async () => {
        const { status } = await Camera.requestCameraPermissionsAsync();
        if (status !== 'granted') {
            Alert.alert('خطأ', 'نحتاج إذن الكاميرا لتشخيص الصور');
            return false;
        }
        return true;
    };

    const requestGalleryPermission = async () => {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
            Alert.alert('خطأ', 'نحتاج إذن الوصول للصور لتشخيص الصور');
            return false;
        }
        return true;
    };

    const takePhoto = async () => {
        const hasPermission = await requestCameraPermission();
        if (!hasPermission) return;

        const result = await ImagePicker.launchCameraAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.Images,
            allowsEditing: true,
            aspect: [4, 3],
            quality: 0.8,
        });

        if (!result.canceled && result.assets[0]) {
            setSelectedImage(result.assets[0].uri);
            setAnalysisResult(null);
        }
    };

    const pickImage = async () => {
        const hasPermission = await requestGalleryPermission();
        if (!hasPermission) return;

        const result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.Images,
            allowsEditing: true,
            aspect: [4, 3],
            quality: 0.8,
        });

        if (!result.canceled && result.assets[0]) {
            setSelectedImage(result.assets[0].uri);
            setAnalysisResult(null);
        }
    };

    const analyzeImage = async () => {
        if (!selectedImage) return;

        setIsAnalyzing(true);

        try {
            const result = await imageAnalysisService.analyzeImage(selectedImage, selectedCategory);
            setAnalysisResult(result);
            setAnalysisHistory(prev => [result, ...prev]);
            setIsAnalyzing(false);
        } catch (error) {
            console.error('Error analyzing image:', error);
            setIsAnalyzing(false);
            Alert.alert('خطأ', 'حدث خطأ أثناء تحليل الصورة');
        }
    };

    const renderCategorySelector = () => (
        <View className="bg-white rounded-xl p-4 mb-4 shadow-sm">
            <Text className="text-lg font-semibold text-earth-900 mb-3">اختر نوع التشخيص</Text>
            <View className="flex-row justify-between">
                {categories.map((category) => (
                    <Pressable
                        key={category.id}
                        onPress={() => setSelectedCategory(category.id)}
                        className={`
                            flex-1 mx-1 p-3 rounded-lg border-2
                            ${selectedCategory === category.id
                                ? 'border-primary-500 bg-primary-50'
                                : 'border-earth-200 bg-white'
                            }
                        `}
                        accessibilityRole="button"
                        accessibilityLabel={category.name}>
                        <View className="items-center">
                            <Text className="text-2xl mb-1">{category.icon}</Text>
                            <Text className={`
                                text-sm font-medium text-center
                                ${selectedCategory === category.id ? 'text-primary-700' : 'text-earth-700'}
                            `}>
                                {category.name}
                            </Text>
                        </View>
                    </Pressable>
                ))}
            </View>
        </View>
    );

    const renderImageCapture = () => (
        <View className="bg-white rounded-xl p-4 mb-4 shadow-sm">
            <Text className="text-lg font-semibold text-earth-900 mb-3">التقط أو اختر صورة</Text>

            {selectedImage ? (
                <View className="mb-4">
                    <Image
                        source={{ uri: selectedImage }}
                        className="w-full h-48 rounded-lg"
                        resizeMode="cover"
                    />
                    <Pressable
                        onPress={() => setSelectedImage(null)}
                        className="absolute top-2 right-2 w-8 h-8 bg-red-500 rounded-full items-center justify-center"
                        accessibilityRole="button"
                        accessibilityLabel="حذف الصورة">
                        <Text className="text-white font-bold">×</Text>
                    </Pressable>
                </View>
            ) : (
                <View className="h-48 bg-earth-100 rounded-lg items-center justify-center mb-4 border-2 border-dashed border-earth-300">
                    <Text className="text-4xl mb-2">📷</Text>
                    <Text className="text-earth-600 text-center">
                        التقط صورة أو اختر من المعرض
                    </Text>
                </View>
            )}

            <View className="flex-row gap-3">
                <Pressable
                    onPress={takePhoto}
                    className="flex-1 bg-primary-600 py-3 rounded-lg active:bg-primary-700"
                    accessibilityRole="button"
                    accessibilityLabel="التقاط صورة">
                    <Text className="text-white font-semibold text-center">📷 التقط صورة</Text>
                </Pressable>

                <Pressable
                    onPress={pickImage}
                    className="flex-1 bg-secondary-600 py-3 rounded-lg active:bg-secondary-700"
                    accessibilityRole="button"
                    accessibilityLabel="اختيار من المعرض">
                    <Text className="text-white font-semibold text-center">🖼️ من المعرض</Text>
                </Pressable>
            </View>

            {selectedImage && (
                <Pressable
                    onPress={analyzeImage}
                    disabled={isAnalyzing}
                    className={`
                        mt-3 py-3 rounded-lg
                        ${isAnalyzing ? 'bg-earth-300' : 'bg-green-600 active:bg-green-700'}
                    `}
                    accessibilityRole="button"
                    accessibilityLabel="تحليل الصورة">
                    <Text className="text-white font-semibold text-center">
                        {isAnalyzing ? '🔍 جاري التحليل...' : '🔍 تحليل الصورة'}
                    </Text>
                </Pressable>
            )}
        </View>
    );

    const renderAnalysisResult = () => {
        if (!analysisResult) return null;

        return (
            <View className="bg-white rounded-xl p-4 mb-4 shadow-sm">
                <View className="flex-row items-center justify-between mb-3">
                    <Text className="text-lg font-semibold text-earth-900">نتائج التحليل</Text>
                    <View className="bg-green-100 px-3 py-1 rounded-full">
                        <Text className="text-green-700 font-medium">
                            دقة {Math.round(analysisResult.confidence * 100)}%
                        </Text>
                    </View>
                </View>

                {/* Issues */}
                <View className="mb-4">
                    <Text className="text-base font-semibold text-earth-800 mb-2">المشاكل المكتشفة:</Text>
                    {analysisResult.issues.map((issue, index) => (
                        <View key={index} className="bg-red-50 border border-red-200 rounded-lg p-3 mb-2">
                            <View className="flex-row items-center justify-between mb-1">
                                <Text className="font-semibold text-red-800">{issue.name}</Text>
                                <View className={`
                                    px-2 py-1 rounded-full
                                    ${issue.severity === 'high' ? 'bg-red-200' :
                                        issue.severity === 'medium' ? 'bg-yellow-200' : 'bg-green-200'}
                                `}>
                                    <Text className={`
                                        text-xs font-medium
                                        ${issue.severity === 'high' ? 'text-red-800' :
                                            issue.severity === 'medium' ? 'text-yellow-800' : 'text-green-800'}
                                    `}>
                                        {issue.severity === 'high' ? 'عالي' :
                                            issue.severity === 'medium' ? 'متوسط' : 'منخفض'}
                                    </Text>
                                </View>
                            </View>
                            <Text className="text-red-700">{issue.description}</Text>
                        </View>
                    ))}
                </View>

                {/* Recommendations */}
                <View>
                    <Text className="text-base font-semibold text-earth-800 mb-2">التوصيات:</Text>
                    {analysisResult.recommendations.map((rec, index) => (
                        <View key={index} className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-2">
                            <Text className="font-semibold text-blue-800 mb-1">{rec.title}</Text>
                            <Text className="text-blue-700 mb-2">{rec.description}</Text>
                            {rec.products && (
                                <View className="flex-row flex-wrap">
                                    {rec.products.map((product, pIndex) => (
                                        <Pressable
                                            key={pIndex}
                                            className="bg-blue-200 px-2 py-1 rounded-full mr-2 mb-1"
                                            accessibilityRole="button"
                                            accessibilityLabel={`شراء ${product}`}>
                                            <Text className="text-blue-800 text-xs">🛒 {product}</Text>
                                        </Pressable>
                                    ))}
                                </View>
                            )}
                        </View>
                    ))}
                </View>
            </View>
        );
    };

    return (
        <SafeAreaView className="flex-1 bg-earth-50">
            {/* Header */}
            <View className="bg-white border-b border-earth-200 px-4 py-3 shadow-sm">
                <View className="flex-row items-center justify-between">
                    <View className="flex-row items-center flex-1">
                        <Pressable
                            onPress={() => router.back()}
                            className="w-10 h-10 bg-earth-100 rounded-full items-center justify-center mr-3"
                            accessibilityRole="button"
                            accessibilityLabel="العودة">
                            <Text className="text-earth-600 text-lg">←</Text>
                        </Pressable>

                        <View className="flex-1">
                            <Text className="text-lg font-semibold text-earth-900">تشخيص الصور بالذكاء الاصطناعي</Text>
                            <Text className="text-sm text-earth-600">
                                {isAnalyzing ? 'جاري تحليل الصورة...' : 'التقط صورة للحصول على تشخيص فوري'}
                            </Text>
                        </View>
                    </View>

                    <View className="flex-row gap-2">
                        <Pressable
                            onPress={() => setShowHistory(!showHistory)}
                            className="w-10 h-10 bg-secondary-100 rounded-full items-center justify-center active:bg-secondary-200"
                            accessibilityRole="button"
                            accessibilityLabel="سجل التحليلات">
                            <Text className="text-secondary-600 text-lg">📋</Text>
                        </Pressable>

                        <Pressable
                            onPress={() => router.push('/ai-chat')}
                            className="w-10 h-10 bg-primary-100 rounded-full items-center justify-center active:bg-primary-200"
                            accessibilityRole="button"
                            accessibilityLabel="المحادثة">
                            <Text className="text-primary-600 text-lg">💬</Text>
                        </Pressable>
                    </View>
                </View>
            </View>

            <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={false}>
                {renderCategorySelector()}
                {renderImageCapture()}
                {renderAnalysisResult()}

                {/* Analysis History */}
                {showHistory && analysisHistory.length > 0 && (
                    <View className="bg-white rounded-xl p-4 shadow-sm">
                        <Text className="text-lg font-semibold text-earth-900 mb-3">سجل التحليلات</Text>
                        {analysisHistory.slice(0, 5).map((result) => (
                            <Pressable
                                key={result.id}
                                className="flex-row items-center p-3 border border-earth-200 rounded-lg mb-2"
                                accessibilityRole="button"
                                accessibilityLabel="عرض نتيجة التحليل">
                                <Image
                                    source={{ uri: result.imageUri }}
                                    className="w-12 h-12 rounded-lg mr-3"
                                />
                                <View className="flex-1">
                                    <Text className="font-medium text-earth-900">
                                        {categories.find(c => c.id === result.category)?.name}
                                    </Text>
                                    <Text className="text-sm text-earth-600">
                                        {result.timestamp.toLocaleDateString('ar')}
                                    </Text>
                                </View>
                                <Text className="text-green-600 font-medium">
                                    {Math.round(result.confidence * 100)}%
                                </Text>
                            </Pressable>
                        ))}
                    </View>
                )}
            </ScrollView>
        </SafeAreaView>
    );
}