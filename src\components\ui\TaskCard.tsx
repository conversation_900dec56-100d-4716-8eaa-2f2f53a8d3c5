import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Animated } from 'react-native';
import { Task } from '../../types/tasks';
import { TaskService } from '../../services/tasks';
import { VoiceService } from '../../services/voice';

interface TaskCardProps {
    task: Task;
    onTaskPress?: (task: Task) => void;
    onTaskComplete?: (task: Task) => void;
    showDetails?: boolean;
}

export const TaskCard: React.FC<TaskCardProps> = ({
    task,
    onTaskPress,
    onTaskComplete,
    showDetails = false,
}) => {
    const [isCompleting, setIsCompleting] = useState(false);
    const [scaleAnim] = useState(new Animated.Value(1));

    const taskService = TaskService.getInstance();
    const voiceService = VoiceService.getInstance();

    const handleTaskPress = async () => {
        const taskText = `Task: ${task.title}. ${task.description}. Estimated duration: ${taskService.formatDuration(task.estimatedDuration)}. Points reward: ${task.pointsReward}.`;
        await voiceService.speak(taskText);
        onTaskPress?.(task);
    };

    const handleToggleComplete = async () => {
        if (isCompleting) return;

        setIsCompleting(true);

        // Animate the card
        Animated.sequence([
            Animated.timing(scaleAnim, {
                toValue: 0.95,
                duration: 100,
                useNativeDriver: true,
            }),
            Animated.timing(scaleAnim, {
                toValue: 1,
                duration: 100,
                useNativeDriver: true,
            }),
        ]).start();

        try {
            if (task.completed) {
                await taskService.uncompleteTask(task.id);
                await voiceService.speak(`Task ${task.title} marked as incomplete`);
            } else {
                await taskService.completeTask(task.id, {
                    taskId: task.id,
                    completedAt: new Date(),
                    pointsEarned: task.pointsReward,
                });
                await voiceService.speak(`Task completed! You earned ${task.pointsReward} points.`);
            }

            onTaskComplete?.(task);
        } catch (error) {
            console.error('Failed to toggle task completion:', error);
        } finally {
            setIsCompleting(false);
        }
    };

    const taskIcon = taskService.getTaskIcon(task.type);
    const priorityColor = taskService.getTaskPriorityColor(task.priority);
    const isOverdue = taskService.isTaskOverdue(task);
    const statusText = taskService.getTaskStatusText(task);

    return (
        <Animated.View
            style={{ transform: [{ scale: scaleAnim }] }}
            className={`bg-white rounded-lg p-4 mb-3 shadow-sm border-l-4 ${task.completed
                ? 'border-l-green-500 bg-green-50'
                : isOverdue
                    ? 'border-l-red-500 bg-red-50'
                    : 'border-l-blue-500'
                }`}
        >
            <View className="flex-row items-start justify-between">
                {/* Task Content */}
                <TouchableOpacity
                    onPress={handleTaskPress}
                    className="flex-1 mr-3"
                    accessibilityLabel={`Task: ${task.title}. ${task.description}`}
                    accessibilityRole="button"
                >
                    <View className="flex-row items-center mb-2">
                        <Text className="text-lg mr-2">{taskIcon}</Text>
                        <Text
                            className={`text-base font-semibold flex-1 ${task.completed ? 'text-gray-500 line-through' : 'text-gray-900'
                                }`}
                            numberOfLines={2}
                        >
                            {task.title}
                        </Text>
                        <View className={`px-2 py-1 rounded-full ${task.priority === 'high' ? 'bg-red-100' :
                            task.priority === 'medium' ? 'bg-yellow-100' : 'bg-green-100'
                            }`}>
                            <Text className={`text-xs font-medium ${priorityColor}`}>
                                {task.priority.toUpperCase()}
                            </Text>
                        </View>
                    </View>

                    <Text
                        className={`text-sm mb-2 ${task.completed ? 'text-gray-400' : 'text-gray-600'
                            }`}
                        numberOfLines={showDetails ? undefined : 2}
                    >
                        {task.description}
                    </Text>

                    <View className="flex-row items-center justify-between">
                        <View className="flex-row items-center gap-4">
                            <View className="flex-row items-center">
                                <Text className="text-xs text-gray-500 mr-1">⏱️</Text>
                                <Text className="text-xs text-gray-500">
                                    {taskService.formatDuration(task.estimatedDuration)}
                                </Text>
                            </View>

                            <View className="flex-row items-center">
                                <Text className="text-xs text-gray-500 mr-1">🏆</Text>
                                <Text className="text-xs text-gray-500">
                                    {task.pointsReward} pts
                                </Text>
                            </View>
                        </View>

                        <Text className={`text-xs font-medium ${task.completed ? 'text-green-600' :
                            isOverdue ? 'text-red-600' : 'text-gray-500'
                            }`}>
                            {statusText}
                        </Text>
                    </View>

                    {task.completedAt && (
                        <Text className="text-xs text-green-600 mt-1">
                            ✅ Completed {task.completedAt.toLocaleTimeString('en', {
                                hour: '2-digit',
                                minute: '2-digit'
                            })}
                        </Text>
                    )}
                </TouchableOpacity>

                {/* Completion Checkbox */}
                <TouchableOpacity
                    onPress={handleToggleComplete}
                    disabled={isCompleting}
                    className={`w-8 h-8 rounded-full border-2 items-center justify-center ${task.completed
                        ? 'bg-green-500 border-green-500'
                        : 'border-gray-300 bg-white'
                        }`}
                    accessibilityLabel={task.completed ? 'Mark as incomplete' : 'Mark as complete'}
                    accessibilityRole="checkbox"
                    accessibilityState={{ checked: task.completed }}
                >
                    {task.completed && (
                        <Text className="text-white text-sm font-bold">✓</Text>
                    )}
                </TouchableOpacity>
            </View>

            {/* Task Instructions (shown when showDetails is true) */}
            {showDetails && task.instructions && task.instructions.length > 0 && (
                <View className="mt-3 pt-3 border-t border-gray-100">
                    <Text className="text-sm font-medium text-gray-700 mb-2">Instructions:</Text>
                    {task.instructions.map((instruction, index) => (
                        <View key={index} className="flex-row items-start mb-1">
                            <Text className="text-gray-500 mr-2 text-sm">{index + 1}.</Text>
                            <Text className="text-sm text-gray-600 flex-1">{instruction}</Text>
                        </View>
                    ))}
                </View>
            )}
        </Animated.View>
    );
};