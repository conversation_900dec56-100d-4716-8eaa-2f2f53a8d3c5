import { supabase } from './client';
import { Database } from '../../types/database';

type UserProfile = Database['public']['Tables']['user_profiles']['Row'];
type UserProfileInsert = Database['public']['Tables']['user_profiles']['Insert'];
type UserProfileUpdate = Database['public']['Tables']['user_profiles']['Update'];

export interface GeoLocation {
    latitude: number;
    longitude: number;
}

export interface ProfileUpdateData {
    farm_location?: GeoLocation;
    crop_types?: string[];
    experience_level?: 'beginner' | 'intermediate' | 'expert';
    preferred_language?: string;
    voice_enabled?: boolean;
}

export interface ProfileImageUpload {
    uri: string;
    type: string;
    name: string;
}

export class ProfileService {
    /**
     * Get user profile by ID
     */
    static async getProfile(userId: string): Promise<{ profile: UserProfile | null; error: string | null }> {
        try {
            const { data, error } = await supabase
                .from('user_profiles')
                .select('*')
                .eq('id', userId)
                .single();

            if (error) {
                return { profile: null, error: error.message };
            }

            return { profile: data, error: null };
        } catch (error) {
            return {
                profile: null,
                error: error instanceof Error ? error.message : 'Failed to fetch profile',
            };
        }
    }

    /**
     * Create initial user profile
     */
    static async createProfile(profileData: UserProfileInsert): Promise<{ profile: UserProfile | null; error: string | null }> {
        try {
            const { data, error } = await supabase
                .from('user_profiles')
                .insert(profileData)
                .select()
                .single();

            if (error) {
                return { profile: null, error: error.message };
            }

            return { profile: data, error: null };
        } catch (error) {
            return {
                profile: null,
                error: error instanceof Error ? error.message : 'Failed to create profile',
            };
        }
    }

    /**
     * Update user profile
     */
    static async updateProfile(
        userId: string,
        updates: ProfileUpdateData
    ): Promise<{ profile: UserProfile | null; error: string | null }> {
        try {
            // Convert GeoLocation to PostGIS point format if provided
            const profileUpdates: UserProfileUpdate = { ...updates };

            if (updates.farm_location) {
                profileUpdates.farm_location = `POINT(${updates.farm_location.longitude} ${updates.farm_location.latitude})`;
            }

            const { data, error } = await supabase
                .from('user_profiles')
                .update(profileUpdates)
                .eq('id', userId)
                .select()
                .single();

            if (error) {
                return { profile: null, error: error.message };
            }

            return { profile: data, error: null };
        } catch (error) {
            return {
                profile: null,
                error: error instanceof Error ? error.message : 'Failed to update profile',
            };
        }
    }

    /**
     * Update farm location
     */
    static async updateFarmLocation(
        userId: string,
        location: GeoLocation
    ): Promise<{ success: boolean; error: string | null }> {
        try {
            const { error } = await supabase
                .from('user_profiles')
                .update({
                    farm_location: `POINT(${location.longitude} ${location.latitude})`,
                })
                .eq('id', userId);

            if (error) {
                return { success: false, error: error.message };
            }

            return { success: true, error: null };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to update farm location',
            };
        }
    }

    /**
     * Update crop types preferences
     */
    static async updateCropTypes(
        userId: string,
        cropTypes: string[]
    ): Promise<{ success: boolean; error: string | null }> {
        try {
            const { error } = await supabase
                .from('user_profiles')
                .update({ crop_types: cropTypes })
                .eq('id', userId);

            if (error) {
                return { success: false, error: error.message };
            }

            return { success: true, error: null };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to update crop types',
            };
        }
    }

    /**
     * Update experience level
     */
    static async updateExperienceLevel(
        userId: string,
        level: 'beginner' | 'intermediate' | 'expert'
    ): Promise<{ success: boolean; error: string | null }> {
        try {
            const { error } = await supabase
                .from('user_profiles')
                .update({ experience_level: level })
                .eq('id', userId);

            if (error) {
                return { success: false, error: error.message };
            }

            return { success: true, error: null };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to update experience level',
            };
        }
    }

    /**
     * Toggle voice mode
     */
    static async toggleVoiceMode(
        userId: string,
        enabled: boolean
    ): Promise<{ success: boolean; error: string | null }> {
        try {
            const { error } = await supabase
                .from('user_profiles')
                .update({ voice_enabled: enabled })
                .eq('id', userId);

            if (error) {
                return { success: false, error: error.message };
            }

            return { success: true, error: null };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to toggle voice mode',
            };
        }
    }

    /**
     * Update preferred language
     */
    static async updateLanguage(
        userId: string,
        language: string
    ): Promise<{ success: boolean; error: string | null }> {
        try {
            const { error } = await supabase
                .from('user_profiles')
                .update({ preferred_language: language })
                .eq('id', userId);

            if (error) {
                return { success: false, error: error.message };
            }

            return { success: true, error: null };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to update language',
            };
        }
    }

    /**
     * Add points to user profile
     */
    static async addPoints(
        userId: string,
        points: number
    ): Promise<{ success: boolean; error: string | null }> {
        try {
            const { error } = await supabase.rpc('add_points', {
                user_id: userId,
                points_to_add: points,
            });

            if (error) {
                return { success: false, error: error.message };
            }

            return { success: true, error: null };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to add points',
            };
        }
    }

    /**
     * Deduct points from user profile
     */
    static async deductPoints(
        userId: string,
        points: number
    ): Promise<{ success: boolean; error: string | null }> {
        try {
            const { data, error } = await supabase.rpc('deduct_points', {
                user_id: userId,
                points_to_deduct: points,
            });

            if (error) {
                return { success: false, error: error.message };
            }

            return { success: data, error: null };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to deduct points',
            };
        }
    }

    /**
     * Get current points balance
     */
    static async getPointsBalance(userId: string): Promise<{ points: number; error: string | null }> {
        try {
            const { data, error } = await supabase
                .from('user_profiles')
                .select('points')
                .eq('id', userId)
                .single();

            if (error) {
                return { points: 0, error: error.message };
            }

            return { points: data.points, error: null };
        } catch (error) {
            return {
                points: 0,
                error: error instanceof Error ? error.message : 'Failed to get points balance',
            };
        }
    }

    /**
     * Update subscription tier
     */
    static async updateSubscription(
        userId: string,
        tier: string,
        expiresAt?: Date
    ): Promise<{ success: boolean; error: string | null }> {
        try {
            const { error } = await supabase
                .from('user_profiles')
                .update({
                    subscription_tier: tier,
                    subscription_expires_at: expiresAt?.toISOString(),
                })
                .eq('id', userId);

            if (error) {
                return { success: false, error: error.message };
            }

            return { success: true, error: null };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to update subscription',
            };
        }
    }

    /**
     * Check if subscription is active
     */
    static async isSubscriptionActive(userId: string): Promise<{ active: boolean; error: string | null }> {
        try {
            const { data, error } = await supabase.rpc('is_subscription_active', {
                user_id: userId,
            });

            if (error) {
                return { active: false, error: error.message };
            }

            return { active: data, error: null };
        } catch (error) {
            return {
                active: false,
                error: error instanceof Error ? error.message : 'Failed to check subscription status',
            };
        }
    }

    /**
     * Upload profile image
     */
    static async uploadProfileImage(
        userId: string,
        image: ProfileImageUpload
    ): Promise<{ imageUrl: string | null; error: string | null }> {
        try {
            // Convert image URI to blob for upload
            const response = await fetch(image.uri);
            const blob = await response.blob();

            const fileName = `${userId}/profile-${Date.now()}.${image.type.split('/')[1]}`;

            const { data, error } = await supabase.storage
                .from('profile-images')
                .upload(fileName, blob, {
                    contentType: image.type,
                    upsert: true,
                });

            if (error) {
                return { imageUrl: null, error: error.message };
            }

            // Get public URL
            const { data: urlData } = supabase.storage
                .from('profile-images')
                .getPublicUrl(data.path);

            return { imageUrl: urlData.publicUrl, error: null };
        } catch (error) {
            return {
                imageUrl: null,
                error: error instanceof Error ? error.message : 'Failed to upload profile image',
            };
        }
    }

    /**
     * Delete profile image
     */
    static async deleteProfileImage(
        userId: string,
        imagePath: string
    ): Promise<{ success: boolean; error: string | null }> {
        try {
            const { error } = await supabase.storage
                .from('profile-images')
                .remove([imagePath]);

            if (error) {
                return { success: false, error: error.message };
            }

            return { success: true, error: null };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to delete profile image',
            };
        }
    }

    /**
     * Get nearby farmers (within specified radius in km)
     */
    static async getNearbyFarmers(
        location: GeoLocation,
        radiusKm: number = 50
    ): Promise<{ farmers: UserProfile[]; error: string | null }> {
        try {
            const { data, error } = await supabase.rpc('get_nearby_farmers', {
                user_lat: location.latitude,
                user_lng: location.longitude,
                radius_km: radiusKm,
            });

            if (error) {
                return { farmers: [], error: error.message };
            }

            return { farmers: data || [], error: null };
        } catch (error) {
            return {
                farmers: [],
                error: error instanceof Error ? error.message : 'Failed to get nearby farmers',
            };
        }
    }

    /**
     * Delete user profile
     */
    static async deleteProfile(userId: string): Promise<{ success: boolean; error: string | null }> {
        try {
            const { error } = await supabase
                .from('user_profiles')
                .delete()
                .eq('id', userId);

            if (error) {
                return { success: false, error: error.message };
            }

            return { success: true, error: null };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to delete profile',
            };
        }
    }
}