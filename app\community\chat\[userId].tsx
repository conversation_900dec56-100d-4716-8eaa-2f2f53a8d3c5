import React, { useState, useEffect, useRef } from 'react';
import {
    View,
    Text,
    FlatList,
    TextInput,
    TouchableOpacity,
    KeyboardAvoidingView,
    Platform,
    Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../../../src/design-system';
import { VoiceButton } from '../../../src/components/ui/VoiceButton';
import { SocialService, Message } from '../../../src/services/supabase/social';
import { useVoiceStore } from '../../../src/stores/voice';

export default function ChatScreen() {
    const { userId } = useLocalSearchParams<{ userId: string }>();
    const [messages, setMessages] = useState<Message[]>([]);
    const [messageText, setMessageText] = useState('');
    const [loading, setLoading] = useState(true);
    const [sending, setSending] = useState(false);
    const [otherUserName, setOtherUserName] = useState('User');

    const flatListRef = useRef<FlatList>(null);
    const { speak, isVoiceEnabled, startListening } = useVoiceStore();

    useEffect(() => {
        if (userId) {
            loadConversation();
        }
    }, [userId]);

    const loadConversation = async () => {
        try {
            setLoading(true);

            // Mock conversation data for now
            const mockMessages: Message[] = [
                {
                    id: '1',
                    senderId: userId,
                    recipientId: 'current-user',
                    content: 'Hi! I saw your post about corn irrigation. Could you share more details?',
                    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
                    sender: {
                        id: userId,
                        name: 'Ahmed Hassan',
                    },
                },
                {
                    id: '2',
                    senderId: 'current-user',
                    recipientId: userId,
                    content: 'Sure! I use drip irrigation with a timer system. It saves a lot of water and the plants love it.',
                    createdAt: new Date(Date.now() - 1.5 * 60 * 60 * 1000),
                    sender: {
                        id: 'current-user',
                        name: 'You',
                    },
                },
                {
                    id: '3',
                    senderId: userId,
                    recipientId: 'current-user',
                    content: 'That sounds great! What brand of drip irrigation system do you recommend?',
                    createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
                    sender: {
                        id: userId,
                        name: 'Ahmed Hassan',
                    },
                },
            ];

            setMessages(mockMessages);
            setOtherUserName(mockMessages[0]?.sender.name || 'User');
        } catch (error) {
            Alert.alert('Error', 'Failed to load conversation');
        } finally {
            setLoading(false);
        }
    };

    const sendMessage = async () => {
        if (!messageText.trim()) return;

        const tempMessage: Message = {
            id: Date.now().toString(),
            senderId: 'current-user',
            recipientId: userId,
            content: messageText.trim(),
            createdAt: new Date(),
            sender: {
                id: 'current-user',
                name: 'You',
            },
        };

        // Optimistic update
        setMessages(prev => [...prev, tempMessage]);
        setMessageText('');
        setSending(true);

        try {
            // In a real implementation, this would call the API
            await new Promise(resolve => setTimeout(resolve, 1000));

            if (isVoiceEnabled) {
                speak('Message sent');
            }
        } catch (error) {
            // Remove the optimistic message on error
            setMessages(prev => prev.filter(m => m.id !== tempMessage.id));
            Alert.alert('Error', 'Failed to send message');
        } finally {
            setSending(false);
        }
    };

    const handleVoiceMessage = async () => {
        try {
            if (isVoiceEnabled) {
                speak('Say your message');
                const voiceText = await startListening();

                if (voiceText) {
                    setMessageText(voiceText);
                    speak('Message text added');
                }
            }
        } catch (error) {
            Alert.alert('Voice Error', 'Failed to process voice input');
        }
    };

    const formatTime = (date: Date) => {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    };

    const renderMessage = ({ item }: { item: Message }) => {
        const isCurrentUser = item.senderId === 'current-user';

        return (
            <View style={{
                flexDirection: 'row',
                justifyContent: isCurrentUser ? 'flex-end' : 'flex-start',
                marginVertical: 4,
                marginHorizontal: 16,
            }}>
                <View style={{
                    maxWidth: '80%',
                    backgroundColor: isCurrentUser ? colors.primary[500] : 'white',
                    borderRadius: 16,
                    padding: 12,
                    shadowColor: '#000',
                    shadowOffset: { width: 0, height: 1 },
                    shadowOpacity: 0.1,
                    shadowRadius: 2,
                    elevation: 2,
                }}>
                    <Text style={{
                        fontSize: 16,
                        color: isCurrentUser ? 'white' : colors.primary[900],
                        lineHeight: 20,
                    }}>
                        {item.content}
                    </Text>
                    <Text style={{
                        fontSize: 12,
                        color: isCurrentUser ? 'rgba(255,255,255,0.8)' : colors.earth[500],
                        marginTop: 4,
                        textAlign: 'right',
                    }}>
                        {formatTime(item.createdAt)}
                    </Text>
                </View>
            </View>
        );
    };

    return (
        <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary[50] }}>
            <KeyboardAvoidingView
                style={{ flex: 1 }}
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            >
                {/* Header */}
                <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingHorizontal: 16,
                    paddingVertical: 12,
                    backgroundColor: 'white',
                    borderBottomWidth: 1,
                    borderBottomColor: colors.primary[100],
                }}>
                    <TouchableOpacity
                        onPress={() => router.back()}
                        style={{ marginRight: 12 }}
                        accessibilityLabel="Go back"
                    >
                        <Ionicons name="arrow-back" size={24} color={colors.primary[900]} />
                    </TouchableOpacity>

                    {/* User Avatar */}
                    <View style={{
                        width: 40,
                        height: 40,
                        borderRadius: 20,
                        backgroundColor: colors.primary[100],
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginRight: 12,
                    }}>
                        <Ionicons name="person" size={20} color={colors.primary[500]} />
                    </View>

                    <View style={{ flex: 1 }}>
                        <Text style={{
                            fontSize: 18,
                            fontWeight: '600',
                            color: colors.primary[900],
                        }}>
                            {otherUserName}
                        </Text>
                        <Text style={{
                            fontSize: 14,
                            color: colors.earth[500],
                        }}>
                            Active now
                        </Text>
                    </View>

                    <TouchableOpacity
                        onPress={() => {
                            Alert.alert('Coming Soon', 'Voice call feature will be available soon');
                        }}
                        accessibilityLabel="Voice call"
                    >
                        <Ionicons name="call-outline" size={24} color={colors.primary[500]} />
                    </TouchableOpacity>
                </View>

                {/* Messages */}
                {loading ? (
                    <View style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}>
                        <Text style={{ color: colors.earth[500] }}>Loading messages...</Text>
                    </View>
                ) : (
                    <FlatList
                        ref={flatListRef}
                        data={messages}
                        renderItem={renderMessage}
                        keyExtractor={(item) => item.id}
                        style={{ flex: 1 }}
                        contentContainerStyle={{ paddingVertical: 16 }}
                        onContentSizeChange={() => flatListRef.current?.scrollToEnd()}
                        showsVerticalScrollIndicator={false}
                    />
                )}

                {/* Message Input */}
                <View style={{
                    flexDirection: 'row',
                    alignItems: 'flex-end',
                    paddingHorizontal: 16,
                    paddingVertical: 12,
                    backgroundColor: 'white',
                    borderTopWidth: 1,
                    borderTopColor: colors.primary[100],
                    gap: 8,
                }}>
                    <TextInput
                        value={messageText}
                        onChangeText={setMessageText}
                        placeholder="Type a message..."
                        multiline
                        style={{
                            flex: 1,
                            borderWidth: 1,
                            borderColor: colors.primary[200],
                            borderRadius: 20,
                            paddingHorizontal: 16,
                            paddingVertical: 12,
                            fontSize: 16,
                            maxHeight: 100,
                        }}
                        accessibilityLabel="Message input"
                    />

                    <VoiceButton
                        onPress={handleVoiceMessage}
                        size="small"
                    />

                    <TouchableOpacity
                        onPress={sendMessage}
                        disabled={!messageText.trim() || sending}
                        style={{
                            backgroundColor: (!messageText.trim() || sending)
                                ? colors.earth[200]
                                : colors.primary[500],
                            paddingHorizontal: 16,
                            paddingVertical: 12,
                            borderRadius: 20,
                        }}
                        accessibilityLabel="Send message"
                    >
                        <Ionicons
                            name="send"
                            size={20}
                            color="white"
                        />
                    </TouchableOpacity>
                </View>
            </KeyboardAvoidingView>
        </SafeAreaView>
    );
}