/**
 * Accessible Text Component
 * Enhanced text component with automatic scaling and voice support
 */

import React from 'react';
import { Text, TextProps } from 'react-native';
import { useAccessibility } from '../../hooks/useAccessibility';

interface AccessibleTextProps extends TextProps {
    variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'body' | 'bodyLarge' | 'bodySmall' | 'caption' | 'label';
    voiceEnabled?: boolean;
    semanticRole?: 'header' | 'text' | 'label' | 'summary';
    children: React.ReactNode;
}

export const AccessibleText: React.FC<AccessibleTextProps> = ({
    variant = 'body',
    voiceEnabled = false,
    semanticRole = 'text',
    style,
    children,
    accessibilityLabel,
    accessibilityHint,
    ...props
}) => {
    const {
        getScaledFontSize,
        getHighContrastColors,
        getAccessibilityProps,
        isVoiceEnabled,
        speakText,
    } = useAccessibility();

    const highContrastColors = getHighContrastColors();

    const getVariantStyles = () => {
        const baseStyles = {
            h1: { fontSize: getScaledFontSize(36), fontWeight: 'bold' as const },
            h2: { fontSize: getScaledFontSize(30), fontWeight: 'bold' as const },
            h3: { fontSize: getScaledFontSize(24), fontWeight: '600' as const },
            h4: { fontSize: getScaledFontSize(20), fontWeight: '600' as const },
            body: { fontSize: getScaledFontSize(16), fontWeight: 'normal' as const },
            bodyLarge: { fontSize: getScaledFontSize(18), fontWeight: 'normal' as const },
            bodySmall: { fontSize: getScaledFontSize(14), fontWeight: 'normal' as const },
            caption: { fontSize: getScaledFontSize(12), fontWeight: 'normal' as const },
            label: { fontSize: getScaledFontSize(14), fontWeight: '500' as const },
        };

        return baseStyles[variant];
    };

    const getColorStyles = () => {
        if (highContrastColors) {
            return { color: highContrastColors.foreground };
        }

        const colorMap = {
            h1: '#1c1917',
            h2: '#1c1917',
            h3: '#292524',
            h4: '#292524',
            body: '#44403c',
            bodyLarge: '#44403c',
            bodySmall: '#57534e',
            caption: '#78716c',
            label: '#44403c',
        };

        return { color: colorMap[variant] };
    };

    const handlePress = async () => {
        if ((voiceEnabled || isVoiceEnabled) && typeof children === 'string') {
            await speakText(children);
        }
    };

    const textContent = typeof children === 'string' ? children : '';
    const accessibilityRole = semanticRole === 'header' ? 'header' : 'text';

    return (
        <Text
            {...props}
            style={[
                getVariantStyles(),
                getColorStyles(),
                style,
            ]}
            onPress={voiceEnabled || isVoiceEnabled ? handlePress : props.onPress}
            {...getAccessibilityProps(
                accessibilityLabel || textContent,
                accessibilityHint,
                accessibilityRole
            )}
        >
            {children}
        </Text>
    );
};