import React from 'react';
import { View, Pressable, Text } from 'react-native';

export interface TabItem {
  id: string;
  label: string;
  icon: string;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  badge?: number;
}

export interface TabNavigationProps {
  tabs: TabItem[];
  activeTab: string;
  onTabPress: (tabId: string) => void;
  voiceFeedbackEnabled?: boolean;
  onVoiceFeedback?: (text: string) => void;
  testID?: string;
}

export const TabNavigation: React.FC<TabNavigationProps> = ({
  tabs,
  activeTab,
  onTabPress,
  voiceFeedbackEnabled = false,
  onVoiceFeedback,
  testID,
}) => {
  const handleTabPress = (tab: TabItem) => {
    if (voiceFeedbackEnabled && onVoiceFeedback) {
      onVoiceFeedback(`${tab.label} tab selected`);
    }
    onTabPress(tab.id);
  };

  return (
    <View className="flex-row border-t border-earth-200 bg-white shadow-lg" testID={testID}>
      {tabs.map((tab) => {
        const isActive = activeTab === tab.id;

        return (
          <Pressable
            key={tab.id}
            onPress={() => handleTabPress(tab)}
            className={`
                            min-h-[72px] flex-1 items-center justify-center px-2 py-3
                            ${isActive ? 'bg-primary-50' : 'bg-white'}
                            active:opacity-80
                        `}
            accessibilityRole="tab"
            accessibilityState={{ selected: isActive }}
            accessibilityLabel={tab.accessibilityLabel || tab.label}
            accessibilityHint={tab.accessibilityHint || `Navigate to ${tab.label} section`}
            testID={`tab-${tab.id}`}>
            <View className="relative items-center">
              {/* Tab Icon */}
              <Text className={`mb-1 text-2xl ${isActive ? 'opacity-100' : 'opacity-70'}`}>
                {tab.icon}
              </Text>

              {/* Badge */}
              {tab.badge && tab.badge > 0 && (
                <View className="absolute -right-2 -top-1 h-5 min-w-[20px] items-center justify-center rounded-full bg-red-500 px-1">
                  <Text className="text-xs font-bold text-white">
                    {tab.badge > 99 ? '99+' : tab.badge}
                  </Text>
                </View>
              )}
            </View>

            {/* Tab Label */}
            <Text
              className={`text-center text-xs font-medium ${
                isActive ? 'text-primary-700' : 'text-earth-600'
              }`}>
              {tab.label}
            </Text>

            {/* Active Indicator */}
            {isActive && (
              <View className="absolute bottom-0 left-1/2 h-1 w-8 -translate-x-1/2 transform rounded-t-full bg-primary-500" />
            )}
          </Pressable>
        );
      })}
    </View>
  );
};

// Default agricultural tabs configuration
export const AGRICULTURAL_TABS: TabItem[] = [
  {
    id: 'home',
    label: 'Home',
    icon: '🏠',
    accessibilityLabel: 'Home tab',
    accessibilityHint: 'View dashboard with weather, tasks, and quick actions',
  },
  {
    id: 'crops',
    label: 'Crops',
    icon: '🌱',
    accessibilityLabel: 'Crops tab',
    accessibilityHint: 'Manage crop plans and view growth progress',
  },
  {
    id: 'ai',
    label: 'AI Assistant',
    icon: '🤖',
    accessibilityLabel: 'AI Assistant tab',
    accessibilityHint: 'Chat with AI for farming advice and image analysis',
  },
  {
    id: 'store',
    label: 'Store',
    icon: '🛒',
    accessibilityLabel: 'Store tab',
    accessibilityHint: 'Browse and purchase agricultural products',
  },
  {
    id: 'community',
    label: 'Community',
    icon: '👥',
    accessibilityLabel: 'Community tab',
    accessibilityHint: 'Connect with other farmers and share experiences',
  },
];
