-- Function to get nearby farmers based on location
CREATE OR REPLACE FUNCTION public.get_nearby_farmers(
  user_lat FLOAT,
  user_lng FLOAT,
  radius_km FLOAT DEFAULT 50
)
RETURNS TABLE (
  id UUID,
  farm_location POINT,
  crop_types TEXT[],
  experience_level VARCHAR,
  preferred_language VARCHAR,
  distance_km FLOAT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    up.id,
    up.farm_location,
    up.crop_types,
    up.experience_level,
    up.preferred_language,
    ST_Distance(
      ST_GeogFromText('POINT(' || user_lng || ' ' || user_lat || ')'),
      ST_GeogFromText(ST_AsText(up.farm_location))
    ) / 1000 AS distance_km
  FROM user_profiles up
  WHERE up.farm_location IS NOT NULL
    AND ST_DWithin(
      ST_GeogFromText('POINT(' || user_lng || ' ' || user_lat || ')'),
      ST_GeogFromText(ST_AsText(up.farm_location)),
      radius_km * 1000
    )
    AND up.id != auth.uid()
  ORDER BY distance_km ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user statistics
CREATE OR REPLACE FUNCTION public.get_user_stats(user_id UUID)
RETURNS TABLE (
  total_points INTEGER,
  completed_tasks INTEGER,
  active_crop_plans INTEGER,
  community_posts INTEGER,
  days_active INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COALESCE(up.points, 0) as total_points,
    COALESCE(task_count.completed, 0) as completed_tasks,
    COALESCE(plan_count.active, 0) as active_crop_plans,
    COALESCE(post_count.posts, 0) as community_posts,
    COALESCE(
      EXTRACT(DAY FROM (NOW() - u.created_at))::INTEGER,
      0
    ) as days_active
  FROM users u
  LEFT JOIN user_profiles up ON u.id = up.id
  LEFT JOIN (
    SELECT 
      cp.user_id,
      COUNT(t.id) as completed
    FROM crop_plans cp
    LEFT JOIN tasks t ON cp.id = t.crop_plan_id AND t.completed = true
    WHERE cp.user_id = user_id
    GROUP BY cp.user_id
  ) task_count ON u.id = task_count.user_id
  LEFT JOIN (
    SELECT 
      user_id,
      COUNT(id) as active
    FROM crop_plans
    WHERE user_id = user_id AND status = 'active'
    GROUP BY user_id
  ) plan_count ON u.id = plan_count.user_id
  LEFT JOIN (
    SELECT 
      user_id,
      COUNT(id) as posts
    FROM community_posts
    WHERE user_id = user_id
    GROUP BY user_id
  ) post_count ON u.id = post_count.user_id
  WHERE u.id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update user activity timestamp
CREATE OR REPLACE FUNCTION public.update_user_activity()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE users 
  SET updated_at = NOW() 
  WHERE id = auth.uid();
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate crop types
CREATE OR REPLACE FUNCTION public.validate_crop_types(crop_list TEXT[])
RETURNS BOOLEAN AS $$
DECLARE
  valid_crops TEXT[] := ARRAY[
    'corn', 'wheat', 'rice', 'soybeans', 'barley', 'oats',
    'tomatoes', 'potatoes', 'onions', 'carrots', 'lettuce',
    'spinach', 'peppers', 'cucumbers', 'beans', 'peas',
    'apples', 'oranges', 'grapes', 'strawberries', 'blueberries',
    'cotton', 'sugarcane', 'sunflower', 'canola', 'alfalfa'
  ];
  crop TEXT;
BEGIN
  FOREACH crop IN ARRAY crop_list
  LOOP
    IF NOT (crop = ANY(valid_crops)) THEN
      RETURN FALSE;
    END IF;
  END LOOP;
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to get crop recommendations based on location and experience
CREATE OR REPLACE FUNCTION public.get_crop_recommendations(
  user_lat FLOAT,
  user_lng FLOAT,
  experience VARCHAR DEFAULT 'beginner'
)
RETURNS TABLE (
  crop_name TEXT,
  difficulty_level VARCHAR,
  season TEXT,
  description TEXT
) AS $$
BEGIN
  -- This is a simplified version - in production, this would use
  -- climate data, soil conditions, and regional agricultural data
  RETURN QUERY
  SELECT 
    crop.name,
    crop.difficulty,
    crop.season,
    crop.description
  FROM (
    VALUES 
      ('tomatoes', 'beginner', 'spring', 'Easy to grow, high yield vegetable'),
      ('lettuce', 'beginner', 'spring', 'Fast-growing leafy green'),
      ('beans', 'beginner', 'summer', 'Nitrogen-fixing legume, good for soil'),
      ('corn', 'intermediate', 'summer', 'Requires more space and nutrients'),
      ('wheat', 'intermediate', 'fall', 'Grain crop for experienced farmers'),
      ('rice', 'expert', 'summer', 'Requires water management expertise')
  ) AS crop(name, difficulty, season, description)
  WHERE crop.difficulty = experience OR experience = 'expert';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create storage bucket for profile images
INSERT INTO storage.buckets (id, name, public) 
VALUES ('profile-images', 'profile-images', true)
ON CONFLICT (id) DO NOTHING;

-- Create storage policy for profile images
CREATE POLICY "Users can upload their own profile images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'profile-images' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can view all profile images" ON storage.objects
  FOR SELECT USING (bucket_id = 'profile-images');

CREATE POLICY "Users can update their own profile images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'profile-images' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own profile images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'profile-images' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );