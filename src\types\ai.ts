export interface ChatMessage {
    id: string;
    type: 'user' | 'ai';
    content: string;
    imageUrl?: string;
    timestamp: Date;
    analysisResult?: ImageAnalysisResult;
    category?: string;
}

export interface ImageAnalysisResult {
    id: string;
    imageUri: string;
    category: 'plant' | 'soil' | 'fertilizer';
    issues: Issue[];
    recommendations: Recommendation[];
    confidence: number;
    timestamp: Date;
}

export interface Issue {
    name: string;
    severity: 'low' | 'medium' | 'high';
    description: string;
}

export interface Recommendation {
    title: string;
    description: string;
    priority: 'low' | 'medium' | 'high';
    products?: string[];
}

export interface Category {
    id: string;
    name: string;
    icon: string;
    description: string;
    color: string;
    bgColor: string;
}

export interface ChatContext {
    category?: string;
    previousMessages?: ChatMessage[];
    userProfile?: {
        cropTypes: string[];
        location: string;
        experienceLevel: string;
    };
}

export interface AIService {
    sendMessage(message: string, context?: ChatContext): Promise<string>;
    analyzeImage(imageUri: string, category: string): Promise<ImageAnalysisResult>;
}

export interface TTSOptions {
    language: string;
    rate: number;
    pitch: number;
}

export interface VoiceController {
    enableVoiceMode(): void;
    disableVoiceMode(): void;
    speak(text: string, options?: TTSOptions): Promise<void>;
    startListening(): Promise<string>;
    stopListening(): void;
    isVoiceEnabled(): boolean;
}