import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, Alert, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Button } from '../../src/components/ui/Button';
import { ProgressIndicator } from '../../src/components/ui/ProgressIndicator';
import { CropSelector } from '../../src/components/ui/CropSelector';
import { AppProvider, useApp } from '../../src/contexts/AppContext';
import { LocationService, FarmLocation } from '../../src/services/location';
import { experienceLevels } from '../../src/data/crops';

const FarmSetupContent: React.FC = () => {
    const { t, isRTL, isVoiceEnabled, speak, currentLanguage } = useApp();
    const [farmLocation, setFarmLocation] = useState<FarmLocation | null>(null);
    const [selectedCrops, setSelectedCrops] = useState<string[]>([]);
    const [experienceLevel, setExperienceLevel] = useState<string>('');
    const [isLoadingLocation, setIsLoadingLocation] = useState(false);

    const locationService = LocationService.getInstance();

    useEffect(() => {
        if (isVoiceEnabled) {
            speak(t('farmSetup.title') + '. ' + t('farmSetup.subtitle'));
        }
    }, [isVoiceEnabled]);

    const handleUseCurrentLocation = async () => {
        setIsLoadingLocation(true);

        try {
            const location = await locationService.getCurrentLocation();
            if (location) {
                setFarmLocation(location);
                const locationText = locationService.formatLocationForDisplay(location);
                if (isVoiceEnabled) {
                    speak(`Location set to ${locationText}`);
                }
            } else {
                Alert.alert(
                    'Location Error',
                    'Unable to get your current location. Please check your location permissions and try again.',
                    [{ text: 'OK' }]
                );
                if (isVoiceEnabled) {
                    speak('Unable to get current location');
                }
            }
        } catch (error) {
            console.error('Location error:', error);
            Alert.alert(
                'Location Error',
                'Failed to get your location. Please try again or select manually.',
                [{ text: 'OK' }]
            );
            if (isVoiceEnabled) {
                speak('Failed to get location');
            }
        } finally {
            setIsLoadingLocation(false);
        }
    };

    const handleSelectOnMap = () => {
        // In a real implementation, this would open a map picker
        Alert.alert(
            'Map Selection',
            'Map-based location selection would be implemented here. For now, please use current location.',
            [{ text: 'OK' }]
        );
        if (isVoiceEnabled) {
            speak('Map selection feature coming soon');
        }
    };

    const handleExperienceLevelSelect = (level: string) => {
        setExperienceLevel(level);
        const selectedLevel = experienceLevels.find(l => l.id === level);
        if (selectedLevel && isVoiceEnabled) {
            const levelName = currentLanguage === 'ar' ? selectedLevel.nameAr : selectedLevel.name;
            speak(`Experience level set to ${levelName}`);
        }
    };

    const handleComplete = () => {
        // Validate form
        if (!farmLocation) {
            Alert.alert(
                'Missing Information',
                'Please set your farm location before continuing.',
                [{ text: 'OK' }]
            );
            if (isVoiceEnabled) {
                speak('Please set your farm location');
            }
            return;
        }

        if (selectedCrops.length === 0) {
            Alert.alert(
                'Missing Information',
                'Please select at least one crop type.',
                [{ text: 'OK' }]
            );
            if (isVoiceEnabled) {
                speak('Please select at least one crop type');
            }
            return;
        }

        if (!experienceLevel) {
            Alert.alert(
                'Missing Information',
                'Please select your experience level.',
                [{ text: 'OK' }]
            );
            if (isVoiceEnabled) {
                speak('Please select your experience level');
            }
            return;
        }

        // In a real app, you would save all the registration data here
        const farmData = {
            location: farmLocation,
            crops: selectedCrops,
            experienceLevel,
        };

        console.log('Farm setup data:', farmData);

        if (isVoiceEnabled) {
            speak('Registration completed successfully');
        }

        router.push('/(auth)/completion');
    };

    const handleBack = () => {
        if (isVoiceEnabled) {
            speak('Going back to personal information');
        }
        router.back();
    };

    const isFormValid = farmLocation && selectedCrops.length > 0 && experienceLevel;

    return (
        <SafeAreaView className={`flex-1 bg-earth-50 ${isRTL ? 'rtl' : 'ltr'}`}>
            <StatusBar style="dark" />

            <ScrollView
                className="flex-1"
                contentContainerStyle={{ flexGrow: 1 }}
                showsVerticalScrollIndicator={false}
            >
                <View className="flex-1 px-6 py-8">
                    {/* Progress Indicator */}
                    <ProgressIndicator
                        currentStep={2}
                        totalSteps={3}
                        stepLabels={[t('personalInfo.title'), t('farmSetup.title'), 'Complete']}
                        className="mb-8"
                        isRTL={isRTL}
                    />

                    {/* Header */}
                    <View className="mb-8">
                        <Text className={`mb-2 text-3xl font-bold text-earth-900 ${isRTL ? 'text-right' : 'text-left'}`}>
                            {t('farmSetup.title')}
                        </Text>
                        <Text className={`text-lg text-earth-600 ${isRTL ? 'text-right' : 'text-left'}`}>
                            {t('farmSetup.subtitle')}
                        </Text>
                    </View>

                    {/* Farm Location Section */}
                    <View className="mb-8">
                        <Text className={`mb-4 text-xl font-semibold text-earth-800 ${isRTL ? 'text-right' : 'text-left'}`}>
                            📍 {t('farmSetup.farmLocation')}
                        </Text>

                        {farmLocation ? (
                            <View className="mb-4 rounded-xl bg-primary-50 p-4">
                                <Text className={`text-lg font-medium text-primary-700 ${isRTL ? 'text-right' : 'text-left'}`}>
                                    {locationService.formatLocationForDisplay(farmLocation)}
                                </Text>
                                <Text className={`mt-1 text-sm text-primary-600 ${isRTL ? 'text-right' : 'text-left'}`}>
                                    {currentLanguage === 'ar' ? 'تم تحديد الموقع' : 'Location set'}
                                </Text>
                            </View>
                        ) : (
                            <Text className={`mb-4 text-earth-600 ${isRTL ? 'text-right' : 'text-left'}`}>
                                {currentLanguage === 'ar'
                                    ? 'يرجى تحديد موقع مزرعتك للحصول على توصيات محلية'
                                    : 'Please set your farm location to get localized recommendations'
                                }
                            </Text>
                        )}

                        <View className={`flex-row gap-4 ${isRTL ? 'flex-row-reverse gap-reverse' : ''}`}>
                            <Button
                                title={t('farmSetup.useCurrentLocation')}
                                onPress={handleUseCurrentLocation}
                                variant="primary"
                                className="flex-1"
                                loading={isLoadingLocation}
                                icon={isLoadingLocation ? undefined : <Text className="text-xl">📍</Text>}
                                accessibilityLabel="Use current GPS location"
                                accessibilityHint="Get your current location using GPS"
                                voiceFeedbackEnabled={isVoiceEnabled}
                                onVoiceFeedback={speak}
                            />

                            <Button
                                title={t('farmSetup.selectOnMap')}
                                onPress={handleSelectOnMap}
                                variant="outline"
                                className="flex-1"
                                icon={<Text className="text-xl">🗺️</Text>}
                                accessibilityLabel="Select location on map"
                                accessibilityHint="Choose your farm location using a map"
                                voiceFeedbackEnabled={isVoiceEnabled}
                                onVoiceFeedback={speak}
                            />
                        </View>
                    </View>

                    {/* Crop Selection Section */}
                    <View className="mb-8">
                        <Text className={`mb-4 text-xl font-semibold text-earth-800 ${isRTL ? 'text-right' : 'text-left'}`}>
                            🌱 {t('farmSetup.whatDoYouGrow')}
                        </Text>

                        <CropSelector
                            selectedCrops={selectedCrops}
                            onCropsChange={setSelectedCrops}
                            maxSelections={5}
                            isRTL={isRTL}
                            currentLanguage={currentLanguage}
                            voiceFeedbackEnabled={isVoiceEnabled}
                            onVoiceFeedback={speak}
                        />
                    </View>

                    {/* Experience Level Section */}
                    <View className="mb-8">
                        <Text className={`mb-4 text-xl font-semibold text-earth-800 ${isRTL ? 'text-right' : 'text-left'}`}>
                            🎯 {t('farmSetup.experienceLevel')}
                        </Text>

                        <View className="gap-3">
                            {experienceLevels.map(level => (
                                <Button
                                    key={level.id}
                                    title={`${level.icon} ${currentLanguage === 'ar' ? level.nameAr : level.name}`}
                                    onPress={() => handleExperienceLevelSelect(level.id)}
                                    variant={experienceLevel === level.id ? 'primary' : 'outline'}
                                    className="w-full"
                                    accessibilityLabel={`Select ${currentLanguage === 'ar' ? level.nameAr : level.name} experience level`}
                                    accessibilityHint={currentLanguage === 'ar' ? level.descriptionAr : level.description}
                                    voiceFeedbackEnabled={isVoiceEnabled}
                                    onVoiceFeedback={speak}
                                />
                            ))}
                        </View>
                    </View>

                    {/* Navigation Buttons */}
                    <View className={`mt-8 flex-row gap-4 ${isRTL ? 'flex-row-reverse gap-reverse' : ''}`}>
                        <Button
                            title={t('personalInfo.back')}
                            onPress={handleBack}
                            variant="outline"
                            size="large"
                            className="flex-1"
                            accessibilityLabel="Go back to personal information"
                            accessibilityHint="Return to the previous screen"
                            voiceFeedbackEnabled={isVoiceEnabled}
                            onVoiceFeedback={speak}
                        />

                        <Button
                            title={t('farmSetup.complete')}
                            onPress={handleComplete}
                            variant="primary"
                            size="large"
                            className="flex-1"
                            disabled={!isFormValid}
                            icon={<Text className="text-xl">✅</Text>}
                            accessibilityLabel="Complete registration"
                            accessibilityHint="Finish the registration process"
                            voiceFeedbackEnabled={isVoiceEnabled}
                            onVoiceFeedback={speak}
                        />
                    </View>
                </View>
            </ScrollView>
        </SafeAreaView>
    );
};

export default function FarmSetup() {
    return (
        <AppProvider>
            <FarmSetupContent />
        </AppProvider>
    );
}