#!/usr/bin/env node

/**
 * Supabase Setup Verification Script
 * Quick verification that the production Supabase project is properly configured
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

async function verifySetup() {
  console.log('🔍 Verifying Supabase Production Setup...\n');

  // Check environment variables
  const requiredVars = ['EXPO_PUBLIC_SUPABASE_URL', 'EXPO_PUBLIC_SUPABASE_ANON_KEY'];

  let missingVars = [];
  requiredVars.forEach((varName) => {
    if (!process.env[varName]) {
      missingVars.push(varName);
    }
  });

  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingVars.forEach((varName) => console.error(`   - ${varName}`));
    return false;
  }

  console.log('✅ Environment variables configured');

  // Test connection
  const supabase = createClient(
    process.env.EXPO_PUBLIC_SUPABASE_URL,
    process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY
  );

  try {
    // Test basic connectivity
    const { data, error } = await supabase.auth.getSession();

    if (error && !error.message.includes('session_not_found')) {
      throw error;
    }

    console.log('✅ Connection to Supabase successful');

    // Extract project info
    const projectId = process.env.EXPO_PUBLIC_SUPABASE_URL.match(
      /https:\/\/([^.]+)\.supabase\.co/
    )?.[1];
    console.log(`✅ Project ID: ${projectId}`);
    console.log(`✅ Dashboard: https://supabase.com/dashboard/project/${projectId}`);

    console.log('\n🎉 Supabase setup verification completed successfully!');
    console.log('📋 Ready for next task: Database Migration');

    return true;
  } catch (error) {
    console.error('❌ Connection test failed:', error.message);
    return false;
  }
}

if (require.main === module) {
  verifySetup().then((success) => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { verifySetup };
