/**
 * Accessibility Validator Component
 * Real-time accessibility validation and feedback
 */

import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAccessibility } from '../../hooks/useAccessibility';
import { AccessibilityTester, AccessibilityTestSuite } from '../../utils/accessibilityTesting';
import { Button } from './Button';

interface AccessibilityValidatorProps {
    children: React.ReactNode;
    componentName?: string;
    autoValidate?: boolean;
    showResults?: boolean;
}

export const AccessibilityValidator: React.FC<AccessibilityValidatorProps> = ({
    children,
    componentName = 'Component',
    autoValidate = false,
    showResults = false,
}) => {
    const [testResults, setTestResults] = useState<AccessibilityTestSuite | null>(null);
    const [isValidating, setIsValidating] = useState(false);

    const {
        getScaledFontSize,
        getHighContrastColors,
        getAccessibilityProps,
        announceToScreenReader,
    } = useAccessibility();

    const highContrastColors = getHighContrastColors();
    const tester = AccessibilityTester.getInstance();

    useEffect(() => {
        if (autoValidate) {
            validateAccessibility();
        }
    }, [autoValidate]);

    const validateAccessibility = async () => {
        setIsValidating(true);

        try {
            // Simulate component validation
            const mockProps = {
                accessibilityLabel: `${componentName} component`,
                accessibilityHint: 'Interactive component',
                onPress: () => { },
            };

            const results = tester.runTestSuite(
                componentName,
                mockProps,
                { width: 120, height: 48 },
                {
                    foreground: highContrastColors?.foreground || '#1c1917',
                    background: highContrastColors?.background || '#ffffff'
                },
                getScaledFontSize(16)
            );

            setTestResults(results);

            // Announce results
            const score = results.overallScore;
            const message = `Accessibility validation complete. Score: ${score}%`;
            announceToScreenReader(message);

            if (score < 80) {
                Alert.alert(
                    'Accessibility Issues Found',
                    `${componentName} has accessibility issues. Score: ${score}%`,
                    [{ text: 'OK' }]
                );
            }
        } catch (error) {
            console.warn('Accessibility validation failed:', error);
        } finally {
            setIsValidating(false);
        }
    };

    const getScoreColor = (score: number) => {
        if (highContrastColors) {
            if (score >= 90) return highContrastColors.success;
            if (score >= 70) return highContrastColors.warning;
            return highContrastColors.error;
        }

        if (score >= 90) return '#22c55e';
        if (score >= 70) return '#eab308';
        return '#ef4444';
    };

    const getScoreIcon = (score: number) => {
        if (score >= 90) return 'checkmark-circle';
        if (score >= 70) return 'warning';
        return 'close-circle';
    };

    return (
        <View>
            {children}

            {showResults && (
                <View
                    style={{
                        marginTop: 16,
                        padding: 16,
                        borderRadius: 12,
                        backgroundColor: highContrastColors?.background || '#f9fafb',
                        borderWidth: 1,
                        borderColor: highContrastColors?.foreground || '#e5e7eb',
                    }}
                >
                    <View className="flex-row items-center justify-between mb-4">
                        <Text
                            style={{
                                fontSize: getScaledFontSize(16),
                                fontWeight: '600',
                                color: highContrastColors?.foreground || '#1f2937',
                            }}
                            {...getAccessibilityProps('Accessibility validation results', 'Results from accessibility testing')}
                        >
                            🔍 Accessibility Validation
                        </Text>

                        <Button
                            title={isValidating ? 'Validating...' : 'Validate'}
                            onPress={validateAccessibility}
                            variant="outline"
                            size="small"
                            loading={isValidating}
                            accessibilityLabel="Run accessibility validation"
                            accessibilityHint="Tap to validate component accessibility"
                        />
                    </View>

                    {testResults && (
                        <View>
                            <View className="flex-row items-center mb-3">
                                <Ionicons
                                    name={getScoreIcon(testResults.overallScore)}
                                    size={24}
                                    color={getScoreColor(testResults.overallScore)}
                                />
                                <Text
                                    style={{
                                        marginLeft: 8,
                                        fontSize: getScaledFontSize(18),
                                        fontWeight: 'bold',
                                        color: getScoreColor(testResults.overallScore),
                                    }}
                                    {...getAccessibilityProps(
                                        `Overall accessibility score: ${testResults.overallScore}%`,
                                        'Accessibility validation score'
                                    )}
                                >
                                    {testResults.overallScore}% Score
                                </Text>
                            </View>

                            <ScrollView
                                style={{ maxHeight: 200 }}
                                showsVerticalScrollIndicator={true}
                            >
                                {testResults.tests.map((test, index) => (
                                    <View
                                        key={index}
                                        style={{
                                            flexDirection: 'row',
                                            alignItems: 'flex-start',
                                            marginBottom: 8,
                                            padding: 8,
                                            borderRadius: 8,
                                            backgroundColor: test.passed
                                                ? (highContrastColors?.success + '20' || '#dcfce7')
                                                : (highContrastColors?.error + '20' || '#fecaca'),
                                        }}
                                    >
                                        <Ionicons
                                            name={test.passed ? 'checkmark' : 'close'}
                                            size={16}
                                            color={test.passed
                                                ? (highContrastColors?.success || '#16a34a')
                                                : (highContrastColors?.error || '#dc2626')
                                            }
                                            style={{ marginTop: 2, marginRight: 8 }}
                                        />

                                        <View style={{ flex: 1 }}>
                                            <Text
                                                style={{
                                                    fontSize: getScaledFontSize(14),
                                                    fontWeight: '500',
                                                    color: highContrastColors?.foreground || '#374151',
                                                    marginBottom: 2,
                                                }}
                                                {...getAccessibilityProps(test.message, 'Test result message')}
                                            >
                                                {test.message}
                                            </Text>

                                            {test.recommendation && (
                                                <Text
                                                    style={{
                                                        fontSize: getScaledFontSize(12),
                                                        color: highContrastColors?.foreground || '#6b7280',
                                                        fontStyle: 'italic',
                                                    }}
                                                    {...getAccessibilityProps(test.recommendation, 'Accessibility recommendation')}
                                                >
                                                    💡 {test.recommendation}
                                                </Text>
                                            )}
                                        </View>
                                    </View>
                                ))}
                            </ScrollView>
                        </View>
                    )}
                </View>
            )}
        </View>
    );
};