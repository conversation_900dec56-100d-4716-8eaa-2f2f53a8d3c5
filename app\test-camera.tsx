import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

export default function TestCameraPage() {
    const router = useRouter();

    const openCamera = (mode: 'plant' | 'soil' | 'fertilizer') => {
        router.push(`/camera?mode=${mode}`);
    };

    return (
        <View className="flex-1 bg-gray-50 p-4 pt-12">
            <Text className="text-2xl font-bold text-gray-900 mb-8 text-center">
                Test Camera Interface
            </Text>

            <View className="gap-4">
                <TouchableOpacity
                    onPress={() => openCamera('plant')}
                    className="bg-green-600 p-6 rounded-xl flex-row items-center"
                >
                    <Ionicons name="leaf" size={24} color="white" />
                    <Text className="text-white text-lg font-semibold ml-3">
                        Analyze Plant
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity
                    onPress={() => openCamera('soil')}
                    className="bg-amber-600 p-6 rounded-xl flex-row items-center"
                >
                    <Ionicons name="earth" size={24} color="white" />
                    <Text className="text-white text-lg font-semibold ml-3">
                        Analyze Soil
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity
                    onPress={() => openCamera('fertilizer')}
                    className="bg-blue-600 p-6 rounded-xl flex-row items-center"
                >
                    <Ionicons name="water" size={24} color="white" />
                    <Text className="text-white text-lg font-semibold ml-3">
                        Analyze Fertilizer
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity
                    onPress={() => router.back()}
                    className="bg-gray-600 p-6 rounded-xl flex-row items-center justify-center"
                >
                    <Ionicons name="arrow-back" size={24} color="white" />
                    <Text className="text-white text-lg font-semibold ml-3">
                        Go Back
                    </Text>
                </TouchableOpacity>
            </View>
        </View>
    );
}