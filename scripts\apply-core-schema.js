#!/usr/bin/env node

/**
 * Core Schema Application Script
 * This script applies the core database schema using direct SQL execution
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.EXPO_PUBLIC_SUPABASE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// Core schema SQL
const coreSchemaSQL = `
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR UNIQUE NOT NULL,
  phone VARCHAR UNIQUE,
  first_name VARCHAR NOT NULL,
  last_name VARCHAR NOT NULL,
  avatar_url VARCHAR,
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true,
  email_verified BOOLEAN DEFAULT false,
  phone_verified BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
  farm_location GEOGRAPHY(POINT, 4326),
  farm_size_hectares DECIMAL(10,2),
  crop_types TEXT[] DEFAULT '{}',
  experience_level VARCHAR CHECK (experience_level IN ('beginner', 'intermediate', 'expert')),
  preferred_language VARCHAR DEFAULT 'ar',
  voice_enabled BOOLEAN DEFAULT false,
  points INTEGER DEFAULT 0,
  subscription_tier VARCHAR DEFAULT 'free',
  subscription_expires_at TIMESTAMP WITH TIME ZONE,
  notification_preferences JSONB DEFAULT '{}',
  privacy_settings JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create crop_plans table
CREATE TABLE IF NOT EXISTS crop_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  crop_type VARCHAR NOT NULL,
  variety VARCHAR,
  planting_date DATE,
  expected_harvest_date DATE,
  actual_harvest_date DATE,
  location GEOGRAPHY(POINT, 4326),
  area_hectares DECIMAL(10,2),
  status VARCHAR DEFAULT 'active' CHECK (status IN ('planning', 'active', 'harvested', 'failed')),
  weather_alerts_enabled BOOLEAN DEFAULT true,
  ai_recommendations_enabled BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create tasks table
CREATE TABLE IF NOT EXISTS tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  crop_plan_id UUID REFERENCES crop_plans(id) ON DELETE CASCADE,
  title VARCHAR NOT NULL,
  description TEXT,
  due_date TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  task_type VARCHAR NOT NULL,
  priority VARCHAR DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  weather_dependent BOOLEAN DEFAULT false,
  estimated_duration_minutes INTEGER,
  actual_duration_minutes INTEGER,
  completion_notes TEXT,
  photo_evidence_urls TEXT[],
  points_reward INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create chat_sessions table
CREATE TABLE IF NOT EXISTS chat_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  session_type VARCHAR DEFAULT 'general',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create chat_messages table
CREATE TABLE IF NOT EXISTS chat_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID REFERENCES chat_sessions(id) ON DELETE CASCADE,
  message_type VARCHAR CHECK (message_type IN ('user', 'ai')),
  content TEXT,
  image_url VARCHAR,
  analysis_result JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create community_posts table
CREATE TABLE IF NOT EXISTS community_posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR NOT NULL,
  content TEXT,
  image_urls TEXT[],
  location GEOGRAPHY(POINT, 4326),
  likes_count INTEGER DEFAULT 0,
  comments_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create post_comments table
CREATE TABLE IF NOT EXISTS post_comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  post_id UUID REFERENCES community_posts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create products table
CREATE TABLE IF NOT EXISTS products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR NOT NULL,
  description TEXT,
  category VARCHAR NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  image_urls TEXT[] DEFAULT '{}',
  stock_quantity INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create orders table
CREATE TABLE IF NOT EXISTS orders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  total_amount DECIMAL(10,2) NOT NULL,
  status VARCHAR DEFAULT 'pending',
  shipping_address JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create order_items table
CREATE TABLE IF NOT EXISTS order_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  quantity INTEGER NOT NULL,
  unit_price DECIMAL(10,2) NOT NULL
);

-- Create achievements table
CREATE TABLE IF NOT EXISTS achievements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) UNIQUE NOT NULL,
  description TEXT NOT NULL,
  icon VARCHAR(10) NOT NULL,
  category VARCHAR(20) CHECK (category IN ('farming', 'community', 'learning', 'consistency', 'milestone')),
  points_reward INTEGER NOT NULL,
  requirements JSONB NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create subscription_plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(50) UNIQUE NOT NULL,
  description TEXT NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  billing_period VARCHAR(10) CHECK (billing_period IN ('monthly', 'yearly')),
  features JSONB NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create basic indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone);
CREATE INDEX IF NOT EXISTS idx_user_profiles_location ON user_profiles USING GIST(farm_location);
CREATE INDEX IF NOT EXISTS idx_crop_plans_user_id ON crop_plans(user_id);
CREATE INDEX IF NOT EXISTS idx_crop_plans_status ON crop_plans(status);
CREATE INDEX IF NOT EXISTS idx_tasks_crop_plan_id ON tasks(crop_plan_id);
CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON tasks(due_date);
CREATE INDEX IF NOT EXISTS idx_community_posts_user_id ON community_posts(user_id);
CREATE INDEX IF NOT EXISTS idx_community_posts_location ON community_posts USING GIST(location);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_products_active ON products(is_active);
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE crop_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE community_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY IF NOT EXISTS "Users can view their own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY IF NOT EXISTS "Users can update their own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY IF NOT EXISTS "Users can access their own profile" ON user_profiles
  FOR ALL USING (auth.uid() = id);

CREATE POLICY IF NOT EXISTS "Users can access their own crop plans" ON crop_plans
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can access tasks for their crop plans" ON tasks
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM crop_plans 
      WHERE crop_plans.id = tasks.crop_plan_id 
      AND crop_plans.user_id = auth.uid()
    )
  );

CREATE POLICY IF NOT EXISTS "Users can access their own chat sessions" ON chat_sessions
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can access messages from their sessions" ON chat_messages
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM chat_sessions 
      WHERE chat_sessions.id = chat_messages.session_id 
      AND chat_sessions.user_id = auth.uid()
    )
  );

CREATE POLICY IF NOT EXISTS "Community posts are publicly readable" ON community_posts
  FOR SELECT USING (true);

CREATE POLICY IF NOT EXISTS "Users can create their own posts" ON community_posts
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can update their own posts" ON community_posts
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Comments are publicly readable" ON post_comments
  FOR SELECT USING (true);

CREATE POLICY IF NOT EXISTS "Users can create comments" ON post_comments
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Products are publicly readable" ON products
  FOR SELECT USING (true);

CREATE POLICY IF NOT EXISTS "Users can access their own orders" ON orders
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can access items from their orders" ON order_items
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM orders 
      WHERE orders.id = order_items.order_id 
      AND orders.user_id = auth.uid()
    )
  );

CREATE POLICY IF NOT EXISTS "Achievements are publicly readable" ON achievements
  FOR SELECT USING (true);

CREATE POLICY IF NOT EXISTS "Subscription plans are publicly readable" ON subscription_plans
  FOR SELECT USING (true);

-- Insert sample achievements
INSERT INTO achievements (name, description, icon, category, points_reward, requirements, is_active)
VALUES 
  ('first_steps', 'Complete your first farming task', '🌱', 'farming', 25, '{"type": "task_count", "target_value": 1}', true),
  ('green_thumb', 'Successfully complete your first crop harvest', '🌿', 'farming', 200, '{"type": "harvest_count", "target_value": 1}', true),
  ('community_helper', 'Create 5 community posts', '🤝', 'community', 75, '{"type": "community_posts", "target_value": 5}', true)
ON CONFLICT (name) DO NOTHING;

-- Insert sample subscription plans
INSERT INTO subscription_plans (name, description, price, currency, billing_period, features, is_active)
VALUES 
  ('free', 'Basic farming features', 0, 'USD', 'monthly', '["basic_crop_planning", "weather_alerts", "community_access"]'::jsonb, true),
  ('premium', 'Advanced farming with AI', 19.99, 'USD', 'monthly', '["unlimited_ai", "advanced_analytics", "priority_support"]'::jsonb, true)
ON CONFLICT (name) DO NOTHING;

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$ language 'plpgsql';

-- Apply triggers
CREATE TRIGGER IF NOT EXISTS update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS update_user_profiles_updated_at BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS update_crop_plans_updated_at BEFORE UPDATE ON crop_plans
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER IF NOT EXISTS update_tasks_updated_at BEFORE UPDATE ON tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
`;

async function applySchema() {
  console.log('🚀 Applying core database schema...\n');

  try {
    // Split the SQL into smaller chunks to avoid timeout
    const sqlChunks = coreSchemaSQL.split('-- Create');

    for (let i = 0; i < sqlChunks.length; i++) {
      const chunk = i === 0 ? sqlChunks[i] : '-- Create' + sqlChunks[i];

      if (chunk.trim().length === 0) continue;

      console.log(`🔄 Applying chunk ${i + 1}/${sqlChunks.length}...`);

      const { error } = await supabase.rpc('exec', { sql: chunk });

      if (error) {
        console.error(`❌ Error in chunk ${i + 1}:`, error.message);
        // Continue with other chunks
      } else {
        console.log(`✅ Chunk ${i + 1} applied successfully`);
      }
    }

    console.log('\n🎉 Core schema application completed!');
    return true;
  } catch (error) {
    console.error('❌ Schema application failed:', error.message);
    return false;
  }
}

async function verifySchema() {
  console.log('\n🔍 Verifying schema...');

  const tables = [
    'users',
    'user_profiles',
    'crop_plans',
    'tasks',
    'achievements',
    'subscription_plans',
  ];
  let verified = 0;

  for (const table of tables) {
    try {
      const { data, error } = await supabase.from(table).select('*').limit(1);

      if (error && error.message.includes('does not exist')) {
        console.log(`❌ ${table} - Not found`);
      } else {
        console.log(`✅ ${table} - Verified`);
        verified++;
      }
    } catch (error) {
      console.log(`⚠️  ${table} - Check failed: ${error.message}`);
    }
  }

  console.log(`\n📊 Verification: ${verified}/${tables.length} tables verified`);
  return verified === tables.length;
}

async function main() {
  const schemaApplied = await applySchema();

  if (schemaApplied) {
    // Wait a moment for the schema to be applied
    console.log('\n⏳ Waiting for schema to be applied...');
    await new Promise((resolve) => setTimeout(resolve, 3000));

    const schemaVerified = await verifySchema();

    if (schemaVerified) {
      console.log('\n🎉 Database setup completed successfully!');
      console.log('✅ Production database is ready for use.');
    } else {
      console.log('\n⚠️  Schema verification had issues.');
      console.log('❌ Some tables may not be accessible yet.');
    }
  } else {
    console.log('\n❌ Schema application failed.');
  }

  process.exit(0); // Don't fail on verification issues
}

// Run the schema application
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Schema application failed:', error);
    process.exit(1);
  });
}
