/**
 * Accessibility Settings Component
 * Comprehensive accessibility controls for agricultural users
 */

import React from 'react';
import { View, Text, ScrollView, Switch, Pressable } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAccessibility } from '../../hooks/useAccessibility';
import { Button } from './Button';
import { colors } from '../../design-system';

interface AccessibilitySettingsProps {
    onClose?: () => void;
}

export const AccessibilitySettings: React.FC<AccessibilitySettingsProps> = ({ onClose }) => {
    const {
        config,
        isScreenReaderEnabled,
        isVoiceEnabled,
        isHighContrastEnabled,
        isLargeTextEnabled,
        fontScale,
        toggleVoiceMode,
        toggleHighContrast,
        toggleLargeText,
        setFontScale,
        announceToScreenReader,
        speakText,
        getAccessibilityProps,
        getScaledFontSize,
        getHighContrastColors,
    } = useAccessibility();

    const highContrastColors = getHighContrastColors();
    const textColor = highContrastColors?.foreground || colors.earth[900];
    const backgroundColor = highContrastColors?.background || colors.neutral.white;
    const primaryColor = highContrastColors?.primary || colors.primary[600];

    const handleVoiceToggle = async () => {
        await toggleVoiceMode();
        const message = !isVoiceEnabled ? 'Voice mode enabled' : 'Voice mode disabled';
        announceToScreenReader(message);
        if (!isVoiceEnabled) {
            await speakText(message);
        }
    };

    const handleHighContrastToggle = async () => {
        await toggleHighContrast();
    };

    const handleLargeTextToggle = async () => {
        await toggleLargeText();
    };

    const handleFontScaleChange = async (increment: boolean) => {
        const newScale = increment ? fontScale + 0.1 : fontScale - 0.1;
        await setFontScale(newScale);
    };

    const testVoice = async () => {
        await speakText('This is a test of the voice system. Voice is working correctly.');
        announceToScreenReader('Voice test completed');
    };

    const testScreenReader = () => {
        announceToScreenReader('Screen reader test: This message should be announced by your screen reader', 'high');
    };

    return (
        <ScrollView
            style={{ backgroundColor }}
            contentContainerStyle={{ padding: 16 }}
            {...getAccessibilityProps('Accessibility settings screen', 'Configure accessibility options for better app experience')}
        >
            <View className="mb-6">
                <Text
                    style={{
                        fontSize: getScaledFontSize(24),
                        fontWeight: 'bold',
                        color: textColor,
                        marginBottom: 8
                    }}
                    {...getAccessibilityProps('Accessibility Settings', 'Main heading for accessibility options')}
                >
                    Accessibility Settings
                </Text>
                <Text
                    style={{
                        fontSize: getScaledFontSize(16),
                        color: textColor,
                        opacity: 0.8
                    }}
                    {...getAccessibilityProps('Configure options to make the app easier to use', 'Description of accessibility settings')}
                >
                    Configure options to make the app easier to use
                </Text>
            </View>

            {/* Voice Settings */}
            <View className="mb-8 p-4 rounded-xl border-2" style={{ borderColor: primaryColor, backgroundColor: backgroundColor }}>
                <Text
                    style={{
                        fontSize: getScaledFontSize(18),
                        fontWeight: '600',
                        color: textColor,
                        marginBottom: 16
                    }}
                    {...getAccessibilityProps('Voice Settings', 'Section for voice-related accessibility options')}
                >
                    🗣️ Voice Settings
                </Text>

                {/* Voice Mode Toggle */}
                <View className="flex-row items-center justify-between mb-4 p-3 rounded-lg" style={{ backgroundColor: isHighContrastEnabled ? highContrastColors?.background : colors.earth[50] }}>
                    <View className="flex-1 mr-4">
                        <Text
                            style={{
                                fontSize: getScaledFontSize(16),
                                fontWeight: '500',
                                color: textColor
                            }}
                            {...getAccessibilityProps('Voice Mode', 'Toggle voice announcements and text-to-speech')}
                        >
                            Voice Mode
                        </Text>
                        <Text
                            style={{
                                fontSize: getScaledFontSize(14),
                                color: textColor,
                                opacity: 0.7
                            }}
                            {...getAccessibilityProps('Enable voice announcements and text-to-speech', 'Description of voice mode feature')}
                        >
                            Enable voice announcements and text-to-speech
                        </Text>
                    </View>
                    <Switch
                        value={isVoiceEnabled}
                        onValueChange={handleVoiceToggle}
                        trackColor={{ false: colors.earth[300], true: primaryColor }}
                        thumbColor={isVoiceEnabled ? colors.neutral.white : colors.earth[500]}
                        {...getAccessibilityProps(
                            isVoiceEnabled ? 'Voice mode enabled' : 'Voice mode disabled',
                            'Toggle to enable or disable voice announcements'
                        )}
                    />
                </View>

                {/* Voice Test Button */}
                <Button
                    title="Test Voice"
                    onPress={testVoice}
                    variant="outline"
                    size="medium"
                    accessibilityLabel="Test voice system"
                    accessibilityHint="Tap to hear a test voice announcement"
                    className="mb-2"
                />
            </View>

            {/* Visual Settings */}
            <View className="mb-8 p-4 rounded-xl border-2" style={{ borderColor: primaryColor, backgroundColor: backgroundColor }}>
                <Text
                    style={{
                        fontSize: getScaledFontSize(18),
                        fontWeight: '600',
                        color: textColor,
                        marginBottom: 16
                    }}
                    {...getAccessibilityProps('Visual Settings', 'Section for visual accessibility options')}
                >
                    👁️ Visual Settings
                </Text>

                {/* High Contrast Toggle */}
                <View className="flex-row items-center justify-between mb-4 p-3 rounded-lg" style={{ backgroundColor: isHighContrastEnabled ? highContrastColors?.background : colors.earth[50] }}>
                    <View className="flex-1 mr-4">
                        <Text
                            style={{
                                fontSize: getScaledFontSize(16),
                                fontWeight: '500',
                                color: textColor
                            }}
                            {...getAccessibilityProps('High Contrast Mode', 'Toggle high contrast colors for better visibility')}
                        >
                            High Contrast Mode
                        </Text>
                        <Text
                            style={{
                                fontSize: getScaledFontSize(14),
                                color: textColor,
                                opacity: 0.7
                            }}
                            {...getAccessibilityProps('Better visibility in bright sunlight', 'Description of high contrast mode')}
                        >
                            Better visibility in bright sunlight
                        </Text>
                    </View>
                    <Switch
                        value={isHighContrastEnabled}
                        onValueChange={handleHighContrastToggle}
                        trackColor={{ false: colors.earth[300], true: primaryColor }}
                        thumbColor={isHighContrastEnabled ? colors.neutral.white : colors.earth[500]}
                        {...getAccessibilityProps(
                            isHighContrastEnabled ? 'High contrast enabled' : 'High contrast disabled',
                            'Toggle to enable or disable high contrast colors'
                        )}
                    />
                </View>

                {/* Large Text Toggle */}
                <View className="flex-row items-center justify-between mb-4 p-3 rounded-lg" style={{ backgroundColor: isHighContrastEnabled ? highContrastColors?.background : colors.earth[50] }}>
                    <View className="flex-1 mr-4">
                        <Text
                            style={{
                                fontSize: getScaledFontSize(16),
                                fontWeight: '500',
                                color: textColor
                            }}
                            {...getAccessibilityProps('Large Text Mode', 'Toggle larger text for better readability')}
                        >
                            Large Text Mode
                        </Text>
                        <Text
                            style={{
                                fontSize: getScaledFontSize(14),
                                color: textColor,
                                opacity: 0.7
                            }}
                            {...getAccessibilityProps('Increase text size for better readability', 'Description of large text mode')}
                        >
                            Increase text size for better readability
                        </Text>
                    </View>
                    <Switch
                        value={isLargeTextEnabled}
                        onValueChange={handleLargeTextToggle}
                        trackColor={{ false: colors.earth[300], true: primaryColor }}
                        thumbColor={isLargeTextEnabled ? colors.neutral.white : colors.earth[500]}
                        {...getAccessibilityProps(
                            isLargeTextEnabled ? 'Large text enabled' : 'Large text disabled',
                            'Toggle to enable or disable large text'
                        )}
                    />
                </View>

                {/* Font Scale Controls */}
                <View className="mb-4">
                    <Text
                        style={{
                            fontSize: getScaledFontSize(16),
                            fontWeight: '500',
                            color: textColor,
                            marginBottom: 8
                        }}
                        {...getAccessibilityProps('Font Size', 'Adjust text size with plus and minus buttons')}
                    >
                        Font Size: {Math.round(fontScale * 100)}%
                    </Text>
                    <View className="flex-row items-center justify-center gap-4">
                        <Pressable
                            onPress={() => handleFontScaleChange(false)}
                            className="w-12 h-12 items-center justify-center rounded-full border-2"
                            style={{ borderColor: primaryColor, backgroundColor: backgroundColor }}
                            {...getAccessibilityProps('Decrease font size', 'Tap to make text smaller')}
                        >
                            <Ionicons name="remove" size={24} color={primaryColor} />
                        </Pressable>

                        <Text
                            style={{
                                fontSize: getScaledFontSize(18),
                                color: textColor,
                                minWidth: 100,
                                textAlign: 'center'
                            }}
                            {...getAccessibilityProps(`Sample text at ${Math.round(fontScale * 100)}% size`, 'Example of current text size')}
                        >
                            Sample Text
                        </Text>

                        <Pressable
                            onPress={() => handleFontScaleChange(true)}
                            className="w-12 h-12 items-center justify-center rounded-full border-2"
                            style={{ borderColor: primaryColor, backgroundColor: backgroundColor }}
                            {...getAccessibilityProps('Increase font size', 'Tap to make text larger')}
                        >
                            <Ionicons name="add" size={24} color={primaryColor} />
                        </Pressable>
                    </View>
                </View>
            </View>

            {/* Screen Reader Settings */}
            <View className="mb-8 p-4 rounded-xl border-2" style={{ borderColor: primaryColor, backgroundColor: backgroundColor }}>
                <Text
                    style={{
                        fontSize: getScaledFontSize(18),
                        fontWeight: '600',
                        color: textColor,
                        marginBottom: 16
                    }}
                    {...getAccessibilityProps('Screen Reader Settings', 'Section for screen reader accessibility options')}
                >
                    📱 Screen Reader Settings
                </Text>

                <View className="mb-4 p-3 rounded-lg" style={{ backgroundColor: isHighContrastEnabled ? highContrastColors?.background : colors.earth[50] }}>
                    <Text
                        style={{
                            fontSize: getScaledFontSize(16),
                            fontWeight: '500',
                            color: textColor,
                            marginBottom: 4
                        }}
                        {...getAccessibilityProps('Screen Reader Status', 'Current status of screen reader')}
                    >
                        Screen Reader Status
                    </Text>
                    <Text
                        style={{
                            fontSize: getScaledFontSize(14),
                            color: isScreenReaderEnabled ? colors.status.success : colors.status.warning
                        }}
                        {...getAccessibilityProps(
                            isScreenReaderEnabled ? 'Screen reader is enabled' : 'Screen reader is not detected',
                            'Current screen reader detection status'
                        )}
                    >
                        {isScreenReaderEnabled ? '✅ Enabled' : '⚠️ Not Detected'}
                    </Text>
                </View>

                <Button
                    title="Test Screen Reader"
                    onPress={testScreenReader}
                    variant="outline"
                    size="medium"
                    accessibilityLabel="Test screen reader"
                    accessibilityHint="Tap to test screen reader announcement"
                />
            </View>

            {/* Keyboard Navigation Info */}
            <View className="mb-8 p-4 rounded-xl border-2" style={{ borderColor: primaryColor, backgroundColor: backgroundColor }}>
                <Text
                    style={{
                        fontSize: getScaledFontSize(18),
                        fontWeight: '600',
                        color: textColor,
                        marginBottom: 16
                    }}
                    {...getAccessibilityProps('Keyboard Navigation', 'Information about keyboard navigation support')}
                >
                    ⌨️ Keyboard Navigation
                </Text>

                <Text
                    style={{
                        fontSize: getScaledFontSize(14),
                        color: textColor,
                        lineHeight: getScaledFontSize(20)
                    }}
                    {...getAccessibilityProps('Keyboard navigation instructions', 'How to navigate the app using keyboard')}
                >
                    • Use Tab to move between elements{'\n'}
                    • Use Enter or Space to activate buttons{'\n'}
                    • Use arrow keys to navigate lists{'\n'}
                    • Use Escape to close dialogs{'\n'}
                    • Voice commands work throughout the app
                </Text>
            </View>

            {/* Close Button */}
            {onClose && (
                <Button
                    title="Close Settings"
                    onPress={onClose}
                    variant="primary"
                    size="large"
                    fullWidth
                    accessibilityLabel="Close accessibility settings"
                    accessibilityHint="Return to the previous screen"
                />
            )}
        </ScrollView>
    );
};