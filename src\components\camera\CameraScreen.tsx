import React, { useState } from 'react';
import { View } from 'react-native';
import { CaptureMode, ImageAnalysisRequest, ImageAnalysisResult } from '../../types/camera';
import { CameraService } from '../../services/camera';
import { CameraInterface } from './CameraInterface';
import { AnalysisLoadingScreen } from './AnalysisLoadingScreen';
import { AnalysisResultsScreen } from './AnalysisResultsScreen';

type CameraScreenState = 'camera' | 'analyzing' | 'results';

interface CameraScreenProps {
    initialMode?: CaptureMode;
    onClose: () => void;
    onSaveResult?: (result: ImageAnalysisResult) => void;
    onBuyTreatment?: (recommendations: any[]) => void;
}

export function CameraScreen({
    initialMode = 'plant',
    onClose,
    onSaveResult,
    onBuyTreatment
}: CameraScreenProps) {
    const [screenState, setScreenState] = useState<CameraScreenState>('camera');
    const [currentMode, setCurrentMode] = useState<CaptureMode>(initialMode);
    const [capturedImageUri, setCapturedImageUri] = useState<string>('');
    const [analysisResult, setAnalysisResult] = useState<ImageAnalysisResult | null>(null);

    const handleCapture = async (imageUri: string) => {
        setCapturedImageUri(imageUri);
        setScreenState('analyzing');

        try {
            // Create analysis request
            const request: ImageAnalysisRequest = {
                imageUri,
                mode: currentMode,
                // Add location if available
            };

            // Perform analysis
            const result = await CameraService.analyzeImage(request);
            setAnalysisResult(result);
            setScreenState('results');
        } catch (error) {
            console.error('Analysis failed:', error);
            // Return to camera on error
            setScreenState('camera');
        }
    };

    const handleAnalysisCancel = () => {
        setScreenState('camera');
        setCapturedImageUri('');
    };

    const handleAnalysisComplete = () => {
        // This is called when loading animation completes
        // The actual transition to results happens in handleCapture
    };

    const handleSaveResult = (result: ImageAnalysisResult) => {
        if (onSaveResult) {
            onSaveResult(result);
        }
        // Could also save to local storage or database here
    };

    const handleBuyTreatment = (recommendations: any[]) => {
        if (onBuyTreatment) {
            onBuyTreatment(recommendations);
        }
        // Navigate to store with recommended products
    };

    const handleRetake = () => {
        setScreenState('camera');
        setCapturedImageUri('');
        setAnalysisResult(null);
    };

    const handleResultsClose = () => {
        onClose();
    };

    const renderCurrentScreen = () => {
        switch (screenState) {
            case 'camera':
                return (
                    <CameraInterface
                        mode={currentMode}
                        onCapture={handleCapture}
                        onModeChange={setCurrentMode}
                        onClose={onClose}
                    />
                );

            case 'analyzing':
                return (
                    <AnalysisLoadingScreen
                        imageUri={capturedImageUri}
                        mode={currentMode}
                        onCancel={handleAnalysisCancel}
                        onComplete={handleAnalysisComplete}
                    />
                );

            case 'results':
                return analysisResult ? (
                    <AnalysisResultsScreen
                        result={analysisResult}
                        onClose={handleResultsClose}
                        onSave={handleSaveResult}
                        onBuyTreatment={handleBuyTreatment}
                        onRetake={handleRetake}
                    />
                ) : null;

            default:
                return null;
        }
    };

    return (
        <View className="flex-1">
            {renderCurrentScreen()}
        </View>
    );
}