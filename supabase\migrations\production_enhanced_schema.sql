-- Enhanced Production Database Schema for AI Farming Assistant
-- This migration enhances the existing schema with production-ready optimizations

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- For text search
CREATE EXTENSION IF NOT EXISTS "btree_gin"; -- For composite indexes

-- ==========================================
-- ENHANCED CORE TABLES WITH PRODUCTION OPTIMIZATIONS
-- ==========================================

-- Enhanced users table with additional production fields
ALTER TABLE users ADD COLUMN IF NOT EXISTS avatar_url VARCHAR;
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW();
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;
ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT false;
ALTER TABLE users ADD COLUMN IF NOT EXISTS phone_verified BOOLEAN DEFAULT false;
ALTER TABLE users ADD COLUMN IF NOT EXISTS timezone VARCHAR DEFAULT 'UTC';
ALTER TABLE users ADD COLUMN IF NOT EXISTS language_preference VARCHAR DEFAULT 'ar';

-- Enhanced user_profiles table with production fields
ALTER TABLE user_profiles ADD COLUMN IF NOT EXISTS farm_size_hectares DECIMAL(10,2);
ALTER TABLE user_profiles ADD COLUMN IF NOT EXISTS notification_preferences JSONB DEFAULT '{}';
ALTER TABLE user_profiles ADD COLUMN IF NOT EXISTS privacy_settings JSONB DEFAULT '{}';
ALTER TABLE user_profiles ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
ALTER TABLE user_profiles ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Convert POINT to PostGIS GEOGRAPHY for better location handling
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'user_profiles' AND column_name = 'farm_location' 
               AND data_type = 'point') THEN
        ALTER TABLE user_profiles ALTER COLUMN farm_location TYPE GEOGRAPHY(POINT, 4326) 
        USING ST_SetSRID(ST_MakePoint(ST_X(farm_location), ST_Y(farm_location)), 4326);
    END IF;
END $$;

-- Enhanced crop_plans table
ALTER TABLE crop_plans ADD COLUMN IF NOT EXISTS variety VARCHAR;
ALTER TABLE crop_plans ADD COLUMN IF NOT EXISTS expected_harvest_date DATE;
ALTER TABLE crop_plans ADD COLUMN IF NOT EXISTS actual_harvest_date DATE;
ALTER TABLE crop_plans ADD COLUMN IF NOT EXISTS area_hectares DECIMAL(10,2);
ALTER TABLE crop_plans ADD COLUMN IF NOT EXISTS weather_alerts_enabled BOOLEAN DEFAULT true;
ALTER TABLE crop_plans ADD COLUMN IF NOT EXISTS ai_recommendations_enabled BOOLEAN DEFAULT true;
ALTER TABLE crop_plans ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Convert crop_plans location to PostGIS GEOGRAPHY
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'crop_plans' AND column_name = 'location' 
               AND data_type = 'point') THEN
        ALTER TABLE crop_plans ALTER COLUMN location TYPE GEOGRAPHY(POINT, 4326) 
        USING ST_SetSRID(ST_MakePoint(ST_X(location), ST_Y(location)), 4326);
    END IF;
END $$;

-- Enhanced tasks table with production features
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS priority VARCHAR DEFAULT 'medium' 
    CHECK (priority IN ('low', 'medium', 'high', 'urgent'));
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS weather_dependent BOOLEAN DEFAULT false;
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS estimated_duration_minutes INTEGER;
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS actual_duration_minutes INTEGER;
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS completion_notes TEXT;
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS photo_evidence_urls TEXT[];
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Update existing completed boolean to completed_at timestamp
UPDATE tasks SET completed_at = created_at WHERE completed = true AND completed_at IS NULL;

-- ==========================================
-- IMAGE ANALYSIS AND AI FEATURES
-- ==========================================

-- Create image_analyses table for AI analysis results
CREATE TABLE IF NOT EXISTS image_analyses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    crop_plan_id UUID REFERENCES crop_plans(id) ON DELETE SET NULL,
    image_url VARCHAR NOT NULL,
    analysis_type VARCHAR NOT NULL CHECK (analysis_type IN ('plant_health', 'soil_analysis', 'pest_detection', 'nutrient_deficiency')),
    ai_model_used VARCHAR NOT NULL,
    confidence_score DECIMAL(5,2),
    detected_issues JSONB DEFAULT '[]',
    recommendations JSONB DEFAULT '[]',
    severity_level VARCHAR CHECK (severity_level IN ('low', 'medium', 'high', 'critical')),
    follow_up_required BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==========================================
-- ENHANCED PERFORMANCE INDEXES
-- ==========================================

-- Core table indexes with performance optimizations
CREATE INDEX IF NOT EXISTS idx_users_email_active ON users(email) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_users_phone_verified ON users(phone) WHERE phone_verified = true;
CREATE INDEX IF NOT EXISTS idx_users_last_seen ON users(last_seen DESC);

-- User profiles indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_location ON user_profiles USING GIST(farm_location);
CREATE INDEX IF NOT EXISTS idx_user_profiles_crop_types ON user_profiles USING GIN(crop_types);
CREATE INDEX IF NOT EXISTS idx_user_profiles_experience ON user_profiles(experience_level);

-- Crop plans indexes
CREATE INDEX IF NOT EXISTS idx_crop_plans_user_status ON crop_plans(user_id, status);
CREATE INDEX IF NOT EXISTS idx_crop_plans_location ON crop_plans USING GIST(location);
CREATE INDEX IF NOT EXISTS idx_crop_plans_planting_date ON crop_plans(planting_date);
CREATE INDEX IF NOT EXISTS idx_crop_plans_harvest_date ON crop_plans(expected_harvest_date);
CREATE INDEX IF NOT EXISTS idx_crop_plans_crop_type ON crop_plans(crop_type);

-- Tasks indexes
CREATE INDEX IF NOT EXISTS idx_tasks_crop_plan_due ON tasks(crop_plan_id, due_date);
CREATE INDEX IF NOT EXISTS idx_tasks_user_pending ON tasks(crop_plan_id) 
    WHERE completed_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_tasks_priority_due ON tasks(priority, due_date) 
    WHERE completed_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_tasks_weather_dependent ON tasks(weather_dependent) 
    WHERE weather_dependent = true AND completed_at IS NULL;

-- Image analyses indexes
CREATE INDEX IF NOT EXISTS idx_image_analyses_user_date ON image_analyses(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_image_analyses_crop_plan ON image_analyses(crop_plan_id);
CREATE INDEX IF NOT EXISTS idx_image_analyses_type ON image_analyses(analysis_type);
CREATE INDEX IF NOT EXISTS idx_image_analyses_severity ON image_analyses(severity_level);

-- Community posts indexes with location optimization
CREATE INDEX IF NOT EXISTS idx_community_posts_location_radius ON community_posts 
    USING GIST(location) WHERE location IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_community_posts_user_created ON community_posts(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_community_posts_likes_created ON community_posts(likes_count DESC, created_at DESC);

-- ==========================================
-- ENHANCED FUNCTIONS AND TRIGGERS
-- ==========================================

-- Enhanced updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$ language 'plpgsql';

-- Apply updated_at triggers to enhanced tables
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_crop_plans_updated_at ON crop_plans;
CREATE TRIGGER update_crop_plans_updated_at BEFORE UPDATE ON crop_plans
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_tasks_updated_at ON tasks;
CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to calculate distance between two geographic points
CREATE OR REPLACE FUNCTION calculate_distance_km(
    lat1 DOUBLE PRECISION,
    lon1 DOUBLE PRECISION,
    lat2 DOUBLE PRECISION,
    lon2 DOUBLE PRECISION
)
RETURNS DOUBLE PRECISION AS $
BEGIN
    RETURN ST_Distance(
        ST_SetSRID(ST_MakePoint(lon1, lat1), 4326)::geography,
        ST_SetSRID(ST_MakePoint(lon2, lat2), 4326)::geography
    ) / 1000.0; -- Convert meters to kilometers
END;
$ LANGUAGE plpgsql IMMUTABLE;

-- Function to get nearby community posts
CREATE OR REPLACE FUNCTION get_nearby_posts(
    user_lat DOUBLE PRECISION,
    user_lon DOUBLE PRECISION,
    radius_km INTEGER DEFAULT 50,
    limit_count INTEGER DEFAULT 20
)
RETURNS TABLE (
    id UUID,
    title VARCHAR,
    content TEXT,
    user_id UUID,
    distance_km DOUBLE PRECISION,
    created_at TIMESTAMP
) AS $
BEGIN
    RETURN QUERY
    SELECT 
        cp.id,
        cp.title,
        cp.content,
        cp.user_id,
        ST_Distance(
            cp.location,
            ST_SetSRID(ST_MakePoint(user_lon, user_lat), 4326)::geography
        ) / 1000.0 as distance_km,
        cp.created_at
    FROM community_posts cp
    WHERE cp.location IS NOT NULL
    AND ST_DWithin(
        cp.location,
        ST_SetSRID(ST_MakePoint(user_lon, user_lat), 4326)::geography,
        radius_km * 1000
    )
    ORDER BY distance_km ASC, cp.created_at DESC
    LIMIT limit_count;
END;
$ LANGUAGE plpgsql;

-- Function to update user last seen timestamp
CREATE OR REPLACE FUNCTION update_user_last_seen(user_uuid UUID)
RETURNS VOID AS $
BEGIN
    UPDATE users 
    SET last_seen = NOW() 
    WHERE id = user_uuid;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- ==========================================
-- ENHANCED ROW LEVEL SECURITY POLICIES
-- ==========================================

-- Enhanced RLS policies for image analyses
ALTER TABLE image_analyses ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own image analyses" ON image_analyses
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create image analyses" ON image_analyses
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Enhanced policies for tasks with priority access
DROP POLICY IF EXISTS "Users can access tasks for their crop plans" ON tasks;
CREATE POLICY "Users can access tasks for their crop plans" ON tasks
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM crop_plans 
            WHERE crop_plans.id = tasks.crop_plan_id 
            AND crop_plans.user_id = auth.uid()
        )
    );

-- ==========================================
-- PRODUCTION DATA VALIDATION
-- ==========================================

-- Add constraints for data integrity
ALTER TABLE users ADD CONSTRAINT users_email_format 
    CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE user_profiles ADD CONSTRAINT user_profiles_farm_size_positive 
    CHECK (farm_size_hectares IS NULL OR farm_size_hectares > 0);

ALTER TABLE crop_plans ADD CONSTRAINT crop_plans_area_positive 
    CHECK (area_hectares IS NULL OR area_hectares > 0);

ALTER TABLE crop_plans ADD CONSTRAINT crop_plans_dates_logical 
    CHECK (expected_harvest_date IS NULL OR planting_date IS NULL OR expected_harvest_date >= planting_date);

ALTER TABLE tasks ADD CONSTRAINT tasks_duration_positive 
    CHECK (estimated_duration_minutes IS NULL OR estimated_duration_minutes > 0);

ALTER TABLE tasks ADD CONSTRAINT tasks_actual_duration_positive 
    CHECK (actual_duration_minutes IS NULL OR actual_duration_minutes > 0);

-- ==========================================
-- PERFORMANCE MONITORING TABLES
-- ==========================================

-- Create performance monitoring table
CREATE TABLE IF NOT EXISTS performance_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    operation VARCHAR NOT NULL,
    duration_ms INTEGER NOT NULL,
    status VARCHAR NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_performance_logs_operation ON performance_logs(operation);
CREATE INDEX IF NOT EXISTS idx_performance_logs_created_at ON performance_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_performance_logs_duration ON performance_logs(duration_ms DESC);

-- Create error logs table
CREATE TABLE IF NOT EXISTS error_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    error_message TEXT NOT NULL,
    error_stack TEXT,
    context VARCHAR NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    app_version VARCHAR,
    device_info JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_error_logs_context ON error_logs(context);
CREATE INDEX IF NOT EXISTS idx_error_logs_created_at ON error_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_error_logs_user_id ON error_logs(user_id);

-- Enable RLS for monitoring tables
ALTER TABLE performance_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE error_logs ENABLE ROW LEVEL SECURITY;

-- Allow system to insert logs
CREATE POLICY "System can insert performance logs" ON performance_logs
    FOR INSERT WITH CHECK (true);

CREATE POLICY "System can insert error logs" ON error_logs
    FOR INSERT WITH CHECK (true);

-- Users can view their own logs
CREATE POLICY "Users can view own performance logs" ON performance_logs
    FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can view own error logs" ON error_logs
    FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);

-- ==========================================
-- CLEANUP AND MAINTENANCE FUNCTIONS
-- ==========================================

-- Function to clean up old logs
CREATE OR REPLACE FUNCTION cleanup_old_logs()
RETURNS VOID AS $
BEGIN
    -- Delete performance logs older than 30 days
    DELETE FROM performance_logs 
    WHERE created_at < NOW() - INTERVAL '30 days';
    
    -- Delete error logs older than 90 days
    DELETE FROM error_logs 
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    -- Delete old image analyses older than 1 year
    DELETE FROM image_analyses 
    WHERE created_at < NOW() - INTERVAL '1 year';
    
    RAISE NOTICE 'Cleanup completed successfully';
END;
$ LANGUAGE plpgsql;

-- ==========================================
-- SAMPLE DATA FOR TESTING (OPTIONAL)
-- ==========================================

-- Insert sample achievements if they don't exist
INSERT INTO achievements (id, name, description, icon, category, points_reward, requirements, is_active)
VALUES 
    ('green_thumb', 'Green Thumb', 'Successfully complete your first crop harvest', '🌿', 'farming', 200, '{"type": "harvest_count", "target_value": 1}', true),
    ('tech_savvy', 'Tech Savvy Farmer', 'Use AI image analysis 5 times', '📱', 'learning', 100, '{"type": "ai_analysis_count", "target_value": 5}', true),
    ('community_leader', 'Community Leader', 'Get 50 likes on your community posts', '👑', 'community', 300, '{"type": "total_likes", "target_value": 50}', true)
ON CONFLICT (name) DO NOTHING;

-- ==========================================
-- FINAL VERIFICATION
-- ==========================================

-- Create a function to verify the database setup
CREATE OR REPLACE FUNCTION verify_production_setup()
RETURNS TABLE (
    check_name TEXT,
    status TEXT,
    details TEXT
) AS $
DECLARE
    table_count INTEGER;
    index_count INTEGER;
    policy_count INTEGER;
BEGIN
    -- Check table count
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables 
    WHERE table_schema = 'public';
    
    RETURN NEXT ('Table Count', 
                CASE WHEN table_count >= 20 THEN 'PASS' ELSE 'FAIL' END,
                table_count::TEXT || ' tables found');
    
    -- Check index count
    SELECT COUNT(*) INTO index_count
    FROM pg_indexes 
    WHERE schemaname = 'public';
    
    RETURN NEXT ('Index Count', 
                CASE WHEN index_count >= 30 THEN 'PASS' ELSE 'FAIL' END,
                index_count::TEXT || ' indexes found');
    
    -- Check RLS policies
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies 
    WHERE schemaname = 'public';
    
    RETURN NEXT ('RLS Policies', 
                CASE WHEN policy_count >= 20 THEN 'PASS' ELSE 'FAIL' END,
                policy_count::TEXT || ' policies found');
    
    -- Check extensions
    RETURN NEXT ('PostGIS Extension', 
                CASE WHEN EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'postgis') 
                     THEN 'PASS' ELSE 'FAIL' END,
                'Required for geographic features');
    
    RETURN NEXT ('UUID Extension', 
                CASE WHEN EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'uuid-ossp') 
                     THEN 'PASS' ELSE 'FAIL' END,
                'Required for UUID generation');
                
    RETURN;
END;
$ LANGUAGE plpgsql;

-- Log the completion of this migration
INSERT INTO performance_logs (operation, duration_ms, status, metadata)
VALUES ('production_schema_migration', 0, 'completed', '{"version": "1.0", "timestamp": "' || NOW() || '"}');

RAISE NOTICE 'Production database schema migration completed successfully!';
RAISE NOTICE 'Run SELECT * FROM verify_production_setup(); to verify the setup.';