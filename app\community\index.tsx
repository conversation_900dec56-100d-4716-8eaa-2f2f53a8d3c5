import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    RefreshControl,
    Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../../src/design-system';
import { CommunityPost } from '../../src/components/community/CommunityPost';
import { VoiceButton } from '../../src/components/ui/VoiceButton';
import { useCommunityStore } from '../../src/stores/community';
import { useVoiceStore } from '../../src/stores/voice';

export default function CommunityFeedScreen() {
    const [refreshing, setRefreshing] = useState(false);
    const [selectedFilter, setSelectedFilter] = useState<'all' | 'local' | 'following'>('all');

    const {
        posts,
        loading,
        fetchPosts,
        likePost,
        sharePost
    } = useCommunityStore();

    const { speak, isVoiceEnabled } = useVoiceStore();

    useEffect(() => {
        fetchPosts(selectedFilter);
    }, [selectedFilter]);

    const handleRefresh = async () => {
        setRefreshing(true);
        await fetchPosts(selectedFilter);
        setRefreshing(false);
    };

    const handleCreatePost = () => {
        if (isVoiceEnabled) {
            speak('Opening post creation screen');
        }
        router.push('/community/create-post');
    };

    const handleFilterChange = (filter: 'all' | 'local' | 'following') => {
        setSelectedFilter(filter);
        if (isVoiceEnabled) {
            speak(`Filtering posts by ${filter}`);
        }
    };

    const handlePostPress = (postId: string) => {
        if (isVoiceEnabled) {
            speak('Opening post details');
        }
        router.push(`/community/post/${postId}`);
    };

    const handleLike = async (postId: string) => {
        try {
            await likePost(postId);
            if (isVoiceEnabled) {
                speak('Post liked');
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to like post');
        }
    };

    const handleShare = async (postId: string) => {
        try {
            await sharePost(postId);
            if (isVoiceEnabled) {
                speak('Post shared');
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to share post');
        }
    };

    const handleVoiceCommand = async () => {
        try {
            if (isVoiceEnabled) {
                speak('Say create post to make a new post, or say refresh to update the feed');
                // Voice command handling would be implemented here
            }
        } catch (error) {
            Alert.alert('Voice Error', 'Failed to process voice command');
        }
    };

    return (
        <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary[50] }}>
            {/* Header */}
            <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingHorizontal: 16,
                paddingVertical: 12,
                backgroundColor: 'white',
                borderBottomWidth: 1,
                borderBottomColor: colors.primary[100],
            }}>
                <Text style={{
                    fontSize: 24,
                    fontWeight: 'bold',
                    color: colors.primary[900],
                }}>
                    Community
                </Text>

                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 12 }}>
                    <TouchableOpacity
                        onPress={() => router.push('/community/events')}
                        accessibilityLabel="View community events"
                    >
                        <Ionicons name="calendar-outline" size={24} color={colors.primary[500]} />
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => router.push('/community/messages')}
                        accessibilityLabel="View messages"
                    >
                        <Ionicons name="chatbubbles-outline" size={24} color={colors.primary[500]} />
                    </TouchableOpacity>
                    <VoiceButton onPress={handleVoiceCommand} />
                    <TouchableOpacity
                        onPress={handleCreatePost}
                        style={{
                            backgroundColor: colors.primary[500],
                            paddingHorizontal: 16,
                            paddingVertical: 8,
                            borderRadius: 20,
                            flexDirection: 'row',
                            alignItems: 'center',
                            gap: 6,
                        }}
                        accessibilityLabel="Create new post"
                    >
                        <Ionicons name="add" size={20} color="white" />
                        <Text style={{ color: 'white', fontWeight: '600' }}>Post</Text>
                    </TouchableOpacity>
                </View>
            </View>

            {/* Filter Tabs */}
            <View style={{
                flexDirection: 'row',
                backgroundColor: 'white',
                paddingHorizontal: 16,
                paddingVertical: 8,
                borderBottomWidth: 1,
                borderBottomColor: colors.primary[100],
            }}>
                {[
                    { key: 'all', label: 'All Posts' },
                    { key: 'local', label: 'Local' },
                    { key: 'following', label: 'Following' },
                ].map((filter) => (
                    <TouchableOpacity
                        key={filter.key}
                        onPress={() => handleFilterChange(filter.key as any)}
                        style={{
                            flex: 1,
                            paddingVertical: 8,
                            alignItems: 'center',
                            borderBottomWidth: 2,
                            borderBottomColor: selectedFilter === filter.key
                                ? colors.primary[500]
                                : 'transparent',
                        }}
                        accessibilityLabel={`Filter by ${filter.label}`}
                    >
                        <Text style={{
                            color: selectedFilter === filter.key
                                ? colors.primary[500]
                                : colors.earth[500],
                            fontWeight: selectedFilter === filter.key ? '600' : '400',
                        }}>
                            {filter.label}
                        </Text>
                    </TouchableOpacity>
                ))}
            </View>

            {/* Posts Feed */}
            <ScrollView
                style={{ flex: 1 }}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        colors={[colors.primary[500]]}
                    />
                }
                showsVerticalScrollIndicator={false}
            >
                {loading && posts.length === 0 ? (
                    <View style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        paddingVertical: 40,
                    }}>
                        <Text style={{ color: colors.earth[500] }}>Loading posts...</Text>
                    </View>
                ) : posts.length === 0 ? (
                    <View style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        paddingVertical: 40,
                        paddingHorizontal: 20,
                    }}>
                        <Ionicons name="people-outline" size={64} color={colors.earth[300]} />
                        <Text style={{
                            fontSize: 18,
                            fontWeight: '600',
                            color: colors.earth[600],
                            marginTop: 16,
                            textAlign: 'center',
                        }}>
                            No posts yet
                        </Text>
                        <Text style={{
                            fontSize: 14,
                            color: colors.earth[500],
                            marginTop: 8,
                            textAlign: 'center',
                        }}>
                            Be the first to share your farming experience!
                        </Text>
                        <TouchableOpacity
                            onPress={handleCreatePost}
                            style={{
                                backgroundColor: colors.primary[500],
                                paddingHorizontal: 24,
                                paddingVertical: 12,
                                borderRadius: 24,
                                marginTop: 20,
                            }}
                        >
                            <Text style={{ color: 'white', fontWeight: '600' }}>
                                Create First Post
                            </Text>
                        </TouchableOpacity>
                    </View>
                ) : (
                    <View style={{ paddingBottom: 20 }}>
                        {posts.map((post) => (
                            <CommunityPost
                                key={post.id}
                                post={post}
                                onPress={() => handlePostPress(post.id)}
                                onLike={() => handleLike(post.id)}
                                onShare={() => handleShare(post.id)}
                                onComment={() => handlePostPress(post.id)}
                            />
                        ))}
                    </View>
                )}
            </ScrollView>
        </SafeAreaView>
    );
}