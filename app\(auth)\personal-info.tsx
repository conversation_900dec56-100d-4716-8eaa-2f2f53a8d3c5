import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Button } from '../../src/components/ui/Button';
import { Input } from '../../src/components/ui/Input';
import { ProgressIndicator } from '../../src/components/ui/ProgressIndicator';
import { AppProvider, useApp } from '../../src/contexts/AppContext';
import { useFormValidation, ValidationRules } from '../../src/hooks/useFormValidation';

const PersonalInfoContent: React.FC = () => {
    const { t, isRTL, isVoiceEnabled, speak } = useApp();
    const { method } = useLocalSearchParams<{ method?: string }>();
    const [isVoiceRecording, setIsVoiceRecording] = useState<string | null>(null);

    // Form validation rules
    const validationRules: ValidationRules = {
        firstName: {
            required: true,
            minLength: 2,
            maxLength: 50,
        },
        lastName: {
            required: true,
            minLength: 2,
            maxLength: 50,
        },
        phone: {
            required: method === 'phone',
            pattern: /^[\+]?[1-9][\d]{0,15}$/,
        },
        email: {
            required: method === 'email',
            pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        },
    };

    const { formState, getFieldProps, validateForm, isValid } = useFormValidation(
        {
            firstName: '',
            lastName: '',
            phone: '',
            email: '',
        },
        validationRules
    );

    useEffect(() => {
        if (isVoiceEnabled) {
            speak(t('personalInfo.title') + '. ' + t('personalInfo.subtitle'));
        }
    }, [isVoiceEnabled]);

    const handleVoiceInput = async (fieldName: string) => {
        if (isVoiceRecording === fieldName) {
            // Stop recording
            setIsVoiceRecording(null);
            if (isVoiceEnabled) {
                speak('Voice input stopped');
            }
            return;
        }

        // Start recording
        setIsVoiceRecording(fieldName);
        if (isVoiceEnabled) {
            speak(`Starting voice input for ${fieldName}`);
        }

        // Simulate voice input (in real implementation, this would use speech recognition)
        setTimeout(() => {
            setIsVoiceRecording(null);
            // For demo purposes, we'll show an alert
            Alert.alert(
                'Voice Input',
                'Voice recognition would be implemented here. For now, please type manually.',
                [{ text: 'OK' }]
            );
        }, 2000);
    };

    const handleNext = () => {
        if (validateForm()) {
            if (isVoiceEnabled) {
                speak(t('farmSetup.title'));
            }

            // In a real app, you would save the form data here
            const formData = {
                firstName: formState.firstName.value,
                lastName: formState.lastName.value,
                phone: formState.phone.value,
                email: formState.email.value,
                method,
            };

            console.log('Personal info form data:', formData);
            router.push('/(auth)/farm-setup');
        } else {
            if (isVoiceEnabled) {
                speak('Please fill in all required fields correctly');
            }
        }
    };

    const handleBack = () => {
        if (isVoiceEnabled) {
            speak('Going back to welcome screen');
        }
        router.back();
    };

    return (
        <SafeAreaView className={`flex-1 bg-earth-50 ${isRTL ? 'rtl' : 'ltr'}`}>
            <StatusBar style="dark" />

            <KeyboardAvoidingView
                className="flex-1"
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            >
                <ScrollView
                    className="flex-1"
                    contentContainerStyle={{ flexGrow: 1 }}
                    showsVerticalScrollIndicator={false}
                    keyboardShouldPersistTaps="handled"
                >
                    <View className="flex-1 px-6 py-8">
                        {/* Progress Indicator */}
                        <ProgressIndicator
                            currentStep={1}
                            totalSteps={3}
                            stepLabels={[t('personalInfo.title'), t('farmSetup.title'), 'Complete']}
                            className="mb-8"
                            isRTL={isRTL}
                        />

                        {/* Header */}
                        <View className="mb-8">
                            <Text className={`mb-2 text-3xl font-bold text-earth-900 ${isRTL ? 'text-right' : 'text-left'}`}>
                                {t('personalInfo.title')}
                            </Text>
                            <Text className={`text-lg text-earth-600 ${isRTL ? 'text-right' : 'text-left'}`}>
                                {t('personalInfo.subtitle')}
                            </Text>
                        </View>

                        {/* Form Fields */}
                        <View className="gap-6">
                            {/* First Name */}
                            <Input
                                label={t('personalInfo.firstName')}
                                placeholder={t('personalInfo.firstName')}
                                {...getFieldProps('firstName')}
                                autoCapitalize="words"
                                accessibilityLabel={t('personalInfo.firstName')}
                                accessibilityHint="Enter your first name"
                                voiceInputEnabled={true}
                                onVoiceInput={() => handleVoiceInput('firstName')}
                                isVoiceRecording={isVoiceRecording === 'firstName'}
                                voiceFeedbackEnabled={isVoiceEnabled}
                                onVoiceFeedback={speak}
                            />

                            {/* Last Name */}
                            <Input
                                label={t('personalInfo.lastName')}
                                placeholder={t('personalInfo.lastName')}
                                {...getFieldProps('lastName')}
                                autoCapitalize="words"
                                accessibilityLabel={t('personalInfo.lastName')}
                                accessibilityHint="Enter your last name"
                                voiceInputEnabled={true}
                                onVoiceInput={() => handleVoiceInput('lastName')}
                                isVoiceRecording={isVoiceRecording === 'lastName'}
                                voiceFeedbackEnabled={isVoiceEnabled}
                                onVoiceFeedback={speak}
                            />

                            {/* Phone Number (if method is phone) */}
                            {method === 'phone' && (
                                <Input
                                    label={t('personalInfo.phone')}
                                    placeholder="+****************"
                                    {...getFieldProps('phone')}
                                    keyboardType="phone-pad"
                                    accessibilityLabel={t('personalInfo.phone')}
                                    accessibilityHint="Enter your phone number"
                                    voiceInputEnabled={true}
                                    onVoiceInput={() => handleVoiceInput('phone')}
                                    isVoiceRecording={isVoiceRecording === 'phone'}
                                    voiceFeedbackEnabled={isVoiceEnabled}
                                    onVoiceFeedback={speak}
                                />
                            )}

                            {/* Email Address (if method is email) */}
                            {method === 'email' && (
                                <Input
                                    label={t('personalInfo.email')}
                                    placeholder="<EMAIL>"
                                    {...getFieldProps('email')}
                                    keyboardType="email-address"
                                    autoCapitalize="none"
                                    accessibilityLabel={t('personalInfo.email')}
                                    accessibilityHint="Enter your email address"
                                    voiceInputEnabled={true}
                                    onVoiceInput={() => handleVoiceInput('email')}
                                    isVoiceRecording={isVoiceRecording === 'email'}
                                    voiceFeedbackEnabled={isVoiceEnabled}
                                    onVoiceFeedback={speak}
                                />
                            )}
                        </View>

                        {/* Voice Fill Button */}
                        <View className="mt-8">
                            <Button
                                title={t('personalInfo.fillWithVoice')}
                                onPress={() => {
                                    Alert.alert(
                                        'Voice Fill',
                                        'This feature would use advanced speech recognition to fill all fields at once. For now, use individual voice buttons on each field.',
                                        [{ text: 'OK' }]
                                    );
                                }}
                                variant="ghost"
                                icon={<Text className="text-xl">🎤</Text>}
                                accessibilityLabel="Fill all fields with voice"
                                accessibilityHint="Use voice to fill all form fields at once"
                                voiceFeedbackEnabled={isVoiceEnabled}
                                onVoiceFeedback={speak}
                            />
                        </View>

                        {/* Spacer */}
                        <View className="flex-1" />

                        {/* Navigation Buttons */}
                        <View className={`mt-8 flex-row gap-4 ${isRTL ? 'flex-row-reverse gap-reverse' : ''}`}>
                            <Button
                                title={t('personalInfo.back')}
                                onPress={handleBack}
                                variant="outline"
                                size="large"
                                className="flex-1"
                                accessibilityLabel="Go back to previous screen"
                                accessibilityHint="Return to the welcome screen"
                                voiceFeedbackEnabled={isVoiceEnabled}
                                onVoiceFeedback={speak}
                            />

                            <Button
                                title={t('personalInfo.next')}
                                onPress={handleNext}
                                variant="primary"
                                size="large"
                                className="flex-1"
                                disabled={!isValid}
                                accessibilityLabel="Continue to next step"
                                accessibilityHint="Proceed to farm setup screen"
                                voiceFeedbackEnabled={isVoiceEnabled}
                                onVoiceFeedback={speak}
                            />
                        </View>
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        </SafeAreaView>
    );
};

export default function PersonalInfo() {
    return (
        <AppProvider>
            <PersonalInfoContent />
        </AppProvider>
    );
}