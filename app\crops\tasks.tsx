import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Task, TaskCompletion } from '../../src/types/tasks';
import TaskCalendar from '../../src/components/crops/TaskCalendar';
import TaskDetailModal from '../../src/components/crops/TaskDetailModal';

// Mock data - in real app this would come from state management/API
const mockTasks: Task[] = [
    {
        id: 'task-1',
        title: 'Water Tomato Plants',
        description: 'Water the tomato plants in the greenhouse. Check soil moisture first.',
        dueDate: new Date(),
        type: 'watering',
        completed: false,
        pointsReward: 10,
        priority: 'high',
        cropPlanId: 'plan-1',
        estimatedDuration: 30,
        instructions: [
            'Check soil moisture by inserting finger 2 inches deep',
            'Water slowly at the base of plants',
            'Avoid getting water on leaves to prevent disease',
            'Water until soil is moist but not waterlogged',
        ],
    },
    {
        id: 'task-2',
        title: 'Monitor Corn Growth',
        description: 'Check corn plants for pests, diseases, and overall health.',
        dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        type: 'monitoring',
        completed: false,
        pointsReward: 12,
        priority: 'medium',
        cropPlanId: 'plan-2',
        estimatedDuration: 20,
        instructions: [
            'Inspect leaves for discoloration or spots',
            'Check for pest damage on stalks and leaves',
            'Measure plant height and record growth',
            'Look for signs of nutrient deficiency',
        ],
    },
    {
        id: 'task-3',
        title: 'Apply Fertilizer to Peppers',
        description: 'Apply balanced fertilizer to pepper plants for optimal growth.',
        dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // Day after tomorrow
        type: 'fertilizing',
        completed: false,
        pointsReward: 15,
        priority: 'medium',
        cropPlanId: 'plan-3',
        estimatedDuration: 45,
        instructions: [
            'Use balanced 10-10-10 fertilizer',
            'Apply around the base of each plant',
            'Water thoroughly after application',
            'Avoid getting fertilizer on leaves',
        ],
    },
    {
        id: 'task-4',
        title: 'Harvest Lettuce',
        description: 'Harvest mature lettuce heads from the garden bed.',
        dueDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday (overdue)
        type: 'harvesting',
        completed: true,
        pointsReward: 20,
        priority: 'high',
        cropPlanId: 'plan-4',
        estimatedDuration: 60,
        completedAt: new Date(Date.now() - 12 * 60 * 60 * 1000),
        instructions: [
            'Harvest in the morning when cool',
            'Cut at the base with clean knife',
            'Wash immediately after harvest',
            'Store in cool, humid conditions',
        ],
    },
];

export default function TaskManagementScreen() {
    const [tasks, setTasks] = useState<Task[]>(mockTasks);
    const [selectedDate, setSelectedDate] = useState(new Date());
    const [viewMode, setViewMode] = useState<'daily' | 'weekly'>('daily');
    const [selectedTask, setSelectedTask] = useState<Task | null>(null);
    const [showTaskDetail, setShowTaskDetail] = useState(false);
    const [voiceEnabled, setVoiceEnabled] = useState(false);

    const handleTaskPress = (task: Task) => {
        setSelectedTask(task);
        setShowTaskDetail(true);
    };

    const handleTaskComplete = async (completion: TaskCompletion) => {
        try {
            // Update task in state
            setTasks(prevTasks =>
                prevTasks.map(task =>
                    task.id === completion.taskId
                        ? {
                            ...task,
                            completed: true,
                            completedAt: completion.completedAt,
                        }
                        : task
                )
            );

            // TODO: Save to backend/state management
            console.log('Task completed:', completion);

            Alert.alert(
                'Task Completed! 🎉',
                `You earned ${completion.pointsEarned} points for completing this task.`,
                [{ text: 'Great!' }]
            );
        } catch (error) {
            console.error('Error completing task:', error);
            throw error;
        }
    };

    const handleTaskUpdate = async (taskId: string, updates: Partial<Task>) => {
        try {
            setTasks(prevTasks =>
                prevTasks.map(task =>
                    task.id === taskId ? { ...task, ...updates } : task
                )
            );

            // TODO: Save to backend/state management
            console.log('Task updated:', taskId, updates);
        } catch (error) {
            console.error('Error updating task:', error);
        }
    };

    const handleVoiceCommand = (command: string) => {
        // TODO: Implement voice commands
        console.log('Voice command:', command);

        switch (command) {
            case 'navigate_calendar':
                // Handle calendar navigation
                break;
            case 'task_details':
                // Handle task detail commands
                break;
            default:
                console.log('Unknown voice command:', command);
        }
    };

    const getTaskStats = () => {
        const today = new Date();
        const todayTasks = tasks.filter(task => {
            const taskDate = new Date(task.dueDate);
            return (
                taskDate.getDate() === today.getDate() &&
                taskDate.getMonth() === today.getMonth() &&
                taskDate.getFullYear() === today.getFullYear()
            );
        });

        const completedToday = todayTasks.filter(task => task.completed).length;
        const overdueTasks = tasks.filter(task => {
            const taskDate = new Date(task.dueDate);
            return taskDate < today && !task.completed;
        }).length;

        return {
            totalToday: todayTasks.length,
            completedToday,
            pendingToday: todayTasks.length - completedToday,
            overdue: overdueTasks,
        };
    };

    const stats = getTaskStats();

    return (
        <SafeAreaView className="flex-1 bg-gray-50">
            {/* Header */}
            <View className="bg-white border-b border-gray-200 px-4 py-3">
                <View className="flex-row items-center justify-between">
                    <TouchableOpacity
                        onPress={() => router.back()}
                        className="p-2"
                        accessibilityLabel="Go back"
                    >
                        <Text className="text-2xl">←</Text>
                    </TouchableOpacity>

                    <View className="flex-1 mx-4">
                        <Text className="text-lg font-semibold text-gray-900 text-center">
                            Task Management
                        </Text>
                        <Text className="text-sm text-gray-500 text-center">
                            Manage your farming tasks
                        </Text>
                    </View>

                    <TouchableOpacity
                        onPress={() => setVoiceEnabled(!voiceEnabled)}
                        className={`p-2 rounded-full ${voiceEnabled ? 'bg-green-500' : 'bg-gray-200'}`}
                        accessibilityLabel="Toggle voice mode"
                    >
                        <Text className={`text-sm ${voiceEnabled ? 'text-white' : 'text-gray-600'}`}>
                            🎤
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>

            {/* Task Stats */}
            <View className="bg-white border-b border-gray-200 px-4 py-3">
                <View className="flex-row justify-between">
                    <View className="items-center">
                        <Text className="text-2xl font-bold text-blue-600">
                            {stats.totalToday}
                        </Text>
                        <Text className="text-xs text-gray-600">Today's Tasks</Text>
                    </View>

                    <View className="items-center">
                        <Text className="text-2xl font-bold text-green-600">
                            {stats.completedToday}
                        </Text>
                        <Text className="text-xs text-gray-600">Completed</Text>
                    </View>

                    <View className="items-center">
                        <Text className="text-2xl font-bold text-yellow-600">
                            {stats.pendingToday}
                        </Text>
                        <Text className="text-xs text-gray-600">Pending</Text>
                    </View>

                    <View className="items-center">
                        <Text className="text-2xl font-bold text-red-600">
                            {stats.overdue}
                        </Text>
                        <Text className="text-xs text-gray-600">Overdue</Text>
                    </View>
                </View>
            </View>

            {/* Task Calendar */}
            <TaskCalendar
                tasks={tasks}
                selectedDate={selectedDate}
                onDateSelect={setSelectedDate}
                onTaskPress={handleTaskPress}
                viewMode={viewMode}
                onViewModeChange={setViewMode}
                voiceEnabled={voiceEnabled}
                onVoiceCommand={handleVoiceCommand}
            />

            {/* Task Detail Modal */}
            <TaskDetailModal
                task={selectedTask}
                visible={showTaskDetail}
                onClose={() => {
                    setShowTaskDetail(false);
                    setSelectedTask(null);
                }}
                onTaskComplete={handleTaskComplete}
                onTaskUpdate={handleTaskUpdate}
                voiceEnabled={voiceEnabled}
                onVoiceCommand={handleVoiceCommand}
            />

            {/* Voice Commands Help */}
            {voiceEnabled && (
                <View className="absolute bottom-4 left-4 right-4 bg-blue-50 rounded-lg p-3 border border-blue-200">
                    <Text className="text-sm font-semibold text-blue-800 mb-1">
                        🗣️ Voice Commands Available
                    </Text>
                    <Text className="text-xs text-blue-700">
                        "Show today's tasks" • "Complete task" • "Next day" • "Previous day"
                    </Text>
                </View>
            )}
        </SafeAreaView>
    );
}