import React, { useState } from 'react';
import { View, Text, ScrollView } from 'react-native';
import { Button, Input, Card, Modal } from '../ui';
import { WeatherWidget, TaskCard } from '../agricultural';
import type { WeatherData, Task } from '../agricultural';

/**
 * Design System Showcase Component
 * Demonstrates all design system components and patterns
 */
export const DesignSystemShowcase: React.FC = () => {
  const [inputValue, setInputValue] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const [tasks, setTasks] = useState<Task[]>([
    {
      id: '1',
      title: 'Water tomato plants',
      description: 'Check soil moisture and water if needed',
      dueDate: new Date(),
      type: 'watering',
      completed: false,
      priority: 'high',
      pointsReward: 10,
    },
    {
      id: '2',
      title: 'Monitor corn growth',
      description: 'Check for pests and disease signs',
      dueDate: new Date(Date.now() + 86400000), // Tomorrow
      type: 'monitoring',
      completed: true,
      priority: 'medium',
      pointsReward: 15,
    },
  ]);

  const sampleWeather: WeatherData = {
    temperature: 75,
    humidity: 65,
    condition: 'sunny',
    windSpeed: 8,
    precipitation: 0,
    uvIndex: 6,
  };

  const toggleTaskComplete = (taskId: string) => {
    setTasks((prev) =>
      prev.map((task) => (task.id === taskId ? { ...task, completed: !task.completed } : task))
    );
  };

  return (
    <ScrollView className="flex-1 bg-earth-50 p-4">
      <Text className="mb-6 text-center text-3xl font-bold text-earth-900">
        🌾 Design System Showcase
      </Text>

      {/* Colors Section */}
      <Card className="mb-6">
        <Text className="mb-4 text-xl font-semibold text-earth-900">Color Palette</Text>
        <View className="flex-row flex-wrap gap-2">
          <View className="h-16 w-16 rounded-lg bg-primary-500" />
          <View className="h-16 w-16 rounded-lg bg-secondary-500" />
          <View className="h-16 w-16 rounded-lg bg-earth-500" />
          <View className="h-16 w-16 rounded-lg bg-red-500" />
        </View>
        <Text className="mt-2 text-sm text-earth-600">
          Primary, Secondary, Earth, and Status colors
        </Text>
      </Card>

      {/* Typography Section */}
      <Card className="mb-6">
        <Text className="mb-4 text-xl font-semibold text-earth-900">Typography</Text>
        <Text className="mb-2 text-4xl font-bold text-earth-900">Heading 1</Text>
        <Text className="mb-2 text-2xl font-semibold text-earth-800">Heading 2</Text>
        <Text className="mb-2 text-lg text-earth-700">Body Large</Text>
        <Text className="mb-2 text-base text-earth-700">Body Regular</Text>
        <Text className="text-sm text-earth-600">Body Small</Text>
      </Card>

      {/* Buttons Section */}
      <Card className="mb-6">
        <Text className="mb-4 text-xl font-semibold text-earth-900">Buttons</Text>
        <View className="gap-3">
          <Button
            title="Primary Button"
            onPress={() => console.log('Primary pressed')}
            variant="primary"
          />
          <Button
            title="Secondary Button"
            onPress={() => console.log('Secondary pressed')}
            variant="secondary"
          />
          <Button
            title="Outline Button"
            onPress={() => console.log('Outline pressed')}
            variant="outline"
          />
          <Button title="Loading Button" onPress={() => {}} loading />
          <Button title="Disabled Button" onPress={() => {}} disabled />
        </View>
      </Card>

      {/* Inputs Section */}
      <Card className="mb-6">
        <Text className="mb-4 text-xl font-semibold text-earth-900">Input Fields</Text>
        <View className="gap-4">
          <Input
            label="Farm Name"
            placeholder="Enter your farm name"
            value={inputValue}
            onChangeText={setInputValue}
          />
          <Input
            label="Email Address"
            placeholder="<EMAIL>"
            value=""
            onChangeText={() => {}}
            keyboardType="email-address"
          />
          <Input
            label="Notes"
            placeholder="Add farming notes..."
            value=""
            onChangeText={() => {}}
            multiline
            numberOfLines={3}
          />
          <Input
            label="Voice Input Example"
            placeholder="Speak or type..."
            value=""
            onChangeText={() => {}}
            voiceInputEnabled
            onVoiceInput={() => console.log('Voice input activated')}
          />
        </View>
      </Card>

      {/* Weather Widget Section */}
      <Card className="mb-6">
        <Text className="mb-4 text-xl font-semibold text-earth-900">Weather Widget</Text>
        <WeatherWidget
          weather={sampleWeather}
          location="Springfield Farm"
          onPress={() => console.log('Weather pressed')}
        />
      </Card>

      {/* Task Cards Section */}
      <Card className="mb-6">
        <Text className="mb-4 text-xl font-semibold text-earth-900">Task Management</Text>
        <View className="gap-3">
          {tasks.map((task) => (
            <TaskCard
              key={task.id}
              task={task}
              onToggleComplete={toggleTaskComplete}
              onPress={() => console.log(`Task ${task.id} pressed`)}
            />
          ))}
        </View>
      </Card>

      {/* Modal Section */}
      <Card className="mb-6">
        <Text className="mb-4 text-xl font-semibold text-earth-900">Modal Dialog</Text>
        <Button title="Open Modal" onPress={() => setModalVisible(true)} variant="outline" />
      </Card>

      {/* Accessibility Features */}
      <Card className="mb-6">
        <Text className="mb-4 text-xl font-semibold text-earth-900">Accessibility Features</Text>
        <View className="gap-2">
          <Text className="text-base text-earth-700">✓ Large touch targets (minimum 44px)</Text>
          <Text className="text-base text-earth-700">
            ✓ High contrast colors for outdoor visibility
          </Text>
          <Text className="text-base text-earth-700">✓ Voice input support on form fields</Text>
          <Text className="text-base text-earth-700">✓ Screen reader compatible labels</Text>
          <Text className="text-base text-earth-700">✓ Keyboard navigation support</Text>
        </View>
      </Card>

      <Modal visible={modalVisible} onClose={() => setModalVisible(false)} title="Sample Modal">
        <Text className="mb-4 text-base text-earth-700">
          This is a sample modal dialog demonstrating the modal component with proper accessibility
          features and agricultural-friendly styling.
        </Text>
        <Button title="Close Modal" onPress={() => setModalVisible(false)} variant="primary" />
      </Modal>
    </ScrollView>
  );
};
