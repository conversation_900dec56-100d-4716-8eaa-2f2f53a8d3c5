export interface CropType {
    id: string;
    name: string;
    nameAr: string;
    icon: string;
    category: 'grains' | 'vegetables' | 'fruits' | 'legumes' | 'herbs';
    growingSeasonMonths: number[];
    difficulty: 'beginner' | 'intermediate' | 'expert';
    description: string;
    descriptionAr: string;
}

export const cropTypes: CropType[] = [
    // Grains
    {
        id: 'corn',
        name: 'Corn',
        nameAr: 'ذرة',
        icon: '🌽',
        category: 'grains',
        growingSeasonMonths: [4, 5, 6, 7, 8, 9],
        difficulty: 'beginner',
        description: 'A versatile grain crop that grows well in warm weather',
        descriptionAr: 'محصول حبوب متعدد الاستخدامات ينمو جيداً في الطقس الدافئ',
    },
    {
        id: 'wheat',
        name: 'Wheat',
        nameAr: 'قمح',
        icon: '🌾',
        category: 'grains',
        growingSeasonMonths: [3, 4, 5, 6, 10, 11],
        difficulty: 'intermediate',
        description: 'A staple grain crop for bread and flour production',
        descriptionAr: 'محصول حبوب أساسي لإنتاج الخبز والدقيق',
    },
    {
        id: 'rice',
        name: 'Rice',
        nameAr: 'أرز',
        icon: '🌾',
        category: 'grains',
        growingSeasonMonths: [5, 6, 7, 8, 9],
        difficulty: 'expert',
        description: 'A water-intensive grain crop requiring flooded fields',
        descriptionAr: 'محصول حبوب يتطلب كميات كبيرة من الماء والحقول المغمورة',
    },

    // Vegetables
    {
        id: 'tomatoes',
        name: 'Tomatoes',
        nameAr: 'طماطم',
        icon: '🍅',
        category: 'vegetables',
        growingSeasonMonths: [4, 5, 6, 7, 8, 9],
        difficulty: 'beginner',
        description: 'Popular vegetable crop with high nutritional value',
        descriptionAr: 'محصول خضار شائع بقيمة غذائية عالية',
    },
    {
        id: 'peppers',
        name: 'Peppers',
        nameAr: 'فلفل',
        icon: '🌶️',
        category: 'vegetables',
        growingSeasonMonths: [4, 5, 6, 7, 8, 9],
        difficulty: 'beginner',
        description: 'Colorful vegetables that add flavor to dishes',
        descriptionAr: 'خضروات ملونة تضيف نكهة للأطباق',
    },
    {
        id: 'onions',
        name: 'Onions',
        nameAr: 'بصل',
        icon: '🧅',
        category: 'vegetables',
        growingSeasonMonths: [3, 4, 5, 9, 10, 11],
        difficulty: 'beginner',
        description: 'Essential cooking ingredient with long storage life',
        descriptionAr: 'مكون طبخ أساسي بعمر تخزين طويل',
    },
    {
        id: 'carrots',
        name: 'Carrots',
        nameAr: 'جزر',
        icon: '🥕',
        category: 'vegetables',
        growingSeasonMonths: [3, 4, 5, 8, 9, 10],
        difficulty: 'beginner',
        description: 'Root vegetable rich in vitamins and minerals',
        descriptionAr: 'خضار جذرية غنية بالفيتامينات والمعادن',
    },
    {
        id: 'potatoes',
        name: 'Potatoes',
        nameAr: 'بطاطس',
        icon: '🥔',
        category: 'vegetables',
        growingSeasonMonths: [3, 4, 5, 8, 9],
        difficulty: 'intermediate',
        description: 'Starchy tuber crop that is a dietary staple',
        descriptionAr: 'محصول درني نشوي يعتبر غذاء أساسي',
    },

    // Fruits
    {
        id: 'strawberries',
        name: 'Strawberries',
        nameAr: 'فراولة',
        icon: '🍓',
        category: 'fruits',
        growingSeasonMonths: [3, 4, 5, 9, 10],
        difficulty: 'intermediate',
        description: 'Sweet berries perfect for fresh eating and processing',
        descriptionAr: 'توت حلو مثالي للأكل الطازج والمعالجة',
    },
    {
        id: 'watermelon',
        name: 'Watermelon',
        nameAr: 'بطيخ',
        icon: '🍉',
        category: 'fruits',
        growingSeasonMonths: [5, 6, 7, 8],
        difficulty: 'intermediate',
        description: 'Large, refreshing fruit perfect for hot weather',
        descriptionAr: 'فاكهة كبيرة منعشة مثالية للطقس الحار',
    },

    // Legumes
    {
        id: 'soybeans',
        name: 'Soybeans',
        nameAr: 'فول الصويا',
        icon: '🫘',
        category: 'legumes',
        growingSeasonMonths: [5, 6, 7, 8, 9],
        difficulty: 'intermediate',
        description: 'Protein-rich legume crop with nitrogen-fixing properties',
        descriptionAr: 'محصول بقولي غني بالبروتين بخصائص تثبيت النيتروجين',
    },
    {
        id: 'beans',
        name: 'Beans',
        nameAr: 'فاصولياء',
        icon: '🫘',
        category: 'legumes',
        growingSeasonMonths: [4, 5, 6, 7, 8],
        difficulty: 'beginner',
        description: 'Versatile legume crop that improves soil fertility',
        descriptionAr: 'محصول بقولي متعدد الاستخدامات يحسن خصوبة التربة',
    },

    // Herbs
    {
        id: 'basil',
        name: 'Basil',
        nameAr: 'ريحان',
        icon: '🌿',
        category: 'herbs',
        growingSeasonMonths: [4, 5, 6, 7, 8, 9],
        difficulty: 'beginner',
        description: 'Aromatic herb used in cooking and traditional medicine',
        descriptionAr: 'عشبة عطرية تستخدم في الطبخ والطب التقليدي',
    },
    {
        id: 'mint',
        name: 'Mint',
        nameAr: 'نعناع',
        icon: '🌿',
        category: 'herbs',
        growingSeasonMonths: [3, 4, 5, 6, 7, 8, 9, 10],
        difficulty: 'beginner',
        description: 'Refreshing herb that grows easily in most conditions',
        descriptionAr: 'عشبة منعشة تنمو بسهولة في معظم الظروف',
    },
];

export const cropCategories = [
    { id: 'grains', name: 'Grains', nameAr: 'حبوب', icon: '🌾' },
    { id: 'vegetables', name: 'Vegetables', nameAr: 'خضروات', icon: '🥕' },
    { id: 'fruits', name: 'Fruits', nameAr: 'فواكه', icon: '🍓' },
    { id: 'legumes', name: 'Legumes', nameAr: 'بقوليات', icon: '🫘' },
    { id: 'herbs', name: 'Herbs', nameAr: 'أعشاب', icon: '🌿' },
];

export const experienceLevels = [
    {
        id: 'beginner',
        name: 'Beginner',
        nameAr: 'مبتدئ',
        description: 'New to farming, looking for easy-to-grow crops',
        descriptionAr: 'جديد في الزراعة، يبحث عن محاصيل سهلة النمو',
        icon: '🌱',
    },
    {
        id: 'intermediate',
        name: 'Intermediate',
        nameAr: 'متوسط',
        description: 'Some farming experience, ready for moderate challenges',
        descriptionAr: 'لديه بعض الخبرة في الزراعة، مستعد للتحديات المتوسطة',
        icon: '🌿',
    },
    {
        id: 'expert',
        name: 'Expert',
        nameAr: 'خبير',
        description: 'Experienced farmer, comfortable with complex crops',
        descriptionAr: 'مزارع ذو خبرة، مرتاح مع المحاصيل المعقدة',
        icon: '🌳',
    },
];