/**
 * Keyboard Navigation Utilities Tests
 * Tests for keyboard navigation and focus management
 */

import { KeyboardNavigationManager, handleKeyboardShortcut, keyboardShortcuts } from '../keyboardNavigation';

// Mock React Native components
jest.mock('react-native', () => ({
    Platform: {
        isTV: false,
    },
    TVEventHandler: jest.fn().mockImplementation(() => ({
        enable: jest.fn(),
        disable: jest.fn(),
    })),
}));

describe('KeyboardNavigationManager', () => {
    let manager: KeyboardNavigationManager;

    beforeEach(() => {
        manager = KeyboardNavigationManager.getInstance();
        manager.clearElements();
    });

    describe('Element Registration', () => {
        test('should register focusable elements', () => {
            const mockRef = { current: { focus: jest.fn() } };
            const element = {
                id: 'test-element',
                ref: mockRef,
                order: 0,
                onFocus: jest.fn(),
            };

            const cleanup = manager.registerElement(element);
            expect(typeof cleanup).toBe('function');

            // Test cleanup
            cleanup();
        });

        test('should handle element cleanup', () => {
            const mockRef = { current: { focus: jest.fn() } };
            const element = {
                id: 'test-element',
                ref: mockRef,
                order: 0,
            };

            const cleanup = manager.registerElement(element);
            cleanup();

            // Should not be able to focus after cleanup
            const focused = manager.focusElement('test-element');
            expect(focused).toBe(false);
        });
    });

    describe('Focus Navigation', () => {
        beforeEach(() => {
            // Register test elements
            const elements = [
                {
                    id: 'element-1',
                    ref: { current: { focus: jest.fn() } },
                    order: 1,
                    onFocus: jest.fn(),
                },
                {
                    id: 'element-2',
                    ref: { current: { focus: jest.fn() } },
                    order: 2,
                    onFocus: jest.fn(),
                },
                {
                    id: 'element-3',
                    ref: { current: { focus: jest.fn() } },
                    order: 3,
                    onFocus: jest.fn(),
                },
            ];

            elements.forEach(element => manager.registerElement(element));
        });

        test('should focus next element', () => {
            manager.focusElement('element-1');
            const focused = manager.focusNext();
            expect(focused).toBe(true);
        });

        test('should focus previous element', () => {
            manager.focusElement('element-2');
            const focused = manager.focusPrevious();
            expect(focused).toBe(true);
        });

        test('should focus first element', () => {
            const focused = manager.focusFirst();
            expect(focused).toBe(true);
        });

        test('should focus last element', () => {
            const focused = manager.focusLast();
            expect(focused).toBe(true);
        });

        test('should handle circular navigation', () => {
            // Focus last element and go next (should wrap to first)
            manager.focusLast();
            const focused = manager.focusNext();
            expect(focused).toBe(true);

            // Focus first element and go previous (should wrap to last)
            manager.focusFirst();
            const focusedPrev = manager.focusPrevious();
            expect(focusedPrev).toBe(true);
        });

        test('should skip disabled elements', () => {
            const disabledElement = {
                id: 'disabled-element',
                ref: { current: { focus: jest.fn() } },
                order: 1.5,
                disabled: true,
            };

            manager.registerElement(disabledElement);

            manager.focusElement('element-1');
            manager.focusNext();

            // Should skip disabled element and go to element-2
            // This test verifies the ordering logic works correctly
        });
    });

    describe('Element Activation', () => {
        test('should activate element with onActivate callback', () => {
            const onActivate = jest.fn();
            const element = {
                id: 'activatable-element',
                ref: { current: {} },
                order: 0,
                onActivate,
            };

            manager.registerElement(element);
            manager.focusElement('activatable-element');

            const activated = manager.activateCurrentElement();
            expect(activated).toBe(true);
            expect(onActivate).toHaveBeenCalled();
        });

        test('should activate element with onPress prop', () => {
            const onPress = jest.fn();
            const element = {
                id: 'pressable-element',
                ref: { current: { props: { onPress } } },
                order: 0,
            };

            manager.registerElement(element);
            manager.focusElement('pressable-element');

            const activated = manager.activateCurrentElement();
            expect(activated).toBe(true);
            expect(onPress).toHaveBeenCalled();
        });

        test('should return false for non-activatable elements', () => {
            const element = {
                id: 'non-activatable-element',
                ref: { current: {} },
                order: 0,
            };

            manager.registerElement(element);
            manager.focusElement('non-activatable-element');

            const activated = manager.activateCurrentElement();
            expect(activated).toBe(false);
        });
    });

    describe('Configuration Management', () => {
        test('should update configuration', () => {
            const newConfig = {
                enabled: false,
                focusOnMount: false,
                trapFocus: false,
                skipLinks: false,
            };

            manager.updateConfig(newConfig);
            const config = manager.getConfig();

            expect(config.enabled).toBe(false);
            expect(config.focusOnMount).toBe(false);
            expect(config.trapFocus).toBe(false);
            expect(config.skipLinks).toBe(false);
        });

        test('should maintain default values for partial updates', () => {
            manager.updateConfig({ enabled: false });
            const config = manager.getConfig();

            expect(config.enabled).toBe(false);
            expect(config.focusOnMount).toBe(true); // Should remain default
        });
    });

    describe('Element Management', () => {
        test('should clear all elements', () => {
            const element = {
                id: 'test-element',
                ref: { current: { focus: jest.fn() } },
                order: 0,
            };

            manager.registerElement(element);
            manager.focusElement('test-element');

            manager.clearElements();

            const focused = manager.focusElement('test-element');
            expect(focused).toBe(false);
        });
    });
});

describe('Keyboard Shortcuts', () => {
    test('should handle navigation shortcuts', () => {
        const onShortcut = jest.fn();

        const handled = handleKeyboardShortcut('h', {}, onShortcut);
        expect(handled).toBe(true);
        expect(onShortcut).toHaveBeenCalledWith('Go to Home');
    });

    test('should handle action shortcuts', () => {
        const onShortcut = jest.fn();

        const handled = handleKeyboardShortcut('p', {}, onShortcut);
        expect(handled).toBe(true);
        expect(onShortcut).toHaveBeenCalledWith('Take Photo');
    });

    test('should handle modifier key combinations', () => {
        const onShortcut = jest.fn();

        const handled = handleKeyboardShortcut('v', { alt: true }, onShortcut);
        expect(handled).toBe(true);
        expect(onShortcut).toHaveBeenCalledWith('Toggle Voice Mode');
    });

    test('should handle complex key combinations', () => {
        const onShortcut = jest.fn();

        const handled = handleKeyboardShortcut('Tab', { shift: true }, onShortcut);
        expect(handled).toBe(true);
        expect(onShortcut).toHaveBeenCalledWith('Previous Element');
    });

    test('should return false for unrecognized shortcuts', () => {
        const onShortcut = jest.fn();

        const handled = handleKeyboardShortcut('z', {}, onShortcut);
        expect(handled).toBe(false);
        expect(onShortcut).not.toHaveBeenCalled();
    });

    test('should include all expected shortcut categories', () => {
        // Navigation shortcuts
        expect(keyboardShortcuts['h']).toBe('Go to Home');
        expect(keyboardShortcuts['c']).toBe('Open Crops');
        expect(keyboardShortcuts['w']).toBe('Check Weather');

        // Action shortcuts
        expect(keyboardShortcuts['p']).toBe('Take Photo');
        expect(keyboardShortcuts['t']).toBe('Add Task');
        expect(keyboardShortcuts['v']).toBe('Voice Mode');

        // System shortcuts
        expect(keyboardShortcuts['Escape']).toBe('Go Back');
        expect(keyboardShortcuts['Enter']).toBe('Activate');
        expect(keyboardShortcuts['Tab']).toBe('Next Element');

        // Accessibility shortcuts
        expect(keyboardShortcuts['Alt+v']).toBe('Toggle Voice Mode');
        expect(keyboardShortcuts['Alt+c']).toBe('Toggle High Contrast');
        expect(keyboardShortcuts['Alt+t']).toBe('Toggle Large Text');
    });
});

describe('Agricultural Context', () => {
    test('should include agricultural-specific shortcuts', () => {
        expect(keyboardShortcuts['c']).toBe('Open Crops');
        expect(keyboardShortcuts['w']).toBe('Check Weather');
        expect(keyboardShortcuts['p']).toBe('Take Photo');
        expect(keyboardShortcuts['t']).toBe('Add Task');
    });

    test('should support voice mode toggle', () => {
        const onShortcut = jest.fn();

        const handled = handleKeyboardShortcut('v', { alt: true }, onShortcut);
        expect(handled).toBe(true);
        expect(onShortcut).toHaveBeenCalledWith('Toggle Voice Mode');
    });

    test('should support accessibility toggles', () => {
        const onShortcut = jest.fn();

        // High contrast toggle
        let handled = handleKeyboardShortcut('c', { alt: true }, onShortcut);
        expect(handled).toBe(true);
        expect(onShortcut).toHaveBeenCalledWith('Toggle High Contrast');

        // Large text toggle
        handled = handleKeyboardShortcut('t', { alt: true }, onShortcut);
        expect(handled).toBe(true);
        expect(onShortcut).toHaveBeenCalledWith('Toggle Large Text');

        // Screen reader
        handled = handleKeyboardShortcut('r', { alt: true }, onShortcut);
        expect(handled).toBe(true);
        expect(onShortcut).toHaveBeenCalledWith('Read Screen');
    });
});