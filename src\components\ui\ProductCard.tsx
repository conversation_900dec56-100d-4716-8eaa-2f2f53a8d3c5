import React from 'react';
import { View, Text, Image, Pressable } from 'react-native';
import { Product } from '../../types/product';
import { Button } from './Button';

export interface ProductCardProps {
    product: Product;
    onPress: (product: Product) => void;
    onAddToCart: (productId: string) => void;
    showAddToCart?: boolean;
    variant?: 'grid' | 'list';
    voiceFeedbackEnabled?: boolean;
    onVoiceFeedback?: (text: string) => void;
}

export const ProductCard: React.FC<ProductCardProps> = ({
    product,
    onPress,
    onAddToCart,
    showAddToCart = true,
    variant = 'grid',
    voiceFeedbackEnabled = false,
    onVoiceFeedback,
}) => {
    const handlePress = () => {
        if (voiceFeedbackEnabled && onVoiceFeedback) {
            onVoiceFeedback(`Selected ${product.name}, priced at ${product.price} dollars`);
        }
        onPress(product);
    };

    const handleAddToCart = () => {
        if (voiceFeedbackEnabled && onVoiceFeedback) {
            onVoiceFeedback(`Added ${product.name} to cart`);
        }
        onAddToCart(product.id);
    };

    const formatPrice = (price: number) => {
        return `$${price.toFixed(2)}`;
    };

    const renderRating = () => {
        const stars = Math.floor(product.rating);
        const hasHalfStar = product.rating % 1 !== 0;

        return (
            <View className="flex-row items-center gap-1">
                <Text className="text-yellow-500">
                    {'★'.repeat(stars)}
                    {hasHalfStar ? '☆' : ''}
                    {'☆'.repeat(5 - stars - (hasHalfStar ? 1 : 0))}
                </Text>
                <Text className="text-xs text-gray-600">
                    ({product.reviewCount})
                </Text>
            </View>
        );
    };

    const renderBadges = () => {
        const badges = [];

        if (product.isRecommended) {
            badges.push(
                <View key="recommended" className="bg-green-100 px-2 py-1 rounded-full">
                    <Text className="text-xs font-medium text-green-800">Recommended</Text>
                </View>
            );
        }

        if (product.isFeatured) {
            badges.push(
                <View key="featured" className="bg-blue-100 px-2 py-1 rounded-full">
                    <Text className="text-xs font-medium text-blue-800">Featured</Text>
                </View>
            );
        }

        if (product.originalPrice && product.originalPrice > product.price) {
            const discount = Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);
            badges.push(
                <View key="discount" className="bg-red-100 px-2 py-1 rounded-full">
                    <Text className="text-xs font-medium text-red-800">{discount}% OFF</Text>
                </View>
            );
        }

        return badges.length > 0 ? (
            <View className="flex-row flex-wrap gap-1 mb-2">
                {badges}
            </View>
        ) : null;
    };

    if (variant === 'list') {
        return (
            <Pressable
                onPress={handlePress}
                className="bg-white rounded-xl p-4 mb-3 shadow-sm border border-gray-100 active:opacity-80"
                accessibilityRole="button"
                accessibilityLabel={`${product.name}, ${formatPrice(product.price)}`}
                accessibilityHint="Tap to view product details"
            >
                <View className="flex-row gap-4">
                    <Image
                        source={{ uri: product.imageUrls[0] }}
                        className="w-20 h-20 rounded-lg bg-gray-100"
                        resizeMode="cover"
                    />

                    <View className="flex-1">
                        {renderBadges()}

                        <Text className="text-lg font-semibold text-gray-900 mb-1" numberOfLines={2}>
                            {product.name}
                        </Text>

                        <Text className="text-sm text-gray-600 mb-2" numberOfLines={2}>
                            {product.description}
                        </Text>

                        {renderRating()}

                        <View className="flex-row items-center justify-between mt-2">
                            <View className="flex-row items-center gap-2">
                                <Text className="text-xl font-bold text-primary-600">
                                    {formatPrice(product.price)}
                                </Text>
                                {product.originalPrice && product.originalPrice > product.price && (
                                    <Text className="text-sm text-gray-500 line-through">
                                        {formatPrice(product.originalPrice)}
                                    </Text>
                                )}
                            </View>

                            {showAddToCart && (
                                <Button
                                    title="Add to Cart"
                                    onPress={handleAddToCart}
                                    size="small"
                                    variant="primary"
                                    disabled={product.stockQuantity === 0}
                                    voiceFeedbackEnabled={voiceFeedbackEnabled}
                                    onVoiceFeedback={onVoiceFeedback}
                                />
                            )}
                        </View>
                    </View>
                </View>

                {product.stockQuantity === 0 && (
                    <View className="absolute top-4 right-4 bg-red-100 px-2 py-1 rounded-full">
                        <Text className="text-xs font-medium text-red-800">Out of Stock</Text>
                    </View>
                )}
            </Pressable>
        );
    }

    // Grid variant
    return (
        <Pressable
            onPress={handlePress}
            className="bg-white rounded-xl p-4 shadow-sm border border-gray-100 active:opacity-80"
            style={{ width: '48%' }}
            accessibilityRole="button"
            accessibilityLabel={`${product.name}, ${formatPrice(product.price)}`}
            accessibilityHint="Tap to view product details"
        >
            <View className="relative mb-3">
                <Image
                    source={{ uri: product.imageUrls[0] }}
                    className="w-full h-32 rounded-lg bg-gray-100"
                    resizeMode="cover"
                />

                {product.stockQuantity === 0 && (
                    <View className="absolute inset-0 bg-black/50 rounded-lg items-center justify-center">
                        <Text className="text-white font-medium">Out of Stock</Text>
                    </View>
                )}
            </View>

            {renderBadges()}

            <Text className="text-base font-semibold text-gray-900 mb-1" numberOfLines={2}>
                {product.name}
            </Text>

            <Text className="text-sm text-gray-600 mb-2" numberOfLines={2}>
                {product.description}
            </Text>

            {renderRating()}

            <View className="flex-row items-center justify-between mt-2 mb-3">
                <View>
                    <Text className="text-lg font-bold text-primary-600">
                        {formatPrice(product.price)}
                    </Text>
                    {product.originalPrice && product.originalPrice > product.price && (
                        <Text className="text-xs text-gray-500 line-through">
                            {formatPrice(product.originalPrice)}
                        </Text>
                    )}
                </View>
            </View>

            {showAddToCart && (
                <Button
                    title="Add to Cart"
                    onPress={handleAddToCart}
                    size="small"
                    variant="primary"
                    fullWidth
                    disabled={product.stockQuantity === 0}
                    voiceFeedbackEnabled={voiceFeedbackEnabled}
                    onVoiceFeedback={onVoiceFeedback}
                />
            )}
        </Pressable>
    );
};