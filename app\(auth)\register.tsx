import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { AppProvider, useApp } from '../../src/contexts/AppContext';
import { Button } from '../../src/components/ui/Button';
import { Input } from '../../src/components/ui/Input';
import { useFormValidation, ValidationRules } from '../../src/hooks/useFormValidation';
import { useAuthStore } from '../../src/stores/auth';

const RegisterContent: React.FC = () => {
  const { isRTL, isVoiceEnabled, speak } = useApp();
  const [isLoading, setIsLoading] = useState(false);
  const [passwordValue, setPasswordValue] = useState('');

  const validationRules: ValidationRules = {
    firstName: { required: true, minLength: 2, maxLength: 50 },
    lastName: { required: true, minLength: 2, maxLength: 50 },
    email: { required: true, pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ },
    phone: { required: true, pattern: /^[\+]?[(]?[0-9]{1,4}[)]?[-\s\./0-9]*$/ },
    password: { required: true, minLength: 6 },
    confirmPassword: {
      required: true,
      custom: (value: string) => (value !== passwordValue ? 'كلمتا المرور غير متطابقتين' : null),
    },
  };

  const { formState, getFieldProps, updateField, validateForm } = useFormValidation(
    {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
    },
    validationRules
  );

  useEffect(() => {
    if (isVoiceEnabled) {
      speak('إنشاء حساب بالبريد الإلكتروني');
    }
  }, [isVoiceEnabled]);

  const handleRegister = async () => {
    if (!validateForm()) {
      if (isVoiceEnabled) speak('تحقق من الحقول المطلوبة');
      return;
    }

    setIsLoading(true);
    try {
      const ok = await useAuthStore.getState().register({
        email: formState.email.value,
        phone: formState.phone.value,
        password: formState.password.value,
        firstName: formState.firstName.value,
        lastName: formState.lastName.value,
      });

      if (!ok) {
        Alert.alert('خطأ في التسجيل', 'تعذر إكمال التسجيل');
        if (isVoiceEnabled) speak('تعذر إكمال التسجيل');
        return;
      }

      if (isVoiceEnabled) speak('تم إنشاء الحساب بنجاح');
      router.replace('/(tabs)');
    } catch (e: any) {
      Alert.alert('خطأ', e?.message || 'حدث خطأ غير متوقع');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => router.back();

  return (
    <SafeAreaView className={`flex-1 bg-earth-50 ${isRTL ? 'rtl' : 'ltr'}`}>
      <StatusBar style="dark" />
      <KeyboardAvoidingView
        className="flex-1"
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <ScrollView
          className="flex-1"
          contentContainerStyle={{ flexGrow: 1 }}
          keyboardShouldPersistTaps="handled">
          <View className="flex-1 px-6 py-8">
            {/* Header */}
            <View className="mb-8">
              <Text
                className={`mb-2 text-3xl font-bold text-earth-900 ${isRTL ? 'text-right' : 'text-left'}`}>
                إنشاء حساب
              </Text>
              <Text className={`text-lg text-earth-600 ${isRTL ? 'text-right' : 'text-left'}`}>
                سجّل باستخدام بريدك الإلكتروني وأضف رقم هاتفك للتواصل.
              </Text>
            </View>

            {/* Form Fields */}
            <View className="gap-6">
              <Input
                label="الاسم الأول"
                placeholder="الاسم الأول"
                {...getFieldProps('firstName')}
                autoCapitalize="words"
                voiceFeedbackEnabled={isVoiceEnabled}
                onVoiceFeedback={speak}
              />
              <Input
                label="اسم العائلة"
                placeholder="اسم العائلة"
                {...getFieldProps('lastName')}
                autoCapitalize="words"
                voiceFeedbackEnabled={isVoiceEnabled}
                onVoiceFeedback={speak}
              />
              <Input
                label="البريد الإلكتروني"
                placeholder="<EMAIL>"
                {...getFieldProps('email')}
                keyboardType="email-address"
                autoCapitalize="none"
                voiceFeedbackEnabled={isVoiceEnabled}
                onVoiceFeedback={speak}
              />
              <Input
                label="رقم الهاتف"
                placeholder="****** 123 4567"
                {...getFieldProps('phone')}
                keyboardType="phone-pad"
                autoCapitalize="none"
                voiceFeedbackEnabled={isVoiceEnabled}
                onVoiceFeedback={speak}
              />

              {/* Password */}
              <Input
                label="كلمة المرور"
                placeholder="••••••••"
                value={formState.password.value}
                onChangeText={(v) => {
                  updateField('password', v);
                  setPasswordValue(v);
                }}
                secureTextEntry
                autoCapitalize="none"
                voiceFeedbackEnabled={isVoiceEnabled}
                onVoiceFeedback={speak}
                error={formState.password.touched ? formState.password.error : null}
              />

              {/* Confirm Password */}
              <Input
                label="تأكيد كلمة المرور"
                placeholder="••••••••"
                {...getFieldProps('confirmPassword')}
                secureTextEntry
                autoCapitalize="none"
                voiceFeedbackEnabled={isVoiceEnabled}
                onVoiceFeedback={speak}
              />
            </View>

            {/* Spacer */}
            <View className="flex-1" />

            {/* Actions */}
            <View className={`mt-8 flex-row gap-4 ${isRTL ? 'gap-reverse flex-row-reverse' : ''}`}>
              <Button
                title="رجوع"
                onPress={handleBack}
                variant="outline"
                size="large"
                className="flex-1"
                voiceFeedbackEnabled={isVoiceEnabled}
                onVoiceFeedback={speak}
              />
              <Button
                title="إنشاء الحساب"
                onPress={handleRegister}
                variant="primary"
                size="large"
                className="flex-1"
                loading={isLoading}
                voiceFeedbackEnabled={isVoiceEnabled}
                onVoiceFeedback={speak}
              />
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default function Register() {
  return (
    <AppProvider>
      <RegisterContent />
    </AppProvider>
  );
}
