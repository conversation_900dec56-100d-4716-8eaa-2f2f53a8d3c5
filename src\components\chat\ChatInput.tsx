import React, { useState, useRef } from 'react';
import { View, TextInput, Pressable, Text, Animated, Alert } from 'react-native';
import { Input } from '../ui/Input';

interface ChatInputProps {
    onSendMessage: (message: string) => void;
    onSendImage: (imageUri: string) => void;
    onVoiceInput: () => void;
    disabled?: boolean;
    voiceEnabled?: boolean;
    isRecording?: boolean;
    isLoading?: boolean;
    placeholder?: string;
}

export const ChatInput: React.FC<ChatInputProps> = ({
    onSendMessage,
    onSendImage,
    onVoiceInput,
    disabled = false,
    voiceEnabled = false,
    isRecording = false,
    isLoading = false,
    placeholder = "اسأل عن محاصيلك، التربة، أو الزراعة...",
}) => {
    const [message, setMessage] = useState('');
    const [isExpanded, setIsExpanded] = useState(false);

    // Voice recording animation
    const recordingAnimation = useRef(new Animated.Value(1)).current;
    const waveformAnimation = useRef(new Animated.Value(0)).current;

    React.useEffect(() => {
        if (isRecording) {
            // Pulsing animation for recording button
            Animated.loop(
                Animated.sequence([
                    Animated.timing(recordingAnimation, {
                        toValue: 1.2,
                        duration: 800,
                        useNativeDriver: true,
                    }),
                    Animated.timing(recordingAnimation, {
                        toValue: 1,
                        duration: 800,
                        useNativeDriver: true,
                    }),
                ])
            ).start();

            // Waveform animation
            Animated.loop(
                Animated.sequence([
                    Animated.timing(waveformAnimation, {
                        toValue: 1,
                        duration: 300,
                        useNativeDriver: true,
                    }),
                    Animated.timing(waveformAnimation, {
                        toValue: 0,
                        duration: 300,
                        useNativeDriver: true,
                    }),
                ])
            ).start();
        } else {
            recordingAnimation.setValue(1);
            waveformAnimation.setValue(0);
        }
    }, [isRecording]);

    const handleSend = () => {
        if (message.trim() && !disabled && !isLoading) {
            onSendMessage(message.trim());
            setMessage('');
            setIsExpanded(false);
        }
    };

    const handleCameraPress = () => {
        Alert.alert(
            'Add Photo',
            'Choose how to add a photo for analysis',
            [
                { text: 'Camera', onPress: () => handleImageCapture('camera') },
                { text: 'Gallery', onPress: () => handleImageCapture('gallery') },
                { text: 'Cancel', style: 'cancel' },
            ]
        );
    };

    const handleImageCapture = (source: 'camera' | 'gallery') => {
        // This would integrate with expo-image-picker
        // For now, we'll simulate with a placeholder
        const mockImageUri = `mock-image-${Date.now()}.jpg`;
        onSendImage(mockImageUri);
    };

    const renderWaveform = () => {
        if (!isRecording) return null;

        return (
            <View className="flex-row items-center justify-center gap-1 py-2">
                {[...Array(5)].map((_, index) => (
                    <Animated.View
                        key={index}
                        style={{
                            transform: [{
                                scaleY: waveformAnimation.interpolate({
                                    inputRange: [0, 1],
                                    outputRange: [0.3, 1.5 + Math.random() * 0.5],
                                }),
                            }],
                        }}
                        className="w-1 h-6 bg-red-500 rounded-full mx-0.5"
                    />
                ))}
            </View>
        );
    };

    return (
        <View className="bg-white border-t border-earth-200 px-4 py-3 shadow-lg">
            {/* Voice Recording Waveform */}
            {isRecording && (
                <View className="mb-3 bg-red-50 rounded-xl p-3">
                    <View className="flex-row items-center justify-between mb-2">
                        <Text className="text-red-600 font-medium">🔴 Recording...</Text>
                        <Text className="text-red-500 text-sm">Tap to stop</Text>
                    </View>
                    {renderWaveform()}
                </View>
            )}

            <View className="flex-row items-end gap-3">
                {/* Text Input */}
                <View className="flex-1">
                    <TextInput
                        value={message}
                        onChangeText={setMessage}
                        placeholder={placeholder}
                        placeholderTextColor="#9ca3af"
                        multiline={isExpanded}
                        numberOfLines={isExpanded ? 4 : 1}
                        maxLength={500}
                        editable={!disabled && !isRecording}
                        onFocus={() => setIsExpanded(true)}
                        onBlur={() => !message && setIsExpanded(false)}
                        className={`
              bg-earth-50 border border-earth-200 rounded-2xl px-4 py-3
              text-base text-earth-900 min-h-[48px]
              ${isExpanded ? 'max-h-[120px]' : ''}
              ${disabled || isRecording ? 'opacity-50' : ''}
            `}
                        style={{ textAlignVertical: 'center' }}
                        accessibilityLabel="Chat message input"
                        accessibilityHint="Type your farming question here"
                    />
                </View>

                {/* Action Buttons */}
                <View className="flex-row gap-2">
                    {/* Camera Button */}
                    <Pressable
                        onPress={handleCameraPress}
                        disabled={disabled || isRecording}
                        className={`
              w-12 h-12 bg-secondary-100 rounded-full items-center justify-center
              active:bg-secondary-200
              ${disabled || isRecording ? 'opacity-50' : ''}
            `}
                        accessibilityRole="button"
                        accessibilityLabel="Add photo"
                        accessibilityHint="Take or select a photo for analysis">
                        <Text className="text-secondary-600 text-lg">📷</Text>
                    </Pressable>

                    {/* Voice Input Button */}
                    {voiceEnabled && (
                        <Animated.View
                            style={{
                                transform: [{ scale: recordingAnimation }],
                            }}>
                            <Pressable
                                onPress={onVoiceInput}
                                disabled={disabled}
                                className={`
                  w-12 h-12 rounded-full items-center justify-center
                  ${isRecording
                                        ? 'bg-red-100 active:bg-red-200'
                                        : 'bg-primary-100 active:bg-primary-200'
                                    }
                  ${disabled ? 'opacity-50' : ''}
                `}
                                accessibilityRole="button"
                                accessibilityLabel={isRecording ? 'Stop recording' : 'Start voice input'}
                                accessibilityHint="Use voice to ask your question">
                                <Text className={`text-lg ${isRecording ? 'text-red-600' : 'text-primary-600'}`}>
                                    {isRecording ? '⏹️' : '🎤'}
                                </Text>
                            </Pressable>
                        </Animated.View>
                    )}

                    {/* Send Button */}
                    <Pressable
                        onPress={handleSend}
                        disabled={!message.trim() || disabled || isLoading || isRecording}
                        className={`
              w-12 h-12 rounded-full items-center justify-center
              ${message.trim() && !disabled && !isLoading && !isRecording
                                ? 'bg-primary-600 active:bg-primary-700'
                                : 'bg-earth-200'
                            }
            `}
                        accessibilityRole="button"
                        accessibilityLabel="Send message"
                        accessibilityHint="Send your message to the AI assistant">
                        {isLoading ? (
                            <Text className="text-white text-sm">⏳</Text>
                        ) : (
                            <Text className={`text-lg ${message.trim() && !disabled && !isRecording ? 'text-white' : 'text-earth-500'
                                }`}>
                                ➤
                            </Text>
                        )}
                    </Pressable>
                </View>
            </View>

            {/* Character Counter */}
            {message.length > 400 && (
                <Text className="text-xs text-earth-500 text-right mt-1">
                    {message.length}/500
                </Text>
            )}
        </View>
    );
};