/**
 * Inventory Management Service
 * Handles stock tracking, inventory transactions, and low stock alerts
 */

import { supabase } from './supabase/client';
import { Database } from '../types/database';

type InventoryTransaction = Database['public']['Tables']['inventory_transactions']['Row'];
type InventoryTransactionInsert = Database['public']['Tables']['inventory_transactions']['Insert'];

export interface InventoryStatus {
    productId: string;
    currentStock: number;
    reservedStock: number;
    availableStock: number;
    lowStockThreshold: number;
    isLowStock: boolean;
    isOutOfStock: boolean;
    lastRestocked: Date | null;
    averageDailySales: number;
    estimatedDaysUntilOutOfStock: number | null;
}

export interface StockAdjustment {
    productId: string;
    variantId?: string;
    quantityChange: number;
    reason: string;
    notes?: string;
}

export interface InventoryAlert {
    id: string;
    productId: string;
    productName: string;
    alertType: 'low_stock' | 'out_of_stock' | 'overstock';
    currentStock: number;
    threshold: number;
    severity: 'low' | 'medium' | 'high' | 'critical';
    createdAt: Date;
}

export class InventoryManagerService {
    /**
     * Get inventory status for a product
     */
    async getInventoryStatus(productId: string): Promise<InventoryStatus | null> {
        try {
            // Get current product stock
            const { data: product, error: productError } = await supabase
                .from('products')
                .select('id, name, stock_quantity, created_at')
                .eq('id', productId)
                .single();

            if (productError || !product) {
                return null;
            }

            // Get reserved stock (items in pending orders)
            const { data: reservedItems } = await supabase
                .from('order_items')
                .select(`
                    quantity,
                    order:order_id!inner(status)
                `)
                .eq('product_id', productId)
                .in('order.status', ['pending', 'confirmed', 'processing']);

            const reservedStock = reservedItems?.reduce((total, item) => total + item.quantity, 0) || 0;
            const availableStock = Math.max(0, product.stock_quantity - reservedStock);

            // Calculate average daily sales (last 30 days)
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            const { data: salesData } = await supabase
                .from('order_items')
                .select(`
                    quantity,
                    order:order_id!inner(status, created_at)
                `)
                .eq('product_id', productId)
                .eq('order.status', 'delivered')
                .gte('order.created_at', thirtyDaysAgo.toISOString());

            const totalSold = salesData?.reduce((total, item) => total + item.quantity, 0) || 0;
            const averageDailySales = totalSold / 30;

            // Get last restock date
            const { data: lastRestock } = await supabase
                .from('inventory_transactions')
                .select('created_at')
                .eq('product_id', productId)
                .eq('transaction_type', 'purchase')
                .order('created_at', { ascending: false })
                .limit(1);

            // Calculate estimated days until out of stock
            let estimatedDaysUntilOutOfStock: number | null = null;
            if (averageDailySales > 0 && availableStock > 0) {
                estimatedDaysUntilOutOfStock = Math.floor(availableStock / averageDailySales);
            }

            const lowStockThreshold = this.calculateLowStockThreshold(averageDailySales);

            return {
                productId: product.id,
                currentStock: product.stock_quantity,
                reservedStock,
                availableStock,
                lowStockThreshold,
                isLowStock: availableStock <= lowStockThreshold && availableStock > 0,
                isOutOfStock: availableStock === 0,
                lastRestocked: lastRestock?.[0] ? new Date(lastRestock[0].created_at) : null,
                averageDailySales,
                estimatedDaysUntilOutOfStock,
            };
        } catch (error) {
            console.error('Error getting inventory status:', error);
            return null;
        }
    }

    /**
     * Get inventory status for multiple products
     */
    async getBulkInventoryStatus(productIds: string[]): Promise<Record<string, InventoryStatus>> {
        const statuses: Record<string, InventoryStatus> = {};

        // Process in batches to avoid overwhelming the database
        const batchSize = 10;
        for (let i = 0; i < productIds.length; i += batchSize) {
            const batch = productIds.slice(i, i + batchSize);
            const batchPromises = batch.map(id => this.getInventoryStatus(id));
            const batchResults = await Promise.all(batchPromises);

            batchResults.forEach((status, index) => {
                if (status) {
                    statuses[batch[index]] = status;
                }
            });
        }

        return statuses;
    }

    /**
     * Adjust stock levels
     */
    async adjustStock(adjustments: StockAdjustment[]): Promise<void> {
        try {
            for (const adjustment of adjustments) {
                // Get current stock
                const { data: product, error: productError } = await supabase
                    .from('products')
                    .select('stock_quantity')
                    .eq('id', adjustment.productId)
                    .single();

                if (productError || !product) {
                    throw new Error(`Product ${adjustment.productId} not found`);
                }

                const previousQuantity = product.stock_quantity;
                const newQuantity = Math.max(0, previousQuantity + adjustment.quantityChange);

                // Update product stock
                const { error: updateError } = await supabase
                    .from('products')
                    .update({ stock_quantity: newQuantity })
                    .eq('id', adjustment.productId);

                if (updateError) {
                    throw new Error(`Failed to update stock for ${adjustment.productId}: ${updateError.message}`);
                }

                // Record inventory transaction
                const transaction: InventoryTransactionInsert = {
                    product_id: adjustment.productId,
                    variant_id: adjustment.variantId || null,
                    transaction_type: adjustment.quantityChange > 0 ? 'purchase' : 'adjustment',
                    quantity_change: adjustment.quantityChange,
                    previous_quantity: previousQuantity,
                    new_quantity: newQuantity,
                    reference_type: 'manual_adjustment',
                    notes: `${adjustment.reason}${adjustment.notes ? ` - ${adjustment.notes}` : ''}`,
                };

                const { error: transactionError } = await supabase
                    .from('inventory_transactions')
                    .insert(transaction);

                if (transactionError) {
                    console.error('Failed to record inventory transaction:', transactionError);
                }
            }
        } catch (error) {
            console.error('Error adjusting stock:', error);
            throw error;
        }
    }

    /**
     * Get inventory alerts
     */
    async getInventoryAlerts(): Promise<InventoryAlert[]> {
        try {
            const { data: products, error } = await supabase
                .from('products')
                .select('id, name, stock_quantity')
                .eq('is_active', true);

            if (error) {
                throw new Error(`Failed to get products: ${error.message}`);
            }

            const alerts: InventoryAlert[] = [];

            for (const product of products || []) {
                const status = await this.getInventoryStatus(product.id);
                if (!status) continue;

                // Out of stock alert
                if (status.isOutOfStock) {
                    alerts.push({
                        id: `${product.id}_out_of_stock`,
                        productId: product.id,
                        productName: product.name,
                        alertType: 'out_of_stock',
                        currentStock: status.currentStock,
                        threshold: 0,
                        severity: 'critical',
                        createdAt: new Date(),
                    });
                }
                // Low stock alert
                else if (status.isLowStock) {
                    const severity = this.calculateAlertSeverity(
                        status.availableStock,
                        status.lowStockThreshold,
                        status.estimatedDaysUntilOutOfStock
                    );

                    alerts.push({
                        id: `${product.id}_low_stock`,
                        productId: product.id,
                        productName: product.name,
                        alertType: 'low_stock',
                        currentStock: status.currentStock,
                        threshold: status.lowStockThreshold,
                        severity,
                        createdAt: new Date(),
                    });
                }
                // Overstock alert (more than 90 days of inventory)
                else if (status.estimatedDaysUntilOutOfStock && status.estimatedDaysUntilOutOfStock > 90) {
                    alerts.push({
                        id: `${product.id}_overstock`,
                        productId: product.id,
                        productName: product.name,
                        alertType: 'overstock',
                        currentStock: status.currentStock,
                        threshold: Math.ceil(status.averageDailySales * 90),
                        severity: 'low',
                        createdAt: new Date(),
                    });
                }
            }

            // Sort by severity and creation date
            return alerts.sort((a, b) => {
                const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
                const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];
                if (severityDiff !== 0) return severityDiff;
                return b.createdAt.getTime() - a.createdAt.getTime();
            });
        } catch (error) {
            console.error('Error getting inventory alerts:', error);
            return [];
        }
    }

    /**
     * Get inventory transactions history
     */
    async getInventoryTransactions(
        productId?: string,
        limit: number = 50
    ): Promise<InventoryTransaction[]> {
        try {
            let query = supabase
                .from('inventory_transactions')
                .select('*')
                .order('created_at', { ascending: false })
                .limit(limit);

            if (productId) {
                query = query.eq('product_id', productId);
            }

            const { data, error } = await query;

            if (error) {
                throw new Error(`Failed to get inventory transactions: ${error.message}`);
            }

            return data || [];
        } catch (error) {
            console.error('Error getting inventory transactions:', error);
            return [];
        }
    }

    /**
     * Generate inventory report
     */
    async generateInventoryReport(): Promise<{
        totalProducts: number;
        totalValue: number;
        lowStockProducts: number;
        outOfStockProducts: number;
        overstockProducts: number;
        topSellingProducts: Array<{
            productId: string;
            productName: string;
            unitsSold: number;
            revenue: number;
        }>;
        slowMovingProducts: Array<{
            productId: string;
            productName: string;
            daysSinceLastSale: number;
            currentStock: number;
        }>;
    }> {
        try {
            // Get all active products
            const { data: products, error: productsError } = await supabase
                .from('products')
                .select('id, name, price, stock_quantity')
                .eq('is_active', true);

            if (productsError) {
                throw new Error(`Failed to get products: ${productsError.message}`);
            }

            const totalProducts = products?.length || 0;
            const totalValue = products?.reduce((total, product) =>
                total + (product.price * product.stock_quantity), 0) || 0;

            // Get inventory statuses
            const productIds = products?.map(p => p.id) || [];
            const inventoryStatuses = await this.getBulkInventoryStatus(productIds);

            let lowStockProducts = 0;
            let outOfStockProducts = 0;
            let overstockProducts = 0;

            Object.values(inventoryStatuses).forEach(status => {
                if (status.isOutOfStock) outOfStockProducts++;
                else if (status.isLowStock) lowStockProducts++;
                else if (status.estimatedDaysUntilOutOfStock && status.estimatedDaysUntilOutOfStock > 90) {
                    overstockProducts++;
                }
            });

            // Get top selling products (last 30 days)
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            const { data: salesData } = await supabase
                .from('order_items')
                .select(`
                    product_id,
                    quantity,
                    unit_price,
                    product:product_id!inner(name),
                    order:order_id!inner(status, created_at)
                `)
                .eq('order.status', 'delivered')
                .gte('order.created_at', thirtyDaysAgo.toISOString());

            const productSales: Record<string, {
                name: string;
                unitsSold: number;
                revenue: number;
            }> = {};

            salesData?.forEach(item => {
                const productId = item.product_id;
                if (!productSales[productId]) {
                    productSales[productId] = {
                        name: (item.product as any).name,
                        unitsSold: 0,
                        revenue: 0,
                    };
                }
                productSales[productId].unitsSold += item.quantity;
                productSales[productId].revenue += item.quantity * item.unit_price;
            });

            const topSellingProducts = Object.entries(productSales)
                .map(([productId, data]) => ({
                    productId,
                    productName: data.name,
                    unitsSold: data.unitsSold,
                    revenue: data.revenue,
                }))
                .sort((a, b) => b.unitsSold - a.unitsSold)
                .slice(0, 10);

            // Get slow moving products (no sales in last 60 days)
            const sixtyDaysAgo = new Date();
            sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

            const { data: recentSales } = await supabase
                .from('order_items')
                .select('product_id')
                .gte('created_at', sixtyDaysAgo.toISOString());

            const recentlySoldProductIds = new Set(recentSales?.map(item => item.product_id) || []);

            const slowMovingProducts = products
                ?.filter(product => !recentlySoldProductIds.has(product.id) && product.stock_quantity > 0)
                .map(product => ({
                    productId: product.id,
                    productName: product.name,
                    daysSinceLastSale: 60, // Simplified - in production, calculate exact days
                    currentStock: product.stock_quantity,
                }))
                .slice(0, 10) || [];

            return {
                totalProducts,
                totalValue: Math.round(totalValue * 100) / 100,
                lowStockProducts,
                outOfStockProducts,
                overstockProducts,
                topSellingProducts,
                slowMovingProducts,
            };
        } catch (error) {
            console.error('Error generating inventory report:', error);
            throw error;
        }
    }

    /**
     * Reserve stock for pending orders
     */
    async reserveStock(productId: string, quantity: number, orderId: string): Promise<boolean> {
        try {
            const status = await this.getInventoryStatus(productId);
            if (!status || status.availableStock < quantity) {
                return false;
            }

            // Record reservation transaction
            const { data: product } = await supabase
                .from('products')
                .select('stock_quantity')
                .eq('id', productId)
                .single();

            if (!product) return false;

            const transaction: InventoryTransactionInsert = {
                product_id: productId,
                transaction_type: 'sale',
                quantity_change: -quantity,
                previous_quantity: product.stock_quantity,
                new_quantity: product.stock_quantity, // Stock doesn't change, just reserved
                reference_id: orderId,
                reference_type: 'order',
                notes: `Stock reserved for order ${orderId}`,
            };

            const { error } = await supabase
                .from('inventory_transactions')
                .insert(transaction);

            if (error) {
                console.error('Failed to record stock reservation:', error);
            }

            return true;
        } catch (error) {
            console.error('Error reserving stock:', error);
            return false;
        }
    }

    // Private helper methods

    private calculateLowStockThreshold(averageDailySales: number): number {
        // Default to 7 days of inventory as low stock threshold
        const daysOfInventory = 7;
        return Math.max(5, Math.ceil(averageDailySales * daysOfInventory));
    }

    private calculateAlertSeverity(
        currentStock: number,
        threshold: number,
        daysUntilOutOfStock: number | null
    ): 'low' | 'medium' | 'high' | 'critical' {
        if (currentStock === 0) return 'critical';
        if (daysUntilOutOfStock && daysUntilOutOfStock <= 2) return 'critical';
        if (daysUntilOutOfStock && daysUntilOutOfStock <= 5) return 'high';
        if (currentStock <= threshold * 0.5) return 'high';
        if (currentStock <= threshold * 0.75) return 'medium';
        return 'low';
    }
}

export const inventoryManagerService = new InventoryManagerService();