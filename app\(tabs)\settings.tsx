import React, { useState } from 'react';
import { 
  View, 
  Text, 
  ScrollView, 
  TouchableOpacity, 
  Switch, 
  Alert,
  Linking 
} from 'react-native';
import {
  Ionicons,
  MaterialIcons,
  Feather 
} from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { AuthService } from '../../src/services/supabase/auth';

export default function SettingsScreen() {
  const router = useRouter();
  const [pushNotifications, setPushNotifications] = useState(true);
  const [weatherAlerts, setWeatherAlerts] = useState(true);
  const [taskReminders, setTaskReminders] = useState(true);
  const [voiceMode, setVoiceMode] = useState(false);
  const [offlineMode, setOfflineMode] = useState(false);
  const [darkMode, setDarkMode] = useState(false);

  const handleLogout = () => {
    Alert.alert(
      "Confirm Logout",
      "Are you sure you want to log out?",
      [
        { text: "Cancel", style: "cancel" },
        { text: "Logout", style: "destructive", onPress: async () => {
          try {
            const { error } = await AuthService.logout();
            if (error) {
              Alert.alert("Logout Error", error.message);
              return;
            }
            console.log("User logged out successfully");
            router.replace('/(auth)/login');
          } catch (err) {
            console.error("Logout failed:", err);
            Alert.alert("Logout Error", "An unexpected error occurred");
          }
        }}
      ]
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      "Delete Account",
      "This action cannot be undone. All your data will be permanently deleted.",
      [
        { text: "Cancel", style: "cancel" },
        { text: "Delete", style: "destructive", onPress: () => {
          // Implement account deletion logic here
          console.log("Account deletion requested");
        }}
      ]
    );
  };

  const openURL = (url: string) => {
    Linking.openURL(url);
  };

  const SettingsSection = ({ title, children }: { title: string; children: React.ReactNode }) => (
    <View className="mb-6">
      <Text className="text-lg font-semibold text-gray-800 mb-3 px-4">
        {title}
      </Text>
      <View className="bg-white rounded-lg mx-4 shadow-sm">
        {children}
      </View>
    </View>
  );

  const SettingsItem = ({ 
    icon, 
    iconLibrary = 'Ionicons', 
    title, 
    subtitle, 
    onPress, 
    showArrow = true, 
    rightComponent 
  }: {
    icon: string;
    iconLibrary?: 'Ionicons' | 'MaterialIcons' | 'Feather';
    title: string;
    subtitle?: string;
    onPress?: () => void;
    showArrow?: boolean;
    rightComponent?: React.ReactNode;
  }) => {
    const IconComponent = iconLibrary === 'MaterialIcons' ? MaterialIcons : 
                         iconLibrary === 'Feather' ? Feather : Ionicons;
    
    return (
      <TouchableOpacity 
        className="flex-row items-center py-4 px-4 border-b border-gray-100 last:border-b-0"
        onPress={onPress}
        disabled={!onPress}
      >
        <View className="w-8 h-8 items-center justify-center mr-3">
          <IconComponent name={icon} size={24} color="#059669" />
        </View>
        
        <View className="flex-1">
          <Text className="text-base font-medium text-gray-900">{title}</Text>
          {subtitle && (
            <Text className="text-sm text-gray-500 mt-1">{subtitle}</Text>
          )}
        </View>
        
        {rightComponent || (showArrow && (
          <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
        ))}
      </TouchableOpacity>
    );
  };

  const ToggleItem = ({ 
    icon, 
    title, 
    subtitle, 
    value, 
    onToggle 
  }: {
    icon: string;
    title: string;
    subtitle?: string;
    value: boolean;
    onToggle: (value: boolean) => void;
  }) => (
    <SettingsItem
      icon={icon}
      title={title}
      subtitle={subtitle}
      showArrow={false}
      rightComponent={
        <Switch
          value={value}
          onValueChange={onToggle}
          trackColor={{ false: '#D1D5DB', true: '#10B981' }}
          thumbColor={value ? '#FFFFFF' : '#FFFFFF'}
        />
      }
    />
  );

  return (
    <ScrollView className="flex-1 bg-gray-50">
      {/* Header */}
      <View className="bg-green-600 pt-12 pb-6 px-4">
        <Text className="text-2xl font-bold text-white">Settings</Text>
        <Text className="text-green-100 mt-1">Manage your farming assistant</Text>
      </View>

      {/* Account Section */}
      <SettingsSection title="Account">
        <SettingsItem
          icon="person-outline"
          title="Profile"
          subtitle="Edit your personal information"
          onPress={() => console.log('Navigate to Profile')}
        />
        <SettingsItem
          icon="card-outline"
          title="Subscription"
          subtitle="Manage your premium plan"
          onPress={() => console.log('Navigate to Subscription')}
        />
        <SettingsItem
          icon="trophy-outline"
          title="Points & Rewards"
          subtitle="View your farming points"
          onPress={() => console.log('Navigate to Points')}
        />
      </SettingsSection>

      {/* Notifications Section */}
      <SettingsSection title="Notifications">
        <ToggleItem
          icon="notifications-outline"
          title="Push Notifications"
          subtitle="General app notifications"
          value={pushNotifications}
          onToggle={setPushNotifications}
        />
        <ToggleItem
          icon="cloud-outline"
          title="Weather Alerts"
          subtitle="Important weather updates"
          value={weatherAlerts}
          onToggle={setWeatherAlerts}
        />
        <ToggleItem
          icon="time-outline"
          title="Task Reminders"
          subtitle="Daily and weekly farming tasks"
          value={taskReminders}
          onToggle={setTaskReminders}
        />
      </SettingsSection>

      {/* Accessibility Section */}
      <SettingsSection title="Accessibility">
        <ToggleItem
          icon="volume-high-outline"
          title="Voice Mode"
          subtitle="Enable text-to-speech throughout the app"
          value={voiceMode}
          onToggle={setVoiceMode}
        />
        <SettingsItem
          icon="language-outline"
          title="Language"
          subtitle="English"
          onPress={() => console.log('Navigate to Language Settings')}
        />
        <SettingsItem
          icon="text-outline"
          title="Font Size"
          subtitle="Adjust text size for better readability"
          onPress={() => console.log('Navigate to Font Settings')}
        />
      </SettingsSection>

      {/* App Preferences Section */}
      <SettingsSection title="App Preferences">
        <ToggleItem
          icon="moon-outline"
          title="Dark Mode"
          subtitle="Switch to dark theme"
          value={darkMode}
          onToggle={setDarkMode}
        />
        <ToggleItem
          icon="cloud-offline-outline"
          title="Offline Mode"
          subtitle="Save data for offline use"
          value={offlineMode}
          onToggle={setOfflineMode}
        />
        <SettingsItem
          icon="location-outline"
          title="Location Services"
          subtitle="Manage GPS and weather integration"
          onPress={() => console.log('Navigate to Location Settings')}
        />
      </SettingsSection>

      {/* Data & Storage Section */}
      <SettingsSection title="Data & Storage">
        <SettingsItem
          icon="download-outline"
          title="Download Data"
          subtitle="Export your farming data"
          onPress={() => console.log('Export data')}
        />
        <SettingsItem
          icon="trash-outline"
          title="Clear Cache"
          subtitle="Free up storage space"
          onPress={() => {
            Alert.alert(
              "Clear Cache",
              "This will clear temporary files and free up storage space.",
              [
                { text: "Cancel", style: "cancel" },
                { text: "Clear", onPress: () => console.log("Cache cleared") }
              ]
            );
          }}
        />
        <SettingsItem
          icon="sync-outline"
          title="Sync Settings"
          subtitle="Backup and sync across devices"
          onPress={() => console.log('Navigate to Sync Settings')}
        />
      </SettingsSection>

      {/* Support Section */}
      <SettingsSection title="Support & Info">
        <SettingsItem
          icon="help-circle-outline"
          title="Help Center"
          subtitle="Get help and tutorials"
          onPress={() => console.log('Navigate to Help')}
        />
        <SettingsItem
          icon="chatbubble-outline"
          title="Contact Support"
          subtitle="Get assistance from our team"
          onPress={() => console.log('Navigate to Support')}
        />
        <SettingsItem
          icon="star-outline"
          title="Rate App"
          subtitle="Rate us on the app store"
          onPress={() => openURL('https://apps.apple.com')}
        />
        <SettingsItem
          icon="information-circle-outline"
          title="About"
          subtitle="Version 1.0.0"
          onPress={() => console.log('Navigate to About')}
        />
      </SettingsSection>

      {/* Legal Section */}
      <SettingsSection title="Legal">
        <SettingsItem
          icon="document-text-outline"
          title="Terms of Service"
          onPress={() => openURL('https://yourapp.com/terms')}
        />
        <SettingsItem
          icon="shield-outline"
          title="Privacy Policy"
          onPress={() => openURL('https://yourapp.com/privacy')}
        />
        <SettingsItem
          icon="lock-closed-outline"
          title="Data Protection"
          subtitle="Learn how we protect your data"
          onPress={() => console.log('Navigate to Data Protection')}
        />
      </SettingsSection>

      {/* Account Actions Section */}
      <SettingsSection title="Account Actions">
        <TouchableOpacity 
          className="flex-row items-center py-4 px-4 bg-white rounded-lg mx-4 mb-3"
          onPress={handleLogout}
        >
          <View className="w-8 h-8 items-center justify-center mr-3">
            <Ionicons name="log-out-outline" size={24} color="#DC2626" />
          </View>
          <Text className="text-base font-medium text-red-600 flex-1">
            Logout
          </Text>
        </TouchableOpacity>

        <TouchableOpacity 
          className="flex-row items-center py-4 px-4 bg-white rounded-lg mx-4"
          onPress={handleDeleteAccount}
        >
          <View className="w-8 h-8 items-center justify-center mr-3">
            <Ionicons name="trash-outline" size={24} color="#DC2626" />
          </View>
          <Text className="text-base font-medium text-red-600 flex-1">
            Delete Account
          </Text>
        </TouchableOpacity>
      </SettingsSection>

      {/* Footer spacing */}
      <View className="h-8" />
    </ScrollView>
  );
}