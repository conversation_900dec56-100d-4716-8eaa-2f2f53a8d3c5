import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, Pressable, Animated, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Button } from '../../src/components/ui/Button';
import { Input } from '../../src/components/ui/Input';
import { AppProvider, useApp } from '../../src/contexts/AppContext';

const NewPasswordContent: React.FC = () => {
    const { t, isRTL, isVoiceEnabled, speak } = useApp();
    const params = useLocalSearchParams<{ method: string; contact: string; resetCode: string }>();
    const [fadeAnim] = useState(new Animated.Value(0));
    const [slideAnim] = useState(new Animated.Value(50));

    // Form state
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    // Form validation
    const [errors, setErrors] = useState<{
        password?: string;
        confirmPassword?: string;
    }>({});

    // Password strength indicators
    const [passwordStrength, setPasswordStrength] = useState({
        hasMinLength: false,
        hasUpperCase: false,
        hasLowerCase: false,
        hasNumber: false,
        hasSpecialChar: false,
    });

    useEffect(() => {
        // Animate entrance
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 800,
                useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 800,
                useNativeDriver: true,
            }),
        ]).start();

        // Speak welcome message if voice is enabled
        if (isVoiceEnabled) {
            speak(t('newPassword.title') + '. ' + t('newPassword.description'));
        }
    }, [isVoiceEnabled]);

    useEffect(() => {
        // Update password strength indicators
        setPasswordStrength({
            hasMinLength: password.length >= 8,
            hasUpperCase: /[A-Z]/.test(password),
            hasLowerCase: /[a-z]/.test(password),
            hasNumber: /\d/.test(password),
            hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password),
        });
    }, [password]);

    const validateForm = () => {
        const newErrors: typeof errors = {};

        if (!password.trim()) {
            newErrors.password = t('newPassword.errors.passwordRequired');
        } else if (password.length < 8) {
            newErrors.password = t('newPassword.errors.passwordTooShort');
        } else if (!passwordStrength.hasUpperCase || !passwordStrength.hasLowerCase || !passwordStrength.hasNumber) {
            newErrors.password = t('newPassword.errors.passwordWeak');
        }

        if (!confirmPassword.trim()) {
            newErrors.confirmPassword = t('newPassword.errors.confirmPasswordRequired');
        } else if (password !== confirmPassword) {
            newErrors.confirmPassword = t('newPassword.errors.passwordMismatch');
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleResetPassword = async () => {
        if (!validateForm()) {
            if (isVoiceEnabled) {
                const errorMessages = Object.values(errors).join('. ');
                speak(t('newPassword.errors.validationFailed') + '. ' + errorMessages);
            }
            return;
        }

        setIsLoading(true);

        try {
            // TODO: Implement actual password reset
            await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call

            if (isVoiceEnabled) {
                speak(t('newPassword.success'));
            }

            Alert.alert(
                t('newPassword.successTitle'),
                t('newPassword.successMessage'),
                [
                    {
                        text: t('common.ok'),
                        onPress: () => {
                            // Navigate back to login
                            router.replace('/(auth)/login');
                        }
                    }
                ]
            );
        } catch (error) {
            if (isVoiceEnabled) {
                speak(t('newPassword.errors.resetFailed'));
            }
            Alert.alert(t('newPassword.errors.title'), t('newPassword.errors.resetFailed'));
        } finally {
            setIsLoading(false);
        }
    };

    const handleBack = () => {
        if (isVoiceEnabled) {
            speak(t('common.back'));
        }
        router.back();
    };

    const getPasswordStrengthColor = () => {
        const strengthCount = Object.values(passwordStrength).filter(Boolean).length;
        if (strengthCount < 2) return 'text-red-500';
        if (strengthCount < 4) return 'text-yellow-500';
        return 'text-green-500';
    };

    const getPasswordStrengthText = () => {
        const strengthCount = Object.values(passwordStrength).filter(Boolean).length;
        if (strengthCount < 2) return t('newPassword.strength.weak');
        if (strengthCount < 4) return t('newPassword.strength.medium');
        return t('newPassword.strength.strong');
    };

    return (
        <SafeAreaView className={`flex-1 bg-gradient-to-b from-primary-50 to-white ${isRTL ? 'rtl' : 'ltr'}`}>
            <StatusBar style="dark" />

            <ScrollView
                className="flex-1"
                contentContainerStyle={{ flexGrow: 1 }}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
            >
                <Animated.View
                    className="flex-1 px-6 py-8"
                    style={{
                        opacity: fadeAnim,
                        transform: [{ translateY: slideAnim }],
                    }}
                >
                    {/* Back Button */}
                    <Pressable
                        onPress={handleBack}
                        className={`mb-6 flex-row items-center ${isRTL ? 'flex-row-reverse' : ''}`}
                        accessibilityRole="button"
                        accessibilityLabel={t('common.back')}
                    >
                        <Text className={`text-2xl text-earth-600 ${isRTL ? 'ml-2' : 'mr-2'}`}>
                            {isRTL ? '→' : '←'}
                        </Text>
                        <Text className="text-base font-medium text-earth-600">
                            {t('common.back')}
                        </Text>
                    </Pressable>

                    {/* Header */}
                    <View className="items-center mb-8">
                        <View className="mb-6 h-20 w-20 items-center justify-center rounded-full bg-green-100">
                            <Text className="text-3xl">🔐</Text>
                        </View>

                        <Text className={`mb-2 text-center text-2xl font-bold text-earth-900 ${isRTL ? 'text-right' : 'text-left'}`}>
                            {t('newPassword.title')}
                        </Text>

                        <Text className={`text-center text-base text-earth-600 ${isRTL ? 'text-right' : 'text-left'}`}>
                            {t('newPassword.description')}
                        </Text>
                    </View>

                    {/* Password Form */}
                    <View className="gap-4 mb-6">
                        <Input
                            label={t('newPassword.newPassword')}
                            placeholder={t('newPassword.newPasswordPlaceholder')}
                            value={password}
                            onChangeText={setPassword}
                            error={errors.password}
                            secureTextEntry={!showPassword}
                            leftIcon={<Text className="text-lg text-earth-500">🔒</Text>}
                            rightIcon={
                                <Text className="text-lg text-earth-500">
                                    {showPassword ? '👁️' : '🙈'}
                                </Text>
                            }
                            onRightIconPress={() => setShowPassword(!showPassword)}
                            voiceInputEnabled={isVoiceEnabled}
                            voiceFeedbackEnabled={isVoiceEnabled}
                            onVoiceFeedback={speak}
                            accessibilityLabel={t('newPassword.newPassword')}
                            accessibilityHint={t('newPassword.newPasswordHint')}
                        />

                        {/* Password Strength Indicator */}
                        {password.length > 0 && (
                            <View className="rounded-xl bg-white p-4 shadow-sm">
                                <View className={`flex-row items-center justify-between mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                                    <Text className={`text-sm font-medium text-earth-700 ${isRTL ? 'text-right' : 'text-left'}`}>
                                        {t('newPassword.passwordStrength')}
                                    </Text>
                                    <Text className={`text-sm font-semibold ${getPasswordStrengthColor()}`}>
                                        {getPasswordStrengthText()}
                                    </Text>
                                </View>

                                <View className="gap-2">
                                    <View className={`flex-row items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                                        <Text className={`text-lg ${isRTL ? 'ml-2' : 'mr-2'}`}>
                                            {passwordStrength.hasMinLength ? '✅' : '❌'}
                                        </Text>
                                        <Text className={`text-sm text-earth-600 ${isRTL ? 'text-right' : 'text-left'}`}>
                                            {t('newPassword.requirements.minLength')}
                                        </Text>
                                    </View>

                                    <View className={`flex-row items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                                        <Text className={`text-lg ${isRTL ? 'ml-2' : 'mr-2'}`}>
                                            {passwordStrength.hasUpperCase ? '✅' : '❌'}
                                        </Text>
                                        <Text className={`text-sm text-earth-600 ${isRTL ? 'text-right' : 'text-left'}`}>
                                            {t('newPassword.requirements.uppercase')}
                                        </Text>
                                    </View>

                                    <View className={`flex-row items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                                        <Text className={`text-lg ${isRTL ? 'ml-2' : 'mr-2'}`}>
                                            {passwordStrength.hasLowerCase ? '✅' : '❌'}
                                        </Text>
                                        <Text className={`text-sm text-earth-600 ${isRTL ? 'text-right' : 'text-left'}`}>
                                            {t('newPassword.requirements.lowercase')}
                                        </Text>
                                    </View>

                                    <View className={`flex-row items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                                        <Text className={`text-lg ${isRTL ? 'ml-2' : 'mr-2'}`}>
                                            {passwordStrength.hasNumber ? '✅' : '❌'}
                                        </Text>
                                        <Text className={`text-sm text-earth-600 ${isRTL ? 'text-right' : 'text-left'}`}>
                                            {t('newPassword.requirements.number')}
                                        </Text>
                                    </View>
                                </View>
                            </View>
                        )}

                        <Input
                            label={t('newPassword.confirmPassword')}
                            placeholder={t('newPassword.confirmPasswordPlaceholder')}
                            value={confirmPassword}
                            onChangeText={setConfirmPassword}
                            error={errors.confirmPassword}
                            secureTextEntry={!showConfirmPassword}
                            leftIcon={<Text className="text-lg text-earth-500">🔒</Text>}
                            rightIcon={
                                <Text className="text-lg text-earth-500">
                                    {showConfirmPassword ? '👁️' : '🙈'}
                                </Text>
                            }
                            onRightIconPress={() => setShowConfirmPassword(!showConfirmPassword)}
                            voiceInputEnabled={isVoiceEnabled}
                            voiceFeedbackEnabled={isVoiceEnabled}
                            onVoiceFeedback={speak}
                            accessibilityLabel={t('newPassword.confirmPassword')}
                            accessibilityHint={t('newPassword.confirmPasswordHint')}
                        />
                    </View>

                    {/* Security Tips */}
                    <View className="mb-8 rounded-xl bg-blue-50 p-4">
                        <View className={`flex-row items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <Text className={`text-2xl ${isRTL ? 'ml-3' : 'mr-3'}`}>🛡️</Text>
                            <View className="flex-1">
                                <Text className={`text-sm font-medium text-blue-800 mb-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                                    {t('newPassword.securityTips.title')}
                                </Text>
                                <Text className={`text-sm text-blue-700 leading-relaxed ${isRTL ? 'text-right' : 'text-left'}`}>
                                    {t('newPassword.securityTips.description')}
                                </Text>
                            </View>
                        </View>
                    </View>

                    {/* Spacer */}
                    <View className="flex-1" />

                    {/* Reset Password Button */}
                    <Button
                        title={t('newPassword.resetPassword')}
                        onPress={handleResetPassword}
                        variant="primary"
                        size="large"
                        fullWidth
                        loading={isLoading}
                        disabled={isLoading}
                        accessibilityLabel={t('newPassword.resetPassword')}
                        accessibilityHint={t('newPassword.resetPasswordHint')}
                        voiceFeedbackEnabled={isVoiceEnabled}
                        onVoiceFeedback={speak}
                    />
                </Animated.View>
            </ScrollView>
        </SafeAreaView>
    );
};

export default function NewPassword() {
    return (
        <AppProvider>
            <NewPasswordContent />
        </AppProvider>
    );
}