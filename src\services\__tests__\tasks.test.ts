import { TaskService } from '../tasks';
import { LocationCoordinates } from '../location';

describe('TaskService', () => {
    let taskService: TaskService;
    const testLocation: LocationCoordinates = {
        latitude: 40.7128,
        longitude: -74.0060,
    };

    beforeEach(() => {
        taskService = TaskService.getInstance();
    });

    test('should get today\'s tasks', async () => {
        const tasks = await taskService.getTodaysTasks();
        expect(tasks).toBeDefined();
        expect(Array.isArray(tasks)).toBe(true);
    });

    test('should complete task with evidence', async () => {
        const tasks = await taskService.getTodaysTasks();
        if (tasks.length > 0) {
            const result = await taskService.completeTaskWithEvidence(tasks[0].id, {
                taskId: tasks[0].id,
                completedAt: new Date(),
                pointsEarned: tasks[0].pointsReward,
                imageEvidence: 'test-image-url',
            });

            expect(result.success).toBe(true);
            expect(result.pointsEarned).toBeGreaterThan(tasks[0].pointsReward); // Should include bonus
        }
    });

    test('should generate daily task summary', async () => {
        const summary = await taskService.getDailyTaskSummary();

        expect(summary).toBeDefined();
        expect(summary).toHaveProperty('date');
        expect(summary).toHaveProperty('totalTasks');
        expect(summary).toHaveProperty('completedTasks');
        expect(summary).toHaveProperty('pendingTasks');
        expect(summary).toHaveProperty('pointsEarned');
        expect(summary).toHaveProperty('tasks');
    });

    test('should get weekly task summary', async () => {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 7);

        const summary = await taskService.getWeeklyTaskSummary(startDate);

        expect(summary).toBeDefined();
        expect(summary).toHaveProperty('totalTasks');
        expect(summary).toHaveProperty('completedTasks');
        expect(summary).toHaveProperty('totalPointsEarned');
        expect(summary).toHaveProperty('tasksByType');
        expect(summary).toHaveProperty('dailySummaries');
        expect(summary.dailySummaries).toHaveLength(7);
    });

    test('should format task duration correctly', () => {
        expect(taskService.formatDuration(30)).toBe('30m');
        expect(taskService.formatDuration(60)).toBe('1h');
        expect(taskService.formatDuration(90)).toBe('1h 30m');
        expect(taskService.formatDuration(120)).toBe('2h');
    });

    test('should identify overdue tasks', () => {
        const overdueTask = {
            id: 'test-overdue',
            title: 'Test Task',
            description: 'Test',
            dueDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
            type: 'watering' as const,
            completed: false,
            pointsReward: 10,
            priority: 'medium' as const,
            estimatedDuration: 30,
        };

        expect(taskService.isTaskOverdue(overdueTask)).toBe(true);
        expect(taskService.getTaskStatusText(overdueTask)).toBe('Overdue');
    });
});