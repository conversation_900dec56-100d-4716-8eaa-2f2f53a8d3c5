import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import * as Location from 'expo-location';

interface LocationPickerProps {
    selectedLocation?: {
        latitude: number;
        longitude: number;
        address?: string;
    };
    onLocationChange: (location: {
        latitude: number;
        longitude: number;
        address?: string;
    }) => void;
    voiceEnabled?: boolean;
    onVoiceCommand?: (command: string) => void;
}

export default function LocationPicker({
    selectedLocation,
    onLocationChange,
    voiceEnabled = false,
    onVoiceCommand,
}: LocationPickerProps) {
    const [loading, setLoading] = useState(false);
    const [locationPermission, setLocationPermission] = useState<boolean | null>(null);

    useEffect(() => {
        checkLocationPermission();
    }, []);

    const checkLocationPermission = async () => {
        try {
            const { status } = await Location.getForegroundPermissionsAsync();
            setLocationPermission(status === 'granted');
        } catch (error) {
            console.error('Error checking location permission:', error);
            setLocationPermission(false);
        }
    };

    const requestLocationPermission = async (): Promise<boolean> => {
        try {
            const { status } = await Location.requestForegroundPermissionsAsync();
            const granted = status === 'granted';
            setLocationPermission(granted);
            return granted;
        } catch (error) {
            console.error('Error requesting location permission:', error);
            setLocationPermission(false);
            return false;
        }
    };

    const getCurrentLocation = async () => {
        setLoading(true);
        try {
            // Check permission first
            let hasPermission = locationPermission;
            if (!hasPermission) {
                hasPermission = await requestLocationPermission();
            }

            if (!hasPermission) {
                Alert.alert(
                    'Location Permission Required',
                    'Please enable location access to get your current location for better crop recommendations.',
                    [{ text: 'OK' }]
                );
                return;
            }

            // Get current location
            const location = await Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.Balanced,
            });

            // Get address from coordinates
            const address = await getAddressFromCoordinates(
                location.coords.latitude,
                location.coords.longitude
            );

            const locationData = {
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
                address,
            };

            onLocationChange(locationData);
        } catch (error) {
            console.error('Error getting current location:', error);
            Alert.alert(
                'Location Error',
                'Unable to get your current location. Please try again or select manually.',
                [{ text: 'OK' }]
            );
        } finally {
            setLoading(false);
        }
    };

    const getAddressFromCoordinates = async (
        latitude: number,
        longitude: number
    ): Promise<string> => {
        try {
            const addresses = await Location.reverseGeocodeAsync({
                latitude,
                longitude,
            });

            if (addresses.length > 0) {
                const address = addresses[0];
                const parts = [
                    address.name,
                    address.street,
                    address.city,
                    address.region,
                    address.country,
                ].filter(Boolean);

                return parts.join(', ');
            }
        } catch (error) {
            console.error('Error getting address:', error);
        }

        return `${latitude.toFixed(4)}, ${longitude.toFixed(4)}`;
    };

    const handleVoiceCommand = () => {
        if (onVoiceCommand) {
            onVoiceCommand('select_location');
        }
    };

    const formatCoordinates = (lat: number, lng: number): string => {
        return `${lat.toFixed(4)}°, ${lng.toFixed(4)}°`;
    };

    const getLocationRecommendations = () => {
        if (!selectedLocation) return null;

        const { latitude } = selectedLocation;
        const absLatitude = Math.abs(latitude);

        if (absLatitude < 23.5) {
            return {
                climate: 'Tropical',
                climateAr: 'استوائي',
                recommendations: [
                    'Great for warm-season crops like tomatoes, peppers, and corn',
                    'Year-round growing season possible',
                    'Watch for high humidity and pest pressure',
                ],
                recommendationsAr: [
                    'ممتاز للمحاصيل الموسمية الدافئة مثل الطماطم والفلفل والذرة',
                    'موسم نمو على مدار السنة ممكن',
                    'انتبه للرطوبة العالية وضغط الآفات',
                ],
            };
        } else if (absLatitude < 35) {
            return {
                climate: 'Subtropical',
                climateAr: 'شبه استوائي',
                recommendations: [
                    'Excellent for diverse crop varieties',
                    'Two growing seasons possible',
                    'Good balance of temperature and rainfall',
                ],
                recommendationsAr: [
                    'ممتاز لأصناف المحاصيل المتنوعة',
                    'موسمان للنمو ممكنان',
                    'توازن جيد بين درجة الحرارة وهطول الأمطار',
                ],
            };
        } else if (absLatitude < 50) {
            return {
                climate: 'Temperate',
                climateAr: 'معتدل',
                recommendations: [
                    'Perfect for grains like wheat and barley',
                    'Distinct seasons with winter dormancy',
                    'Plan for frost protection',
                ],
                recommendationsAr: [
                    'مثالي للحبوب مثل القمح والشعير',
                    'فصول متميزة مع سكون شتوي',
                    'خطط للحماية من الصقيع',
                ],
            };
        } else {
            return {
                climate: 'Cold',
                climateAr: 'بارد',
                recommendations: [
                    'Short growing season',
                    'Focus on cold-hardy crops',
                    'Greenhouse growing recommended',
                ],
                recommendationsAr: [
                    'موسم نمو قصير',
                    'ركز على المحاصيل المقاومة للبرد',
                    'النمو في البيوت المحمية موصى به',
                ],
            };
        }
    };

    const locationInfo = getLocationRecommendations();

    return (
        <View className="bg-white rounded-lg p-4 border border-gray-200">
            <Text className="text-lg font-semibold text-gray-900 mb-4">
                Farm Location
            </Text>

            {/* Current Location Button */}
            <TouchableOpacity
                onPress={getCurrentLocation}
                disabled={loading}
                className="flex-row items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200 mb-4"
                accessibilityLabel="Use current location"
                accessibilityHint="Get your current GPS location for crop recommendations"
            >
                <View className="flex-row items-center flex-1">
                    <Text className="text-2xl mr-3">📍</Text>
                    <View className="flex-1">
                        <Text className="text-base font-medium text-green-800">
                            Use Current Location
                        </Text>
                        <Text className="text-sm text-green-600">
                            Get GPS location for better recommendations
                        </Text>
                    </View>
                </View>
                {loading ? (
                    <ActivityIndicator size="small" color="#16a34a" />
                ) : (
                    <View className="flex-row items-center">
                        <Text className="text-green-600 mr-2">📡</Text>
                        {voiceEnabled && (
                            <TouchableOpacity
                                onPress={handleVoiceCommand}
                                className="p-2 bg-green-500 rounded-full ml-2"
                                accessibilityLabel="Voice location command"
                            >
                                <Text className="text-white text-sm">🎤</Text>
                            </TouchableOpacity>
                        )}
                    </View>
                )}
            </TouchableOpacity>

            {/* Selected Location Display */}
            {selectedLocation && (
                <View className="p-4 bg-blue-50 rounded-lg border border-blue-200 mb-4">
                    <View className="flex-row items-center mb-2">
                        <Text className="text-2xl mr-2">🗺️</Text>
                        <Text className="text-base font-semibold text-blue-800">
                            Selected Location
                        </Text>
                    </View>

                    {selectedLocation.address && (
                        <Text className="text-sm text-blue-700 mb-2">
                            {selectedLocation.address}
                        </Text>
                    )}

                    <Text className="text-sm text-blue-600">
                        Coordinates: {formatCoordinates(selectedLocation.latitude, selectedLocation.longitude)}
                    </Text>
                </View>
            )}

            {/* Location-Based Recommendations */}
            {locationInfo && selectedLocation && (
                <View className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                    <View className="flex-row items-center mb-3">
                        <Text className="text-2xl mr-2">🌍</Text>
                        <View>
                            <Text className="text-base font-semibold text-yellow-800">
                                Climate Zone: {locationInfo.climate}
                            </Text>
                            <Text className="text-sm text-yellow-700">
                                المنطقة المناخية: {locationInfo.climateAr}
                            </Text>
                        </View>
                    </View>

                    <Text className="text-sm font-medium text-yellow-800 mb-2">
                        Recommendations:
                    </Text>
                    {locationInfo.recommendations.map((rec, index) => (
                        <Text key={index} className="text-sm text-yellow-700 mb-1">
                            • {rec}
                        </Text>
                    ))}

                    <Text className="text-sm font-medium text-yellow-800 mb-2 mt-3 text-right">
                        التوصيات:
                    </Text>
                    {locationInfo.recommendationsAr.map((rec, index) => (
                        <Text key={index} className="text-sm text-yellow-700 mb-1 text-right">
                            • {rec}
                        </Text>
                    ))}
                </View>
            )}

            {/* Manual Location Selection */}
            <TouchableOpacity
                className="flex-row items-center justify-center p-4 bg-gray-50 rounded-lg border border-gray-200 mt-4"
                accessibilityLabel="Select location manually"
                accessibilityHint="Choose location on map"
            >
                <Text className="text-2xl mr-2">🗺️</Text>
                <Text className="text-base font-medium text-gray-700">
                    Select on Map
                </Text>
                <Text className="text-gray-500 ml-2">(Coming Soon)</Text>
            </TouchableOpacity>

            {/* Permission Status */}
            {locationPermission === false && (
                <View className="mt-4 p-3 bg-red-50 rounded-lg border border-red-200">
                    <Text className="text-sm text-red-700">
                        ⚠️ Location permission is required for personalized crop recommendations.
                        Please enable location access in your device settings.
                    </Text>
                </View>
            )}
        </View>
    );
}