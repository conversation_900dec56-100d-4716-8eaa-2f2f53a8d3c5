/**
 * Order Management Service
 * Handles order lifecycle, tracking, and fulfillment
 */

import { supabase } from './supabase/client';
import { paymentProcessorService, PaymentRequest } from './paymentProcessor';
import {
    Order,
    OrderStatus,
    ShippingAddress,
    PaymentMethod,
    CartItem,
} from '../types/product';
import { Database } from '../types/database';

type OrderRow = Database['public']['Tables']['orders']['Row'];
type OrderInsert = Database['public']['Tables']['orders']['Insert'];
type OrderUpdate = Database['public']['Tables']['orders']['Update'];

export interface OrderSummary {
    subtotal: number;
    taxAmount: number;
    shippingAmount: number;
    discountAmount: number;
    processingFee: number;
    totalAmount: number;
}

export interface OrderTrackingInfo {
    orderId: string;
    orderNumber: string;
    status: OrderStatus;
    trackingNumber?: string;
    estimatedDelivery?: Date;
    deliveredAt?: Date;
    trackingEvents: Array<{
        timestamp: Date;
        status: string;
        description: string;
        location?: string;
    }>;
}

export interface OrderFilters {
    status?: OrderStatus[];
    dateRange?: {
        start: Date;
        end: Date;
    };
    minAmount?: number;
    maxAmount?: number;
}

export class OrderManagerService {
    /**
     * Create a new order from cart items
     */
    async createOrder(
        userId: string,
        cartItems: CartItem[],
        shippingAddress: ShippingAddress,
        paymentMethod: PaymentMethod,
        billingAddress?: ShippingAddress
    ): Promise<Order> {
        try {
            if (!cartItems || cartItems.length === 0) {
                throw new Error('Cannot create order with empty cart');
            }

            // Calculate order totals
            const orderSummary = await this.calculateOrderSummary(
                cartItems,
                shippingAddress,
                paymentMethod
            );

            // Validate stock availability
            await this.validateStockAvailability(cartItems);

            // Create order in database
            const orderData: OrderInsert = {
                user_id: userId,
                total_amount: orderSummary.totalAmount,
                tax_amount: orderSummary.taxAmount,
                shipping_amount: orderSummary.shippingAmount,
                discount_amount: orderSummary.discountAmount,
                status: 'pending',
                payment_status: 'pending',
                payment_method: paymentMethod as any,
                shipping_address: shippingAddress as any,
                billing_address: billingAddress as any,
                estimated_delivery: this.calculateEstimatedDelivery(shippingAddress),
                notes: `Order created with ${cartItems.length} items`,
            };

            const { data: order, error: orderError } = await supabase
                .from('orders')
                .insert(orderData)
                .select()
                .single();

            if (orderError) {
                throw new Error(`Failed to create order: ${orderError.message}`);
            }

            // Create order items
            const orderItems = cartItems.map(item => ({
                order_id: order.id,
                product_id: item.product.id,
                variant_id: item.selectedVariant?.id || null,
                quantity: item.quantity,
                unit_price: item.selectedVariant?.price || item.product.price,
                total_price: (item.selectedVariant?.price || item.product.price) * item.quantity,
                product_snapshot: {
                    name: item.product.name,
                    description: item.product.description,
                    imageUrls: item.product.imageUrls,
                    specifications: item.product.specifications,
                    brand: item.product.brand,
                } as any,
            }));

            const { error: itemsError } = await supabase
                .from('order_items')
                .insert(orderItems);

            if (itemsError) {
                // Rollback order creation
                await supabase.from('orders').delete().eq('id', order.id);
                throw new Error(`Failed to create order items: ${itemsError.message}`);
            }

            // Process payment if not cash on delivery
            if (paymentMethod.type !== 'cash_on_delivery') {
                const paymentResult = await this.processOrderPayment(order, paymentMethod);

                if (!paymentResult.success) {
                    // Update order status to failed
                    await this.updateOrderStatus(order.id, 'cancelled', 'failed');
                    throw new Error(`Payment failed: ${paymentResult.error}`);
                }

                // Update order with payment success
                await this.updateOrderStatus(order.id, 'confirmed', 'paid');
            } else {
                // For COD, confirm the order immediately
                await this.updateOrderStatus(order.id, 'confirmed', 'pending');
            }

            // Reserve inventory
            await this.reserveInventoryForOrder(cartItems);

            // Send order confirmation (would integrate with notification service)
            await this.sendOrderConfirmation(order.id);

            // Return complete order object
            return this.mapOrderRowToOrder(order, cartItems);
        } catch (error) {
            console.error('Error creating order:', error);
            throw error;
        }
    }

    /**
     * Calculate order summary with all costs
     */
    async calculateOrderSummary(
        cartItems: CartItem[],
        shippingAddress: ShippingAddress,
        paymentMethod: PaymentMethod
    ): Promise<OrderSummary> {
        try {
            // Calculate subtotal
            const subtotal = cartItems.reduce((total, item) => {
                const price = item.selectedVariant?.price || item.product.price;
                return total + (price * item.quantity);
            }, 0);

            // Calculate tax (simplified - in production, use tax service)
            const taxRate = this.getTaxRate(shippingAddress.state, shippingAddress.country);
            const taxAmount = Math.round(subtotal * taxRate * 100) / 100;

            // Calculate shipping
            const shippingAmount = await this.calculateShippingCost(cartItems, shippingAddress);

            // Calculate discount (if any)
            const discountAmount = 0; // Would integrate with coupon/discount service

            // Calculate payment processing fee
            const processingFee = paymentProcessorService.calculateProcessingFee(
                subtotal + taxAmount + shippingAmount - discountAmount,
                paymentMethod
            );

            const totalAmount = subtotal + taxAmount + shippingAmount - discountAmount + processingFee;

            return {
                subtotal: Math.round(subtotal * 100) / 100,
                taxAmount: Math.round(taxAmount * 100) / 100,
                shippingAmount: Math.round(shippingAmount * 100) / 100,
                discountAmount: Math.round(discountAmount * 100) / 100,
                processingFee: Math.round(processingFee * 100) / 100,
                totalAmount: Math.round(totalAmount * 100) / 100,
            };
        } catch (error) {
            console.error('Error calculating order summary:', error);
            throw new Error('Failed to calculate order total');
        }
    }

    /**
     * Update order status
     */
    async updateOrderStatus(
        orderId: string,
        status: OrderStatus,
        paymentStatus?: 'pending' | 'paid' | 'failed' | 'refunded',
        trackingNumber?: string
    ): Promise<void> {
        try {
            const updateData: OrderUpdate = {
                status,
                updated_at: new Date().toISOString(),
            };

            if (paymentStatus) {
                updateData.payment_status = paymentStatus;
            }

            if (trackingNumber) {
                updateData.tracking_number = trackingNumber;
            }

            if (status === 'delivered') {
                updateData.delivered_at = new Date().toISOString();
            }

            const { error } = await supabase
                .from('orders')
                .update(updateData)
                .eq('id', orderId);

            if (error) {
                throw new Error(`Failed to update order status: ${error.message}`);
            }

            // Send status update notification
            await this.sendStatusUpdateNotification(orderId, status);
        } catch (error) {
            console.error('Error updating order status:', error);
            throw error;
        }
    }

    /**
     * Get order by ID with full details
     */
    async getOrderById(orderId: string, userId?: string): Promise<Order | null> {
        try {
            let query = supabase
                .from('orders')
                .select(`
                    *,
                    order_items (
                        *,
                        product:product_id (*)
                    )
                `)
                .eq('id', orderId);

            if (userId) {
                query = query.eq('user_id', userId);
            }

            const { data: order, error } = await query.single();

            if (error) {
                if (error.code === 'PGRST116') {
                    return null;
                }
                throw new Error(`Failed to get order: ${error.message}`);
            }

            return this.mapOrderRowToOrder(order, this.mapOrderItemsToCartItems(order.order_items));
        } catch (error) {
            console.error('Error getting order by ID:', error);
            return null;
        }
    }

    /**
     * Get user's order history with filtering
     */
    async getUserOrders(
        userId: string,
        filters?: OrderFilters,
        limit: number = 20,
        offset: number = 0
    ): Promise<{
        orders: Order[];
        totalCount: number;
        hasMore: boolean;
    }> {
        try {
            let query = supabase
                .from('orders')
                .select(`
                    *,
                    order_items (
                        *,
                        product:product_id (*)
                    )
                `, { count: 'exact' })
                .eq('user_id', userId);

            // Apply filters
            if (filters?.status && filters.status.length > 0) {
                query = query.in('status', filters.status);
            }

            if (filters?.dateRange) {
                query = query
                    .gte('created_at', filters.dateRange.start.toISOString())
                    .lte('created_at', filters.dateRange.end.toISOString());
            }

            if (filters?.minAmount) {
                query = query.gte('total_amount', filters.minAmount);
            }

            if (filters?.maxAmount) {
                query = query.lte('total_amount', filters.maxAmount);
            }

            // Apply pagination and sorting
            query = query
                .order('created_at', { ascending: false })
                .range(offset, offset + limit - 1);

            const { data: orders, error, count } = await query;

            if (error) {
                throw new Error(`Failed to get user orders: ${error.message}`);
            }

            const mappedOrders = orders?.map(order =>
                this.mapOrderRowToOrder(order, this.mapOrderItemsToCartItems(order.order_items))
            ) || [];

            return {
                orders: mappedOrders,
                totalCount: count || 0,
                hasMore: (count || 0) > offset + limit,
            };
        } catch (error) {
            console.error('Error getting user orders:', error);
            return {
                orders: [],
                totalCount: 0,
                hasMore: false,
            };
        }
    }

    /**
     * Get order tracking information
     */
    async getOrderTracking(orderId: string): Promise<OrderTrackingInfo | null> {
        try {
            const { data: order, error } = await supabase
                .from('orders')
                .select('id, order_number, status, tracking_number, estimated_delivery, delivered_at')
                .eq('id', orderId)
                .single();

            if (error || !order) {
                return null;
            }

            // In a real implementation, this would fetch tracking events from shipping provider
            const trackingEvents = this.generateTrackingEvents(
                order.status as OrderStatus,
                order.tracking_number
            );

            return {
                orderId: order.id,
                orderNumber: order.order_number,
                status: order.status as OrderStatus,
                trackingNumber: order.tracking_number || undefined,
                estimatedDelivery: order.estimated_delivery ? new Date(order.estimated_delivery) : undefined,
                deliveredAt: order.delivered_at ? new Date(order.delivered_at) : undefined,
                trackingEvents,
            };
        } catch (error) {
            console.error('Error getting order tracking:', error);
            return null;
        }
    }

    /**
     * Cancel order
     */
    async cancelOrder(orderId: string, userId: string, reason?: string): Promise<void> {
        try {
            // Get order details
            const order = await this.getOrderById(orderId, userId);
            if (!order) {
                throw new Error('Order not found');
            }

            // Check if order can be cancelled
            if (!['pending', 'confirmed'].includes(order.status)) {
                throw new Error('Order cannot be cancelled at this stage');
            }

            // Process refund if payment was made
            if (order.status === 'confirmed' && order.paymentMethod.type !== 'cash_on_delivery') {
                // Would integrate with payment processor for refund
                console.log('Processing refund for order:', orderId);
            }

            // Update order status
            await this.updateOrderStatus(orderId, 'cancelled');

            // Release reserved inventory
            await this.releaseInventoryForOrder(order.items);

            // Update order with cancellation reason
            await supabase
                .from('orders')
                .update({
                    notes: `Order cancelled by user. Reason: ${reason || 'No reason provided'}`,
                })
                .eq('id', orderId);

            // Send cancellation notification
            await this.sendCancellationNotification(orderId);
        } catch (error) {
            console.error('Error cancelling order:', error);
            throw error;
        }
    }

    /**
     * Generate order receipt
     */
    async generateOrderReceipt(orderId: string): Promise<{
        orderNumber: string;
        orderDate: Date;
        items: Array<{
            name: string;
            quantity: number;
            unitPrice: number;
            totalPrice: number;
        }>;
        summary: OrderSummary;
        shippingAddress: ShippingAddress;
        paymentMethod: PaymentMethod;
    }> {
        try {
            const order = await this.getOrderById(orderId);
            if (!order) {
                throw new Error('Order not found');
            }

            const items = order.items.map(item => ({
                name: item.product.name,
                quantity: item.quantity,
                unitPrice: item.selectedVariant?.price || item.product.price,
                totalPrice: (item.selectedVariant?.price || item.product.price) * item.quantity,
            }));

            const summary: OrderSummary = {
                subtotal: items.reduce((sum, item) => sum + item.totalPrice, 0),
                taxAmount: 0, // Would calculate from order data
                shippingAmount: 0, // Would get from order data
                discountAmount: 0, // Would get from order data
                processingFee: 0, // Would get from order data
                totalAmount: order.totalAmount,
            };

            return {
                orderNumber: orderId, // Would use actual order number
                orderDate: order.createdAt,
                items,
                summary,
                shippingAddress: order.shippingAddress,
                paymentMethod: order.paymentMethod,
            };
        } catch (error) {
            console.error('Error generating order receipt:', error);
            throw error;
        }
    }

    // Private helper methods

    private async validateStockAvailability(cartItems: CartItem[]): Promise<void> {
        for (const item of cartItems) {
            const { data: product, error } = await supabase
                .from('products')
                .select('stock_quantity')
                .eq('id', item.product.id)
                .single();

            if (error || !product) {
                throw new Error(`Product ${item.product.name} not found`);
            }

            if (product.stock_quantity < item.quantity) {
                throw new Error(
                    `Insufficient stock for ${item.product.name}. Available: ${product.stock_quantity}, Requested: ${item.quantity}`
                );
            }
        }
    }

    private async processOrderPayment(order: OrderRow, paymentMethod: PaymentMethod) {
        const paymentRequest: PaymentRequest = {
            amount: order.total_amount,
            currency: 'USD',
            paymentMethod,
            orderId: order.id,
            userId: order.user_id,
            description: `Payment for order ${order.order_number}`,
        };

        return paymentProcessorService.processPayment(paymentRequest);
    }

    private async calculateShippingCost(
        cartItems: CartItem[],
        shippingAddress: ShippingAddress
    ): Promise<number> {
        // Simplified shipping calculation
        const totalWeight = cartItems.reduce((weight, item) => weight + item.quantity, 0);
        const baseShipping = 5.00;
        const weightCost = totalWeight * 0.5;

        // Free shipping for orders over $100
        const subtotal = cartItems.reduce((total, item) => {
            const price = item.selectedVariant?.price || item.product.price;
            return total + (price * item.quantity);
        }, 0);

        return subtotal >= 100 ? 0 : Math.round((baseShipping + weightCost) * 100) / 100;
    }

    private getTaxRate(state: string, country: string): number {
        // Simplified tax calculation - in production, use tax service
        if (country === 'US') {
            return 0.08; // 8% average US tax rate
        }
        return 0.0; // No tax for other countries in this example
    }

    private calculateEstimatedDelivery(shippingAddress: ShippingAddress): string {
        const deliveryDate = new Date();
        deliveryDate.setDate(deliveryDate.getDate() + 7); // 7 days from now
        return deliveryDate.toISOString().split('T')[0];
    }

    private async reserveInventoryForOrder(cartItems: CartItem[]): Promise<void> {
        for (const item of cartItems) {
            const { error } = await supabase
                .from('products')
                .update({
                    stock_quantity: item.product.stockQuantity - item.quantity
                })
                .eq('id', item.product.id);

            if (error) {
                console.error(`Failed to reserve inventory for product ${item.product.id}:`, error);
            }
        }
    }

    private async releaseInventoryForOrder(cartItems: CartItem[]): Promise<void> {
        for (const item of cartItems) {
            const { error } = await supabase
                .from('products')
                .update({
                    stock_quantity: item.product.stockQuantity + item.quantity
                })
                .eq('id', item.product.id);

            if (error) {
                console.error(`Failed to release inventory for product ${item.product.id}:`, error);
            }
        }
    }

    private generateTrackingEvents(status: OrderStatus, trackingNumber?: string) {
        const events = [
            {
                timestamp: new Date(),
                status: 'Order Placed',
                description: 'Your order has been received and is being processed',
            },
        ];

        if (['confirmed', 'processing', 'shipped', 'delivered'].includes(status)) {
            events.push({
                timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
                status: 'Order Confirmed',
                description: 'Your order has been confirmed and payment processed',
            });
        }

        if (['processing', 'shipped', 'delivered'].includes(status)) {
            events.push({
                timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000),
                status: 'Processing',
                description: 'Your order is being prepared for shipment',
            });
        }

        if (['shipped', 'delivered'].includes(status)) {
            events.push({
                timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
                status: 'Shipped',
                description: `Your order has been shipped${trackingNumber ? ` with tracking number ${trackingNumber}` : ''}`,
            });
        }

        if (status === 'delivered') {
            events.push({
                timestamp: new Date(),
                status: 'Delivered',
                description: 'Your order has been delivered successfully',
            });
        }

        return events.reverse(); // Most recent first
    }

    private mapOrderRowToOrder(orderRow: any, items: CartItem[]): Order {
        return {
            id: orderRow.id,
            userId: orderRow.user_id,
            items,
            totalAmount: orderRow.total_amount,
            status: orderRow.status as OrderStatus,
            shippingAddress: orderRow.shipping_address as ShippingAddress,
            paymentMethod: orderRow.payment_method as PaymentMethod,
            trackingNumber: orderRow.tracking_number || undefined,
            createdAt: new Date(orderRow.created_at),
            updatedAt: new Date(orderRow.updated_at),
            estimatedDelivery: orderRow.estimated_delivery ? new Date(orderRow.estimated_delivery) : undefined,
        };
    }

    private mapOrderItemsToCartItems(orderItems: any[]): CartItem[] {
        return orderItems?.map(item => ({
            id: item.id,
            product: {
                id: item.product.id,
                name: item.product.name,
                description: item.product.description || '',
                category: item.product.category,
                price: item.product.price,
                originalPrice: item.product.original_price,
                currency: item.product.currency,
                imageUrls: item.product.image_urls || [],
                specifications: item.product.specifications || [],
                stockQuantity: item.product.stock_quantity,
                rating: item.product.rating,
                reviewCount: item.product.review_count,
                tags: item.product.tags || [],
                brand: item.product.brand,
                isRecommended: item.product.is_recommended,
                isFeatured: item.product.is_featured,
                createdAt: new Date(item.product.created_at),
                updatedAt: new Date(item.product.updated_at),
            },
            quantity: item.quantity,
            selectedVariant: item.variant_id ? { id: item.variant_id } as any : undefined,
        })) || [];
    }

    private async sendOrderConfirmation(orderId: string): Promise<void> {
        // Would integrate with notification service
        console.log('Sending order confirmation for order:', orderId);
    }

    private async sendStatusUpdateNotification(orderId: string, status: OrderStatus): Promise<void> {
        // Would integrate with notification service
        console.log('Sending status update notification for order:', orderId, 'Status:', status);
    }

    private async sendCancellationNotification(orderId: string): Promise<void> {
        // Would integrate with notification service
        console.log('Sending cancellation notification for order:', orderId);
    }
}

export const orderManagerService = new OrderManagerService();