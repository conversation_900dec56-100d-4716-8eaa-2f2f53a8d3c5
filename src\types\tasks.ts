export interface Task {
    id: string;
    title: string;
    description: string;
    dueDate: Date;
    type: 'watering' | 'fertilizing' | 'monitoring' | 'harvesting' | 'planting' | 'pest_control';
    completed: boolean;
    pointsReward: number;
    priority: 'low' | 'medium' | 'high';
    cropPlanId?: string;
    estimatedDuration: number; // in minutes
    instructions?: string[];
    imageUrl?: string;
    completedAt?: Date;
    completedBy?: string;
}

export interface TaskCompletion {
    taskId: string;
    completedAt: Date;
    notes?: string;
    imageEvidence?: string;
    pointsEarned: number;
}

export interface DailyTaskSummary {
    date: Date;
    totalTasks: number;
    completedTasks: number;
    pendingTasks: number;
    pointsEarned: number;
    tasks: Task[];
}

export type TaskFilter = 'all' | 'pending' | 'completed' | 'overdue';
export type TaskSort = 'dueDate' | 'priority' | 'type' | 'pointsReward';