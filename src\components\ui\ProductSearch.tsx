import React, { useState } from 'react';
import { View, Text, TextInput, Pressable, Modal, ScrollView } from 'react-native';
import { ProductCategory, ProductFilter, ProductSearchParams } from '../../types/product';
import { Button } from './Button';

export interface ProductSearchProps {
    onSearch: (params: ProductSearchParams) => void;
    categories: { id: ProductCategory; name: string; icon: string }[];
    voiceFeedbackEnabled?: boolean;
    onVoiceFeedback?: (text: string) => void;
    onVoiceSearch?: () => Promise<string>;
}

export const ProductSearch: React.FC<ProductSearchProps> = ({
    onSearch,
    categories,
    voiceFeedbackEnabled = false,
    onVoiceFeedback,
    onVoiceSearch,
}) => {
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedCategory, setSelectedCategory] = useState<ProductCategory | undefined>();
    const [showFilters, setShowFilters] = useState(false);
    const [sortBy, setSortBy] = useState<'name' | 'price' | 'rating' | 'newest'>('name');
    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
    const [filters, setFilters] = useState<ProductFilter>({});

    const handleSearch = () => {
        const searchParams: ProductSearchParams = {
            query: searchQuery.trim() || undefined,
            category: selectedCategory,
            sortBy,
            sortOrder,
            filters: Object.keys(filters).length > 0 ? filters : undefined,
        };

        if (voiceFeedbackEnabled && onVoiceFeedback) {
            const categoryText = selectedCategory ? ` in ${selectedCategory}` : '';
            const queryText = searchQuery ? ` for "${searchQuery}"` : '';
            onVoiceFeedback(`Searching${queryText}${categoryText}`);
        }

        onSearch(searchParams);
    };

    const handleVoiceSearch = async () => {
        if (onVoiceSearch) {
            try {
                if (voiceFeedbackEnabled && onVoiceFeedback) {
                    onVoiceFeedback('Listening for your search...');
                }
                const voiceQuery = await onVoiceSearch();
                setSearchQuery(voiceQuery);

                // Auto-search after voice input
                const searchParams: ProductSearchParams = {
                    query: voiceQuery.trim(),
                    category: selectedCategory,
                    sortBy,
                    sortOrder,
                    filters: Object.keys(filters).length > 0 ? filters : undefined,
                };
                onSearch(searchParams);
            } catch (error) {
                if (voiceFeedbackEnabled && onVoiceFeedback) {
                    onVoiceFeedback('Voice search failed. Please try again.');
                }
            }
        }
    };

    const clearFilters = () => {
        setSearchQuery('');
        setSelectedCategory(undefined);
        setSortBy('name');
        setSortOrder('asc');
        setFilters({});

        if (voiceFeedbackEnabled && onVoiceFeedback) {
            onVoiceFeedback('Filters cleared');
        }

        onSearch({});
    };

    const updatePriceFilter = (min: number, max: number) => {
        setFilters(prev => ({
            ...prev,
            priceRange: { min, max },
        }));
    };

    const updateRatingFilter = (rating: number) => {
        setFilters(prev => ({
            ...prev,
            rating,
        }));
    };

    const toggleInStockFilter = () => {
        setFilters(prev => ({
            ...prev,
            inStock: !prev.inStock,
        }));
    };

    return (
        <View className="bg-white">
            {/* Search Bar */}
            <View className="flex-row items-center gap-2 p-4 border-b border-gray-100">
                <View className="flex-1 flex-row items-center bg-gray-50 rounded-xl px-4 py-3">
                    <Text className="text-gray-400 mr-2">🔍</Text>
                    <TextInput
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                        placeholder="Search products..."
                        className="flex-1 text-base text-gray-900"
                        onSubmitEditing={handleSearch}
                        accessibilityLabel="Search products"
                        accessibilityHint="Enter product name or keywords"
                    />
                    {onVoiceSearch && (
                        <Pressable
                            onPress={handleVoiceSearch}
                            className="ml-2 p-2 bg-primary-100 rounded-lg active:opacity-80"
                            accessibilityRole="button"
                            accessibilityLabel="Voice search"
                            accessibilityHint="Tap to search using voice"
                        >
                            <Text className="text-primary-600">🎤</Text>
                        </Pressable>
                    )}
                </View>

                <Pressable
                    onPress={() => setShowFilters(true)}
                    className="p-3 bg-gray-50 rounded-xl active:opacity-80"
                    accessibilityRole="button"
                    accessibilityLabel="Open filters"
                    accessibilityHint="Tap to open search filters"
                >
                    <Text className="text-gray-600">⚙️</Text>
                </Pressable>
            </View>

            {/* Category Tabs */}
            <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                className="px-4 py-3 border-b border-gray-100"
                contentContainerStyle={{ gap: 8 }}
            >
                <Pressable
                    onPress={() => {
                        setSelectedCategory(undefined);
                        handleSearch();
                    }}
                    className={`px-4 py-2 rounded-full border ${!selectedCategory
                            ? 'bg-primary-600 border-primary-600'
                            : 'bg-white border-gray-300'
                        }`}
                    accessibilityRole="button"
                    accessibilityLabel="All categories"
                >
                    <Text
                        className={`font-medium ${!selectedCategory ? 'text-white' : 'text-gray-700'
                            }`}
                    >
                        All
                    </Text>
                </Pressable>

                {categories.map((category) => (
                    <Pressable
                        key={category.id}
                        onPress={() => {
                            setSelectedCategory(category.id);
                            handleSearch();
                        }}
                        className={`px-4 py-2 rounded-full border flex-row items-center gap-2 ${selectedCategory === category.id
                                ? 'bg-primary-600 border-primary-600'
                                : 'bg-white border-gray-300'
                            }`}
                        accessibilityRole="button"
                        accessibilityLabel={`${category.name} category`}
                    >
                        <Text className="text-sm">{category.icon}</Text>
                        <Text
                            className={`font-medium ${selectedCategory === category.id ? 'text-white' : 'text-gray-700'
                                }`}
                        >
                            {category.name}
                        </Text>
                    </Pressable>
                ))}
            </ScrollView>

            {/* Filters Modal */}
            <Modal
                visible={showFilters}
                animationType="slide"
                presentationStyle="pageSheet"
                onRequestClose={() => setShowFilters(false)}
            >
                <View className="flex-1 bg-white">
                    <View className="flex-row items-center justify-between p-4 border-b border-gray-200">
                        <Text className="text-xl font-bold text-gray-900">Filters & Sort</Text>
                        <Pressable
                            onPress={() => setShowFilters(false)}
                            className="p-2 active:opacity-80"
                            accessibilityRole="button"
                            accessibilityLabel="Close filters"
                        >
                            <Text className="text-2xl text-gray-500">×</Text>
                        </Pressable>
                    </View>

                    <ScrollView className="flex-1 p-4">
                        {/* Sort Options */}
                        <View className="mb-6">
                            <Text className="text-lg font-semibold text-gray-900 mb-3">Sort By</Text>
                            <View className="gap-2">
                                {[
                                    { value: 'name', label: 'Name' },
                                    { value: 'price', label: 'Price' },
                                    { value: 'rating', label: 'Rating' },
                                    { value: 'newest', label: 'Newest' },
                                ].map((option) => (
                                    <Pressable
                                        key={option.value}
                                        onPress={() => setSortBy(option.value as any)}
                                        className={`flex-row items-center justify-between p-3 rounded-lg border ${sortBy === option.value
                                                ? 'bg-primary-50 border-primary-200'
                                                : 'bg-gray-50 border-gray-200'
                                            }`}
                                        accessibilityRole="radio"
                                        accessibilityState={{ checked: sortBy === option.value }}
                                    >
                                        <Text className="text-base text-gray-900">{option.label}</Text>
                                        {sortBy === option.value && (
                                            <Text className="text-primary-600">✓</Text>
                                        )}
                                    </Pressable>
                                ))}
                            </View>

                            <View className="flex-row gap-2 mt-3">
                                <Pressable
                                    onPress={() => setSortOrder('asc')}
                                    className={`flex-1 p-3 rounded-lg border ${sortOrder === 'asc'
                                            ? 'bg-primary-50 border-primary-200'
                                            : 'bg-gray-50 border-gray-200'
                                        }`}
                                    accessibilityRole="radio"
                                    accessibilityState={{ checked: sortOrder === 'asc' }}
                                >
                                    <Text className="text-center text-gray-900">Ascending</Text>
                                </Pressable>
                                <Pressable
                                    onPress={() => setSortOrder('desc')}
                                    className={`flex-1 p-3 rounded-lg border ${sortOrder === 'desc'
                                            ? 'bg-primary-50 border-primary-200'
                                            : 'bg-gray-50 border-gray-200'
                                        }`}
                                    accessibilityRole="radio"
                                    accessibilityState={{ checked: sortOrder === 'desc' }}
                                >
                                    <Text className="text-center text-gray-900">Descending</Text>
                                </Pressable>
                            </View>
                        </View>

                        {/* Price Range */}
                        <View className="mb-6">
                            <Text className="text-lg font-semibold text-gray-900 mb-3">Price Range</Text>
                            <View className="flex-row gap-2">
                                {[
                                    { min: 0, max: 25, label: 'Under $25' },
                                    { min: 25, max: 50, label: '$25 - $50' },
                                    { min: 50, max: 100, label: '$50 - $100' },
                                    { min: 100, max: 999999, label: 'Over $100' },
                                ].map((range) => (
                                    <Pressable
                                        key={`${range.min}-${range.max}`}
                                        onPress={() => updatePriceFilter(range.min, range.max)}
                                        className={`flex-1 p-3 rounded-lg border ${filters.priceRange?.min === range.min && filters.priceRange?.max === range.max
                                                ? 'bg-primary-50 border-primary-200'
                                                : 'bg-gray-50 border-gray-200'
                                            }`}
                                        accessibilityRole="button"
                                    >
                                        <Text className="text-center text-sm text-gray-900">{range.label}</Text>
                                    </Pressable>
                                ))}
                            </View>
                        </View>

                        {/* Rating Filter */}
                        <View className="mb-6">
                            <Text className="text-lg font-semibold text-gray-900 mb-3">Minimum Rating</Text>
                            <View className="flex-row gap-2">
                                {[4, 4.5, 5].map((rating) => (
                                    <Pressable
                                        key={rating}
                                        onPress={() => updateRatingFilter(rating)}
                                        className={`flex-1 p-3 rounded-lg border ${filters.rating === rating
                                                ? 'bg-primary-50 border-primary-200'
                                                : 'bg-gray-50 border-gray-200'
                                            }`}
                                        accessibilityRole="button"
                                    >
                                        <Text className="text-center text-gray-900">
                                            {rating}+ ⭐
                                        </Text>
                                    </Pressable>
                                ))}
                            </View>
                        </View>

                        {/* In Stock Filter */}
                        <View className="mb-6">
                            <Pressable
                                onPress={toggleInStockFilter}
                                className={`flex-row items-center justify-between p-4 rounded-lg border ${filters.inStock
                                        ? 'bg-primary-50 border-primary-200'
                                        : 'bg-gray-50 border-gray-200'
                                    }`}
                                accessibilityRole="checkbox"
                                accessibilityState={{ checked: !!filters.inStock }}
                            >
                                <Text className="text-base text-gray-900">In Stock Only</Text>
                                {filters.inStock && (
                                    <Text className="text-primary-600">✓</Text>
                                )}
                            </Pressable>
                        </View>
                    </ScrollView>

                    <View className="p-4 border-t border-gray-200 gap-3">
                        <Button
                            title="Apply Filters"
                            onPress={() => {
                                handleSearch();
                                setShowFilters(false);
                            }}
                            variant="primary"
                            fullWidth
                            voiceFeedbackEnabled={voiceFeedbackEnabled}
                            onVoiceFeedback={onVoiceFeedback}
                        />
                        <Button
                            title="Clear All"
                            onPress={clearFilters}
                            variant="outline"
                            fullWidth
                            voiceFeedbackEnabled={voiceFeedbackEnabled}
                            onVoiceFeedback={onVoiceFeedback}
                        />
                    </View>
                </View>
            </Modal>
        </View>
    );
};