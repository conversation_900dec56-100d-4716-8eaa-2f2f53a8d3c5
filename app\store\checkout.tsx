import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { ShoppingCart, ShippingAddress, PaymentMethod } from '../../src/types/product';
import { storeService } from '../../src/services/store';
import { Button } from '../../src/components/ui/Button';
import { Input } from '../../src/components/ui/Input';

export default function CheckoutScreen() {
    const router = useRouter();
    const [cart, setCart] = useState<ShoppingCart | null>(null);
    const [loading, setLoading] = useState(true);
    const [processing, setProcessing] = useState(false);
    const [currentStep, setCurrentStep] = useState<'shipping' | 'payment' | 'review'>('shipping');

    // Shipping form state
    const [shippingAddress, setShippingAddress] = useState<ShippingAddress>({
        fullName: '',
        addressLine1: '',
        addressLine2: '',
        city: '',
        state: '',
        postalCode: '',
        country: 'US',
        phoneNumber: '',
    });

    // Payment form state
    const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>({
        type: 'cash_on_delivery',
        details: {},
    });

    useEffect(() => {
        loadCart();
    }, []);

    const loadCart = async () => {
        try {
            setLoading(true);
            const cartData = await storeService.getCart();
            if (!cartData || cartData.items.length === 0) {
                Alert.alert('Empty Cart', 'Your cart is empty', [
                    { text: 'OK', onPress: () => router.replace('/(tabs)/store') },
                ]);
                return;
            }
            setCart(cartData);
        } catch (error) {
            console.error('Error loading cart:', error);
            Alert.alert('Error', 'Failed to load cart');
        } finally {
            setLoading(false);
        }
    };

    const validateShippingAddress = (): boolean => {
        const required = ['fullName', 'addressLine1', 'city', 'state', 'postalCode', 'phoneNumber'];
        for (const field of required) {
            if (!shippingAddress[field as keyof ShippingAddress]) {
                Alert.alert('Missing Information', `Please fill in ${field.replace(/([A-Z])/g, ' $1').toLowerCase()}`);
                return false;
            }
        }
        return true;
    };

    const handleNextStep = () => {
        if (currentStep === 'shipping') {
            if (validateShippingAddress()) {
                setCurrentStep('payment');
            }
        } else if (currentStep === 'payment') {
            setCurrentStep('review');
        }
    };

    const handlePreviousStep = () => {
        if (currentStep === 'payment') {
            setCurrentStep('shipping');
        } else if (currentStep === 'review') {
            setCurrentStep('payment');
        }
    };

    const handlePlaceOrder = async () => {
        if (!cart) return;

        try {
            setProcessing(true);

            // Simulate order processing
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Clear cart after successful order
            await storeService.clearCart();

            Alert.alert(
                'Order Placed Successfully!',
                'Your order has been placed and will be processed soon. You will receive a confirmation email shortly.',
                [
                    {
                        text: 'Continue Shopping',
                        onPress: () => router.replace('/(tabs)/store'),
                    },
                ]
            );
        } catch (error) {
            console.error('Error placing order:', error);
            Alert.alert('Error', 'Failed to place order. Please try again.');
        } finally {
            setProcessing(false);
        }
    };

    const formatPrice = (price: number) => {
        return `$${price.toFixed(2)}`;
    };

    const renderStepIndicator = () => {
        const steps = [
            { key: 'shipping', label: 'Shipping', icon: '📍' },
            { key: 'payment', label: 'Payment', icon: '💳' },
            { key: 'review', label: 'Review', icon: '✅' },
        ];

        return (
            <View className="flex-row justify-between items-center p-4 bg-white border-b border-gray-100">
                {steps.map((step, index) => {
                    const isActive = step.key === currentStep;
                    const isCompleted = steps.findIndex(s => s.key === currentStep) > index;

                    return (
                        <View key={step.key} className="flex-1 items-center">
                            <View className={`w-10 h-10 rounded-full items-center justify-center mb-2 ${isActive ? 'bg-primary-600' : isCompleted ? 'bg-green-500' : 'bg-gray-200'
                                }`}>
                                <Text className={`text-lg ${isActive || isCompleted ? 'text-white' : 'text-gray-500'
                                    }`}>
                                    {isCompleted ? '✓' : step.icon}
                                </Text>
                            </View>
                            <Text className={`text-sm font-medium ${isActive ? 'text-primary-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                                }`}>
                                {step.label}
                            </Text>
                        </View>
                    );
                })}
            </View>
        );
    };

    const renderShippingForm = () => (
        <ScrollView className="flex-1 p-4">
            <Text className="text-2xl font-bold text-gray-900 mb-6">Shipping Address</Text>

            <View className="gap-4">
                <Input
                    label="Full Name"
                    value={shippingAddress.fullName}
                    onChangeText={(text) => setShippingAddress(prev => ({ ...prev, fullName: text }))}
                    placeholder="Enter your full name"
                    required
                />

                <Input
                    label="Phone Number"
                    value={shippingAddress.phoneNumber}
                    onChangeText={(text) => setShippingAddress(prev => ({ ...prev, phoneNumber: text }))}
                    placeholder="Enter your phone number"
                    keyboardType="phone-pad"
                    required
                />

                <Input
                    label="Address Line 1"
                    value={shippingAddress.addressLine1}
                    onChangeText={(text) => setShippingAddress(prev => ({ ...prev, addressLine1: text }))}
                    placeholder="Street address"
                    required
                />

                <Input
                    label="Address Line 2 (Optional)"
                    value={shippingAddress.addressLine2}
                    onChangeText={(text) => setShippingAddress(prev => ({ ...prev, addressLine2: text }))}
                    placeholder="Apartment, suite, etc."
                />

                <View className="flex-row gap-4">
                    <View className="flex-1">
                        <Input
                            label="City"
                            value={shippingAddress.city}
                            onChangeText={(text) => setShippingAddress(prev => ({ ...prev, city: text }))}
                            placeholder="City"
                            required
                        />
                    </View>
                    <View className="flex-1">
                        <Input
                            label="State"
                            value={shippingAddress.state}
                            onChangeText={(text) => setShippingAddress(prev => ({ ...prev, state: text }))}
                            placeholder="State"
                            required
                        />
                    </View>
                </View>

                <Input
                    label="Postal Code"
                    value={shippingAddress.postalCode}
                    onChangeText={(text) => setShippingAddress(prev => ({ ...prev, postalCode: text }))}
                    placeholder="Postal code"
                    keyboardType="numeric"
                    required
                />
            </View>
        </ScrollView>
    );

    const renderPaymentForm = () => (
        <ScrollView className="flex-1 p-4">
            <Text className="text-2xl font-bold text-gray-900 mb-6">Payment Method</Text>

            <View className="gap-4">
                <Text className="text-lg font-semibold text-gray-900 mb-2">Select Payment Option</Text>

                {/* Cash on Delivery */}
                <View className={`p-4 rounded-xl border-2 ${paymentMethod.type === 'cash_on_delivery'
                        ? 'border-primary-600 bg-primary-50'
                        : 'border-gray-200 bg-white'
                    }`}>
                    <View className="flex-row items-center justify-between">
                        <View className="flex-row items-center gap-3">
                            <Text className="text-2xl">💵</Text>
                            <View>
                                <Text className="text-lg font-semibold text-gray-900">Cash on Delivery</Text>
                                <Text className="text-gray-600">Pay when your order arrives</Text>
                            </View>
                        </View>
                        {paymentMethod.type === 'cash_on_delivery' && (
                            <Text className="text-primary-600 text-xl">✓</Text>
                        )}
                    </View>
                </View>

                {/* Mobile Money (Placeholder) */}
                <View className="p-4 rounded-xl border-2 border-gray-200 bg-gray-50 opacity-60">
                    <View className="flex-row items-center justify-between">
                        <View className="flex-row items-center gap-3">
                            <Text className="text-2xl">📱</Text>
                            <View>
                                <Text className="text-lg font-semibold text-gray-500">Mobile Money</Text>
                                <Text className="text-gray-400">Coming soon</Text>
                            </View>
                        </View>
                    </View>
                </View>

                {/* Credit Card (Placeholder) */}
                <View className="p-4 rounded-xl border-2 border-gray-200 bg-gray-50 opacity-60">
                    <View className="flex-row items-center justify-between">
                        <View className="flex-row items-center gap-3">
                            <Text className="text-2xl">💳</Text>
                            <View>
                                <Text className="text-lg font-semibold text-gray-500">Credit/Debit Card</Text>
                                <Text className="text-gray-400">Coming soon</Text>
                            </View>
                        </View>
                    </View>
                </View>
            </View>

            <View className="mt-6 p-4 bg-blue-50 rounded-xl">
                <Text className="text-blue-800 font-medium mb-1">💡 Payment Information</Text>
                <Text className="text-blue-700 text-sm">
                    Currently, we only accept cash on delivery. More payment options will be available soon.
                </Text>
            </View>
        </ScrollView>
    );

    const renderOrderReview = () => (
        <ScrollView className="flex-1 p-4">
            <Text className="text-2xl font-bold text-gray-900 mb-6">Order Review</Text>

            {/* Order Items */}
            <View className="mb-6">
                <Text className="text-lg font-semibold text-gray-900 mb-3">Order Items</Text>
                {cart?.items.map((item) => (
                    <View key={item.id} className="flex-row items-center gap-3 p-3 bg-white rounded-lg mb-2">
                        <Text className="text-2xl">{item.product.category === 'seeds' ? '🌱' :
                            item.product.category === 'fertilizers' ? '💧' : '🔧'}</Text>
                        <View className="flex-1">
                            <Text className="font-medium text-gray-900" numberOfLines={1}>
                                {item.product.name}
                            </Text>
                            <Text className="text-gray-600">
                                Qty: {item.quantity} × {formatPrice(item.product.price)}
                            </Text>
                        </View>
                        <Text className="font-semibold text-gray-900">
                            {formatPrice(item.product.price * item.quantity)}
                        </Text>
                    </View>
                ))}
            </View>

            {/* Shipping Address */}
            <View className="mb-6">
                <Text className="text-lg font-semibold text-gray-900 mb-3">Shipping Address</Text>
                <View className="p-4 bg-white rounded-xl">
                    <Text className="font-medium text-gray-900">{shippingAddress.fullName}</Text>
                    <Text className="text-gray-700">{shippingAddress.addressLine1}</Text>
                    {shippingAddress.addressLine2 && (
                        <Text className="text-gray-700">{shippingAddress.addressLine2}</Text>
                    )}
                    <Text className="text-gray-700">
                        {shippingAddress.city}, {shippingAddress.state} {shippingAddress.postalCode}
                    </Text>
                    <Text className="text-gray-700">{shippingAddress.phoneNumber}</Text>
                </View>
            </View>

            {/* Payment Method */}
            <View className="mb-6">
                <Text className="text-lg font-semibold text-gray-900 mb-3">Payment Method</Text>
                <View className="p-4 bg-white rounded-xl">
                    <Text className="font-medium text-gray-900">💵 Cash on Delivery</Text>
                    <Text className="text-gray-600">Pay when your order arrives</Text>
                </View>
            </View>

            {/* Order Summary */}
            <View className="p-4 bg-white rounded-xl">
                <Text className="text-lg font-semibold text-gray-900 mb-3">Order Summary</Text>
                <View className="gap-2">
                    <View className="flex-row justify-between">
                        <Text className="text-gray-600">Subtotal:</Text>
                        <Text className="text-gray-900">{formatPrice(cart?.totalAmount || 0)}</Text>
                    </View>
                    <View className="flex-row justify-between">
                        <Text className="text-gray-600">Shipping:</Text>
                        <Text className="text-gray-900">Free</Text>
                    </View>
                    <View className="flex-row justify-between pt-2 border-t border-gray-200">
                        <Text className="text-xl font-bold text-gray-900">Total:</Text>
                        <Text className="text-xl font-bold text-primary-600">
                            {formatPrice(cart?.totalAmount || 0)}
                        </Text>
                    </View>
                </View>
            </View>
        </ScrollView>
    );

    if (loading) {
        return (
            <SafeAreaView className="flex-1 bg-gray-50">
                <View className="flex-1 items-center justify-center">
                    <Text className="text-lg text-gray-600">Loading checkout...</Text>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView className="flex-1 bg-gray-50">
            {/* Header */}
            <View className="flex-row items-center justify-between p-4 bg-white border-b border-gray-100">
                <Button
                    title="← Back"
                    onPress={currentStep === 'shipping' ? () => router.back() : handlePreviousStep}
                    variant="ghost"
                    size="small"
                />
                <Text className="text-xl font-bold text-gray-900">Checkout</Text>
                <View style={{ width: 60 }} />
            </View>

            {renderStepIndicator()}

            {currentStep === 'shipping' && renderShippingForm()}
            {currentStep === 'payment' && renderPaymentForm()}
            {currentStep === 'review' && renderOrderReview()}

            {/* Bottom Actions */}
            <View className="p-4 bg-white border-t border-gray-200">
                {currentStep === 'review' ? (
                    <Button
                        title={processing ? 'Placing Order...' : 'Place Order'}
                        onPress={handlePlaceOrder}
                        variant="primary"
                        size="large"
                        fullWidth
                        loading={processing}
                        disabled={processing}
                    />
                ) : (
                    <Button
                        title="Continue"
                        onPress={handleNextStep}
                        variant="primary"
                        size="large"
                        fullWidth
                    />
                )}
            </View>
        </SafeAreaView>
    );
}