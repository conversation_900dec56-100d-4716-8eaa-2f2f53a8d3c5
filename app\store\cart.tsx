import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, Image, Pressable, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { ShoppingCart, CartItem } from '../../src/types/product';
import { storeService } from '../../src/services/store';
import { Button } from '../../src/components/ui/Button';

export default function CartScreen() {
    const router = useRouter();
    const [cart, setCart] = useState<ShoppingCart | null>(null);
    const [loading, setLoading] = useState(true);
    const [updating, setUpdating] = useState<string | null>(null);

    useEffect(() => {
        loadCart();
    }, []);

    const loadCart = async () => {
        try {
            setLoading(true);
            const cartData = await storeService.getCart();
            setCart(cartData);
        } catch (error) {
            console.error('Error loading cart:', error);
            Alert.alert('Error', 'Failed to load cart');
        } finally {
            setLoading(false);
        }
    };

    const updateQuantity = async (itemId: string, newQuantity: number) => {
        try {
            setUpdating(itemId);
            const updatedCart = await storeService.updateCartItemQuantity(itemId, newQuantity);
            setCart(updatedCart);
        } catch (error) {
            console.error('Error updating quantity:', error);
            Alert.alert('Error', 'Failed to update quantity');
        } finally {
            setUpdating(null);
        }
    };

    const removeItem = async (itemId: string) => {
        Alert.alert(
            'Remove Item',
            'Are you sure you want to remove this item from your cart?',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Remove',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            setUpdating(itemId);
                            const updatedCart = await storeService.removeFromCart(itemId);
                            setCart(updatedCart);
                        } catch (error) {
                            console.error('Error removing item:', error);
                            Alert.alert('Error', 'Failed to remove item');
                        } finally {
                            setUpdating(null);
                        }
                    },
                },
            ]
        );
    };

    const clearCart = async () => {
        Alert.alert(
            'Clear Cart',
            'Are you sure you want to remove all items from your cart?',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Clear All',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            const clearedCart = await storeService.clearCart();
                            setCart(clearedCart);
                        } catch (error) {
                            console.error('Error clearing cart:', error);
                            Alert.alert('Error', 'Failed to clear cart');
                        }
                    },
                },
            ]
        );
    };

    const proceedToCheckout = () => {
        if (!cart || cart.items.length === 0) {
            Alert.alert('Empty Cart', 'Please add items to your cart before checkout');
            return;
        }
        router.push('/store/checkout');
    };

    const formatPrice = (price: number) => {
        return `$${price.toFixed(2)}`;
    };

    const renderCartItem = (item: CartItem) => {
        const isUpdating = updating === item.id;
        const itemTotal = item.product.price * item.quantity;

        return (
            <View key={item.id} className="bg-white rounded-xl p-4 mb-3 shadow-sm border border-gray-100">
                <View className="flex-row gap-4">
                    <Image
                        source={{ uri: item.product.imageUrls[0] }}
                        className="w-20 h-20 rounded-lg bg-gray-100"
                        resizeMode="cover"
                    />

                    <View className="flex-1">
                        <Text className="text-lg font-semibold text-gray-900 mb-1" numberOfLines={2}>
                            {item.product.name}
                        </Text>

                        <Text className="text-sm text-gray-600 mb-2" numberOfLines={1}>
                            {item.product.brand}
                        </Text>

                        <Text className="text-lg font-bold text-primary-600 mb-3">
                            {formatPrice(item.product.price)} each
                        </Text>

                        {/* Quantity Controls */}
                        <View className="flex-row items-center justify-between">
                            <View className="flex-row items-center gap-3">
                                <Pressable
                                    onPress={() => updateQuantity(item.id, item.quantity - 1)}
                                    disabled={isUpdating || item.quantity <= 1}
                                    className={`w-8 h-8 rounded-lg items-center justify-center ${isUpdating || item.quantity <= 1
                                            ? 'bg-gray-100'
                                            : 'bg-gray-200 active:opacity-80'
                                        }`}
                                    accessibilityRole="button"
                                    accessibilityLabel="Decrease quantity"
                                >
                                    <Text className={`text-lg font-bold ${isUpdating || item.quantity <= 1 ? 'text-gray-400' : 'text-gray-600'
                                        }`}>
                                        −
                                    </Text>
                                </Pressable>

                                <Text className="text-lg font-semibold text-gray-900 min-w-[30px] text-center">
                                    {item.quantity}
                                </Text>

                                <Pressable
                                    onPress={() => updateQuantity(item.id, item.quantity + 1)}
                                    disabled={isUpdating || item.quantity >= item.product.stockQuantity}
                                    className={`w-8 h-8 rounded-lg items-center justify-center ${isUpdating || item.quantity >= item.product.stockQuantity
                                            ? 'bg-gray-100'
                                            : 'bg-gray-200 active:opacity-80'
                                        }`}
                                    accessibilityRole="button"
                                    accessibilityLabel="Increase quantity"
                                >
                                    <Text className={`text-lg font-bold ${isUpdating || item.quantity >= item.product.stockQuantity
                                            ? 'text-gray-400'
                                            : 'text-gray-600'
                                        }`}>
                                        +
                                    </Text>
                                </Pressable>
                            </View>

                            <Pressable
                                onPress={() => removeItem(item.id)}
                                disabled={isUpdating}
                                className="p-2 active:opacity-80"
                                accessibilityRole="button"
                                accessibilityLabel="Remove item"
                            >
                                <Text className="text-red-500 text-lg">🗑️</Text>
                            </Pressable>
                        </View>
                    </View>
                </View>

                {/* Item Total */}
                <View className="flex-row justify-between items-center mt-3 pt-3 border-t border-gray-100">
                    <Text className="text-gray-600">Subtotal:</Text>
                    <Text className="text-xl font-bold text-gray-900">
                        {formatPrice(itemTotal)}
                    </Text>
                </View>

                {isUpdating && (
                    <View className="absolute inset-0 bg-white/80 rounded-xl items-center justify-center">
                        <Text className="text-gray-600">Updating...</Text>
                    </View>
                )}
            </View>
        );
    };

    if (loading) {
        return (
            <SafeAreaView className="flex-1 bg-gray-50">
                <View className="flex-1 items-center justify-center">
                    <Text className="text-lg text-gray-600">Loading cart...</Text>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView className="flex-1 bg-gray-50">
            {/* Header */}
            <View className="flex-row items-center justify-between p-4 bg-white border-b border-gray-100">
                <Pressable
                    onPress={() => router.back()}
                    className="p-2 active:opacity-80"
                    accessibilityRole="button"
                    accessibilityLabel="Go back"
                >
                    <Text className="text-2xl">←</Text>
                </Pressable>
                <Text className="text-xl font-bold text-gray-900">Shopping Cart</Text>
                {cart && cart.items.length > 0 && (
                    <Pressable
                        onPress={clearCart}
                        className="p-2 active:opacity-80"
                        accessibilityRole="button"
                        accessibilityLabel="Clear cart"
                    >
                        <Text className="text-red-500 font-medium">Clear</Text>
                    </Pressable>
                )}
            </View>

            {!cart || cart.items.length === 0 ? (
                /* Empty Cart */
                <View className="flex-1 items-center justify-center px-8">
                    <Text className="text-6xl mb-4">🛒</Text>
                    <Text className="text-2xl font-bold text-gray-900 mb-2">
                        Your cart is empty
                    </Text>
                    <Text className="text-gray-600 text-center mb-8 leading-6">
                        Browse our products and add items to your cart to get started
                    </Text>
                    <Button
                        title="Continue Shopping"
                        onPress={() => router.push('/(tabs)/store')}
                        variant="primary"
                        size="large"
                    />
                </View>
            ) : (
                /* Cart with Items */
                <View className="flex-1">
                    <ScrollView className="flex-1 p-4">
                        {cart.items.map(renderCartItem)}
                    </ScrollView>

                    {/* Cart Summary */}
                    <View className="bg-white border-t border-gray-200 p-4">
                        <View className="mb-4">
                            <View className="flex-row justify-between items-center mb-2">
                                <Text className="text-gray-600">Items ({cart.itemCount}):</Text>
                                <Text className="text-gray-900 font-medium">
                                    {formatPrice(cart.totalAmount)}
                                </Text>
                            </View>

                            <View className="flex-row justify-between items-center mb-2">
                                <Text className="text-gray-600">Shipping:</Text>
                                <Text className="text-gray-900 font-medium">
                                    Free
                                </Text>
                            </View>

                            <View className="flex-row justify-between items-center pt-2 border-t border-gray-200">
                                <Text className="text-xl font-bold text-gray-900">Total:</Text>
                                <Text className="text-2xl font-bold text-primary-600">
                                    {formatPrice(cart.totalAmount)}
                                </Text>
                            </View>
                        </View>

                        <View className="gap-3">
                            <Button
                                title="Proceed to Checkout"
                                onPress={proceedToCheckout}
                                variant="primary"
                                size="large"
                                fullWidth
                            />

                            <Button
                                title="Continue Shopping"
                                onPress={() => router.push('/(tabs)/store')}
                                variant="outline"
                                size="large"
                                fullWidth
                            />
                        </View>
                    </View>
                </View>
            )}
        </SafeAreaView>
    );
}