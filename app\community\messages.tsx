import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    FlatList,
    TouchableOpacity,
    TextInput,
    Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../../src/design-system';
import { VoiceButton } from '../../src/components/ui/VoiceButton';
import { SocialService, Message } from '../../src/services/supabase/social';
import { useVoiceStore } from '../../src/stores/voice';

interface Conversation {
    userId: string;
    userName: string;
    userAvatar?: string;
    lastMessage: string;
    lastMessageTime: Date;
    unreadCount: number;
}

export default function MessagesScreen() {
    const [conversations, setConversations] = useState<Conversation[]>([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [loading, setLoading] = useState(true);

    const { speak, isVoiceEnabled, startListening } = useVoiceStore();

    useEffect(() => {
        loadConversations();
    }, []);

    const loadConversations = async () => {
        try {
            setLoading(true);

            // In a real implementation, this would fetch conversation summaries
            // For now, we'll show a placeholder
            const mockConversations: Conversation[] = [
                {
                    userId: 'user1',
                    userName: 'Ahmed Hassan',
                    lastMessage: 'Thanks for the advice about the corn irrigation!',
                    lastMessageTime: new Date(Date.now() - 2 * 60 * 60 * 1000),
                    unreadCount: 2,
                },
                {
                    userId: 'user2',
                    userName: 'Fatima Al-Zahra',
                    lastMessage: 'The fertilizer you recommended worked great.',
                    lastMessageTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
                    unreadCount: 0,
                },
                {
                    userId: 'user3',
                    userName: 'Mohamed Salah',
                    lastMessage: 'Can you share more details about your organic method?',
                    lastMessageTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
                    unreadCount: 1,
                },
            ];

            setConversations(mockConversations);
        } catch (error) {
            Alert.alert('Error', 'Failed to load conversations');
        } finally {
            setLoading(false);
        }
    };

    const handleVoiceSearch = async () => {
        try {
            if (isVoiceEnabled) {
                speak('Say the name of the person you want to message');
                const voiceText = await startListening();

                if (voiceText) {
                    setSearchQuery(voiceText);
                    speak(`Searching for ${voiceText}`);
                }
            }
        } catch (error) {
            Alert.alert('Voice Error', 'Failed to process voice input');
        }
    };

    const formatTime = (date: Date) => {
        const now = new Date();
        const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

        if (diffInHours < 1) return 'Just now';
        if (diffInHours < 24) return `${diffInHours}h ago`;
        if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
        return date.toLocaleDateString();
    };

    const filteredConversations = conversations.filter(conv =>
        conv.userName.toLowerCase().includes(searchQuery.toLowerCase())
    );

    const renderConversation = ({ item }: { item: Conversation }) => (
        <TouchableOpacity
            style={{
                flexDirection: 'row',
                alignItems: 'center',
                padding: 16,
                backgroundColor: 'white',
                marginHorizontal: 16,
                marginVertical: 4,
                borderRadius: 12,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.1,
                shadowRadius: 2,
                elevation: 2,
            }}
            onPress={() => router.push(`/community/chat/${item.userId}`)}
            accessibilityLabel={`Message conversation with ${item.userName}`}
        >
            {/* Avatar */}
            <View style={{
                width: 50,
                height: 50,
                borderRadius: 25,
                backgroundColor: colors.primary[100],
                justifyContent: 'center',
                alignItems: 'center',
                marginRight: 12,
            }}>
                <Ionicons name="person" size={24} color={colors.primary[500]} />
            </View>

            {/* Conversation Info */}
            <View style={{ flex: 1 }}>
                <View style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: 4,
                }}>
                    <Text style={{
                        fontSize: 16,
                        fontWeight: '600',
                        color: colors.primary[900],
                    }}>
                        {item.userName}
                    </Text>
                    <Text style={{
                        fontSize: 12,
                        color: colors.earth[500],
                    }}>
                        {formatTime(item.lastMessageTime)}
                    </Text>
                </View>

                <View style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                }}>
                    <Text
                        style={{
                            fontSize: 14,
                            color: colors.earth[600],
                            flex: 1,
                        }}
                        numberOfLines={1}
                    >
                        {item.lastMessage}
                    </Text>

                    {item.unreadCount > 0 && (
                        <View style={{
                            backgroundColor: colors.primary[500],
                            borderRadius: 10,
                            minWidth: 20,
                            height: 20,
                            justifyContent: 'center',
                            alignItems: 'center',
                            marginLeft: 8,
                        }}>
                            <Text style={{
                                color: 'white',
                                fontSize: 12,
                                fontWeight: '600',
                            }}>
                                {item.unreadCount}
                            </Text>
                        </View>
                    )}
                </View>
            </View>
        </TouchableOpacity>
    );

    return (
        <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary[50] }}>
            {/* Header */}
            <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingHorizontal: 16,
                paddingVertical: 12,
                backgroundColor: 'white',
                borderBottomWidth: 1,
                borderBottomColor: colors.primary[100],
            }}>
                <TouchableOpacity
                    onPress={() => router.back()}
                    accessibilityLabel="Go back"
                >
                    <Ionicons name="arrow-back" size={24} color={colors.primary[900]} />
                </TouchableOpacity>

                <Text style={{
                    fontSize: 20,
                    fontWeight: 'bold',
                    color: colors.primary[900],
                }}>
                    Messages
                </Text>

                <TouchableOpacity
                    onPress={() => {
                        // Navigate to new message screen
                        Alert.alert('Coming Soon', 'New message feature will be available soon');
                    }}
                    accessibilityLabel="Start new message"
                >
                    <Ionicons name="create-outline" size={24} color={colors.primary[500]} />
                </TouchableOpacity>
            </View>

            {/* Search Bar */}
            <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                margin: 16,
                backgroundColor: 'white',
                borderRadius: 12,
                paddingHorizontal: 16,
                paddingVertical: 8,
                gap: 8,
            }}>
                <Ionicons name="search" size={20} color={colors.earth[500]} />
                <TextInput
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                    placeholder="Search conversations..."
                    style={{
                        flex: 1,
                        fontSize: 16,
                        paddingVertical: 8,
                    }}
                    accessibilityLabel="Search conversations"
                />
                <VoiceButton onPress={handleVoiceSearch} size="small" />
            </View>

            {/* Conversations List */}
            {loading ? (
                <View style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                }}>
                    <Text style={{ color: colors.earth[500] }}>Loading conversations...</Text>
                </View>
            ) : filteredConversations.length === 0 ? (
                <View style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                    paddingHorizontal: 40,
                }}>
                    <Ionicons name="chatbubbles-outline" size={64} color={colors.earth[300]} />
                    <Text style={{
                        fontSize: 18,
                        fontWeight: '600',
                        color: colors.earth[600],
                        marginTop: 16,
                        textAlign: 'center',
                    }}>
                        {searchQuery ? 'No conversations found' : 'No messages yet'}
                    </Text>
                    <Text style={{
                        fontSize: 14,
                        color: colors.earth[500],
                        marginTop: 8,
                        textAlign: 'center',
                    }}>
                        {searchQuery
                            ? 'Try searching with a different name'
                            : 'Start connecting with other farmers in the community!'
                        }
                    </Text>
                    {!searchQuery && (
                        <TouchableOpacity
                            onPress={() => router.push('/community')}
                            style={{
                                backgroundColor: colors.primary[500],
                                paddingHorizontal: 24,
                                paddingVertical: 12,
                                borderRadius: 24,
                                marginTop: 20,
                            }}
                        >
                            <Text style={{ color: 'white', fontWeight: '600' }}>
                                Explore Community
                            </Text>
                        </TouchableOpacity>
                    )}
                </View>
            ) : (
                <FlatList
                    data={filteredConversations}
                    renderItem={renderConversation}
                    keyExtractor={(item) => item.userId}
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{ paddingBottom: 20 }}
                />
            )}
        </SafeAreaView>
    );
}