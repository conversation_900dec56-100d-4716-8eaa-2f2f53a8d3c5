import { Camera } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import { CaptureMode, CaptureGuideline, ImageAnalysisRequest, AnalysisProgress, ImageAnalysisResult } from '../types/camera';

export class CameraService {
    static async requestPermissions(): Promise<boolean> {
        const cameraPermission = await Camera.requestCameraPermissionsAsync();
        const mediaLibraryPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();

        return cameraPermission.status === 'granted' && mediaLibraryPermission.status === 'granted';
    }

    static getCaptureGuidelines(): CaptureGuideline[] {
        return [
            {
                mode: 'plant',
                title: 'Plant Analysis',
                tips: [
                    'Hold camera 6-12 inches from the plant',
                    'Ensure good lighting, avoid shadows',
                    'Focus on affected areas or leaves',
                    'Include multiple leaves if possible'
                ],
                voiceInstructions: 'Position your camera 6 to 12 inches from the plant. Make sure you have good lighting and focus on any affected areas or leaves.'
            },
            {
                mode: 'soil',
                title: 'Soil Analysis',
                tips: [
                    'Clear debris from soil surface',
                    'Take photo in natural daylight',
                    'Include a reference object for scale',
                    'Show soil texture and color clearly'
                ],
                voiceInstructions: 'Clear any debris from the soil surface. Take the photo in natural daylight and make sure the soil texture and color are clearly visible.'
            },
            {
                mode: 'fertilizer',
                title: 'Fertilizer Analysis',
                tips: [
                    'Show fertilizer application area',
                    'Capture any discoloration or issues',
                    'Include surrounding healthy areas',
                    'Ensure clear focus on problem areas'
                ],
                voiceInstructions: 'Show the fertilizer application area. Capture any discoloration or issues, and include surrounding healthy areas for comparison.'
            }
        ];
    }

    static getGuidelineForMode(mode: CaptureMode): CaptureGuideline {
        const guidelines = this.getCaptureGuidelines();
        return guidelines.find(g => g.mode === mode) || guidelines[0];
    }

    // Mock analysis function - in real implementation, this would call AI service
    static async analyzeImage(request: ImageAnalysisRequest): Promise<ImageAnalysisResult> {
        // Simulate analysis delay
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Mock result based on mode
        const mockResults: Record<CaptureMode, Partial<ImageAnalysisResult>> = {
            plant: {
                issues: [
                    {
                        id: '1',
                        name: 'Early Blight',
                        severity: 'medium',
                        confidence: 0.85,
                        description: 'Fungal disease affecting leaves with dark spots and yellowing'
                    }
                ],
                recommendations: [
                    {
                        id: '1',
                        title: 'Apply Copper Fungicide',
                        description: 'Spray copper-based fungicide every 7-10 days',
                        priority: 'high',
                        actionType: 'treatment',
                        estimatedCost: 25,
                        productRecommendations: ['Copper Sulfate Spray', 'Organic Copper Fungicide']
                    },
                    {
                        id: '2',
                        title: 'Improve Air Circulation',
                        description: 'Prune lower branches and space plants properly',
                        priority: 'medium',
                        actionType: 'prevention'
                    }
                ],
                overallHealth: 'fair',
                confidence: 0.85,
                summary: 'Your plant shows signs of early blight. Apply copper fungicide and improve air circulation.'
            },
            soil: {
                issues: [
                    {
                        id: '1',
                        name: 'Nutrient Deficiency',
                        severity: 'medium',
                        confidence: 0.78,
                        description: 'Soil appears to lack nitrogen based on color and texture'
                    }
                ],
                recommendations: [
                    {
                        id: '1',
                        title: 'Add Nitrogen Fertilizer',
                        description: 'Apply nitrogen-rich fertilizer to improve soil nutrients',
                        priority: 'high',
                        actionType: 'treatment',
                        estimatedCost: 15,
                        productRecommendations: ['Urea Fertilizer', 'Compost']
                    }
                ],
                overallHealth: 'fair',
                confidence: 0.78,
                summary: 'Your soil shows signs of nitrogen deficiency. Consider adding nitrogen-rich fertilizer.'
            },
            fertilizer: {
                issues: [
                    {
                        id: '1',
                        name: 'Over-fertilization',
                        severity: 'high',
                        confidence: 0.92,
                        description: 'Signs of fertilizer burn visible on plant leaves'
                    }
                ],
                recommendations: [
                    {
                        id: '1',
                        title: 'Flush with Water',
                        description: 'Water thoroughly to dilute excess fertilizer',
                        priority: 'high',
                        actionType: 'treatment'
                    },
                    {
                        id: '2',
                        title: 'Reduce Fertilizer Amount',
                        description: 'Use half the recommended amount in future applications',
                        priority: 'high',
                        actionType: 'prevention'
                    }
                ],
                overallHealth: 'poor',
                confidence: 0.92,
                summary: 'Over-fertilization detected. Flush with water immediately and reduce future applications.'
            }
        };

        return {
            id: Date.now().toString(),
            imageUri: request.imageUri,
            mode: request.mode,
            timestamp: new Date(),
            issues: mockResults[request.mode].issues || [],
            recommendations: mockResults[request.mode].recommendations || [],
            overallHealth: mockResults[request.mode].overallHealth || 'good',
            confidence: mockResults[request.mode].confidence || 0.8,
            summary: mockResults[request.mode].summary || 'Analysis complete.'
        };
    }

    static getAnalysisProgressSteps(mode: CaptureMode): AnalysisProgress[] {
        const baseSteps = [
            { percentage: 10, message: 'Processing image...', voiceMessage: 'Processing your image' },
            { percentage: 30, message: 'Analyzing visual features...', voiceMessage: 'Analyzing visual features' },
            { percentage: 60, message: 'Identifying issues...', voiceMessage: 'Identifying potential issues' },
            { percentage: 80, message: 'Generating recommendations...', voiceMessage: 'Generating recommendations' },
            { percentage: 100, message: 'Analysis complete!', voiceMessage: 'Analysis complete' }
        ];

        const modeSpecificSteps: Record<CaptureMode, AnalysisProgress[]> = {
            plant: [
                { percentage: 10, message: 'Processing plant image...', voiceMessage: 'Processing your plant image' },
                { percentage: 30, message: 'Analyzing leaf patterns...', voiceMessage: 'Analyzing leaf patterns and colors' },
                { percentage: 60, message: 'Detecting diseases and pests...', voiceMessage: 'Detecting diseases and pests' },
                { percentage: 80, message: 'Generating treatment plan...', voiceMessage: 'Generating treatment recommendations' },
                { percentage: 100, message: 'Plant analysis complete!', voiceMessage: 'Plant analysis complete' }
            ],
            soil: [
                { percentage: 10, message: 'Processing soil image...', voiceMessage: 'Processing your soil image' },
                { percentage: 30, message: 'Analyzing soil composition...', voiceMessage: 'Analyzing soil composition and texture' },
                { percentage: 60, message: 'Checking nutrient levels...', voiceMessage: 'Checking nutrient levels' },
                { percentage: 80, message: 'Creating soil improvement plan...', voiceMessage: 'Creating soil improvement recommendations' },
                { percentage: 100, message: 'Soil analysis complete!', voiceMessage: 'Soil analysis complete' }
            ],
            fertilizer: [
                { percentage: 10, message: 'Processing fertilizer image...', voiceMessage: 'Processing your fertilizer image' },
                { percentage: 30, message: 'Analyzing application effects...', voiceMessage: 'Analyzing fertilizer application effects' },
                { percentage: 60, message: 'Detecting over or under-fertilization...', voiceMessage: 'Detecting fertilization issues' },
                { percentage: 80, message: 'Optimizing fertilizer schedule...', voiceMessage: 'Optimizing fertilizer recommendations' },
                { percentage: 100, message: 'Fertilizer analysis complete!', voiceMessage: 'Fertilizer analysis complete' }
            ]
        };

        return modeSpecificSteps[mode] || baseSteps;
    }
}