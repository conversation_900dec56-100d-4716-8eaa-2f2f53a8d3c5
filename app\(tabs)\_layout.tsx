import { Redirect, Tabs, useRootNavigationState } from 'expo-router';
import { Text, View, ActivityIndicator } from 'react-native';
import { colors } from '../../src/design-system';
import { useAuthStore } from '../../src/stores/auth';

export default function TabLayout() {
  const rootNavigationState = useRootNavigationState();
  const { isAuthenticated, isLoading } = useAuthStore();

  // انتظار جاهزية نظام الملاحة قبل أي توجيه
  if (!rootNavigationState?.key || isLoading) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: '#f8f9fa',
        }}>
        <ActivityIndicator size="large" color="#22c55e" />
      </View>
    );
  }

  if (!isAuthenticated) return <Redirect href="/(auth)/register" />;
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: colors.primary[600],
        tabBarInactiveTintColor: colors.neutral.gray[500],
        tabBarStyle: {
          backgroundColor: 'white',
          borderTopWidth: 1,
          borderTopColor: colors.neutral.gray[200],
          paddingBottom: 8,
          paddingTop: 8,
          height: 80,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
          marginTop: 4,
        },
        tabBarIconStyle: {
          marginBottom: 4,
        },
      }}>
      <Tabs.Screen
        name="home"
        options={{
          title: 'Home',
          tabBarIcon: ({ color, size }) => <TabIcon icon="🏠" color={color} size={size} />,
        }}
      />
      <Tabs.Screen
        name="crops"
        options={{
          title: 'Crops',
          tabBarIcon: ({ color, size }) => <TabIcon icon="🌱" color={color} size={size} />,
        }}
      />
      <Tabs.Screen
        name="ai"
        options={{
          title: 'AI Assistant',
          tabBarIcon: ({ color, size }) => <TabIcon icon="💬" color={color} size={size} />,
        }}
      />
      <Tabs.Screen
        name="store"
        options={{
          title: 'Store',
          tabBarIcon: ({ color, size }) => <TabIcon icon="🛒" color={color} size={size} />,
        }}
      />
      <Tabs.Screen
        name="community"
        options={{
          title: 'Community',
          tabBarIcon: ({ color, size }) => <TabIcon icon="👥" color={color} size={size} />,
        }}
      />
    </Tabs>
  );
}

interface TabIconProps {
  icon: string;
  color: string;
  size: number;
}

function TabIcon({ icon, color, size }: TabIconProps) {
  return (
    <Text
      style={{
        fontSize: size,
        color: color,
        textAlign: 'center',
      }}>
      {icon}
    </Text>
  );
}
