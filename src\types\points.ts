export interface PointsTransaction {
    id: string;
    user_id: string;
    amount: number;
    transaction_type: 'earned' | 'spent' | 'bonus' | 'refund';
    source: 'task_completion' | 'daily_checkin' | 'community_post' | 'ai_consultation' | 'subscription_bonus' | 'achievement' | 'referral' | 'admin_adjustment';
    description: string;
    metadata?: {
        task_id?: string;
        post_id?: string;
        achievement_id?: string;
        referral_id?: string;
        admin_note?: string;
    };
    created_at: string;
}

export interface PointsBalance {
    user_id: string;
    total_points: number;
    available_points: number;
    pending_points: number;
    lifetime_earned: number;
    lifetime_spent: number;
    last_updated: string;
}

export interface PointsEarningRule {
    id: string;
    source: PointsTransaction['source'];
    base_points: number;
    multiplier?: number;
    daily_limit?: number;
    weekly_limit?: number;
    monthly_limit?: number;
    requires_subscription?: string[];
    is_active: boolean;
    description: string;
}

export interface Achievement {
    id: string;
    name: string;
    description: string;
    icon: string;
    category: 'farming' | 'community' | 'learning' | 'consistency' | 'milestone';
    points_reward: number;
    requirements: {
        type: 'task_count' | 'days_active' | 'community_posts' | 'ai_consultations' | 'crop_plans' | 'points_earned';
        target_value: number;
        timeframe?: 'daily' | 'weekly' | 'monthly' | 'all_time';
    };
    is_active: boolean;
    created_at: string;
}

export interface UserAchievement {
    id: string;
    user_id: string;
    achievement_id: string;
    earned_at: string;
    points_awarded: number;
}

export interface LeaderboardEntry {
    user_id: string;
    first_name: string;
    last_name: string;
    total_points: number;
    rank: number;
    achievements_count: number;
    completed_tasks: number;
    days_active: number;
}

export interface PointsRedemption {
    id: string;
    user_id: string;
    points_spent: number;
    reward_type: 'ai_consultation' | 'premium_feature' | 'discount_code' | 'physical_reward';
    reward_details: {
        consultation_credits?: number;
        feature_access?: string[];
        discount_percentage?: number;
        discount_code?: string;
        product_id?: string;
        shipping_address?: any;
    };
    status: 'pending' | 'processed' | 'delivered' | 'cancelled';
    created_at: string;
    processed_at?: string;
}

export interface DailyStreak {
    user_id: string;
    current_streak: number;
    longest_streak: number;
    last_activity_date: string;
    streak_multiplier: number;
}

// Constants for points earning
export const POINTS_EARNING_RULES: Omit<PointsEarningRule, 'id'>[] = [
    {
        source: 'task_completion',
        base_points: 10,
        multiplier: 1,
        daily_limit: 200,
        is_active: true,
        description: 'Points earned for completing farming tasks',
    },
    {
        source: 'daily_checkin',
        base_points: 5,
        daily_limit: 5,
        is_active: true,
        description: 'Daily check-in bonus points',
    },
    {
        source: 'community_post',
        base_points: 15,
        daily_limit: 60,
        is_active: true,
        description: 'Points for creating community posts',
    },
    {
        source: 'ai_consultation',
        base_points: -20,
        is_active: true,
        description: 'Points spent for AI consultations',
    },
    {
        source: 'subscription_bonus',
        base_points: 100,
        monthly_limit: 100,
        requires_subscription: ['basic', 'premium', 'pro'],
        is_active: true,
        description: 'Monthly subscription bonus points',
    },
    {
        source: 'achievement',
        base_points: 50,
        is_active: true,
        description: 'Points earned from achievements',
    },
    {
        source: 'referral',
        base_points: 100,
        is_active: true,
        description: 'Points earned for successful referrals',
    },
];

export const DEFAULT_ACHIEVEMENTS: Omit<Achievement, 'id' | 'created_at'>[] = [
    {
        name: 'First Steps',
        description: 'Complete your first farming task',
        icon: '🌱',
        category: 'farming',
        points_reward: 25,
        requirements: {
            type: 'task_count',
            target_value: 1,
        },
        is_active: true,
    },
    {
        name: 'Dedicated Farmer',
        description: 'Complete 10 farming tasks',
        icon: '🚜',
        category: 'farming',
        points_reward: 100,
        requirements: {
            type: 'task_count',
            target_value: 10,
        },
        is_active: true,
    },
    {
        name: 'Community Helper',
        description: 'Create 5 community posts',
        icon: '🤝',
        category: 'community',
        points_reward: 75,
        requirements: {
            type: 'community_posts',
            target_value: 5,
        },
        is_active: true,
    },
    {
        name: 'AI Explorer',
        description: 'Use AI consultation 10 times',
        icon: '🤖',
        category: 'learning',
        points_reward: 50,
        requirements: {
            type: 'ai_consultations',
            target_value: 10,
        },
        is_active: true,
    },
    {
        name: 'Consistent Farmer',
        description: 'Stay active for 7 consecutive days',
        icon: '📅',
        category: 'consistency',
        points_reward: 150,
        requirements: {
            type: 'days_active',
            target_value: 7,
            timeframe: 'daily',
        },
        is_active: true,
    },
    {
        name: 'Points Collector',
        description: 'Earn 1000 total points',
        icon: '💎',
        category: 'milestone',
        points_reward: 200,
        requirements: {
            type: 'points_earned',
            target_value: 1000,
            timeframe: 'all_time',
        },
        is_active: true,
    },
];

export const POINTS_REDEMPTION_OPTIONS = [
    {
        reward_type: 'ai_consultation' as const,
        points_cost: 20,
        title: 'AI Consultation',
        description: 'Get one AI consultation credit',
        icon: '🤖',
    },
    {
        reward_type: 'premium_feature' as const,
        points_cost: 500,
        title: '7-Day Premium Access',
        description: 'Unlock premium features for 7 days',
        icon: '⭐',
    },
    {
        reward_type: 'discount_code' as const,
        points_cost: 200,
        title: '10% Store Discount',
        description: 'Get 10% off your next store purchase',
        icon: '🏷️',
    },
];