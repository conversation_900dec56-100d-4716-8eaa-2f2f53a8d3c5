import React from 'react';
import { View, Pressable, Text } from 'react-native';

export interface HeaderWeatherData {
  temperature: number;
  condition: string;
  icon: string;
  location?: string;
}

export interface HeaderNavigationProps {
  title?: string;
  showBackButton?: boolean;
  onBackPress?: () => void;
  showWeather?: boolean;
  weatherData?: HeaderWeatherData;
  onWeatherPress?: () => void;
  showVoiceToggle?: boolean;
  isVoiceEnabled?: boolean;
  onVoiceToggle?: () => void;
  rightActions?: React.ReactNode;
  voiceFeedbackEnabled?: boolean;
  onVoiceFeedback?: (text: string) => void;
  testID?: string;
}

export const HeaderNavigation: React.FC<HeaderNavigationProps> = ({
  title,
  showBackButton = false,
  onBackPress,
  showWeather = false,
  weatherData,
  onWeatherPress,
  showVoiceToggle = false,
  isVoiceEnabled = false,
  onVoiceToggle,
  rightActions,
  voiceFeedbackEnabled = false,
  onVoiceFeedback,
  testID,
}) => {
  const handleBackPress = () => {
    if (voiceFeedbackEnabled && onVoiceFeedback) {
      onVoiceFeedback('Going back');
    }
    onBackPress?.();
  };

  const handleVoiceToggle = () => {
    if (voiceFeedbackEnabled && onVoiceFeedback) {
      onVoiceFeedback(isVoiceEnabled ? 'Voice mode disabled' : 'Voice mode enabled');
    }
    onVoiceToggle?.();
  };

  const handleWeatherPress = () => {
    if (voiceFeedbackEnabled && onVoiceFeedback && weatherData) {
      onVoiceFeedback(`Weather: ${weatherData.temperature} degrees, ${weatherData.condition}`);
    }
    onWeatherPress?.();
  };

  return (
    <View className="border-b border-earth-200 bg-white shadow-sm" testID={testID}>
      <View className="min-h-[64px] flex-row items-center justify-between px-4 py-3">
        {/* Left Section */}
        <View className="flex-1 flex-row items-center">
          {showBackButton && (
            <Pressable
              onPress={handleBackPress}
              className="mr-3 min-h-[44px] min-w-[44px] items-center justify-center rounded-full bg-earth-100 p-2 active:bg-earth-200"
              accessibilityRole="button"
              accessibilityLabel="Go back"
              accessibilityHint="Navigate to the previous screen"
              testID="back-button">
              <Text className="text-lg font-bold text-earth-700">←</Text>
            </Pressable>
          )}

          {title && <Text className="flex-1 text-xl font-bold text-earth-900">{title}</Text>}

          {showWeather && weatherData && (
            <Pressable
              onPress={handleWeatherPress}
              className="min-h-[44px] flex-row items-center rounded-xl bg-blue-50 px-3 py-2 active:bg-blue-100"
              accessibilityRole="button"
              accessibilityLabel={`Weather: ${weatherData.temperature} degrees ${weatherData.condition}`}
              accessibilityHint="Tap for detailed weather information"
              testID="weather-button">
              <Text className="mr-2 text-lg">{weatherData.icon}</Text>
              <View>
                <Text className="text-sm font-semibold text-blue-700">
                  {weatherData.temperature}°F
                </Text>
                {weatherData.location && (
                  <Text className="text-xs text-blue-600">{weatherData.location}</Text>
                )}
              </View>
            </Pressable>
          )}
        </View>

        {/* Right Section */}
        <View className="flex-row items-center gap-2">
          {showVoiceToggle && (
            <Pressable
              onPress={handleVoiceToggle}
              className={`
                                min-h-[44px] min-w-[44px] items-center justify-center rounded-full p-3
                                ${isVoiceEnabled
                  ? 'bg-primary-100 active:bg-primary-200'
                  : 'bg-earth-100 active:bg-earth-200'
                }
                            `}
              accessibilityRole="switch"
              accessibilityState={{ checked: isVoiceEnabled }}
              accessibilityLabel="Voice mode toggle"
              accessibilityHint={`${isVoiceEnabled ? 'Disable' : 'Enable'} voice feedback and commands`}
              testID="voice-toggle">
              <Text className={`text-lg ${isVoiceEnabled ? 'text-primary-600' : 'text-earth-600'}`}>
                {isVoiceEnabled ? '🔊' : '🔇'}
              </Text>
            </Pressable>
          )}

          {rightActions}
        </View>
      </View>

      {/* Voice Mode Indicator */}
      {isVoiceEnabled && (
        <View className="border-t border-primary-200 bg-primary-100 px-4 py-2">
          <Text className="text-center text-sm font-medium text-primary-700">
            🎤 Voice Mode Active - Speak commands or tap to interact
          </Text>
        </View>
      )}
    </View>
  );
};
