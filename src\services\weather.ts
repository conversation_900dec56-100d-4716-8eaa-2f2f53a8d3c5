import { LocationCoordinates } from './location';

export interface WeatherCondition {
    id: number;
    main: string;
    description: string;
    icon: string;
}

export interface CurrentWeather {
    temperature: number;
    feelsLike: number;
    humidity: number;
    pressure: number;
    visibility: number;
    uvIndex: number;
    windSpeed: number;
    windDirection: number;
    conditions: WeatherCondition[];
    timestamp: Date;
}

export interface WeatherForecast {
    date: Date;
    temperature: {
        min: number;
        max: number;
    };
    conditions: WeatherCondition[];
    humidity: number;
    windSpeed: number;
    precipitation: number;
}

export interface WeatherAlert {
    id: string;
    title: string;
    description: string;
    severity: 'minor' | 'moderate' | 'severe' | 'extreme';
    startTime: Date;
    endTime: Date;
    areas: string[];
    agriculturalImpact?: {
        affectedCrops: string[];
        recommendations: string[];
        recommendationsAr: string[];
        urgency: 'low' | 'medium' | 'high' | 'critical';
    };
}

export interface AgriculturalAlert {
    id: string;
    type: 'frost' | 'drought' | 'heavy_rain' | 'high_wind' | 'extreme_heat' | 'hail' | 'pest_weather';
    severity: 'low' | 'medium' | 'high' | 'critical';
    title: string;
    titleAr: string;
    message: string;
    messageAr: string;
    startDate: Date;
    endDate: Date;
    affectedCrops: string[];
    protectiveMeasures: string[];
    protectiveMeasuresAr: string[];
    taskAdjustments: {
        postpone: string[];
        prioritize: string[];
        cancel: string[];
    };
}

export interface HistoricalWeatherData {
    date: Date;
    temperature: { min: number; max: number; avg: number };
    precipitation: number;
    humidity: number;
    windSpeed: number;
}

export interface WeatherPattern {
    season: 'spring' | 'summer' | 'fall' | 'winter';
    avgTemperature: number;
    avgPrecipitation: number;
    avgHumidity: number;
    optimalCrops: string[];
    riskFactors: string[];
}

export interface WeatherData {
    current: CurrentWeather;
    forecast: WeatherForecast[];
    alerts: WeatherAlert[];
    location: {
        name: string;
        coordinates: LocationCoordinates;
    };
}

export class WeatherService {
    private static instance: WeatherService;
    private apiKey: string = process.env.EXPO_PUBLIC_OPENWEATHER_API_KEY || '';
    private baseUrl: string = 'https://api.openweathermap.org/data/2.5';
    private historicalData: Map<string, HistoricalWeatherData[]> = new Map();

    static getInstance(): WeatherService {
        if (!WeatherService.instance) {
            WeatherService.instance = new WeatherService();
        }
        return WeatherService.instance;
    }

    async getCurrentWeather(coordinates: LocationCoordinates): Promise<CurrentWeather | null> {
        try {
            const response = await fetch(
                `${this.baseUrl}/weather?lat=${coordinates.latitude}&lon=${coordinates.longitude}&appid=${this.apiKey}&units=metric`
            );

            if (!response.ok) {
                throw new Error(`Weather API error: ${response.status}`);
            }

            const data = await response.json();

            return {
                temperature: Math.round(data.main.temp),
                feelsLike: Math.round(data.main.feels_like),
                humidity: data.main.humidity,
                pressure: data.main.pressure,
                visibility: data.visibility / 1000, // Convert to km
                uvIndex: 0, // Would need separate UV API call
                windSpeed: data.wind.speed,
                windDirection: data.wind.deg || 0,
                conditions: data.weather.map((w: any) => ({
                    id: w.id,
                    main: w.main,
                    description: w.description,
                    icon: w.icon,
                })),
                timestamp: new Date(),
            };
        } catch (error) {
            console.error('Failed to fetch current weather:', error);
            return null;
        }
    }

    async getWeatherForecast(coordinates: LocationCoordinates, days: number = 7): Promise<WeatherForecast[]> {
        try {
            const response = await fetch(
                `${this.baseUrl}/forecast?lat=${coordinates.latitude}&lon=${coordinates.longitude}&appid=${this.apiKey}&units=metric&cnt=${days * 8}` // 8 forecasts per day (3-hour intervals)
            );

            if (!response.ok) {
                throw new Error(`Weather API error: ${response.status}`);
            }

            const data = await response.json();

            // Group forecasts by day
            const dailyForecasts: { [key: string]: any[] } = {};

            data.list.forEach((item: any) => {
                const date = new Date(item.dt * 1000);
                const dateKey = date.toDateString();

                if (!dailyForecasts[dateKey]) {
                    dailyForecasts[dateKey] = [];
                }
                dailyForecasts[dateKey].push(item);
            });

            return Object.entries(dailyForecasts).slice(0, days).map(([dateKey, forecasts]) => {
                const temperatures = forecasts.map(f => f.main.temp);
                const conditions = forecasts[0].weather; // Use first forecast's conditions

                return {
                    date: new Date(dateKey),
                    temperature: {
                        min: Math.round(Math.min(...temperatures)),
                        max: Math.round(Math.max(...temperatures)),
                    },
                    conditions: conditions.map((w: any) => ({
                        id: w.id,
                        main: w.main,
                        description: w.description,
                        icon: w.icon,
                    })),
                    humidity: Math.round(forecasts.reduce((sum, f) => sum + f.main.humidity, 0) / forecasts.length),
                    windSpeed: Math.round(forecasts.reduce((sum, f) => sum + f.wind.speed, 0) / forecasts.length),
                    precipitation: forecasts.reduce((sum, f) => sum + (f.rain?.['3h'] || 0), 0),
                };
            });
        } catch (error) {
            console.error('Failed to fetch weather forecast:', error);
            return [];
        }
    }

    async getWeatherAlerts(coordinates: LocationCoordinates): Promise<WeatherAlert[]> {
        try {
            // In production, this would call a weather alerts API
            // For now, we'll generate alerts based on current weather and forecast
            const current = await this.getCurrentWeather(coordinates);
            const forecast = await this.getWeatherForecast(coordinates, 3);

            if (!current || forecast.length === 0) return [];

            const alerts: WeatherAlert[] = [];

            // Check for extreme temperature alerts
            if (current.temperature > 40) {
                alerts.push({
                    id: `heat-${Date.now()}`,
                    title: 'Extreme Heat Warning',
                    description: 'Dangerous heat conditions expected',
                    severity: 'severe',
                    startTime: new Date(),
                    endTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
                    areas: ['Current Location'],
                    agriculturalImpact: {
                        affectedCrops: ['tomatoes', 'peppers', 'corn', 'wheat'],
                        recommendations: [
                            'Increase watering frequency',
                            'Provide shade protection',
                            'Harvest early if possible',
                            'Apply mulch to retain moisture'
                        ],
                        recommendationsAr: [
                            'زيادة تكرار الري',
                            'توفير الحماية من الظل',
                            'الحصاد المبكر إن أمكن',
                            'تطبيق المهاد للاحتفاظ بالرطوبة'
                        ],
                        urgency: 'critical'
                    }
                });
            }

            // Check for frost alerts
            if (current.temperature < 2) {
                alerts.push({
                    id: `frost-${Date.now()}`,
                    title: 'Frost Warning',
                    description: 'Freezing temperatures may damage crops',
                    severity: 'severe',
                    startTime: new Date(),
                    endTime: new Date(Date.now() + 12 * 60 * 60 * 1000),
                    areas: ['Current Location'],
                    agriculturalImpact: {
                        affectedCrops: ['tomatoes', 'peppers', 'basil', 'mint'],
                        recommendations: [
                            'Cover sensitive plants',
                            'Use frost protection cloth',
                            'Water plants before sunset',
                            'Move potted plants indoors'
                        ],
                        recommendationsAr: [
                            'تغطية النباتات الحساسة',
                            'استخدام قماش الحماية من الصقيع',
                            'ري النباتات قبل غروب الشمس',
                            'نقل النباتات المحفوظة بوعاء إلى الداخل'
                        ],
                        urgency: 'critical'
                    }
                });
            }

            // Check for heavy rain alerts
            const heavyRainExpected = forecast.some(day => day.precipitation > 25);
            if (heavyRainExpected) {
                alerts.push({
                    id: `rain-${Date.now()}`,
                    title: 'Heavy Rain Warning',
                    description: 'Heavy rainfall may cause flooding and crop damage',
                    severity: 'moderate',
                    startTime: new Date(),
                    endTime: new Date(Date.now() + 48 * 60 * 60 * 1000),
                    areas: ['Current Location'],
                    agriculturalImpact: {
                        affectedCrops: ['onions', 'carrots', 'potatoes'],
                        recommendations: [
                            'Ensure proper drainage',
                            'Postpone fertilizer application',
                            'Harvest mature crops if possible',
                            'Check for fungal diseases after rain'
                        ],
                        recommendationsAr: [
                            'ضمان التصريف المناسب',
                            'تأجيل تطبيق الأسمدة',
                            'حصاد المحاصيل الناضجة إن أمكن',
                            'فحص الأمراض الفطرية بعد المطر'
                        ],
                        urgency: 'high'
                    }
                });
            }

            return alerts;
        } catch (error) {
            console.error('Failed to fetch weather alerts:', error);
            return [];
        }
    }

    async getCompleteWeatherData(coordinates: LocationCoordinates): Promise<WeatherData | null> {
        try {
            const [current, forecast, alerts] = await Promise.all([
                this.getCurrentWeather(coordinates),
                this.getWeatherForecast(coordinates),
                this.getWeatherAlerts(coordinates),
            ]);

            if (!current) {
                return null;
            }

            return {
                current,
                forecast,
                alerts,
                location: {
                    name: 'Current Location', // Would be resolved from coordinates
                    coordinates,
                },
            };
        } catch (error) {
            console.error('Failed to fetch complete weather data:', error);
            return null;
        }
    }

    getWeatherIcon(iconCode: string): string {
        // Map weather icon codes to emoji or local icons
        const iconMap: { [key: string]: string } = {
            '01d': '☀️', // clear sky day
            '01n': '🌙', // clear sky night
            '02d': '⛅', // few clouds day
            '02n': '☁️', // few clouds night
            '03d': '☁️', // scattered clouds
            '03n': '☁️',
            '04d': '☁️', // broken clouds
            '04n': '☁️',
            '09d': '🌧️', // shower rain
            '09n': '🌧️',
            '10d': '🌦️', // rain day
            '10n': '🌧️', // rain night
            '11d': '⛈️', // thunderstorm
            '11n': '⛈️',
            '13d': '❄️', // snow
            '13n': '❄️',
            '50d': '🌫️', // mist
            '50n': '🌫️',
        };

        return iconMap[iconCode] || '🌤️';
    }

    formatTemperature(temp: number, unit: 'C' | 'F' = 'C'): string {
        if (unit === 'F') {
            return `${Math.round((temp * 9 / 5) + 32)}°F`;
        }
        return `${Math.round(temp)}°C`;
    }

    getWeatherAdvice(weather: CurrentWeather): string[] {
        const advice: string[] = [];

        if (weather.temperature > 30) {
            advice.push('High temperature - ensure adequate irrigation');
        }

        if (weather.humidity > 80) {
            advice.push('High humidity - monitor for fungal diseases');
        }

        if (weather.windSpeed > 10) {
            advice.push('Strong winds - check plant supports');
        }

        if (weather.conditions.some(c => c.main === 'Rain')) {
            advice.push('Rain expected - adjust watering schedule');
        }

        return advice;
    }

    /**
     * Generate agricultural alerts based on weather conditions
     */
    async generateAgriculturalAlerts(
        coordinates: LocationCoordinates,
        activeCrops: string[] = []
    ): Promise<AgriculturalAlert[]> {
        const current = await this.getCurrentWeather(coordinates);
        const forecast = await this.getWeatherForecast(coordinates, 7);

        if (!current) return [];

        const alerts: AgriculturalAlert[] = [];

        // Frost alert
        if (current.temperature <= 2 || forecast.some(day => day.temperature.min <= 2)) {
            alerts.push({
                id: `frost-alert-${Date.now()}`,
                type: 'frost',
                severity: current.temperature <= 0 ? 'critical' : 'high',
                title: 'Frost Warning',
                titleAr: 'تحذير من الصقيع',
                message: 'Freezing temperatures may damage sensitive crops',
                messageAr: 'درجات الحرارة المتجمدة قد تضر بالمحاصيل الحساسة',
                startDate: new Date(),
                endDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
                affectedCrops: activeCrops.filter(crop =>
                    ['tomatoes', 'peppers', 'basil', 'mint', 'watermelon'].includes(crop)
                ),
                protectiveMeasures: [
                    'Cover plants with frost cloth or plastic',
                    'Water plants before sunset to release heat',
                    'Use smudge pots or heaters in severe cases',
                    'Move container plants to protected areas'
                ],
                protectiveMeasuresAr: [
                    'تغطية النباتات بقماش الصقيع أو البلاستيك',
                    'ري النباتات قبل غروب الشمس لإطلاق الحرارة',
                    'استخدام أواني الدخان أو السخانات في الحالات الشديدة',
                    'نقل النباتات المحفوظة بوعاء إلى مناطق محمية'
                ],
                taskAdjustments: {
                    postpone: ['planting', 'transplanting'],
                    prioritize: ['covering', 'protection'],
                    cancel: ['pruning', 'fertilizing']
                }
            });
        }

        // Extreme heat alert
        if (current.temperature >= 40 || forecast.some(day => day.temperature.max >= 40)) {
            alerts.push({
                id: `heat-alert-${Date.now()}`,
                type: 'extreme_heat',
                severity: current.temperature >= 45 ? 'critical' : 'high',
                title: 'Extreme Heat Warning',
                titleAr: 'تحذير من الحر الشديد',
                message: 'Dangerous heat levels may stress or kill plants',
                messageAr: 'مستويات الحرارة الخطيرة قد تجهد أو تقتل النباتات',
                startDate: new Date(),
                endDate: new Date(Date.now() + 48 * 60 * 60 * 1000),
                affectedCrops: activeCrops,
                protectiveMeasures: [
                    'Increase watering frequency to twice daily',
                    'Install shade cloth (30-50% shade)',
                    'Apply thick mulch around plants',
                    'Harvest mature crops early morning'
                ],
                protectiveMeasuresAr: [
                    'زيادة تكرار الري إلى مرتين يومياً',
                    'تركيب قماش الظل (30-50% ظل)',
                    'تطبيق المهاد السميك حول النباتات',
                    'حصاد المحاصيل الناضجة في الصباح الباكر'
                ],
                taskAdjustments: {
                    postpone: ['transplanting', 'heavy_work'],
                    prioritize: ['watering', 'shading'],
                    cancel: ['fertilizing', 'pruning']
                }
            });
        }

        // Heavy rain/flooding alert
        const totalRain = forecast.slice(0, 3).reduce((sum, day) => sum + day.precipitation, 0);
        if (totalRain > 50) {
            alerts.push({
                id: `rain-alert-${Date.now()}`,
                type: 'heavy_rain',
                severity: totalRain > 100 ? 'critical' : 'high',
                title: 'Heavy Rain Warning',
                titleAr: 'تحذير من الأمطار الغزيرة',
                message: 'Heavy rainfall may cause waterlogging and root rot',
                messageAr: 'الأمطار الغزيرة قد تسبب تشبع المياه وتعفن الجذور',
                startDate: new Date(),
                endDate: new Date(Date.now() + 72 * 60 * 60 * 1000),
                affectedCrops: activeCrops.filter(crop =>
                    ['onions', 'carrots', 'potatoes', 'beans'].includes(crop)
                ),
                protectiveMeasures: [
                    'Ensure proper drainage in growing areas',
                    'Harvest mature root vegetables',
                    'Apply fungicide preventively',
                    'Avoid walking on wet soil'
                ],
                protectiveMeasuresAr: [
                    'ضمان التصريف المناسب في مناطق النمو',
                    'حصاد الخضروات الجذرية الناضجة',
                    'تطبيق مبيد الفطريات وقائياً',
                    'تجنب المشي على التربة الرطبة'
                ],
                taskAdjustments: {
                    postpone: ['planting', 'fertilizing', 'cultivation'],
                    prioritize: ['drainage', 'harvesting'],
                    cancel: ['watering', 'soil_work']
                }
            });
        }

        // High wind alert
        if (current.windSpeed > 25 || forecast.some(day => day.windSpeed > 25)) {
            alerts.push({
                id: `wind-alert-${Date.now()}`,
                type: 'high_wind',
                severity: current.windSpeed > 35 ? 'high' : 'medium',
                title: 'High Wind Warning',
                titleAr: 'تحذير من الرياح القوية',
                message: 'Strong winds may damage plants and structures',
                messageAr: 'الرياح القوية قد تضر بالنباتات والهياكل',
                startDate: new Date(),
                endDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
                affectedCrops: activeCrops.filter(crop =>
                    ['corn', 'tomatoes', 'beans', 'peppers'].includes(crop)
                ),
                protectiveMeasures: [
                    'Stake tall plants securely',
                    'Harvest ripe fruits before wind damage',
                    'Secure greenhouse structures and covers',
                    'Remove or secure loose materials'
                ],
                protectiveMeasuresAr: [
                    'ربط النباتات الطويلة بإحكام',
                    'حصاد الثمار الناضجة قبل أضرار الرياح',
                    'تأمين هياكل الدفيئة والأغطية',
                    'إزالة أو تأمين المواد السائبة'
                ],
                taskAdjustments: {
                    postpone: ['spraying', 'planting'],
                    prioritize: ['staking', 'securing'],
                    cancel: ['pruning', 'outdoor_work']
                }
            });
        }

        // Drought conditions
        const recentRain = forecast.slice(0, 7).reduce((sum, day) => sum + day.precipitation, 0);
        if (recentRain < 5 && current.humidity < 40) {
            alerts.push({
                id: `drought-alert-${Date.now()}`,
                type: 'drought',
                severity: recentRain < 2 ? 'high' : 'medium',
                title: 'Drought Conditions',
                titleAr: 'ظروف الجفاف',
                message: 'Low rainfall and humidity may stress plants',
                messageAr: 'قلة الأمطار والرطوبة قد تجهد النباتات',
                startDate: new Date(),
                endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
                affectedCrops: activeCrops,
                protectiveMeasures: [
                    'Increase watering frequency and duration',
                    'Apply mulch to conserve soil moisture',
                    'Install drip irrigation if possible',
                    'Prioritize watering of young plants'
                ],
                protectiveMeasuresAr: [
                    'زيادة تكرار ومدة الري',
                    'تطبيق المهاد للحفاظ على رطوبة التربة',
                    'تركيب الري بالتنقيط إن أمكن',
                    'إعطاء الأولوية لري النباتات الصغيرة'
                ],
                taskAdjustments: {
                    postpone: ['transplanting', 'fertilizing'],
                    prioritize: ['watering', 'mulching'],
                    cancel: ['pruning', 'cultivation']
                }
            });
        }

        return alerts;
    }

    /**
     * Get historical weather data for location-based planning
     */
    async getHistoricalWeatherData(
        coordinates: LocationCoordinates,
        startDate: Date,
        endDate: Date
    ): Promise<HistoricalWeatherData[]> {
        const locationKey = `${coordinates.latitude.toFixed(2)},${coordinates.longitude.toFixed(2)}`;

        // Check cache first
        if (this.historicalData.has(locationKey)) {
            const cached = this.historicalData.get(locationKey)!;
            const filtered = cached.filter(data =>
                data.date >= startDate && data.date <= endDate
            );
            if (filtered.length > 0) return filtered;
        }

        // In production, this would call a historical weather API
        // For now, generate simulated historical data
        const historicalData: HistoricalWeatherData[] = [];
        const currentDate = new Date(startDate);

        while (currentDate <= endDate) {
            // Generate realistic historical data based on season and location
            const dayOfYear = this.getDayOfYear(currentDate);
            const latitude = Math.abs(coordinates.latitude);

            let baseTemp = 20; // Default temperature
            if (latitude < 30) { // Tropical
                baseTemp = 25 + Math.sin((dayOfYear - 80) * Math.PI / 182.5) * 5;
            } else if (latitude < 50) { // Temperate
                baseTemp = 15 + Math.sin((dayOfYear - 80) * Math.PI / 182.5) * 15;
            } else { // Cold
                baseTemp = 5 + Math.sin((dayOfYear - 80) * Math.PI / 182.5) * 20;
            }

            const tempVariation = (Math.random() - 0.5) * 10;
            const avgTemp = baseTemp + tempVariation;

            historicalData.push({
                date: new Date(currentDate),
                temperature: {
                    min: avgTemp - 5 - Math.random() * 5,
                    max: avgTemp + 5 + Math.random() * 5,
                    avg: avgTemp
                },
                precipitation: Math.random() * 15, // 0-15mm
                humidity: 40 + Math.random() * 40, // 40-80%
                windSpeed: Math.random() * 20 // 0-20 km/h
            });

            currentDate.setDate(currentDate.getDate() + 1);
        }

        // Cache the data
        this.historicalData.set(locationKey, historicalData);
        return historicalData;
    }

    /**
     * Analyze weather patterns for crop planning
     */
    async analyzeWeatherPatterns(
        coordinates: LocationCoordinates,
        years: number = 3
    ): Promise<WeatherPattern[]> {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setFullYear(endDate.getFullYear() - years);

        const historicalData = await this.getHistoricalWeatherData(coordinates, startDate, endDate);
        const patterns: WeatherPattern[] = [];

        // Analyze by season
        const seasons = ['spring', 'summer', 'fall', 'winter'] as const;

        for (const season of seasons) {
            const seasonData = historicalData.filter(data => {
                const month = data.date.getMonth() + 1;
                switch (season) {
                    case 'spring': return month >= 3 && month <= 5;
                    case 'summer': return month >= 6 && month <= 8;
                    case 'fall': return month >= 9 && month <= 11;
                    case 'winter': return month === 12 || month <= 2;
                }
            });

            if (seasonData.length === 0) continue;

            const avgTemp = seasonData.reduce((sum, data) => sum + data.temperature.avg, 0) / seasonData.length;
            const avgPrecipitation = seasonData.reduce((sum, data) => sum + data.precipitation, 0) / seasonData.length;
            const avgHumidity = seasonData.reduce((sum, data) => sum + data.humidity, 0) / seasonData.length;

            // Determine optimal crops for this season
            const optimalCrops = this.getOptimalCropsForConditions(avgTemp, avgPrecipitation, avgHumidity);
            const riskFactors = this.identifyRiskFactors(seasonData);

            patterns.push({
                season,
                avgTemperature: Math.round(avgTemp * 10) / 10,
                avgPrecipitation: Math.round(avgPrecipitation * 10) / 10,
                avgHumidity: Math.round(avgHumidity * 10) / 10,
                optimalCrops,
                riskFactors
            });
        }

        return patterns;
    }

    /**
     * Get task scheduling adjustments based on weather forecast
     */
    getTaskSchedulingAdjustments(
        forecast: WeatherForecast[],
        taskType: string
    ): {
        recommendedDays: number[];
        avoidDays: number[];
        adjustments: string[];
    } {
        const recommendedDays: number[] = [];
        const avoidDays: number[] = [];
        const adjustments: string[] = [];

        forecast.forEach((day, index) => {
            const avgTemp = (day.temperature.min + day.temperature.max) / 2;

            switch (taskType) {
                case 'watering':
                    if (day.precipitation > 5) {
                        avoidDays.push(index);
                        adjustments.push(`Day ${index + 1}: Skip watering due to expected rain`);
                    } else if (avgTemp > 30) {
                        recommendedDays.push(index);
                        adjustments.push(`Day ${index + 1}: Increase watering due to high temperature`);
                    }
                    break;

                case 'fertilizing':
                    if (day.precipitation > 10) {
                        avoidDays.push(index);
                        adjustments.push(`Day ${index + 1}: Avoid fertilizing before heavy rain`);
                    } else if (day.precipitation > 2 && day.precipitation < 8) {
                        recommendedDays.push(index);
                        adjustments.push(`Day ${index + 1}: Good day for fertilizing with light rain expected`);
                    }
                    break;

                case 'planting':
                    if (avgTemp < 10 || avgTemp > 35) {
                        avoidDays.push(index);
                        adjustments.push(`Day ${index + 1}: Avoid planting due to extreme temperature`);
                    } else if (day.windSpeed > 20) {
                        avoidDays.push(index);
                        adjustments.push(`Day ${index + 1}: Avoid planting due to strong winds`);
                    } else if (avgTemp >= 15 && avgTemp <= 25 && day.windSpeed < 10) {
                        recommendedDays.push(index);
                        adjustments.push(`Day ${index + 1}: Ideal conditions for planting`);
                    }
                    break;

                case 'harvesting':
                    if (day.precipitation > 5) {
                        avoidDays.push(index);
                        adjustments.push(`Day ${index + 1}: Avoid harvesting during rain`);
                    } else if (day.humidity < 60 && avgTemp < 30) {
                        recommendedDays.push(index);
                        adjustments.push(`Day ${index + 1}: Good conditions for harvesting`);
                    }
                    break;
            }
        });

        return { recommendedDays, avoidDays, adjustments };
    }

    // Helper methods
    private getDayOfYear(date: Date): number {
        const start = new Date(date.getFullYear(), 0, 0);
        const diff = date.getTime() - start.getTime();
        return Math.floor(diff / (1000 * 60 * 60 * 24));
    }

    private getOptimalCropsForConditions(temp: number, precipitation: number, humidity: number): string[] {
        const crops: string[] = [];

        if (temp >= 20 && temp <= 30) {
            if (precipitation >= 5) {
                crops.push('tomatoes', 'peppers', 'corn');
            }
            if (humidity >= 60) {
                crops.push('rice', 'mint');
            }
        }

        if (temp >= 15 && temp <= 25) {
            crops.push('wheat', 'potatoes', 'carrots');
        }

        if (temp >= 25 && precipitation >= 8) {
            crops.push('watermelon', 'beans');
        }

        if (temp <= 20 && precipitation <= 5) {
            crops.push('onions', 'carrots');
        }

        return crops;
    }

    private identifyRiskFactors(seasonData: HistoricalWeatherData[]): string[] {
        const risks: string[] = [];

        const extremeTemps = seasonData.filter(data =>
            data.temperature.max > 40 || data.temperature.min < 0
        );
        if (extremeTemps.length > seasonData.length * 0.1) {
            risks.push('Frequent extreme temperatures');
        }

        const heavyRainDays = seasonData.filter(data => data.precipitation > 20);
        if (heavyRainDays.length > seasonData.length * 0.15) {
            risks.push('Risk of heavy rainfall and flooding');
        }

        const droughtDays = seasonData.filter(data => data.precipitation < 1);
        if (droughtDays.length > seasonData.length * 0.4) {
            risks.push('Extended dry periods');
        }

        const highWindDays = seasonData.filter(data => data.windSpeed > 25);
        if (highWindDays.length > seasonData.length * 0.1) {
            risks.push('Frequent high winds');
        }

        return risks;
    }
}