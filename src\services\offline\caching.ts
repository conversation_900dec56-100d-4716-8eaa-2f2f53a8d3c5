import AsyncStorage from '@react-native-async-storage/async-storage';
import { offlineDatabase } from './database';
import { OfflineConfig } from '../../types/offline';

export interface CacheEntry {
    data: any;
    timestamp: number;
    ttl: number; // Time to live in milliseconds
    priority: 'high' | 'medium' | 'low';
    size: number; // Approximate size in bytes
}

export interface CacheStats {
    totalSize: number;
    entryCount: number;
    hitRate: number;
    missRate: number;
    lastCleanup: number;
}

export class CachingService {
    private memoryCache: Map<string, CacheEntry> = new Map();
    private cacheStats: CacheStats = {
        totalSize: 0,
        entryCount: 0,
        hitRate: 0,
        missRate: 0,
        lastCleanup: Date.now()
    };

    private hits = 0;
    private misses = 0;
    private readonly MAX_MEMORY_CACHE_SIZE = 50 * 1024 * 1024; // 50MB
    private readonly DEFAULT_TTL = 24 * 60 * 60 * 1000; // 24 hours

    private config: OfflineConfig = {
        maxRetries: 3,
        retryDelay: 1000,
        syncInterval: 5 * 60 * 1000, // 5 minutes
        maxCacheSize: 100 * 1024 * 1024, // 100MB
        compressionEnabled: true,
        essentialTables: [
            'user_profiles',
            'crop_plans',
            'tasks',
            'chat_sessions',
            'products'
        ]
    };

    async initialize(config?: Partial<OfflineConfig>): Promise<void> {
        if (config) {
            this.config = { ...this.config, ...config };
        }

        // Load cache stats from storage
        try {
            const stats = await AsyncStorage.getItem('cache_stats');
            if (stats) {
                this.cacheStats = JSON.parse(stats);
            }
        } catch (error) {
            console.warn('Failed to load cache stats:', error);
        }

        // Schedule periodic cleanup
        this.scheduleCleanup();

        console.log('Caching service initialized');
    }

    // Memory cache operations
    async get(key: string): Promise<any> {
        // Check memory cache first
        const memoryEntry = this.memoryCache.get(key);
        if (memoryEntry && !this.isExpired(memoryEntry)) {
            this.hits++;
            return memoryEntry.data;
        }

        // Check persistent cache
        try {
            const persistentData = await this.getPersistentCache(key);
            if (persistentData) {
                // Add to memory cache for faster access
                this.setMemoryCache(key, persistentData, 'medium');
                this.hits++;
                return persistentData;
            }
        } catch (error) {
            console.warn('Failed to get persistent cache:', error);
        }

        this.misses++;
        return null;
    }

    async set(
        key: string,
        data: any,
        options: {
            ttl?: number;
            priority?: 'high' | 'medium' | 'low';
            persistent?: boolean;
        } = {}
    ): Promise<void> {
        const { ttl = this.DEFAULT_TTL, priority = 'medium', persistent = true } = options;

        // Set in memory cache
        this.setMemoryCache(key, data, priority, ttl);

        // Set in persistent cache if requested
        if (persistent) {
            try {
                await this.setPersistentCache(key, data, ttl);
            } catch (error) {
                console.warn('Failed to set persistent cache:', error);
            }
        }
    }

    private setMemoryCache(
        key: string,
        data: any,
        priority: 'high' | 'medium' | 'low',
        ttl: number = this.DEFAULT_TTL
    ): void {
        const size = this.estimateSize(data);

        // Check if we need to free up space
        if (this.cacheStats.totalSize + size > this.MAX_MEMORY_CACHE_SIZE) {
            this.evictLRU(size);
        }

        const entry: CacheEntry = {
            data,
            timestamp: Date.now(),
            ttl,
            priority,
            size
        };

        this.memoryCache.set(key, entry);
        this.cacheStats.totalSize += size;
        this.cacheStats.entryCount++;
    }

    private async getPersistentCache(key: string): Promise<any> {
        try {
            const cached = await AsyncStorage.getItem(`cache_${key}`);
            if (!cached) return null;

            const entry = JSON.parse(cached);
            if (this.isExpired(entry)) {
                await AsyncStorage.removeItem(`cache_${key}`);
                return null;
            }

            return entry.data;
        } catch (error) {
            console.warn('Failed to get persistent cache:', error);
            return null;
        }
    }

    private async setPersistentCache(key: string, data: any, ttl: number): Promise<void> {
        try {
            const entry = {
                data,
                timestamp: Date.now(),
                ttl
            };

            await AsyncStorage.setItem(`cache_${key}`, JSON.stringify(entry));
        } catch (error) {
            console.warn('Failed to set persistent cache:', error);
        }
    }

    // Essential data caching for offline access
    async cacheEssentialData(userId: string): Promise<void> {
        console.log('Caching essential data for offline access...');

        try {
            // Cache user profile
            await this.cacheUserProfile(userId);

            // Cache active crop plans
            await this.cacheCropPlans(userId);

            // Cache pending tasks
            await this.cacheTasks(userId);

            // Cache recent chat sessions
            await this.cacheChatSessions(userId);

            // Cache featured products
            await this.cacheProducts();

            console.log('Essential data cached successfully');
        } catch (error) {
            console.error('Failed to cache essential data:', error);
        }
    }

    private async cacheUserProfile(userId: string): Promise<void> {
        // This would typically fetch from Supabase and cache locally
        // For now, we'll cache whatever is in the database
        const profiles = await offlineDatabase.getCachedData('offline_user_profiles', userId);
        if (profiles.length > 0) {
            await this.set(`user_profile_${userId}`, profiles[0], { priority: 'high' });
        }
    }

    private async cacheCropPlans(userId: string): Promise<void> {
        const cropPlans = await offlineDatabase.getCachedData('offline_crop_plans', undefined, userId);
        for (const plan of cropPlans) {
            await this.set(`crop_plan_${plan.id}`, plan, { priority: 'high' });
        }
        await this.set(`user_crop_plans_${userId}`, cropPlans, { priority: 'high' });
    }

    private async cacheTasks(userId: string): Promise<void> {
        const tasks = await offlineDatabase.getCachedData('offline_tasks', undefined, userId);
        for (const task of tasks) {
            await this.set(`task_${task.id}`, task, { priority: 'high' });
        }
        await this.set(`user_tasks_${userId}`, tasks, { priority: 'high' });
    }

    private async cacheChatSessions(userId: string): Promise<void> {
        const sessions = await offlineDatabase.getCachedData('offline_chat_sessions', undefined, userId);
        for (const session of sessions) {
            await this.set(`chat_session_${session.id}`, session, { priority: 'medium' });

            // Cache messages for each session
            const messages = await offlineDatabase.getCachedData('offline_chat_messages', undefined, userId);
            const sessionMessages = messages.filter(msg => msg.session_id === session.id);
            await this.set(`chat_messages_${session.id}`, sessionMessages, { priority: 'medium' });
        }
    }

    private async cacheProducts(): Promise<void> {
        const products = await offlineDatabase.getCachedData('offline_products');
        const featuredProducts = products.filter(p => p.is_featured || p.is_recommended);

        for (const product of featuredProducts) {
            await this.set(`product_${product.id}`, product, { priority: 'low' });
        }
        await this.set('featured_products', featuredProducts, { priority: 'low' });
    }

    // Cache management
    private isExpired(entry: CacheEntry): boolean {
        return Date.now() - entry.timestamp > entry.ttl;
    }

    private evictLRU(requiredSpace: number): void {
        // Sort by priority and timestamp (LRU)
        const entries = Array.from(this.memoryCache.entries()).sort((a, b) => {
            const priorityOrder = { low: 0, medium: 1, high: 2 };
            const priorityDiff = priorityOrder[a[1].priority] - priorityOrder[b[1].priority];

            if (priorityDiff !== 0) return priorityDiff;
            return a[1].timestamp - b[1].timestamp;
        });

        let freedSpace = 0;
        for (const [key, entry] of entries) {
            if (freedSpace >= requiredSpace) break;

            this.memoryCache.delete(key);
            this.cacheStats.totalSize -= entry.size;
            this.cacheStats.entryCount--;
            freedSpace += entry.size;
        }
    }

    private estimateSize(data: any): number {
        try {
            return JSON.stringify(data).length * 2; // Rough estimate (UTF-16)
        } catch {
            return 1024; // Default size if estimation fails
        }
    }

    private scheduleCleanup(): void {
        setInterval(() => {
            this.cleanup();
        }, 60 * 60 * 1000); // Cleanup every hour
    }

    async cleanup(): Promise<void> {
        console.log('Running cache cleanup...');

        // Clean memory cache
        const now = Date.now();
        for (const [key, entry] of this.memoryCache.entries()) {
            if (this.isExpired(entry)) {
                this.memoryCache.delete(key);
                this.cacheStats.totalSize -= entry.size;
                this.cacheStats.entryCount--;
            }
        }

        // Clean persistent cache
        try {
            const keys = await AsyncStorage.getAllKeys();
            const cacheKeys = keys.filter(key => key.startsWith('cache_'));

            for (const key of cacheKeys) {
                const cached = await AsyncStorage.getItem(key);
                if (cached) {
                    const entry = JSON.parse(cached);
                    if (this.isExpired(entry)) {
                        await AsyncStorage.removeItem(key);
                    }
                }
            }
        } catch (error) {
            console.warn('Failed to cleanup persistent cache:', error);
        }

        this.cacheStats.lastCleanup = now;
        await this.saveCacheStats();

        console.log('Cache cleanup completed');
    }

    async clear(pattern?: string): Promise<void> {
        if (pattern) {
            // Clear specific pattern
            for (const key of this.memoryCache.keys()) {
                if (key.includes(pattern)) {
                    const entry = this.memoryCache.get(key)!;
                    this.memoryCache.delete(key);
                    this.cacheStats.totalSize -= entry.size;
                    this.cacheStats.entryCount--;
                }
            }

            // Clear from persistent storage
            try {
                const keys = await AsyncStorage.getAllKeys();
                const matchingKeys = keys.filter(key => key.includes(pattern));
                await AsyncStorage.multiRemove(matchingKeys);
            } catch (error) {
                console.warn('Failed to clear persistent cache:', error);
            }
        } else {
            // Clear all
            this.memoryCache.clear();
            this.cacheStats = {
                totalSize: 0,
                entryCount: 0,
                hitRate: 0,
                missRate: 0,
                lastCleanup: Date.now()
            };

            try {
                const keys = await AsyncStorage.getAllKeys();
                const cacheKeys = keys.filter(key => key.startsWith('cache_'));
                await AsyncStorage.multiRemove(cacheKeys);
            } catch (error) {
                console.warn('Failed to clear persistent cache:', error);
            }
        }
    }

    getStats(): CacheStats {
        const total = this.hits + this.misses;
        return {
            ...this.cacheStats,
            hitRate: total > 0 ? this.hits / total : 0,
            missRate: total > 0 ? this.misses / total : 0
        };
    }

    private async saveCacheStats(): Promise<void> {
        try {
            await AsyncStorage.setItem('cache_stats', JSON.stringify(this.cacheStats));
        } catch (error) {
            console.warn('Failed to save cache stats:', error);
        }
    }

    async preloadData(keys: string[]): Promise<void> {
        console.log('Preloading cache data...');

        const promises = keys.map(key => this.get(key));
        await Promise.allSettled(promises);

        console.log('Cache preloading completed');
    }

    async warmup(userId: string): Promise<void> {
        console.log('Warming up cache...');

        await this.cacheEssentialData(userId);

        // Preload commonly accessed data
        const commonKeys = [
            `user_profile_${userId}`,
            `user_crop_plans_${userId}`,
            `user_tasks_${userId}`,
            'featured_products'
        ];

        await this.preloadData(commonKeys);

        console.log('Cache warmup completed');
    }
}

export const cachingService = new CachingService();