import React, { useEffect, useState } from 'react';
import { View, Text, Animated, Modal, TouchableOpacity } from 'react-native';
import { syncService, SyncProgress } from '../../services/offline/syncService';
import { backgroundSyncService, BackgroundSyncStatus } from '../../services/offline/backgroundSync';
import { useSyncStatus } from '../../stores/offline';

interface SyncProgressIndicatorProps {
    visible?: boolean;
    onClose?: () => void;
    showDetails?: boolean;
}

export const SyncProgressIndicator: React.FC<SyncProgressIndicatorProps> = ({
    visible = true,
    onClose,
    showDetails = false
}) => {
    const { syncInProgress, pendingActions, failedActions } = useSyncStatus();
    const [progress, setProgress] = useState<SyncProgress | null>(null);
    const [backgroundStatus, setBackgroundStatus] = useState<BackgroundSyncStatus | null>(null);
    const [progressAnim] = useState(new Animated.Value(0));
    const [fadeAnim] = useState(new Animated.Value(0));

    useEffect(() => {
        // Listen for sync progress
        const unsubscribeProgress = syncService.addProgressListener((progressData) => {
            setProgress(progressData);

            // Animate progress bar
            Animated.timing(progressAnim, {
                toValue: progressData.percentage / 100,
                duration: 300,
                useNativeDriver: false,
            }).start();
        });

        // Listen for background sync status
        const unsubscribeStatus = backgroundSyncService.addStatusListener((status) => {
            setBackgroundStatus(status);
        });

        return () => {
            unsubscribeProgress();
            unsubscribeStatus();
        };
    }, [progressAnim]);

    useEffect(() => {
        if (visible && (syncInProgress || pendingActions > 0 || failedActions > 0)) {
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
            }).start();
        } else {
            Animated.timing(fadeAnim, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            }).start();
        }
    }, [visible, syncInProgress, pendingActions, failedActions, fadeAnim]);

    const formatTimeRemaining = (progress: SyncProgress): string => {
        if (!progress || progress.completed === 0) return '';

        const rate = progress.completed / (Date.now() - (Date.now() - 10000)); // Rough estimate
        const remaining = progress.total - progress.completed;
        const timeRemaining = remaining / rate;

        if (timeRemaining < 60) {
            return `~${Math.round(timeRemaining)}s remaining`;
        } else {
            return `~${Math.round(timeRemaining / 60)}m remaining`;
        }
    };

    const getStatusColor = (): string => {
        if (failedActions > 0) return 'bg-red-500';
        if (syncInProgress) return 'bg-blue-500';
        if (pendingActions > 0) return 'bg-yellow-500';
        return 'bg-green-500';
    };

    const getStatusText = (): string => {
        if (syncInProgress && progress) {
            return `Syncing ${progress.completed}/${progress.total}`;
        }
        if (syncInProgress) {
            return 'Syncing...';
        }
        if (failedActions > 0) {
            return `${failedActions} failed`;
        }
        if (pendingActions > 0) {
            return `${pendingActions} pending`;
        }
        return 'Up to date';
    };

    if (showDetails) {
        return (
            <Modal
                visible={visible}
                transparent
                animationType="fade"
                onRequestClose={onClose}
            >
                <View className="flex-1 bg-black/50 justify-center items-center p-4">
                    <Animated.View
                        style={{ opacity: fadeAnim }}
                        className="bg-white rounded-lg p-6 w-full max-w-sm"
                    >
                        <View className="flex-row items-center justify-between mb-4">
                            <Text className="text-lg font-semibold text-gray-900">
                                Sync Status
                            </Text>
                            {onClose && (
                                <TouchableOpacity onPress={onClose}>
                                    <Text className="text-blue-500 font-medium">Done</Text>
                                </TouchableOpacity>
                            )}
                        </View>

                        {/* Current sync progress */}
                        {syncInProgress && progress && (
                            <View className="mb-6">
                                <View className="flex-row justify-between items-center mb-2">
                                    <Text className="text-sm text-gray-600">
                                        {progress.current || 'Syncing...'}
                                    </Text>
                                    <Text className="text-sm font-medium text-gray-900">
                                        {Math.round(progress.percentage)}%
                                    </Text>
                                </View>

                                <View className="h-2 bg-gray-200 rounded-full overflow-hidden">
                                    <Animated.View
                                        style={{
                                            width: progressAnim.interpolate({
                                                inputRange: [0, 1],
                                                outputRange: ['0%', '100%'],
                                            }),
                                        }}
                                        className="h-full bg-blue-500 rounded-full"
                                    />
                                </View>

                                <Text className="text-xs text-gray-500 mt-1">
                                    {formatTimeRemaining(progress)}
                                </Text>
                            </View>
                        )}

                        {/* Status summary */}
                        <View className="space-y-3">
                            <StatusRow
                                label="Pending"
                                value={pendingActions}
                                color="text-yellow-600"
                            />
                            <StatusRow
                                label="Failed"
                                value={failedActions}
                                color="text-red-600"
                            />
                            <StatusRow
                                label="In Progress"
                                value={syncInProgress ? 1 : 0}
                                color="text-blue-600"
                            />
                        </View>

                        {/* Background sync info */}
                        {backgroundStatus && (
                            <View className="mt-6 pt-4 border-t border-gray-200">
                                <Text className="text-sm font-medium text-gray-900 mb-2">
                                    Background Sync
                                </Text>
                                <View className="space-y-2">
                                    <View className="flex-row justify-between">
                                        <Text className="text-sm text-gray-600">Status</Text>
                                        <Text className="text-sm text-gray-900">
                                            {backgroundStatus.isEnabled ? 'Enabled' : 'Disabled'}
                                        </Text>
                                    </View>
                                    {backgroundStatus.lastSuccessfulSync && (
                                        <View className="flex-row justify-between">
                                            <Text className="text-sm text-gray-600">Last Sync</Text>
                                            <Text className="text-sm text-gray-900">
                                                {new Date(backgroundStatus.lastSuccessfulSync).toLocaleTimeString()}
                                            </Text>
                                        </View>
                                    )}
                                    {backgroundStatus.consecutiveFailures > 0 && (
                                        <View className="flex-row justify-between">
                                            <Text className="text-sm text-gray-600">Failures</Text>
                                            <Text className="text-sm text-red-600">
                                                {backgroundStatus.consecutiveFailures}
                                            </Text>
                                        </View>
                                    )}
                                </View>
                            </View>
                        )}
                    </Animated.View>
                </View>
            </Modal>
        );
    }

    // Compact indicator
    return (
        <Animated.View
            style={{ opacity: fadeAnim }}
            className="absolute top-12 right-4 z-50"
        >
            <View className={`${getStatusColor()} rounded-full px-3 py-2 shadow-lg`}>
                <View className="flex-row items-center space-x-2">
                    {syncInProgress && (
                        <SyncSpinner />
                    )}
                    <Text className="text-white text-xs font-medium">
                        {getStatusText()}
                    </Text>
                    {(pendingActions > 0 || failedActions > 0) && (
                        <View className="bg-white/20 rounded-full px-2 py-0.5">
                            <Text className="text-white text-xs font-bold">
                                {failedActions > 0 ? '!' : pendingActions}
                            </Text>
                        </View>
                    )}
                </View>

                {/* Progress bar for active sync */}
                {syncInProgress && progress && (
                    <View className="mt-2 h-1 bg-white/20 rounded-full overflow-hidden">
                        <Animated.View
                            style={{
                                width: progressAnim.interpolate({
                                    inputRange: [0, 1],
                                    outputRange: ['0%', '100%'],
                                }),
                            }}
                            className="h-full bg-white rounded-full"
                        />
                    </View>
                )}
            </View>
        </Animated.View>
    );
};

const StatusRow: React.FC<{
    label: string;
    value: number;
    color: string;
}> = ({ label, value, color }) => (
    <View className="flex-row justify-between items-center">
        <Text className="text-sm text-gray-600">{label}</Text>
        <Text className={`text-sm font-medium ${color}`}>
            {value}
        </Text>
    </View>
);

const SyncSpinner: React.FC = () => {
    const [rotation] = useState(new Animated.Value(0));

    useEffect(() => {
        const animate = () => {
            Animated.timing(rotation, {
                toValue: 1,
                duration: 1000,
                useNativeDriver: true,
            }).start(() => {
                rotation.setValue(0);
                animate();
            });
        };
        animate();
    }, [rotation]);

    return (
        <Animated.View
            style={{
                transform: [
                    {
                        rotate: rotation.interpolate({
                            inputRange: [0, 1],
                            outputRange: ['0deg', '360deg'],
                        }),
                    },
                ],
            }}
            className="w-3 h-3 border border-white/30 border-t-white rounded-full"
        />
    );
};

export default SyncProgressIndicator;