import { create } from 'zustand';
import { offlineService, offlineModeService, connectivityService } from '../services/offline';
import { SyncStatus, OfflineModeState, ConnectivityState } from '../types/offline';

interface OfflineState {
    // Connection state
    isOnline: boolean;
    isOfflineMode: boolean;
    connectivityState: ConnectivityState | null;
    offlineModeState: OfflineModeState | null;

    // Sync state
    syncStatus: SyncStatus;
    syncInProgress: boolean;
    lastSyncTime: number | null;

    // Queue stats
    pendingActions: number;
    failedActions: number;

    // UI state
    showOfflineIndicator: boolean;
    showSyncIndicator: boolean;

    // Actions
    initialize: () => Promise<void>;
    forceSync: () => Promise<void>;
    toggleOfflineMode: (enabled: boolean) => Promise<void>;
    clearOfflineData: () => Promise<void>;
    retryFailedActions: () => Promise<void>;

    // Data operations with offline support
    createOfflineData: (table: string, data: any, userId?: string) => Promise<any>;
    updateOfflineData: (table: string, id: string, data: any, userId?: string) => Promise<any>;
    deleteOfflineData: (table: string, id: string, userId?: string) => Promise<void>;
    getOfflineData: (table: string, id?: string, userId?: string) => Promise<any[]>;

    // Internal state updates
    updateConnectivityState: (state: ConnectivityState) => void;
    updateOfflineModeState: (state: OfflineModeState) => void;
    updateSyncStatus: (status: SyncStatus) => void;
}

export const useOfflineStore = create<OfflineState>((set, get) => ({
    // Initial state
    isOnline: false,
    isOfflineMode: false,
    connectivityState: null,
    offlineModeState: null,
    syncStatus: {
        isOnline: false,
        lastSyncTime: null,
        syncInProgress: false,
        pendingActions: 0,
        failedActions: 0,
        nextSyncAttempt: null
    },
    syncInProgress: false,
    lastSyncTime: null,
    pendingActions: 0,
    failedActions: 0,
    showOfflineIndicator: true,
    showSyncIndicator: true,

    // Actions
    initialize: async () => {
        try {
            console.log('Initializing offline store...');

            // Initialize offline service
            await offlineService.initialize();

            // Set up listeners
            connectivityService.addListener((state) => {
                get().updateConnectivityState(state);
            });

            offlineModeService.addListener((state) => {
                get().updateOfflineModeState(state);
            });

            offlineService.addSyncListener((status) => {
                get().updateSyncStatus(status);
            });

            // Get initial states
            const connectivityState = connectivityService.getCurrentState();
            const offlineModeState = offlineModeService.getState();
            const syncStatus = offlineService.getSyncStatus();

            set({
                connectivityState,
                offlineModeState,
                syncStatus,
                isOnline: connectivityState.isConnected && connectivityState.isInternetReachable,
                isOfflineMode: offlineModeState.isOfflineMode,
                syncInProgress: syncStatus.syncInProgress,
                lastSyncTime: syncStatus.lastSyncTime,
                pendingActions: syncStatus.pendingActions,
                failedActions: syncStatus.failedActions
            });

            console.log('Offline store initialized successfully');
        } catch (error) {
            console.error('Failed to initialize offline store:', error);
        }
    },

    forceSync: async () => {
        try {
            console.log('Forcing sync...');
            set({ syncInProgress: true });

            await offlineService.processSyncQueue();

            // Update stats
            const stats = await offlineService.getSyncQueueStats();
            set({
                pendingActions: stats.pending,
                failedActions: stats.failed,
                syncInProgress: false,
                lastSyncTime: Date.now()
            });

            console.log('Force sync completed');
        } catch (error) {
            console.error('Force sync failed:', error);
            set({ syncInProgress: false });
        }
    },

    toggleOfflineMode: async (enabled: boolean) => {
        try {
            await offlineModeService.enableOfflineMode(enabled);

            if (!enabled) {
                // Try to sync when coming back online
                await get().forceSync();
            }
        } catch (error) {
            console.error('Failed to toggle offline mode:', error);
        }
    },

    clearOfflineData: async () => {
        try {
            console.log('Clearing offline data...');
            await offlineService.clearOfflineData();

            // Reset state
            set({
                pendingActions: 0,
                failedActions: 0,
                lastSyncTime: null
            });

            console.log('Offline data cleared');
        } catch (error) {
            console.error('Failed to clear offline data:', error);
        }
    },

    retryFailedActions: async () => {
        try {
            console.log('Retrying failed actions...');

            // This would typically retry failed sync actions
            await get().forceSync();

        } catch (error) {
            console.error('Failed to retry actions:', error);
        }
    },

    // Data operations
    createOfflineData: async (table: string, data: any, userId?: string) => {
        try {
            const result = await offlineService.createData(table, data, userId);

            // Update pending actions count
            const stats = await offlineService.getSyncQueueStats();
            set({ pendingActions: stats.pending });

            return result;
        } catch (error) {
            console.error('Failed to create offline data:', error);
            throw error;
        }
    },

    updateOfflineData: async (table: string, id: string, data: any, userId?: string) => {
        try {
            const result = await offlineService.updateData(table, id, data, userId);

            // Update pending actions count
            const stats = await offlineService.getSyncQueueStats();
            set({ pendingActions: stats.pending });

            return result;
        } catch (error) {
            console.error('Failed to update offline data:', error);
            throw error;
        }
    },

    deleteOfflineData: async (table: string, id: string, userId?: string) => {
        try {
            await offlineService.deleteData(table, id, userId);

            // Update pending actions count
            const stats = await offlineService.getSyncQueueStats();
            set({ pendingActions: stats.pending });

        } catch (error) {
            console.error('Failed to delete offline data:', error);
            throw error;
        }
    },

    getOfflineData: async (table: string, id?: string, userId?: string) => {
        try {
            return await offlineService.getData(table, id, userId);
        } catch (error) {
            console.error('Failed to get offline data:', error);
            return [];
        }
    },

    // State updates
    updateConnectivityState: (state: ConnectivityState) => {
        set({
            connectivityState: state,
            isOnline: state.isConnected && state.isInternetReachable
        });
    },

    updateOfflineModeState: (state: OfflineModeState) => {
        set({
            offlineModeState: state,
            isOfflineMode: state.isOfflineMode
        });
    },

    updateSyncStatus: (status: SyncStatus) => {
        set({
            syncStatus: status,
            syncInProgress: status.syncInProgress,
            lastSyncTime: status.lastSyncTime,
            pendingActions: status.pendingActions,
            failedActions: status.failedActions
        });
    }
}));

// Utility hooks for common offline operations
export const useOfflineData = (table: string, userId?: string) => {
    const { getOfflineData, createOfflineData, updateOfflineData, deleteOfflineData } = useOfflineStore();

    return {
        getData: (id?: string) => getOfflineData(table, id, userId),
        createData: (data: any) => createOfflineData(table, data, userId),
        updateData: (id: string, data: any) => updateOfflineData(table, id, data, userId),
        deleteData: (id: string) => deleteOfflineData(table, id, userId)
    };
};

export const useConnectivity = () => {
    const { isOnline, isOfflineMode, connectivityState } = useOfflineStore();

    return {
        isOnline,
        isOfflineMode,
        connectionType: connectivityState?.type || 'unknown',
        isWifi: connectivityState?.isWifiEnabled || false,
        isCellular: connectivityState?.isCellularEnabled || false
    };
};

export const useSyncStatus = () => {
    const {
        syncInProgress,
        lastSyncTime,
        pendingActions,
        failedActions,
        forceSync,
        retryFailedActions
    } = useOfflineStore();

    return {
        syncInProgress,
        lastSyncTime,
        pendingActions,
        failedActions,
        hasPendingActions: pendingActions > 0,
        hasFailedActions: failedActions > 0,
        forceSync,
        retryFailedActions
    };
};