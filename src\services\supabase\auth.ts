import { supabase } from './client';
import { AuthError, User, Session } from '@supabase/supabase-js';

export interface LoginCredentials {
  email?: string;
  phone?: string;
  password: string;
}

export interface RegisterData {
  email?: string;
  phone?: string;
  password: string;
  firstName: string;
  lastName: string;
}

export interface AuthResponse {
  user: User | null;
  session: Session | null;
  error: AuthError | null;
}

export class AuthService {
  /**
   * Register a new user with email or phone
   */
  static async register(data: RegisterData): Promise<AuthResponse> {
    try {
      const { email, phone, password, firstName, lastName } = data;

      const signUpData = {
        password,
        options: {
          data: {
            first_name: firstName,
            last_name: lastName,
            phone, // store phone in user metadata even if signing up with email
          },
        },
      };

      let response;
      if (email) {
        response = await supabase.auth.signUp({
          email,
          ...signUpData,
        });
      } else if (phone) {
        response = await supabase.auth.signUp({
          phone,
          ...signUpData,
        });
      } else {
        throw new Error('Either email or phone is required for registration');
      }

      return response;
    } catch (error) {
      return {
        user: null,
        session: null,
        error: error as AuthError,
      };
    }
  }

  /**
   * Sign in with email or phone and password
   */
  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const { email, phone, password } = credentials;

      let response;
      if (email) {
        response = await supabase.auth.signInWithPassword({
          email,
          password,
        });
      } else if (phone) {
        response = await supabase.auth.signInWithPassword({
          phone,
          password,
        });
      } else {
        throw new Error('Either email or phone is required for login');
      }

      return response;
    } catch (error) {
      return {
        user: null,
        session: null,
        error: error as AuthError,
      };
    }
  }

  /**
   * Sign out the current user
   */
  static async logout(): Promise<{ error: AuthError | null }> {
    try {
      const { error } = await supabase.auth.signOut();
      return { error };
    } catch (error) {
      return { error: error as AuthError };
    }
  }

  /**
   * Send password reset email
   */
  static async resetPassword(email: string): Promise<{ error: AuthError | null }> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: 'smart-farming://reset-password',
      });
      return { error };
    } catch (error) {
      return { error: error as AuthError };
    }
  }

  /**
   * Update user password
   */
  static async updatePassword(newPassword: string): Promise<AuthResponse> {
    try {
      const response = await supabase.auth.updateUser({
        password: newPassword,
      });
      return response;
    } catch (error) {
      return {
        user: null,
        session: null,
        error: error as AuthError,
      };
    }
  }

  /**
   * Get current user session
   */
  static async getSession(): Promise<{ session: Session | null; error: AuthError | null }> {
    try {
      const { data, error } = await supabase.auth.getSession();
      return { session: data.session, error };
    } catch (error) {
      return { session: null, error: error as AuthError };
    }
  }

  /**
   * Get current user
   */
  static async getCurrentUser(): Promise<{ user: User | null; error: AuthError | null }> {
    try {
      const { data, error } = await supabase.auth.getUser();
      return { user: data.user, error };
    } catch (error) {
      return { user: null, error: error as AuthError };
    }
  }

  /**
   * Sign in with Google OAuth
   */
  static async signInWithGoogle(): Promise<AuthResponse> {
    try {
      const response = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: 'smart-farming://auth/callback',
        },
      });

      return {
        user: null,
        session: null,
        error: response.error,
      };
    } catch (error) {
      return {
        user: null,
        session: null,
        error: error as AuthError,
      };
    }
  }

  /**
   * Sign in with Apple OAuth
   */
  static async signInWithApple(): Promise<AuthResponse> {
    try {
      const response = await supabase.auth.signInWithOAuth({
        provider: 'apple',
        options: {
          redirectTo: 'smart-farming://auth/callback',
        },
      });

      return {
        user: null,
        session: null,
        error: response.error,
      };
    } catch (error) {
      return {
        user: null,
        session: null,
        error: error as AuthError,
      };
    }
  }

  /**
   * Verify OTP for phone authentication
   */
  static async verifyOTP(phone: string, token: string): Promise<AuthResponse> {
    try {
      const response = await supabase.auth.verifyOtp({
        phone,
        token,
        type: 'sms',
      });
      return response;
    } catch (error) {
      return {
        user: null,
        session: null,
        error: error as AuthError,
      };
    }
  }

  /**
   * Resend OTP for phone verification
   */
  static async resendOTP(phone: string): Promise<{ error: AuthError | null }> {
    try {
      const { error } = await supabase.auth.resend({
        type: 'sms',
        phone,
      });
      return { error };
    } catch (error) {
      return { error: error as AuthError };
    }
  }

  /**
   * Listen to auth state changes
   */
  static onAuthStateChange(callback: (event: string, session: Session | null) => void) {
    return supabase.auth.onAuthStateChange(callback);
  }

  /**
   * Refresh the current session
   */
  static async refreshSession(): Promise<AuthResponse> {
    try {
      const response = await supabase.auth.refreshSession();
      return response;
    } catch (error) {
      return {
        user: null,
        session: null,
        error: error as AuthError,
      };
    }
  }
}
