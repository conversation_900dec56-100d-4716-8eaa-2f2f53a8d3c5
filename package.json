{"name": "smart-farming", "version": "1.0.0", "scripts": {"android": "expo start --android", "ios": "expo start --ios", "start": "expo start", "prebuild": "expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.4.1", "@react-native-community/netinfo": "^11.4.1", "@supabase/supabase-js": "^2.55.0", "expo": "^53.0.20", "expo-audio": "~0.4.8", "expo-av": "^15.1.7", "expo-camera": "^16.1.11", "expo-constants": "^17.1.7", "expo-haptics": "~14.1.4", "expo-image-picker": "^16.1.4", "expo-localization": "^16.1.6", "expo-location": "^18.1.6", "expo-notifications": "^0.31.4", "expo-router": "^5.1.4", "expo-secure-store": "^14.2.3", "expo-speech": "^13.1.7", "expo-sqlite": "^15.2.14", "expo-status-bar": "~2.2.3", "nativewind": "latest", "react": "19.0.0", "react-native": "0.79.5", "react-native-get-random-values": "^1.11.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-url-polyfill": "^2.0.0", "zustand": "^5.0.7"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/jest": "^29.5.0", "@types/react": "~19.0.10", "eslint": "^9.25.1", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.2", "install": "^0.13.0", "jest": "~29.7.0npm", "jest-expo": "^53.0.9", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.0", "typescript": "~5.8.3"}, "main": "expo-router/entry", "private": true}