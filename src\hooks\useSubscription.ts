import { useEffect } from 'react';
import { useSubscriptionStore } from '../stores/subscription';
import { useAuthStore } from '../stores/auth';
import { FeatureAccess } from '../types/subscription';

export const useSubscription = () => {
    const { user } = useAuthStore();
    const {
        currentSubscription,
        availablePlans,
        usage,
        featureAccess,
        isLoading,
        error,
        loadSubscriptionPlans,
        loadUserSubscription,
        loadUsage,
        createSubscription,
        cancelSubscription,
        changeSubscription,
        checkFeatureAccess,
        checkAIConsultationLimit,
        updateUsage,
        getSubscriptionTier,
        clearError,
    } = useSubscriptionStore();

    // Load data when user changes
    useEffect(() => {
        loadSubscriptionPlans();
        if (user?.id) {
            loadUserSubscription(user.id);
        }
    }, [user?.id]);

    // Helper functions
    const isSubscribed = (): boolean => {
        return currentSubscription?.status === 'active';
    };

    const getSubscriptionStatus = (): string => {
        if (!currentSubscription) return 'free';
        return currentSubscription.status;
    };

    const getCurrentPlan = () => {
        if (!currentSubscription) return availablePlans.find(p => p.name.toLowerCase() === 'free');
        return availablePlans.find(p => p.id === currentSubscription.plan_id);
    };

    const canAccessFeature = (feature: keyof FeatureAccess): boolean => {
        return featureAccess?.[feature] || false;
    };

    const getRemainingAIConsultations = (): { remaining: number; limit: number; unlimited: boolean } => {
        const currentPlan = getCurrentPlan();
        if (!currentPlan) {
            return { remaining: 5, limit: 5, unlimited: false };
        }

        if (currentPlan.ai_consultations_limit === -1) {
            return { remaining: -1, limit: -1, unlimited: true };
        }

        const used = usage?.ai_consultations_used || 0;
        const limit = currentPlan.ai_consultations_limit;
        const remaining = Math.max(0, limit - used);

        return { remaining, limit, unlimited: false };
    };

    const getUsagePercentage = (usageType: 'ai_consultations' | 'image_analyses' | 'storage' | 'api_calls'): number => {
        if (!usage) return 0;

        const used = (usage as any)[`${usageType}_used`] || 0;
        const limit = (usage as any)[`${usageType}_limit`] || 1;

        if (limit === -1) return 0; // Unlimited
        return Math.min(100, (used / limit) * 100);
    };

    const isNearLimit = (usageType: 'ai_consultations' | 'image_analyses' | 'storage' | 'api_calls', threshold = 80): boolean => {
        return getUsagePercentage(usageType) >= threshold;
    };

    const getDaysUntilRenewal = (): number => {
        if (!currentSubscription) return 0;

        const renewalDate = new Date(currentSubscription.current_period_end);
        const now = new Date();
        const diffTime = renewalDate.getTime() - now.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        return Math.max(0, diffDays);
    };

    const isTrialing = (): boolean => {
        return currentSubscription?.status === 'trialing';
    };

    const isCancelled = (): boolean => {
        return currentSubscription?.cancel_at_period_end === true;
    };

    const canUpgrade = (): boolean => {
        const currentPlan = getCurrentPlan();
        if (!currentPlan) return true;

        return availablePlans.some(plan => plan.sort_order > currentPlan.sort_order);
    };

    const canDowngrade = (): boolean => {
        const currentPlan = getCurrentPlan();
        if (!currentPlan) return false;

        return availablePlans.some(plan => plan.sort_order < currentPlan.sort_order);
    };

    const getRecommendedPlan = () => {
        // Simple logic: recommend Premium for active users
        return availablePlans.find(p => p.name.toLowerCase() === 'premium') || availablePlans[0];
    };

    // Action functions
    const subscribe = async (planId: string, paymentMethodId?: string) => {
        if (!user?.id) return false;
        return await createSubscription(user.id, planId, paymentMethodId);
    };

    const unsubscribe = async (immediate = false) => {
        if (!user?.id) return false;
        return await cancelSubscription(user.id, !immediate);
    };

    const upgradePlan = async (newPlanId: string, paymentMethodId?: string) => {
        if (!user?.id) return false;
        return await changeSubscription(user.id, newPlanId, paymentMethodId);
    };

    const recordAIConsultation = async () => {
        if (!user?.id) return;
        await updateUsage(user.id, 'ai_consultations', 1);
    };

    const recordImageAnalysis = async () => {
        if (!user?.id) return;
        await updateUsage(user.id, 'image_analyses', 1);
    };

    const checkCanUseAI = async (): Promise<boolean> => {
        if (!user?.id) return false;
        const { canUse } = await checkAIConsultationLimit(user.id);
        return canUse;
    };

    const refreshSubscription = async () => {
        if (user?.id) {
            await loadUserSubscription(user.id);
            await loadUsage(user.id);
        }
    };

    return {
        // State
        currentSubscription,
        availablePlans,
        usage,
        featureAccess,
        isLoading,
        error,

        // Helper functions
        isSubscribed,
        getSubscriptionStatus,
        getCurrentPlan,
        canAccessFeature,
        getRemainingAIConsultations,
        getUsagePercentage,
        isNearLimit,
        getDaysUntilRenewal,
        isTrialing,
        isCancelled,
        canUpgrade,
        canDowngrade,
        getRecommendedPlan,

        // Action functions
        subscribe,
        unsubscribe,
        upgradePlan,
        recordAIConsultation,
        recordImageAnalysis,
        checkCanUseAI,
        refreshSubscription,
        clearError,

        // Raw store functions
        createSubscription,
        cancelSubscription,
        changeSubscription,
        checkFeatureAccess: (feature: keyof FeatureAccess) => user?.id && checkFeatureAccess(user.id, feature),
        updateUsage: (usageType: 'ai_consultations' | 'image_analyses' | 'storage' | 'api_calls', increment?: number) =>
            user?.id && updateUsage(user.id, usageType, increment),
        getSubscriptionTier: () => user?.id && getSubscriptionTier(user.id),
    };
};

export default useSubscription;