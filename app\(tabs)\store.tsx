import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, Alert, RefreshControl, Pressable } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { Product, ProductSearchParams, ShoppingCart } from '../../src/types/product';
import { storeService } from '../../src/services/store';
import { ProductCard } from '../../src/components/ui/ProductCard';
import { ProductSearch } from '../../src/components/ui/ProductSearch';

export default function StoreScreen() {
    const router = useRouter();
    const [products, setProducts] = useState<Product[]>([]);
    const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
    const [categories, setCategories] = useState<Array<{
        id: any;
        name: string;
        description: string | null;
        icon: string | null;
        productCount: number;
    }>>([]);
    const [cart, setCart] = useState<ShoppingCart | null>(null);
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);
    const [searchParams, setSearchParams] = useState<ProductSearchParams>({});

    useEffect(() => {
        loadInitialData();
    }, []);

    const loadInitialData = async () => {
        try {
            setLoading(true);
            const [allProducts, featured, categoriesData, cartData] = await Promise.all([
                storeService.searchProducts({}),
                storeService.getFeaturedProducts(),
                storeService.getCategories(),
                storeService.getCart(),
            ]);
            setProducts(allProducts);
            setFeaturedProducts(featured);
            setCategories(categoriesData);
            setCart(cartData);
        } catch (error) {
            console.error('Error loading store data:', error);
            Alert.alert('Error', 'Failed to load store data');
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = async (params: ProductSearchParams) => {
        try {
            setSearchParams(params);
            const results = await storeService.searchProducts(params);
            setProducts(results);
        } catch (error) {
            console.error('Error searching products:', error);
            Alert.alert('Error', 'Failed to search products');
        }
    };

    const handleProductPress = (product: Product) => {
        router.push(`/store/${product.id}`);
    };

    const handleAddToCart = async (productId: string) => {
        try {
            const updatedCart = await storeService.addToCart(productId, 1);
            setCart(updatedCart);
            Alert.alert(
                'Added to Cart',
                'Product has been added to your cart',
                [
                    { text: 'Continue Shopping', style: 'cancel' },
                    { text: 'View Cart', onPress: () => router.push('/store/cart') },
                ]
            );
        } catch (error) {
            console.error('Error adding to cart:', error);
            Alert.alert('Error', 'Failed to add product to cart');
        }
    };

    const onRefresh = async () => {
        setRefreshing(true);
        await loadInitialData();
        setRefreshing(false);
    };

    const renderProduct = ({ item }: { item: Product }) => (
        <ProductCard
            product={item}
            onPress={handleProductPress}
            onAddToCart={handleAddToCart}
            variant="grid"
        />
    );

    const renderFeaturedProduct = ({ item }: { item: Product }) => (
        <View className="mr-4" style={{ width: 200 }}>
            <ProductCard
                product={item}
                onPress={handleProductPress}
                onAddToCart={handleAddToCart}
                variant="grid"
            />
        </View>
    );

    const renderHeader = () => (
        <View>
            {/* Header with Cart */}
            <View className="flex-row items-center justify-between p-4 bg-white border-b border-gray-100">
                <Text className="text-2xl font-bold text-gray-900">🛒 Store</Text>
                <Pressable
                    onPress={() => router.push('/store/cart')}
                    className="relative p-2 active:opacity-80"
                    accessibilityRole="button"
                    accessibilityLabel={`Shopping cart with ${cart?.itemCount || 0} items`}
                >
                    <Text className="text-2xl">🛒</Text>
                    {cart && cart.itemCount > 0 && (
                        <View className="absolute -top-1 -right-1 bg-red-500 rounded-full min-w-[20px] h-5 items-center justify-center">
                            <Text className="text-white text-xs font-bold">
                                {cart.itemCount > 99 ? '99+' : cart.itemCount}
                            </Text>
                        </View>
                    )}
                </Pressable>
            </View>

            {/* Search Component */}
            <ProductSearch
                onSearch={handleSearch}
                categories={categories}
            />

            {/* Featured Products Section */}
            {featuredProducts.length > 0 && !searchParams.query && !searchParams.category && (
                <View className="mb-6">
                    <View className="px-4 py-3 bg-white border-b border-gray-100">
                        <Text className="text-xl font-bold text-gray-900 mb-1">
                            ⭐ Featured Products
                        </Text>
                        <Text className="text-gray-600">
                            Hand-picked products for your farm
                        </Text>
                    </View>
                    <FlatList
                        data={featuredProducts}
                        renderItem={renderFeaturedProduct}
                        keyExtractor={(item) => `featured_${item.id}`}
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        contentContainerStyle={{ paddingHorizontal: 16, paddingVertical: 16 }}
                    />
                </View>
            )}

            {/* Products Section Header */}
            <View className="px-4 py-3 bg-white border-b border-gray-100">
                <Text className="text-xl font-bold text-gray-900 mb-1">
                    {searchParams.query || searchParams.category ? 'Search Results' : 'All Products'}
                </Text>
                <Text className="text-gray-600">
                    {products.length} product{products.length !== 1 ? 's' : ''} found
                </Text>
            </View>
        </View>
    );

    if (loading) {
        return (
            <SafeAreaView className="flex-1 bg-gray-50">
                <View className="flex-1 items-center justify-center">
                    <Text className="text-lg text-gray-600">Loading store...</Text>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView className="flex-1 bg-gray-50">
            <FlatList
                data={products}
                renderItem={renderProduct}
                keyExtractor={(item) => item.id}
                numColumns={2}
                ListHeaderComponent={renderHeader}
                contentContainerStyle={{ paddingBottom: 20 }}
                columnWrapperStyle={{
                    paddingHorizontal: 16,
                    gap: 12,
                    marginBottom: 12
                }}
                refreshControl={
                    <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
                }
                ListEmptyComponent={
                    <View className="flex-1 items-center justify-center py-20">
                        <Text className="text-6xl mb-4">🔍</Text>
                        <Text className="text-xl font-semibold text-gray-700 mb-2">
                            No products found
                        </Text>
                        <Text className="text-gray-500 text-center px-8">
                            Try adjusting your search or filters to find what you're looking for
                        </Text>
                    </View>
                }
            />
        </SafeAreaView>
    );
}