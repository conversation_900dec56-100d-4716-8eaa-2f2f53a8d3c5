import { Stack } from 'expo-router';

export default function CommunityLayout() {
    return (
        <Stack>
            <Stack.Screen
                name="index"
                options={{
                    title: 'Community',
                    headerShown: false
                }}
            />
            <Stack.Screen
                name="create-post"
                options={{
                    title: 'Create Post',
                    presentation: 'modal'
                }}
            />
            <Stack.Screen
                name="post/[id]"
                options={{
                    title: 'Post Details'
                }}
            />
            <Stack.Screen
                name="profile/[id]"
                options={{
                    title: 'Farmer Profile'
                }}
            />
            <Stack.Screen
                name="messages"
                options={{
                    title: 'Messages'
                }}
            />
            <Stack.Screen
                name="chat/[id]"
                options={{
                    title: 'Chat'
                }}
            />
        </Stack>
    );
}