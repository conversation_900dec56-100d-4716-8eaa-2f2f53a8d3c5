import { PointsService } from '../supabase/points';
import { POINTS_EARNING_RULES, DEFAULT_ACHIEVEMENTS } from '../../types/points';

// Mock Supabase client
jest.mock('../supabase/client', () => ({
    supabase: {
        from: jest.fn(() => ({
            insert: jest.fn(() => ({ select: jest.fn(() => ({ single: jest.fn() })) })),
            select: jest.fn(() => ({
                eq: jest.fn(() => ({
                    single: jest.fn(),
                    order: jest.fn(() => ({ range: jest.fn() }))
                }))
            })),
            update: jest.fn(() => ({ eq: jest.fn() })),
            upsert: jest.fn(),
        })),
        rpc: jest.fn(),
    },
}));

describe('PointsService', () => {
    const mockUserId = 'test-user-id';

    describe('initializeUserPoints', () => {
        it('should initialize points system for new user', async () => {
            const result = await PointsService.initializeUserPoints(mockUserId);
            expect(result.error).toBeNull();
        });
    });

    describe('awardPoints', () => {
        it('should award points to user', async () => {
            const result = await PointsService.awardPoints(
                mockUserId,
                10,
                'task_completion',
                'Test task completion'
            );

            // Since we're mocking, we expect the function to complete without error
            expect(result.error).toBeNull();
        });
    });

    describe('spendPoints', () => {
        it('should spend points for user', async () => {
            const result = await PointsService.spendPoints(
                mockUserId,
                20,
                'ai_consultation',
                'AI consultation used'
            );

            // Since we're mocking, we expect the function to complete without error
            expect(result.error).toBeNull();
        });
    });
});

describe('Points Configuration', () => {
    it('should have valid points earning rules', () => {
        expect(POINTS_EARNING_RULES).toBeDefined();
        expect(POINTS_EARNING_RULES.length).toBeGreaterThan(0);

        POINTS_EARNING_RULES.forEach(rule => {
            expect(rule.source).toBeDefined();
            expect(rule.base_points).toBeDefined();
            expect(rule.description).toBeDefined();
            expect(typeof rule.is_active).toBe('boolean');
        });
    });

    it('should have valid default achievements', () => {
        expect(DEFAULT_ACHIEVEMENTS).toBeDefined();
        expect(DEFAULT_ACHIEVEMENTS.length).toBeGreaterThan(0);

        DEFAULT_ACHIEVEMENTS.forEach(achievement => {
            expect(achievement.name).toBeDefined();
            expect(achievement.description).toBeDefined();
            expect(achievement.points_reward).toBeGreaterThan(0);
            expect(achievement.requirements).toBeDefined();
            expect(achievement.category).toBeDefined();
        });
    });
});