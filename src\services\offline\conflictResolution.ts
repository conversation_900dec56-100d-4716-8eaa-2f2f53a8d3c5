import { ConflictResolution } from '../../types/offline';

export interface ConflictResolver {
    resolve(localData: any, serverData: any, strategy?: 'client_wins' | 'server_wins' | 'merge'): ConflictResolution;
}

export class DefaultConflictResolver implements ConflictResolver {
    resolve(
        localData: any,
        serverData: any,
        strategy: 'client_wins' | 'server_wins' | 'merge' = 'server_wins'
    ): ConflictResolution {
        let resolvedData: any;

        switch (strategy) {
            case 'client_wins':
                resolvedData = { ...localData };
                break;

            case 'server_wins':
                resolvedData = { ...serverData };
                break;

            case 'merge':
                resolvedData = this.mergeData(localData, serverData);
                break;

            default:
                resolvedData = { ...serverData };
        }

        return {
            localData,
            serverData,
            resolvedData,
            strategy,
            timestamp: Date.now()
        };
    }

    private mergeData(localData: any, serverData: any): any {
        if (!localData || !serverData) {
            return serverData || localData;
        }

        // For objects, merge properties
        if (typeof localData === 'object' && typeof serverData === 'object') {
            const merged = { ...serverData };

            // Merge based on timestamps if available
            if (localData.updated_at && serverData.updated_at) {
                const localTime = new Date(localData.updated_at).getTime();
                const serverTime = new Date(serverData.updated_at).getTime();

                // Use the more recent data for each field
                Object.keys(localData).forEach(key => {
                    if (key === 'updated_at' || key === 'created_at') return;

                    // If local data is newer for this field, use it
                    if (localTime > serverTime) {
                        merged[key] = localData[key];
                    }
                });
            } else {
                // Simple merge - prefer server data but keep local changes
                Object.keys(localData).forEach(key => {
                    if (key === 'id' || key === 'created_at') return;

                    // Keep local changes for specific fields
                    if (this.shouldPreferLocalData(key, localData[key], serverData[key])) {
                        merged[key] = localData[key];
                    }
                });
            }

            return merged;
        }

        // For non-objects, prefer server data
        return serverData;
    }

    private shouldPreferLocalData(key: string, localValue: any, serverValue: any): boolean {
        // Prefer local data for user input fields
        const userInputFields = [
            'title', 'description', 'content', 'notes',
            'completed', 'status', 'preferences'
        ];

        if (userInputFields.includes(key)) {
            // If local value is more recent or different, prefer it
            return localValue !== serverValue;
        }

        return false;
    }
}

export class TaskConflictResolver implements ConflictResolver {
    resolve(
        localData: any,
        serverData: any,
        strategy: 'client_wins' | 'server_wins' | 'merge' = 'merge'
    ): ConflictResolution {
        let resolvedData: any;

        if (strategy === 'merge') {
            resolvedData = {
                ...serverData,
                // Prefer local completion status and notes
                completed: localData.completed ?? serverData.completed,
                notes: localData.notes || serverData.notes,
                // Merge timestamps - use the latest
                completed_at: this.getLatestTimestamp(
                    localData.completed_at,
                    serverData.completed_at
                ),
                // Keep server data for task details but local progress
                progress: localData.progress ?? serverData.progress,
                updated_at: new Date().toISOString()
            };
        } else if (strategy === 'client_wins') {
            resolvedData = { ...localData };
        } else {
            resolvedData = { ...serverData };
        }

        return {
            localData,
            serverData,
            resolvedData,
            strategy,
            timestamp: Date.now()
        };
    }

    private getLatestTimestamp(local?: string, server?: string): string | undefined {
        if (!local && !server) return undefined;
        if (!local) return server;
        if (!server) return local;

        return new Date(local) > new Date(server) ? local : server;
    }
}

export class CropPlanConflictResolver implements ConflictResolver {
    resolve(
        localData: any,
        serverData: any,
        strategy: 'client_wins' | 'server_wins' | 'merge' = 'merge'
    ): ConflictResolution {
        let resolvedData: any;

        if (strategy === 'merge') {
            resolvedData = {
                ...serverData,
                // Prefer local user modifications
                notes: localData.notes || serverData.notes,
                custom_schedule: localData.custom_schedule ?? serverData.custom_schedule,
                user_preferences: {
                    ...serverData.user_preferences,
                    ...localData.user_preferences
                },
                // Keep server data for system-generated content
                weather_adjustments: serverData.weather_adjustments,
                ai_recommendations: serverData.ai_recommendations,
                updated_at: new Date().toISOString()
            };
        } else if (strategy === 'client_wins') {
            resolvedData = { ...localData };
        } else {
            resolvedData = { ...serverData };
        }

        return {
            localData,
            serverData,
            resolvedData,
            strategy,
            timestamp: Date.now()
        };
    }
}

export class ChatMessageConflictResolver implements ConflictResolver {
    resolve(
        localData: any,
        serverData: any,
        strategy: 'client_wins' | 'server_wins' | 'merge' = 'server_wins'
    ): ConflictResolution {
        // Chat messages are typically immutable, so prefer server data
        // unless it's a local message that hasn't been synced yet

        let resolvedData: any;

        if (localData.id && !serverData.id) {
            // Local message not yet synced
            resolvedData = { ...localData };
            strategy = 'client_wins';
        } else if (strategy === 'merge') {
            resolvedData = {
                ...serverData,
                // Keep local metadata if available
                local_metadata: localData.local_metadata,
                read_status: localData.read_status ?? serverData.read_status
            };
        } else if (strategy === 'client_wins') {
            resolvedData = { ...localData };
        } else {
            resolvedData = { ...serverData };
        }

        return {
            localData,
            serverData,
            resolvedData,
            strategy,
            timestamp: Date.now()
        };
    }
}

export class ConflictResolutionService {
    private resolvers: Map<string, ConflictResolver> = new Map();

    constructor() {
        // Register default resolvers
        this.resolvers.set('default', new DefaultConflictResolver());
        this.resolvers.set('tasks', new TaskConflictResolver());
        this.resolvers.set('crop_plans', new CropPlanConflictResolver());
        this.resolvers.set('chat_messages', new ChatMessageConflictResolver());
    }

    registerResolver(table: string, resolver: ConflictResolver): void {
        this.resolvers.set(table, resolver);
    }

    resolveConflict(
        table: string,
        localData: any,
        serverData: any,
        strategy?: 'client_wins' | 'server_wins' | 'merge'
    ): ConflictResolution {
        const resolver = this.resolvers.get(table) || this.resolvers.get('default')!;
        return resolver.resolve(localData, serverData, strategy);
    }

    getAvailableStrategies(table: string): string[] {
        // Return available strategies based on table type
        switch (table) {
            case 'tasks':
                return ['merge', 'client_wins', 'server_wins'];
            case 'crop_plans':
                return ['merge', 'client_wins', 'server_wins'];
            case 'chat_messages':
                return ['server_wins', 'client_wins'];
            default:
                return ['server_wins', 'client_wins', 'merge'];
        }
    }

    getRecommendedStrategy(table: string, localData: any, serverData: any): 'client_wins' | 'server_wins' | 'merge' {
        // Analyze data to recommend best strategy

        if (!localData || !serverData) {
            return localData ? 'client_wins' : 'server_wins';
        }

        // Check timestamps if available
        const localTime = localData.updated_at ? new Date(localData.updated_at).getTime() : 0;
        const serverTime = serverData.updated_at ? new Date(serverData.updated_at).getTime() : 0;

        // If one is significantly newer, prefer it
        const timeDiff = Math.abs(localTime - serverTime);
        if (timeDiff > 60000) { // More than 1 minute difference
            return localTime > serverTime ? 'client_wins' : 'server_wins';
        }

        // For user-generated content, prefer merge
        if (table === 'tasks' || table === 'crop_plans') {
            return 'merge';
        }

        // For system-generated content, prefer server
        return 'server_wins';
    }
}

export const conflictResolutionService = new ConflictResolutionService();