import { ImageAnalysisResult, Issue, Recommendation } from '../../types/ai';
import { GoogleGenerativeAI } from '@google/generative-ai';
import {
    ComprehensiveImageAnalysisService,
    createComprehensiveImageAnalysisService,
    ComprehensiveAnalysisResult
} from './comprehensiveImageAnalysis';

export interface ImageAnalysisService {
    analyzeImage(imageUri: string, category: 'plant' | 'soil' | 'fertilizer'): Promise<ImageAnalysisResult>;
    analyzeImageComprehensive?(imageUri: string, category: 'plant' | 'soil' | 'fertilizer'): Promise<ComprehensiveAnalysisResult>;
}

export class MockImageAnalysisService implements ImageAnalysisService {
    async analyzeImage(imageUri: string, category: 'plant' | 'soil' | 'fertilizer'): Promise<ImageAnalysisResult> {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Mock analysis based on category
        const mockResults = this.generateMockResult(category);

        return {
            id: Date.now().toString(),
            imageUri,
            category,
            confidence: 0.85 + Math.random() * 0.15, // 85-100% confidence
            timestamp: new Date(),
            ...mockResults,
        };
    }

    private generateMockResult(category: 'plant' | 'soil' | 'fertilizer') {
        switch (category) {
            case 'plant':
                return {
                    issues: [
                        {
                            name: 'اللفحة المبكرة',
                            severity: 'medium' as const,
                            description: 'تم اكتشاف علامات اللفحة المبكرة على الأوراق. هذا مرض فطري شائع يؤثر على النباتات في الطقس الرطب.',
                        },
                        {
                            name: 'نقص النيتروجين',
                            severity: 'low' as const,
                            description: 'الأوراق السفلية تظهر اصفراراً طفيفاً مما يشير إلى نقص طفيف في النيتروجين.',
                        }
                    ],
                    recommendations: [
                        {
                            title: 'العلاج الفوري',
                            description: 'استخدم مبيد فطري نحاسي لعلاج اللفحة المبكرة. رش في الصباح الباكر أو المساء.',
                            priority: 'high' as const,
                            products: ['مبيد فطري نحاسي', 'بوردو خليط'],
                        },
                        {
                            title: 'تحسين التهوية',
                            description: 'قم بتقليم الأوراق السفلية وزيادة المسافة بين النباتات لتحسين دوران الهواء.',
                            priority: 'medium' as const,
                        },
                        {
                            title: 'التسميد',
                            description: 'أضف سماد نيتروجيني متوازن لمعالجة نقص النيتروجين.',
                            priority: 'medium' as const,
                            products: ['سماد NPK متوازن', 'يوريا'],
                        }
                    ],
                };

            case 'soil':
                return {
                    issues: [
                        {
                            name: 'تربة قلوية',
                            severity: 'medium' as const,
                            description: 'التربة تظهر علامات القلوية العالية مما قد يؤثر على امتصاص العناصر الغذائية.',
                        },
                        {
                            name: 'ضعف الصرف',
                            severity: 'high' as const,
                            description: 'التربة تظهر علامات ضعف الصرف مما قد يؤدي إلى تعفن الجذور.',
                        }
                    ],
                    recommendations: [
                        {
                            title: 'تحسين الصرف',
                            description: 'أضف الرمل الخشن والكومبوست لتحسين بنية التربة والصرف.',
                            priority: 'high' as const,
                            products: ['رمل خشن', 'كومبوست عضوي'],
                        },
                        {
                            title: 'تعديل الحموضة',
                            description: 'استخدم الكبريت الزراعي لخفض درجة الحموضة تدريجياً.',
                            priority: 'medium' as const,
                            products: ['كبريت زراعي', 'خث الطحالب'],
                        }
                    ],
                };

            case 'fertilizer':
                return {
                    issues: [
                        {
                            name: 'زيادة الفوسفور',
                            severity: 'medium' as const,
                            description: 'علامات تشير إلى زيادة الفوسفور في التربة مما قد يمنع امتصاص عناصر أخرى.',
                        }
                    ],
                    recommendations: [
                        {
                            title: 'تقليل الفوسفور',
                            description: 'توقف عن استخدام الأسمدة الغنية بالفوسفور واستخدم أسمدة منخفضة الفوسفور.',
                            priority: 'high' as const,
                            products: ['سماد منخفض الفوسفور', 'سماد نيتروجيني'],
                        },
                        {
                            title: 'غسل التربة',
                            description: 'اروِ بكثرة لفترة قصيرة لغسل الفوسفور الزائد من التربة.',
                            priority: 'medium' as const,
                        }
                    ],
                };

            default:
                return {
                    issues: [],
                    recommendations: [],
                };
        }
    }
}

// OpenAI Vision API integration (placeholder for future implementation)
export class OpenAIImageAnalysisService implements ImageAnalysisService {
    private apiKey: string;

    constructor(apiKey: string) {
        this.apiKey = apiKey;
    }

    async analyzeImage(imageUri: string, category: 'plant' | 'soil' | 'fertilizer'): Promise<ImageAnalysisResult> {
        // TODO: Implement actual OpenAI Vision API integration
        // For now, fall back to mock service
        const mockService = new MockImageAnalysisService();
        return mockService.analyzeImage(imageUri, category);
    }
}

// Gemini Vision API integration with full implementation
export class GeminiImageAnalysisService implements ImageAnalysisService {
    private genAI: GoogleGenerativeAI;
    private model: any;

    constructor(apiKey: string) {
        this.genAI = new GoogleGenerativeAI(apiKey);
        this.model = this.genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    }

    async analyzeImage(imageUri: string, category: 'plant' | 'soil' | 'fertilizer'): Promise<ImageAnalysisResult> {
        try {
            const imageData = await this.prepareImageData(imageUri);
            const prompt = this.buildAnalysisPrompt(category);

            const result = await this.model.generateContent([prompt, imageData]);
            const response = await result.response;
            const analysisText = response.text();

            const parsedResult = this.parseAnalysisResult(analysisText, category);

            return {
                id: Date.now().toString(),
                imageUri,
                category,
                confidence: this.calculateConfidence(parsedResult),
                timestamp: new Date(),
                issues: parsedResult.issues,
                recommendations: parsedResult.recommendations,
            };
        } catch (error) {
            console.error('Gemini image analysis error:', error);
            // Fallback to mock service
            const mockService = new MockImageAnalysisService();
            return mockService.analyzeImage(imageUri, category);
        }
    }

    private async prepareImageData(imageUri: string) {
        try {
            // Convert image to base64 for Gemini API
            const response = await fetch(imageUri);
            const blob = await response.blob();

            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => {
                    const base64 = (reader.result as string).split(',')[1];
                    resolve({
                        inlineData: {
                            data: base64,
                            mimeType: blob.type || 'image/jpeg'
                        }
                    });
                };
                reader.onerror = reject;
                reader.readAsDataURL(blob);
            });
        } catch (error) {
            console.error('Error preparing image data:', error);
            throw error;
        }
    }

    private buildAnalysisPrompt(category: 'plant' | 'soil' | 'fertilizer'): string {
        const categoryPrompts = {
            plant: `أنت خبير في أمراض النباتات والصحة النباتية. حلل هذه الصورة للنبات وحدد:

1. المشاكل المرئية (الأمراض، الآفات، نقص العناصر، إلخ)
2. مستوى خطورة كل مشكلة (منخفض/متوسط/عالي)
3. التوصيات العلاجية المحددة
4. المنتجات الزراعية المقترحة للعلاج

اكتب الإجابة بالتنسيق التالي:
المشاكل:
- [اسم المشكلة] | [مستوى الخطورة] | [الوصف]

التوصيات:
- [العنوان] | [الأولوية] | [الوصف] | [المنتجات المقترحة]`,

            soil: `أنت خبير في علوم التربة والزراعة. حلل هذه الصورة للتربة وحدد:

1. حالة التربة (الملمس، اللون، الرطوبة، إلخ)
2. المشاكل المحتملة (الحموضة، الصرف، التغذية، إلخ)
3. التوصيات لتحسين التربة
4. المنتجات المقترحة للتحسين

اكتب الإجابة بالتنسيق التالي:
المشاكل:
- [اسم المشكلة] | [مستوى الخطورة] | [الوصف]

التوصيات:
- [العنوان] | [الأولوية] | [الوصف] | [المنتجات المقترحة]`,

            fertilizer: `أنت خبير في التسميد والتغذية النباتية. حلل هذه الصورة وحدد:

1. علامات نقص أو زيادة العناصر الغذائية
2. حالة النباتات من ناحية التغذية
3. توصيات التسميد المناسبة
4. الأسمدة المقترحة

اكتب الإجابة بالتنسيق التالي:
المشاكل:
- [اسم المشكلة] | [مستوى الخطورة] | [الوصف]

التوصيات:
- [العنوان] | [الأولوية] | [الوصف] | [المنتجات المقترحة]`
        };

        return categoryPrompts[category];
    }

    private parseAnalysisResult(analysisText: string, category: 'plant' | 'soil' | 'fertilizer') {
        const issues: Issue[] = [];
        const recommendations: Recommendation[] = [];

        try {
            const lines = analysisText.split('\n');
            let currentSection = '';

            for (const line of lines) {
                const trimmedLine = line.trim();

                if (trimmedLine.includes('المشاكل:') || trimmedLine.includes('المشاكل')) {
                    currentSection = 'issues';
                    continue;
                }

                if (trimmedLine.includes('التوصيات:') || trimmedLine.includes('التوصيات')) {
                    currentSection = 'recommendations';
                    continue;
                }

                if (trimmedLine.startsWith('-') && trimmedLine.includes('|')) {
                    const parts = trimmedLine.substring(1).split('|').map(p => p.trim());

                    if (currentSection === 'issues' && parts.length >= 3) {
                        const severityMap: { [key: string]: 'low' | 'medium' | 'high' } = {
                            'منخفض': 'low',
                            'متوسط': 'medium',
                            'عالي': 'high',
                            'low': 'low',
                            'medium': 'medium',
                            'high': 'high'
                        };

                        issues.push({
                            name: parts[0],
                            severity: severityMap[parts[1]] || 'medium',
                            description: parts[2]
                        });
                    }

                    if (currentSection === 'recommendations' && parts.length >= 3) {
                        const priorityMap: { [key: string]: 'low' | 'medium' | 'high' } = {
                            'منخفضة': 'low',
                            'متوسطة': 'medium',
                            'عالية': 'high',
                            'low': 'low',
                            'medium': 'medium',
                            'high': 'high'
                        };

                        recommendations.push({
                            title: parts[0],
                            priority: priorityMap[parts[1]] || 'medium',
                            description: parts[2],
                            products: parts[3] ? parts[3].split(',').map(p => p.trim()) : undefined
                        });
                    }
                }
            }
        } catch (error) {
            console.error('Error parsing analysis result:', error);
        }

        // Fallback to mock data if parsing failed
        if (issues.length === 0 && recommendations.length === 0) {
            const mockService = new MockImageAnalysisService();
            const mockResult = mockService['generateMockResult'](category);
            return mockResult;
        }

        return { issues, recommendations };
    }

    private calculateConfidence(result: { issues: Issue[], recommendations: Recommendation[] }): number {
        // Calculate confidence based on the quality of analysis
        let confidence = 0.7; // Base confidence

        // Increase confidence if we have detailed issues
        if (result.issues.length > 0) {
            confidence += 0.1;
        }

        // Increase confidence if we have actionable recommendations
        if (result.recommendations.length > 0) {
            confidence += 0.1;
        }

        // Increase confidence if recommendations have specific products
        const hasProducts = result.recommendations.some(r => r.products && r.products.length > 0);
        if (hasProducts) {
            confidence += 0.1;
        }

        return Math.min(confidence, 1.0);
    }
}

// High-availability image analysis service with automatic fallback
export class HighAvailabilityImageAnalysisService implements ImageAnalysisService {
    private primaryService: ImageAnalysisService;
    private fallbackService: ImageAnalysisService;
    private mockService: ImageAnalysisService;
    private comprehensiveService: ComprehensiveImageAnalysisService;

    constructor(
        primaryService: ImageAnalysisService,
        fallbackService: ImageAnalysisService
    ) {
        this.primaryService = primaryService;
        this.fallbackService = fallbackService;
        this.mockService = new MockImageAnalysisService();
        this.comprehensiveService = createComprehensiveImageAnalysisService();
    }

    async analyzeImage(imageUri: string, category: 'plant' | 'soil' | 'fertilizer'): Promise<ImageAnalysisResult> {
        // Try primary service first
        try {
            const result = await this.primaryService.analyzeImage(imageUri, category);
            return this.enhanceResultWithServiceInfo(result, 'primary');
        } catch (primaryError) {
            console.warn('Primary image analysis service failed, trying fallback:', primaryError);

            // Try fallback service
            try {
                const result = await this.fallbackService.analyzeImage(imageUri, category);
                return this.enhanceResultWithServiceInfo(result, 'fallback');
            } catch (fallbackError) {
                console.warn('Fallback image analysis service failed, using mock:', fallbackError);

                // Use mock service as last resort
                const result = await this.mockService.analyzeImage(imageUri, category);
                return this.enhanceResultWithServiceInfo(result, 'mock');
            }
        }
    }

    async analyzeImageComprehensive(imageUri: string, category: 'plant' | 'soil' | 'fertilizer'): Promise<ComprehensiveAnalysisResult> {
        try {
            return await this.comprehensiveService.analyzeImage(imageUri, category);
        } catch (error) {
            console.error('Comprehensive analysis failed:', error);
            // Fallback to basic analysis
            const basicResult = await this.analyzeImage(imageUri, category);

            // Convert basic result to comprehensive format
            return {
                ...basicResult,
                detailedAnalysis: {},
                productRecommendations: [],
                shoppingList: {
                    id: `list-${Date.now()}`,
                    items: [],
                    totalCost: 0,
                    estimatedDelivery: '2-3 أيام عمل',
                    recommendations: []
                },
                actionPlan: {
                    priority1: [],
                    priority2: [],
                    priority3: [],
                    timeline: '1-2 أسبوع',
                    totalCost: 0
                },
                followUpSchedule: {
                    immediate: [],
                    weekly: [],
                    monthly: []
                }
            };
        }
    }

    private enhanceResultWithServiceInfo(result: ImageAnalysisResult, serviceType: 'primary' | 'fallback' | 'mock'): ImageAnalysisResult {
        // Adjust confidence based on service type
        const confidenceMultiplier = {
            primary: 1.0,
            fallback: 0.9,
            mock: 0.7
        };

        return {
            ...result,
            confidence: result.confidence * confidenceMultiplier[serviceType]
        };
    }
}

// Factory function to create the appropriate service with fallback logic
export function createImageAnalysisService(): ImageAnalysisService {
    const openaiKey = process.env.EXPO_PUBLIC_OPENAI_API_KEY;
    const geminiKey = process.env.EXPO_PUBLIC_GEMINI_API_KEY;
    const mockMode = process.env.EXPO_PUBLIC_MOCK_SERVICES === 'true';

    // In mock mode, return mock service
    if (mockMode) {
        return new MockImageAnalysisService();
    }

    // If both keys available, use high availability service
    if (openaiKey && geminiKey) {
        const primaryService = new OpenAIImageAnalysisService(openaiKey);
        const fallbackService = new GeminiImageAnalysisService(geminiKey);
        return new HighAvailabilityImageAnalysisService(primaryService, fallbackService);
    }

    // If only Gemini key available, use Gemini
    if (geminiKey) {
        return new GeminiImageAnalysisService(geminiKey);
    }

    // If only OpenAI key available, use OpenAI
    if (openaiKey) {
        return new OpenAIImageAnalysisService(openaiKey);
    }

    // Fallback to mock service if no keys
    console.warn('No AI API keys found, using mock image analysis service');
    return new MockImageAnalysisService();
}