import React from 'react';
import { TouchableOpacity, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../../design-system';
import { useVoiceStore } from '../../stores/voice';

interface VoiceButtonProps {
    onPress?: () => void;
    size?: 'small' | 'medium' | 'large';
    variant?: 'primary' | 'secondary' | 'ghost';
    disabled?: boolean;
}

export function VoiceButton({
    onPress,
    size = 'medium',
    variant = 'primary',
    disabled = false
}: VoiceButtonProps) {
    const { isVoiceEnabled, isListening, isSpeaking } = useVoiceStore();

    if (!isVoiceEnabled) return null;

    const sizeMap = {
        small: { button: 32, icon: 16 },
        medium: { button: 44, icon: 20 },
        large: { button: 56, icon: 24 },
    };

    const getButtonStyle = () => {
        const baseStyle = {
            width: sizeMap[size].button,
            height: sizeMap[size].button,
            borderRadius: sizeMap[size].button / 2,
            justifyContent: 'center' as const,
            alignItems: 'center' as const,
            opacity: disabled ? 0.5 : 1,
        };

        switch (variant) {
            case 'primary':
                return {
                    ...baseStyle,
                    backgroundColor: isListening
                        ? colors.status.error
                        : colors.primary[500],
                };
            case 'secondary':
                return {
                    ...baseStyle,
                    backgroundColor: colors.primary[100],
                    borderWidth: 1,
                    borderColor: colors.primary[300],
                };
            case 'ghost':
                return {
                    ...baseStyle,
                    backgroundColor: 'transparent',
                };
            default:
                return baseStyle;
        }
    };

    const getIconColor = () => {
        switch (variant) {
            case 'primary':
                return 'white';
            case 'secondary':
                return colors.primary[500];
            case 'ghost':
                return colors.primary[500];
            default:
                return 'white';
        }
    };

    const getIconName = () => {
        if (isListening) return 'stop';
        if (isSpeaking) return 'volume-high';
        return 'mic';
    };

    return (
        <TouchableOpacity
            onPress={onPress}
            style={getButtonStyle()}
            disabled={disabled}
            accessibilityLabel={
                isListening
                    ? 'Stop listening'
                    : isSpeaking
                        ? 'Speaking'
                        : 'Voice input'
            }
            accessibilityRole="button"
        >
            {isListening || isSpeaking ? (
                <ActivityIndicator
                    size="small"
                    color={getIconColor()}
                />
            ) : (
                <Ionicons
                    name={getIconName()}
                    size={sizeMap[size].icon}
                    color={getIconColor()}
                />
            )}
        </TouchableOpacity>
    );
}