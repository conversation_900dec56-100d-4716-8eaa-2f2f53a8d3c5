import { Recommendation } from '../../types/ai';

export interface EcommerceIntegrationService {
    findProductsForRecommendation(recommendation: Recommendation): Promise<ProductRecommendation[]>;
    searchProducts(query: string, category?: ProductCategory): Promise<Product[]>;
    getProductDetails(productId: string): Promise<ProductDetails>;
    createShoppingList(recommendations: Recommendation[]): Promise<ShoppingList>;
}

export interface Product {
    id: string;
    name: string;
    arabicName: string;
    description: string;
    category: ProductCategory;
    price: number;
    currency: string;
    imageUrl: string;
    brand: string;
    availability: 'in_stock' | 'out_of_stock' | 'limited';
    rating: number;
    reviewCount: number;
    specifications: ProductSpecification[];
}

export interface ProductDetails extends Product {
    detailedDescription: string;
    ingredients: string[];
    applicationInstructions: ApplicationInstruction[];
    safetyInformation: SafetyInfo;
    certifications: string[];
    relatedProducts: Product[];
    reviews: ProductReview[];
}

export interface ProductSpecification {
    name: string;
    value: string;
    unit?: string;
}

export interface ApplicationInstruction {
    step: number;
    instruction: string;
    timing?: string;
    dosage?: string;
    precautions?: string[];
}

export interface SafetyInfo {
    warnings: string[];
    firstAid: string[];
    storage: string[];
    disposal: string[];
}

export interface ProductReview {
    id: string;
    userId: string;
    userName: string;
    rating: number;
    comment: string;
    date: Date;
    verified: boolean;
}

export interface ProductRecommendation {
    product: Product;
    relevanceScore: number;
    reason: string;
    alternativeProducts: Product[];
    bundleOptions: ProductBundle[];
}

export interface ProductBundle {
    id: string;
    name: string;
    products: Product[];
    totalPrice: number;
    savings: number;
    description: string;
}

export interface ShoppingList {
    id: string;
    items: ShoppingListItem[];
    totalCost: number;
    estimatedDelivery: string;
    recommendations: string[];
}

export interface ShoppingListItem {
    product: Product;
    quantity: number;
    priority: 'high' | 'medium' | 'low';
    reason: string;
    alternatives: Product[];
}

export type ProductCategory =
    | 'fertilizers'
    | 'pesticides'
    | 'fungicides'
    | 'herbicides'
    | 'seeds'
    | 'tools'
    | 'irrigation'
    | 'soil_amendments'
    | 'organic_products';

export class AdvancedEcommerceIntegrationService implements EcommerceIntegrationService {
    private productDatabase: Map<string, Product>;

    constructor() {
        this.initializeProductDatabase();
    }

    async findProductsForRecommendation(recommendation: Recommendation): Promise<ProductRecommendation[]> {
        await new Promise(resolve => setTimeout(resolve, 800));

        const mockRecommendations: ProductRecommendation[] = [];

        if (recommendation.products) {
            for (const productName of recommendation.products) {
                const matchingProducts = await this.searchProducts(productName);

                if (matchingProducts.length > 0) {
                    const primaryProduct = matchingProducts[0];
                    const alternatives = matchingProducts.slice(1, 4);

                    mockRecommendations.push({
                        product: primaryProduct,
                        relevanceScore: 0.95,
                        reason: `منتج مناسب لـ ${recommendation.title}`,
                        alternativeProducts: alternatives,
                        bundleOptions: await this.generateBundleOptions(primaryProduct)
                    });
                }
            }
        }

        return mockRecommendations;
    }

    async searchProducts(query: string, category?: ProductCategory): Promise<Product[]> {
        await new Promise(resolve => setTimeout(resolve, 600));

        // Mock product search results
        const mockProducts: Product[] = [
            {
                id: 'fung-001',
                name: 'Copper Fungicide',
                arabicName: 'مبيد فطري نحاسي',
                description: 'مبيد فطري فعال ضد الأمراض الفطرية',
                category: 'fungicides',
                price: 45.00,
                currency: 'SAR',
                imageUrl: '/images/copper-fungicide.jpg',
                brand: 'AgroTech',
                availability: 'in_stock',
                rating: 4.5,
                reviewCount: 128,
                specifications: [
                    { name: 'المادة الفعالة', value: 'كبريتات النحاس', unit: '50%' },
                    { name: 'الحجم', value: '1', unit: 'لتر' },
                    { name: 'نوع التركيب', value: 'سائل قابل للذوبان' }
                ]
            },
            {
                id: 'fert-001',
                name: 'NPK Balanced Fertilizer',
                arabicName: 'سماد NPK متوازن',
                description: 'سماد متوازن يحتوي على النيتروجين والفوسفور والبوتاسيوم',
                category: 'fertilizers',
                price: 35.00,
                currency: 'SAR',
                imageUrl: '/images/npk-fertilizer.jpg',
                brand: 'FertilMax',
                availability: 'in_stock',
                rating: 4.3,
                reviewCount: 89,
                specifications: [
                    { name: 'التركيب', value: '10-10-10' },
                    { name: 'الوزن', value: '25', unit: 'كيلو' },
                    { name: 'النوع', value: 'حبيبات' }
                ]
            },
            {
                id: 'org-001',
                name: 'Neem Oil',
                arabicName: 'زيت النيم',
                description: 'مبيد طبيعي من زيت النيم',
                category: 'organic_products',
                price: 28.00,
                currency: 'SAR',
                imageUrl: '/images/neem-oil.jpg',
                brand: 'OrganicGrow',
                availability: 'in_stock',
                rating: 4.7,
                reviewCount: 156,
                specifications: [
                    { name: 'التركيز', value: '100%', unit: 'طبيعي' },
                    { name: 'الحجم', value: '500', unit: 'مل' },
                    { name: 'الاستخدام', value: 'مبيد ومغذي' }
                ]
            }
        ];

        // Filter by category if specified
        if (category) {
            return mockProducts.filter(product => product.category === category);
        }

        // Simple search by name/description
        const searchTerm = query.toLowerCase();
        return mockProducts.filter(product =>
            product.name.toLowerCase().includes(searchTerm) ||
            product.arabicName.includes(query) ||
            product.description.toLowerCase().includes(searchTerm)
        );
    }

    async getProductDetails(productId: string): Promise<ProductDetails> {
        await new Promise(resolve => setTimeout(resolve, 500));

        const baseProduct = this.productDatabase.get(productId);
        if (!baseProduct) {
            throw new Error('Product not found');
        }

        const mockDetails: ProductDetails = {
            ...baseProduct,
            detailedDescription: `${baseProduct.description}\n\nهذا المنتج مصمم خصيصاً للاستخدام الزراعي المهني والهواة. يتميز بفعالية عالية وأمان في الاستخدام عند اتباع التعليمات.`,
            ingredients: ['كبريتات النحاس 50%', 'مواد مساعدة 30%', 'ماء 20%'],
            applicationInstructions: [
                {
                    step: 1,
                    instruction: 'اخلط 50 مل من المنتج مع 10 لتر ماء',
                    timing: 'في الصباح الباكر أو المساء',
                    dosage: '50 مل / 10 لتر ماء',
                    precautions: ['ارتدي القفازات', 'تجنب الرش في الرياح']
                },
                {
                    step: 2,
                    instruction: 'رش على الأوراق المصابة بالتساوي',
                    timing: 'تجنب أوقات الحر الشديد',
                    precautions: ['لا ترش على الأزهار', 'تجنب الرش قبل المطر']
                },
                {
                    step: 3,
                    instruction: 'كرر العلاج كل 7-10 أيام حسب الحاجة',
                    timing: 'حسب شدة الإصابة'
                }
            ],
            safetyInformation: {
                warnings: [
                    'يحفظ بعيداً عن متناول الأطفال',
                    'لا يستخدم على النباتات المزهرة',
                    'تجنب الاستنشاق المباشر'
                ],
                firstAid: [
                    'في حالة ملامسة العين: اغسل بالماء الجاري لمدة 15 دقيقة',
                    'في حالة البلع: اشرب كمية كبيرة من الماء واستشر الطبيب',
                    'في حالة ملامسة الجلد: اغسل بالماء والصابون'
                ],
                storage: [
                    'يحفظ في مكان بارد وجاف',
                    'درجة حرارة التخزين: 5-25 درجة مئوية',
                    'تجنب أشعة الشمس المباشرة'
                ],
                disposal: [
                    'لا تتخلص من العبوة في القمامة العادية',
                    'اتبع تعليمات التخلص المحلية',
                    'اغسل العبوة ثلاث مرات قبل التخلص منها'
                ]
            },
            certifications: ['ISO 9001', 'SASO', 'عضوي معتمد'],
            relatedProducts: await this.getRelatedProducts(productId),
            reviews: [
                {
                    id: 'rev-001',
                    userId: 'user-123',
                    userName: 'أحمد المزارع',
                    rating: 5,
                    comment: 'منتج ممتاز، نتائج سريعة وفعالة ضد الأمراض الفطرية',
                    date: new Date('2024-01-15'),
                    verified: true
                },
                {
                    id: 'rev-002',
                    userId: 'user-456',
                    userName: 'فاطمة الحديقة',
                    rating: 4,
                    comment: 'جيد جداً لكن الرائحة قوية قليلاً',
                    date: new Date('2024-01-10'),
                    verified: true
                }
            ]
        };

        return mockDetails;
    }

    async createShoppingList(recommendations: Recommendation[]): Promise<ShoppingList> {
        await new Promise(resolve => setTimeout(resolve, 1000));

        const items: ShoppingListItem[] = [];
        let totalCost = 0;

        for (const recommendation of recommendations) {
            if (recommendation.products) {
                for (const productName of recommendation.products) {
                    const products = await this.searchProducts(productName);
                    if (products.length > 0) {
                        const product = products[0];
                        const alternatives = products.slice(1, 3);

                        items.push({
                            product,
                            quantity: 1,
                            priority: recommendation.priority,
                            reason: recommendation.description,
                            alternatives
                        });

                        totalCost += product.price;
                    }
                }
            }
        }

        const shoppingList: ShoppingList = {
            id: `list-${Date.now()}`,
            items,
            totalCost,
            estimatedDelivery: '2-3 أيام عمل',
            recommendations: [
                'اطلب المنتجات عالية الأولوية أولاً',
                'تحقق من تواريخ انتهاء الصلاحية',
                'احفظ المنتجات في مكان آمن بعيداً عن الأطفال',
                'اقرأ تعليمات الاستخدام بعناية'
            ]
        };

        return shoppingList;
    }

    private async generateBundleOptions(product: Product): Promise<ProductBundle[]> {
        const relatedProducts = await this.getRelatedProducts(product.id);

        if (relatedProducts.length === 0) return [];

        const bundle: ProductBundle = {
            id: `bundle-${product.id}`,
            name: `حزمة العناية الشاملة`,
            products: [product, ...relatedProducts.slice(0, 2)],
            totalPrice: product.price + relatedProducts.slice(0, 2).reduce((sum, p) => sum + p.price, 0),
            savings: 15, // 15% discount
            description: 'حزمة متكاملة للعناية بالنباتات تشمل العلاج والوقاية'
        };

        bundle.totalPrice = bundle.totalPrice * 0.85; // Apply discount

        return [bundle];
    }

    private async getRelatedProducts(productId: string): Promise<Product[]> {
        // Mock related products
        const allProducts = Array.from(this.productDatabase.values());
        return allProducts.filter(p => p.id !== productId).slice(0, 3);
    }

    private initializeProductDatabase(): void {
        this.productDatabase = new Map();

        // Initialize with sample products
        const sampleProducts: Product[] = [
            {
                id: 'fung-001',
                name: 'Copper Fungicide',
                arabicName: 'مبيد فطري نحاسي',
                description: 'مبيد فطري فعال ضد الأمراض الفطرية',
                category: 'fungicides',
                price: 45.00,
                currency: 'SAR',
                imageUrl: '/images/copper-fungicide.jpg',
                brand: 'AgroTech',
                availability: 'in_stock',
                rating: 4.5,
                reviewCount: 128,
                specifications: []
            },
            // Add more products...
        ];

        sampleProducts.forEach(product => {
            this.productDatabase.set(product.id, product);
        });
    }
}

export function createEcommerceIntegrationService(): EcommerceIntegrationService {
    return new AdvancedEcommerceIntegrationService();
}