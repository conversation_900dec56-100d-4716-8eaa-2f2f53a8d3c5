export interface NotificationPreferences {
    weatherAlerts: boolean;
    taskReminders: boolean;
    cropStageUpdates: boolean;
    communityUpdates: boolean;
    emergencyAlerts: boolean;
    aiInsights: boolean;
    marketingPromotions: boolean;
    soundEnabled: boolean;
    vibrationEnabled: boolean;
    quietHoursStart: string; // HH:MM format
    quietHoursEnd: string; // HH:MM format
}

export interface NotificationData {
    id: string;
    userId: string;
    type: NotificationType;
    title: string;
    body: string;
    data?: Record<string, any>;
    scheduledFor?: Date;
    sentAt?: Date;
    readAt?: Date;
    actionTaken?: boolean;
    priority: NotificationPriority;
    category: NotificationCategory;
    expiresAt?: Date;
    retryCount: number;
    maxRetries: number;
}

export type NotificationType =
    | 'weather_alert'
    | 'task_reminder'
    | 'crop_stage_update'
    | 'community_post'
    | 'emergency_alert'
    | 'ai_insight'
    | 'subscription_reminder'
    | 'points_earned'
    | 'achievement_unlocked'
    | 'system_maintenance';

export type NotificationPriority = 'low' | 'normal' | 'high' | 'critical';

export type NotificationCategory =
    | 'agricultural'
    | 'weather'
    | 'community'
    | 'system'
    | 'commercial';

export interface ScheduledNotification {
    id: string;
    identifier: string;
    content: {
        title: string;
        body: string;
        data?: Record<string, any>;
        sound?: string;
        badge?: number;
    };
    trigger: {
        type: 'date' | 'timeInterval' | 'calendar' | 'location';
        date?: Date;
        seconds?: number;
        repeats?: boolean;
        hour?: number;
        minute?: number;
        weekday?: number;
    };
}

export interface NotificationAnalytics {
    notificationId: string;
    userId: string;
    type: NotificationType;
    sentAt: Date;
    deliveredAt?: Date;
    openedAt?: Date;
    actionTaken?: boolean;
    deviceInfo: {
        platform: string;
        version: string;
        model?: string;
    };
}

export interface PushToken {
    userId: string;
    token: string;
    platform: 'ios' | 'android' | 'web';
    deviceId: string;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}

export interface NotificationTemplate {
    id: string;
    type: NotificationType;
    title: string;
    body: string;
    variables: string[]; // Variables that can be replaced in title/body
    defaultData?: Record<string, any>;
    priority: NotificationPriority;
    category: NotificationCategory;
    soundFile?: string;
    iconUrl?: string;
}

export interface WeatherAlert {
    id: string;
    location: {
        latitude: number;
        longitude: number;
        name: string;
    };
    alertType: 'severe_weather' | 'frost_warning' | 'drought_alert' | 'flood_warning' | 'heat_wave';
    severity: 'minor' | 'moderate' | 'severe' | 'extreme';
    title: string;
    description: string;
    startTime: Date;
    endTime: Date;
    recommendations: string[];
    affectedCrops?: string[];
}

export interface CropStageAlert {
    id: string;
    cropPlanId: string;
    cropType: string;
    currentStage: string;
    nextStage: string;
    daysToNextStage: number;
    recommendations: string[];
    criticalActions: string[];
    optimalConditions: {
        temperature: { min: number; max: number };
        humidity: { min: number; max: number };
        soilMoisture: string;
    };
}

export interface AIInsightNotification {
    id: string;
    userId: string;
    insightType: 'disease_detected' | 'nutrient_deficiency' | 'pest_warning' | 'optimization_tip';
    confidence: number;
    title: string;
    description: string;
    imageUrl?: string;
    recommendations: string[];
    urgency: 'low' | 'medium' | 'high' | 'critical';
    relatedCropPlan?: string;
    actionRequired: boolean;
    estimatedImpact: string;
}