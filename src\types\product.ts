/**
 * Product types for the e-commerce store
 * Supporting agricultural products including seeds, fertilizers, and tools
 */

export interface Product {
    id: string;
    name: string;
    description: string;
    category: ProductCategory;
    price: number;
    originalPrice?: number; // For showing discounts
    currency: string;
    imageUrls: string[];
    specifications: ProductSpecification[];
    stockQuantity: number;
    rating: number;
    reviewCount: number;
    tags: string[];
    brand?: string;
    isRecommended?: boolean;
    isFeatured?: boolean;
    createdAt: Date;
    updatedAt: Date;
}

export type ProductCategory = 'seeds' | 'fertilizers' | 'tools' | 'equipment' | 'pesticides' | 'irrigation';

export interface ProductSpecification {
    name: string;
    value: string;
    unit?: string;
}

export interface ProductFilter {
    category?: ProductCategory;
    priceRange?: {
        min: number;
        max: number;
    };
    rating?: number;
    inStock?: boolean;
    brand?: string;
    tags?: string[];
}

export interface ProductSearchParams {
    query?: string;
    category?: ProductCategory;
    sortBy?: 'name' | 'price' | 'rating' | 'newest';
    sortOrder?: 'asc' | 'desc';
    filters?: ProductFilter;
    page?: number;
    limit?: number;
}

export interface CartItem {
    id: string;
    product: Product;
    quantity: number;
    selectedVariant?: ProductVariant;
}

export interface ProductVariant {
    id: string;
    name: string;
    price: number;
    stockQuantity: number;
    specifications: ProductSpecification[];
}

export interface ShoppingCart {
    id: string;
    items: CartItem[];
    totalAmount: number;
    itemCount: number;
    updatedAt: Date;
}

export interface Order {
    id: string;
    userId: string;
    items: CartItem[];
    totalAmount: number;
    status: OrderStatus;
    shippingAddress: ShippingAddress;
    paymentMethod: PaymentMethod;
    trackingNumber?: string;
    createdAt: Date;
    updatedAt: Date;
    estimatedDelivery?: Date;
}

export type OrderStatus = 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';

export interface ShippingAddress {
    fullName: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
    phoneNumber: string;
}

export interface PaymentMethod {
    type: 'credit_card' | 'debit_card' | 'mobile_money' | 'cash_on_delivery';
    details: Record<string, any>;
}

export interface ProductRecommendation {
    product: Product;
    reason: string;
    confidence: number;
    relatedTo?: string; // Product ID or category that triggered this recommendation
}