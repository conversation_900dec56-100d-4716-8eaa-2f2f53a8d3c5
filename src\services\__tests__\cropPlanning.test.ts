import { CropPlanningService } from '../cropPlanning';
import { LocationCoordinates } from '../location';

describe('CropPlanningService', () => {
    const testLocation: LocationCoordinates = {
        latitude: 40.7128,
        longitude: -74.0060, // New York coordinates
    };

    test('should generate crop recommendations', async () => {
        const recommendations = await CropPlanningService.getCropRecommendations(
            testLocation,
            'beginner',
            new Date('2024-04-15') // Spring date
        );

        expect(recommendations).toBeDefined();
        expect(recommendations.length).toBeGreaterThan(0);
        expect(recommendations[0]).toHaveProperty('cropId');
        expect(recommendations[0]).toHaveProperty('suitabilityScore');
        expect(recommendations[0]).toHaveProperty('reasons');
        expect(recommendations[0]).toHaveProperty('estimatedYield');
    });

    test('should create crop plan with weather integration', async () => {
        const cropPlan = await CropPlanningService.createCropPlan({
            cropType: 'tomatoes',
            plantingDate: new Date('2024-05-01'),
            location: testLocation,
            notes: 'Test crop plan',
            expectedYield: 10,
        });

        expect(cropPlan).toBeDefined();
        expect(cropPlan.cropType).toBe('tomatoes');
        expect(cropPlan.location).toEqual(testLocation);
        expect(cropPlan.status).toBe('planning');
    });

    test('should generate growth milestones', () => {
        const mockCropPlan = {
            id: 'test-plan',
            cropType: 'tomatoes',
            cropName: 'Tomatoes',
            plantingDate: new Date('2024-05-01'),
            harvestDate: new Date('2024-07-20'),
            location: testLocation,
            status: 'active' as const,
            tasks: [],
            weatherAlerts: [],
            createdAt: new Date(),
            updatedAt: new Date(),
        };

        const milestones = CropPlanningService.generateGrowthMilestones(mockCropPlan);

        expect(milestones).toBeDefined();
        expect(milestones.length).toBe(4); // germination, vegetative, flowering, maturation
        expect(milestones[0].stage).toBe('germination');
        expect(milestones[1].stage).toBe('vegetative');
        expect(milestones[2].stage).toBe('flowering');
        expect(milestones[3].stage).toBe('maturation');
    });
});