import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { PointsBalance as PointsBalanceType } from '../../types/points';
import { formatPoints } from '../../utils/points';

interface PointsBalanceProps {
    balance: PointsBalanceType;
    onViewHistory?: () => void;
    onRedeemPoints?: () => void;
    showActions?: boolean;
}

export const PointsBalance: React.FC<PointsBalanceProps> = ({
    balance,
    onViewHistory,
    onRedeemPoints,
    showActions = true,
}) => {
    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.title}>Points Balance</Text>
                <Text style={styles.icon}>💎</Text>
            </View>

            <View style={styles.balanceContainer}>
                <Text style={styles.availablePoints}>
                    {formatPoints(balance.available_points)}
                </Text>
                <Text style={styles.availableLabel}>Available Points</Text>
            </View>

            <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                    <Text style={styles.statValue}>
                        {formatPoints(balance.lifetime_earned)}
                    </Text>
                    <Text style={styles.statLabel}>Total Earned</Text>
                </View>
                <View style={styles.statDivider} />
                <View style={styles.statItem}>
                    <Text style={styles.statValue}>
                        {formatPoints(balance.lifetime_spent)}
                    </Text>
                    <Text style={styles.statLabel}>Total Spent</Text>
                </View>
            </View>

            {balance.pending_points > 0 && (
                <View style={styles.pendingContainer}>
                    <Text style={styles.pendingText}>
                        {formatPoints(balance.pending_points)} points pending
                    </Text>
                </View>
            )}

            {showActions && (
                <View style={styles.actionsContainer}>
                    {onViewHistory && (
                        <TouchableOpacity
                            style={[styles.actionButton, styles.secondaryButton]}
                            onPress={onViewHistory}
                        >
                            <Text style={styles.secondaryButtonText}>View History</Text>
                        </TouchableOpacity>
                    )}
                    {onRedeemPoints && balance.available_points > 0 && (
                        <TouchableOpacity
                            style={[styles.actionButton, styles.primaryButton]}
                            onPress={onRedeemPoints}
                        >
                            <Text style={styles.primaryButtonText}>Redeem Points</Text>
                        </TouchableOpacity>
                    )}
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#ffffff',
        borderRadius: 16,
        padding: 20,
        marginVertical: 8,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
        borderWidth: 1,
        borderColor: '#f3f4f6',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 20,
    },
    title: {
        fontSize: 18,
        fontWeight: '600',
        color: '#1f2937',
    },
    icon: {
        fontSize: 24,
    },
    balanceContainer: {
        alignItems: 'center',
        marginBottom: 24,
    },
    availablePoints: {
        fontSize: 36,
        fontWeight: 'bold',
        color: '#22c55e',
        marginBottom: 4,
    },
    availableLabel: {
        fontSize: 16,
        color: '#6b7280',
    },
    statsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 20,
    },
    statItem: {
        flex: 1,
        alignItems: 'center',
    },
    statValue: {
        fontSize: 20,
        fontWeight: '600',
        color: '#374151',
        marginBottom: 4,
    },
    statLabel: {
        fontSize: 14,
        color: '#6b7280',
    },
    statDivider: {
        width: 1,
        height: 40,
        backgroundColor: '#e5e7eb',
        marginHorizontal: 20,
    },
    pendingContainer: {
        backgroundColor: '#fef3c7',
        borderRadius: 8,
        padding: 12,
        marginBottom: 20,
        alignItems: 'center',
    },
    pendingText: {
        fontSize: 14,
        color: '#92400e',
        fontWeight: '500',
    },
    actionsContainer: {
        flexDirection: 'row',
        gap: 12,
    },
    actionButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: 'center',
    },
    primaryButton: {
        backgroundColor: '#22c55e',
    },
    secondaryButton: {
        backgroundColor: '#f3f4f6',
        borderWidth: 1,
        borderColor: '#d1d5db',
    },
    primaryButtonText: {
        color: '#ffffff',
        fontSize: 16,
        fontWeight: '600',
    },
    secondaryButtonText: {
        color: '#374151',
        fontSize: 16,
        fontWeight: '600',
    },
});