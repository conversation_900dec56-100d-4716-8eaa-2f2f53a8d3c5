export interface OfflineAction {
    id: string;
    type: 'CREATE' | 'UPDATE' | 'DELETE';
    table: string;
    data: any;
    timestamp: number;
    userId?: string;
    retryCount: number;
    maxRetries: number;
    status: 'pending' | 'syncing' | 'completed' | 'failed';
}

export interface SyncQueueItem extends OfflineAction {
    localId?: string;
    serverId?: string;
    conflictResolution?: 'client_wins' | 'server_wins' | 'merge';
}

export interface OfflineData {
    // Core data that needs to be available offline
    userProfile?: any;
    cropPlans: any[];
    tasks: any[];
    chatSessions: any[];
    chatMessages: any[];
    communityPosts: any[];
    products: any[];

    // Metadata
    lastSyncTimestamp: number;
    syncInProgress: boolean;
}

export interface ConflictResolution {
    localData: any;
    serverData: any;
    resolvedData: any;
    strategy: 'client_wins' | 'server_wins' | 'merge';
    timestamp: number;
}

export interface SyncStatus {
    isOnline: boolean;
    lastSyncTime: number | null;
    syncInProgress: boolean;
    pendingActions: number;
    failedActions: number;
    nextSyncAttempt: number | null;
}

export interface OfflineConfig {
    maxRetries: number;
    retryDelay: number;
    syncInterval: number;
    maxCacheSize: number;
    compressionEnabled: boolean;
    essentialTables: string[];
}