import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { SubscriptionPlan } from '../../types/subscription';
import { formatPrice, getBillingCycleLabel, getSubscriptionBenefits } from '../../utils/subscription';

interface SubscriptionCardProps {
    plan: SubscriptionPlan;
    isCurrentPlan?: boolean;
    isRecommended?: boolean;
    onSelect: (planId: string) => void;
    disabled?: boolean;
}

export const SubscriptionCard: React.FC<SubscriptionCardProps> = ({
    plan,
    isCurrentPlan = false,
    isRecommended = false,
    onSelect,
    disabled = false,
}) => {
    const benefits = getSubscriptionBenefits(plan);

    return (
        <View style={[
            styles.container,
            isRecommended && styles.recommended,
            isCurrentPlan && styles.current,
        ]}>
            {isRecommended && (
                <View style={styles.recommendedBadge}>
                    <Text style={styles.recommendedText}>RECOMMENDED</Text>
                </View>
            )}

            <View style={styles.header}>
                <Text style={styles.planName}>{plan.name}</Text>
                <View style={styles.priceContainer}>
                    <Text style={styles.price}>
                        {formatPrice(plan.price, plan.currency)}
                    </Text>
                    <Text style={styles.billingCycle}>
                        {getBillingCycleLabel(plan.billing_period)}
                    </Text>
                </View>
            </View>

            <Text style={styles.description}>{plan.description}</Text>

            <View style={styles.benefitsContainer}>
                {benefits.slice(0, 4).map((benefit, index) => (
                    <View key={index} style={styles.benefitItem}>
                        <Text style={styles.checkmark}>✓</Text>
                        <Text style={styles.benefitText}>{benefit}</Text>
                    </View>
                ))}
                {benefits.length > 4 && (
                    <Text style={styles.moreBenefits}>
                        +{benefits.length - 4} more features
                    </Text>
                )}
            </View>

            <TouchableOpacity
                style={[
                    styles.selectButton,
                    isCurrentPlan && styles.currentButton,
                    disabled && styles.disabledButton,
                ]}
                onPress={() => onSelect(plan.id)}
                disabled={disabled || isCurrentPlan}
            >
                <Text style={[
                    styles.selectButtonText,
                    isCurrentPlan && styles.currentButtonText,
                ]}>
                    {isCurrentPlan ? 'Current Plan' : 'Select Plan'}
                </Text>
            </TouchableOpacity>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#ffffff',
        borderRadius: 12,
        padding: 20,
        marginVertical: 8,
        borderWidth: 1,
        borderColor: '#e5e7eb',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },
    recommended: {
        borderColor: '#22c55e',
        borderWidth: 2,
    },
    current: {
        borderColor: '#3b82f6',
        backgroundColor: '#f8fafc',
    },
    recommendedBadge: {
        position: 'absolute',
        top: -8,
        left: 20,
        backgroundColor: '#22c55e',
        paddingHorizontal: 12,
        paddingVertical: 4,
        borderRadius: 12,
    },
    recommendedText: {
        color: '#ffffff',
        fontSize: 12,
        fontWeight: 'bold',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    planName: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#1f2937',
        flex: 1,
    },
    priceContainer: {
        alignItems: 'flex-end',
    },
    price: {
        fontSize: 28,
        fontWeight: 'bold',
        color: '#22c55e',
    },
    billingCycle: {
        fontSize: 14,
        color: '#6b7280',
        marginTop: 2,
    },
    description: {
        fontSize: 16,
        color: '#6b7280',
        marginBottom: 20,
        lineHeight: 22,
    },
    benefitsContainer: {
        marginBottom: 24,
    },
    benefitItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },
    checkmark: {
        color: '#22c55e',
        fontSize: 16,
        fontWeight: 'bold',
        marginRight: 12,
        width: 20,
    },
    benefitText: {
        fontSize: 15,
        color: '#374151',
        flex: 1,
        lineHeight: 20,
    },
    moreBenefits: {
        fontSize: 14,
        color: '#6b7280',
        fontStyle: 'italic',
        marginTop: 8,
        textAlign: 'center',
    },
    selectButton: {
        backgroundColor: '#22c55e',
        borderRadius: 8,
        paddingVertical: 14,
        alignItems: 'center',
    },
    currentButton: {
        backgroundColor: '#e5e7eb',
    },
    disabledButton: {
        backgroundColor: '#f3f4f6',
        opacity: 0.6,
    },
    selectButtonText: {
        color: '#ffffff',
        fontSize: 16,
        fontWeight: '600',
    },
    currentButtonText: {
        color: '#6b7280',
    },
});