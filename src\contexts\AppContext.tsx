import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { VoiceService, VoiceSettings } from '../services/voice';
import { LanguageService, LanguageSettings, SupportedLanguage } from '../services/language';

interface AppContextType {
    // Voice
    voiceSettings: VoiceSettings;
    isVoiceEnabled: boolean;
    enableVoiceMode: () => Promise<void>;
    disableVoiceMode: () => Promise<void>;
    speak: (text: string) => Promise<void>;

    // Language
    languageSettings: LanguageSettings;
    currentLanguage: SupportedLanguage;
    isRTL: boolean;
    setLanguage: (language: SupportedLanguage) => Promise<void>;
    t: (key: string) => string;

    // Loading state
    isInitialized: boolean;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

interface AppProviderProps {
    children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
    const [voiceSettings, setVoiceSettings] = useState<VoiceSettings>({
        enabled: false,
        language: 'en',
        rate: 0.8,
        pitch: 1.0,
    });

    const [languageSettings, setLanguageSettings] = useState<LanguageSettings>({
        current: 'en',
        isRTL: false,
    });

    const [isInitialized, setIsInitialized] = useState(false);

    const voiceService = VoiceService.getInstance();
    const languageService = LanguageService.getInstance();

    useEffect(() => {
        const initialize = async () => {
            try {
                await Promise.all([
                    voiceService.initialize(),
                    languageService.initialize(),
                ]);

                setVoiceSettings(voiceService.getSettings());
                setLanguageSettings(languageService.getSettings());
                setIsInitialized(true);
            } catch (error) {
                console.error('Failed to initialize app services:', error);
                setIsInitialized(true); // Still set to true to prevent infinite loading
            }
        };

        initialize();
    }, []);

    const enableVoiceMode = async () => {
        await voiceService.enableVoiceMode();
        setVoiceSettings(voiceService.getSettings());
    };

    const disableVoiceMode = async () => {
        await voiceService.disableVoiceMode();
        setVoiceSettings(voiceService.getSettings());
    };

    const speak = async (text: string) => {
        await voiceService.speak(text);
    };

    const setLanguage = async (language: SupportedLanguage) => {
        await languageService.setLanguage(language);
        await voiceService.setLanguage(language);
        setLanguageSettings(languageService.getSettings());
        setVoiceSettings(voiceService.getSettings());
    };

    const contextValue: AppContextType = {
        // Voice
        voiceSettings,
        isVoiceEnabled: voiceSettings.enabled,
        enableVoiceMode,
        disableVoiceMode,
        speak,

        // Language
        languageSettings,
        currentLanguage: languageSettings.current,
        isRTL: languageSettings.isRTL,
        setLanguage,
        t: languageService.t.bind(languageService),

        // Loading
        isInitialized,
    };

    return <AppContext.Provider value={contextValue}>{children}</AppContext.Provider>;
};

export const useApp = (): AppContextType => {
    const context = useContext(AppContext);
    if (!context) {
        throw new Error('useApp must be used within an AppProvider');
    }
    return context;
};