import 'react-native-get-random-values';
import 'react-native-url-polyfill/auto';

import { Stack, useRouter, useSegments, useRootNavigationState } from 'expo-router';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { AccessibilityProvider } from '../src/contexts/AccessibilityContext';
import '../global.css';
import { useEffect } from 'react';
import { supabase } from '../src/services/supabase/client';
import { useAuthStore } from '../src/stores/auth';

export default function RootLayout() {
  const segments = useSegments();
  const router = useRouter();
  const rootNavigationState = useRootNavigationState();
  const { isAuthenticated, isLoading, initialize } = useAuthStore();

  // تهيئة حالة المصادقة مرة واحدة عند تحميل الـ RootLayout
  useEffect(() => {
    initialize();
  }, [initialize]);

  // حارس بسيط: إذا المستخدم غير مسجل الدخول امنعه من (tabs)
  useEffect(() => {
    // لا تحاول التنقّل قبل جاهزية نظام الملاحة الجذري
    if (!rootNavigationState?.key) return;

    const inAuthGroup = segments?.[0] === '(auth)';
    if (!isLoading) {
      if (!isAuthenticated && !inAuthGroup) {
        router.replace('/(auth)/register');
      } else if (isAuthenticated && inAuthGroup) {
        router.replace('/(tabs)/home');
      }
    }
  }, [rootNavigationState?.key, segments, isAuthenticated, isLoading]);

  // اختبار اتصال Supabase
  useEffect(() => {
    (async () => {
      try {
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
        console.log('[Supabase][Session]', { session: sessionData?.session, sessionError });

        const { data: pingData, error: pingError } = await supabase
          .from('users')
          .select('id')
          .limit(1);
        console.log('[Supabase][Ping users]', { pingData, pingError });
      } catch (e) {
        console.log('[Supabase][SmokeTest][Error]', e);
      }
    })();
  }, []);

  return (
    <SafeAreaProvider>
      <AccessibilityProvider>
        <Stack screenOptions={{ headerShown: false }}>
          <Stack.Screen name="index" />
          <Stack.Screen name="(auth)" options={{ headerShown: false }} />
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen name="ai-chat" options={{ headerShown: false }} />
          <Stack.Screen name="ai-diagnosis" options={{ headerShown: false }} />
        </Stack>
      </AccessibilityProvider>
    </SafeAreaProvider>
  );
}
