import * as Location from 'expo-location';

export interface LocationCoordinates {
    latitude: number;
    longitude: number;
    accuracy?: number;
}

export interface LocationAddress {
    street?: string;
    city?: string;
    region?: string;
    country?: string;
    postalCode?: string;
    formattedAddress?: string;
}

export interface FarmLocation {
    coordinates: LocationCoordinates;
    address?: LocationAddress;
    timestamp: Date;
}

export class LocationService {
    private static instance: LocationService;

    static getInstance(): LocationService {
        if (!LocationService.instance) {
            LocationService.instance = new LocationService();
        }
        return LocationService.instance;
    }

    async requestPermissions(): Promise<boolean> {
        try {
            const { status } = await Location.requestForegroundPermissionsAsync();
            return status === 'granted';
        } catch (error) {
            console.warn('Failed to request location permissions:', error);
            return false;
        }
    }

    async getCurrentLocation(): Promise<FarmLocation | null> {
        try {
            const hasPermission = await this.requestPermissions();
            if (!hasPermission) {
                throw new Error('Location permission not granted');
            }

            const location = await Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.High,
                timeInterval: 10000,
                distanceInterval: 10,
            });

            const coordinates: LocationCoordinates = {
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
                accuracy: location.coords.accuracy || undefined,
            };

            // Get address from coordinates
            let address: LocationAddress | undefined;
            try {
                const addressResults = await Location.reverseGeocodeAsync(coordinates);
                if (addressResults.length > 0) {
                    const result = addressResults[0];
                    address = {
                        street: result.street || undefined,
                        city: result.city || undefined,
                        region: result.region || undefined,
                        country: result.country || undefined,
                        postalCode: result.postalCode || undefined,
                        formattedAddress: this.formatAddress(result),
                    };
                }
            } catch (addressError) {
                console.warn('Failed to get address from coordinates:', addressError);
            }

            return {
                coordinates,
                address,
                timestamp: new Date(),
            };
        } catch (error) {
            console.error('Failed to get current location:', error);
            return null;
        }
    }

    async getLocationFromCoordinates(latitude: number, longitude: number): Promise<FarmLocation | null> {
        try {
            const coordinates: LocationCoordinates = { latitude, longitude };

            let address: LocationAddress | undefined;
            try {
                const addressResults = await Location.reverseGeocodeAsync(coordinates);
                if (addressResults.length > 0) {
                    const result = addressResults[0];
                    address = {
                        street: result.street || undefined,
                        city: result.city || undefined,
                        region: result.region || undefined,
                        country: result.country || undefined,
                        postalCode: result.postalCode || undefined,
                        formattedAddress: this.formatAddress(result),
                    };
                }
            } catch (addressError) {
                console.warn('Failed to get address from coordinates:', addressError);
            }

            return {
                coordinates,
                address,
                timestamp: new Date(),
            };
        } catch (error) {
            console.error('Failed to get location from coordinates:', error);
            return null;
        }
    }

    private formatAddress(result: Location.LocationGeocodedAddress): string {
        const parts = [
            result.street,
            result.city,
            result.region,
            result.country,
        ].filter(Boolean);

        return parts.join(', ');
    }

    formatLocationForDisplay(location: FarmLocation): string {
        if (location.address?.formattedAddress) {
            return location.address.formattedAddress;
        }

        return `${location.coordinates.latitude.toFixed(6)}, ${location.coordinates.longitude.toFixed(6)}`;
    }
}