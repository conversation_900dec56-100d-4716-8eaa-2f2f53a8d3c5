import { GeoLocation, ProfileFormData, ProfileValidationErrors, CropType, COMMON_CROP_TYPES } from '../types/profile';

/**
 * Validates farm location coordinates
 */
export const isValidLocation = (location: GeoLocation): boolean => {
    return (
        location.latitude >= -90 &&
        location.latitude <= 90 &&
        location.longitude >= -180 &&
        location.longitude <= 180
    );
};

/**
 * Calculates distance between two coordinates in kilometers
 */
export const calculateDistance = (
    point1: GeoLocation,
    point2: GeoLocation
): number => {
    const R = 6371; // Earth's radius in kilometers
    const dLat = toRadians(point2.latitude - point1.latitude);
    const dLon = toRadians(point2.longitude - point1.longitude);

    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(toRadians(point1.latitude)) *
        Math.cos(toRadians(point2.latitude)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
};

/**
 * Converts degrees to radians
 */
const toRadians = (degrees: number): number => {
    return degrees * (Math.PI / 180);
};

/**
 * Formats location for display
 */
export const formatLocation = (location: GeoLocation): string => {
    return `${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)}`;
};

/**
 * Parses PostGIS POINT format to GeoLocation
 */
export const parsePostGISPoint = (pointString: string): GeoLocation | null => {
    const match = pointString.match(/POINT\(([^)]+)\)/);
    if (!match) return null;

    const [longitude, latitude] = match[1].split(' ').map(Number);
    if (isNaN(longitude) || isNaN(latitude)) return null;

    return { latitude, longitude };
};

/**
 * Converts GeoLocation to PostGIS POINT format
 */
export const toPostGISPoint = (location: GeoLocation): string => {
    return `POINT(${location.longitude} ${location.latitude})`;
};

/**
 * Validates crop types array
 */
export const isValidCropTypes = (cropTypes: string[]): boolean => {
    if (!Array.isArray(cropTypes) || cropTypes.length === 0) {
        return false;
    }

    const validCropIds = COMMON_CROP_TYPES.map(crop => crop.id);
    return cropTypes.every(crop => validCropIds.includes(crop));
};

/**
 * Gets crop type information by ID
 */
export const getCropTypeById = (id: string): CropType | undefined => {
    return COMMON_CROP_TYPES.find(crop => crop.id === id);
};

/**
 * Filters crop types by difficulty level
 */
export const getCropsByDifficulty = (
    difficulty: 'beginner' | 'intermediate' | 'expert'
): CropType[] => {
    return COMMON_CROP_TYPES.filter(crop => crop.difficulty === difficulty);
};

/**
 * Validates profile form data
 */
export const validateProfileForm = (data: ProfileFormData): ProfileValidationErrors => {
    const errors: ProfileValidationErrors = {};

    if (!data.firstName.trim()) {
        errors.firstName = 'First name is required';
    } else if (data.firstName.trim().length < 2) {
        errors.firstName = 'First name must be at least 2 characters';
    }

    if (!data.lastName.trim()) {
        errors.lastName = 'Last name is required';
    } else if (data.lastName.trim().length < 2) {
        errors.lastName = 'Last name must be at least 2 characters';
    }

    if (data.farmLocation && !isValidLocation(data.farmLocation)) {
        errors.farmLocation = 'Invalid farm location coordinates';
    }

    if (data.cropTypes.length === 0) {
        errors.cropTypes = 'Please select at least one crop type';
    } else if (!isValidCropTypes(data.cropTypes)) {
        errors.cropTypes = 'Invalid crop types selected';
    }

    if (!data.experienceLevel) {
        errors.experienceLevel = 'Please select your experience level';
    }

    if (!data.preferredLanguage) {
        errors.preferredLanguage = 'Please select your preferred language';
    }

    return errors;
};

/**
 * Checks if profile form has errors
 */
export const hasProfileErrors = (errors: ProfileValidationErrors): boolean => {
    return Object.keys(errors).length > 0;
};

/**
 * Formats points for display with commas
 */
export const formatPoints = (points: number): string => {
    return points.toLocaleString();
};

/**
 * Calculates points needed for next tier
 */
export const getPointsForNextTier = (currentPoints: number): number => {
    const tiers = [0, 100, 500, 1000, 2500, 5000, 10000];
    const nextTier = tiers.find(tier => tier > currentPoints);
    return nextTier ? nextTier - currentPoints : 0;
};

/**
 * Gets user tier based on points
 */
export const getUserTier = (points: number): string => {
    if (points >= 10000) return 'Master Farmer';
    if (points >= 5000) return 'Expert Farmer';
    if (points >= 2500) return 'Advanced Farmer';
    if (points >= 1000) return 'Experienced Farmer';
    if (points >= 500) return 'Skilled Farmer';
    if (points >= 100) return 'Novice Farmer';
    return 'Beginner Farmer';
};

/**
 * Sanitizes crop type input
 */
export const sanitizeCropType = (cropType: string): string => {
    return cropType.toLowerCase().trim().replace(/[^a-z0-9\s-]/g, '');
};

/**
 * Validates experience level
 */
export const isValidExperienceLevel = (
    level: string
): level is 'beginner' | 'intermediate' | 'expert' => {
    return ['beginner', 'intermediate', 'expert'].includes(level);
};

/**
 * Gets recommended crops based on experience level
 */
export const getRecommendedCrops = (
    experienceLevel: 'beginner' | 'intermediate' | 'expert',
    limit: number = 5
): CropType[] => {
    let crops: CropType[] = [];

    if (experienceLevel === 'beginner') {
        crops = COMMON_CROP_TYPES.filter(crop => crop.difficulty === 'beginner');
    } else if (experienceLevel === 'intermediate') {
        crops = COMMON_CROP_TYPES.filter(crop =>
            crop.difficulty === 'beginner' || crop.difficulty === 'intermediate'
        );
    } else {
        crops = COMMON_CROP_TYPES;
    }

    return crops.slice(0, limit);
};

/**
 * Formats subscription expiry date
 */
export const formatSubscriptionExpiry = (expiryDate: string | null): string => {
    if (!expiryDate) return 'No expiry';

    const date = new Date(expiryDate);
    const now = new Date();

    if (date < now) return 'Expired';

    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Expires tomorrow';
    if (diffDays <= 7) return `Expires in ${diffDays} days`;
    if (diffDays <= 30) return `Expires in ${Math.ceil(diffDays / 7)} weeks`;

    return date.toLocaleDateString();
};

/**
 * Checks if subscription is active
 */
export const isSubscriptionActive = (expiryDate: string | null): boolean => {
    if (!expiryDate) return false;
    return new Date(expiryDate) > new Date();
};

/**
 * Gets profile completion percentage
 */
export const getProfileCompletionPercentage = (profile: {
    farm_location?: string | null;
    crop_types?: string[] | null;
    experience_level?: string | null;
    preferred_language?: string;
    voice_enabled?: boolean;
}): number => {
    let completed = 0;
    const total = 5;

    if (profile.farm_location) completed++;
    if (profile.crop_types && profile.crop_types.length > 0) completed++;
    if (profile.experience_level) completed++;
    if (profile.preferred_language) completed++;
    if (profile.voice_enabled !== undefined) completed++;

    return Math.round((completed / total) * 100);
};

/**
 * Generates profile completion suggestions
 */
export const getProfileCompletionSuggestions = (profile: {
    farm_location?: string | null;
    crop_types?: string[] | null;
    experience_level?: string | null;
    preferred_language?: string;
    voice_enabled?: boolean;
}): string[] => {
    const suggestions: string[] = [];

    if (!profile.farm_location) {
        suggestions.push('Add your farm location for personalized recommendations');
    }

    if (!profile.crop_types || profile.crop_types.length === 0) {
        suggestions.push('Select your crop types to get relevant advice');
    }

    if (!profile.experience_level) {
        suggestions.push('Set your experience level for appropriate guidance');
    }

    if (!profile.preferred_language) {
        suggestions.push('Choose your preferred language');
    }

    if (profile.voice_enabled === undefined) {
        suggestions.push('Enable voice features for hands-free operation');
    }

    return suggestions;
};