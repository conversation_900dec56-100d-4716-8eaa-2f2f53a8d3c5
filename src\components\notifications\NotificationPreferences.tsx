import React, { useState } from 'react';
import {
    View,
    Text,
    Switch,
    ScrollView,
    Alert,
    TouchableOpacity,
} from 'react-native';
import { useNotificationStore, useNotificationPreferences, useNotificationActions } from '../../stores/notifications';
import { NotificationPreferences } from '../../types/notifications';

interface PreferenceItemProps {
    title: string;
    description: string;
    value: boolean;
    onValueChange: (value: boolean) => void;
    disabled?: boolean;
}

const PreferenceItem: React.FC<PreferenceItemProps> = ({
    title,
    description,
    value,
    onValueChange,
    disabled = false,
}) => (
    <View className="flex-row items-center justify-between py-4 px-4 bg-white rounded-lg mb-2">
        <View className="flex-1 mr-4">
            <Text className="text-base font-semibold text-gray-900">{title}</Text>
            <Text className="text-sm text-gray-600 mt-1">{description}</Text>
        </View>
        <Switch
            value={value}
            onValueChange={onValueChange}
            disabled={disabled}
            trackColor={{ false: '#e5e7eb', true: '#22c55e' }}
            thumbColor={value ? '#ffffff' : '#f3f4f6'}
        />
    </View>
);

interface TimePickerProps {
    title: string;
    value: string;
    onValueChange: (value: string) => void;
}

const TimePicker: React.FC<TimePickerProps> = ({ title, value, onValueChange }) => {
    const [showPicker, setShowPicker] = useState(false);

    const handleTimeSelect = () => {
        // This would open a time picker
        // For now, just show an alert
        Alert.alert(
            'Time Picker',
            'Time picker would open here',
            [{ text: 'OK', onPress: () => setShowPicker(false) }]
        );
    };

    return (
        <TouchableOpacity
            onPress={handleTimeSelect}
            className="flex-row items-center justify-between py-4 px-4 bg-white rounded-lg mb-2"
        >
            <Text className="text-base font-semibold text-gray-900">{title}</Text>
            <Text className="text-base text-green-600 font-medium">{value}</Text>
        </TouchableOpacity>
    );
};

export const NotificationPreferences: React.FC = () => {
    const preferences = useNotificationPreferences();
    const { updatePreferences, sendTestNotification } = useNotificationActions();
    const { isUpdatingPreferences } = useNotificationStore();

    const handlePreferenceChange = async (
        key: keyof NotificationPreferences,
        value: boolean | string
    ) => {
        try {
            await updatePreferences({ [key]: value });
        } catch (error) {
            Alert.alert(
                'Error',
                'Failed to update notification preferences. Please try again.',
                [{ text: 'OK' }]
            );
        }
    };

    const handleTestNotification = async () => {
        try {
            await sendTestNotification();
            Alert.alert(
                'Test Sent',
                'A test notification has been sent. You should receive it shortly.',
                [{ text: 'OK' }]
            );
        } catch (error) {
            Alert.alert(
                'Error',
                'Failed to send test notification. Please check your notification permissions.',
                [{ text: 'OK' }]
            );
        }
    };

    return (
        <ScrollView className="flex-1 bg-gray-50">
            <View className="p-4">
                {/* Header */}
                <View className="mb-6">
                    <Text className="text-2xl font-bold text-gray-900 mb-2">
                        Notification Settings
                    </Text>
                    <Text className="text-base text-gray-600">
                        Customize which notifications you receive and when
                    </Text>
                </View>

                {/* Agricultural Notifications */}
                <View className="mb-6">
                    <Text className="text-lg font-semibold text-gray-900 mb-3">
                        🌱 Agricultural Alerts
                    </Text>

                    <PreferenceItem
                        title="Weather Alerts"
                        description="Get notified about weather conditions that may affect your crops"
                        value={preferences.weatherAlerts}
                        onValueChange={(value) => handlePreferenceChange('weatherAlerts', value)}
                        disabled={isUpdatingPreferences}
                    />

                    <PreferenceItem
                        title="Task Reminders"
                        description="Receive reminders for daily farming tasks and activities"
                        value={preferences.taskReminders}
                        onValueChange={(value) => handlePreferenceChange('taskReminders', value)}
                        disabled={isUpdatingPreferences}
                    />

                    <PreferenceItem
                        title="Crop Stage Updates"
                        description="Get updates when your crops enter new growth stages"
                        value={preferences.cropStageUpdates}
                        onValueChange={(value) => handlePreferenceChange('cropStageUpdates', value)}
                        disabled={isUpdatingPreferences}
                    />

                    <PreferenceItem
                        title="AI Insights"
                        description="Receive AI-powered recommendations and insights"
                        value={preferences.aiInsights}
                        onValueChange={(value) => handlePreferenceChange('aiInsights', value)}
                        disabled={isUpdatingPreferences}
                    />
                </View>

                {/* Emergency Notifications */}
                <View className="mb-6">
                    <Text className="text-lg font-semibold text-gray-900 mb-3">
                        🚨 Emergency Alerts
                    </Text>

                    <PreferenceItem
                        title="Emergency Alerts"
                        description="Critical alerts for severe weather and emergency situations"
                        value={preferences.emergencyAlerts}
                        onValueChange={(value) => handlePreferenceChange('emergencyAlerts', value)}
                        disabled={isUpdatingPreferences}
                    />

                    <View className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-2">
                        <Text className="text-sm text-yellow-800">
                            ⚠️ Emergency alerts cannot be disabled for your safety
                        </Text>
                    </View>
                </View>

                {/* Community Notifications */}
                <View className="mb-6">
                    <Text className="text-lg font-semibold text-gray-900 mb-3">
                        👥 Community & Social
                    </Text>

                    <PreferenceItem
                        title="Community Updates"
                        description="Notifications about community posts and farmer interactions"
                        value={preferences.communityUpdates}
                        onValueChange={(value) => handlePreferenceChange('communityUpdates', value)}
                        disabled={isUpdatingPreferences}
                    />

                    <PreferenceItem
                        title="Marketing & Promotions"
                        description="Special offers, new features, and promotional content"
                        value={preferences.marketingPromotions}
                        onValueChange={(value) => handlePreferenceChange('marketingPromotions', value)}
                        disabled={isUpdatingPreferences}
                    />
                </View>

                {/* Sound & Vibration */}
                <View className="mb-6">
                    <Text className="text-lg font-semibold text-gray-900 mb-3">
                        🔊 Sound & Vibration
                    </Text>

                    <PreferenceItem
                        title="Sound Enabled"
                        description="Play notification sounds"
                        value={preferences.soundEnabled}
                        onValueChange={(value) => handlePreferenceChange('soundEnabled', value)}
                        disabled={isUpdatingPreferences}
                    />

                    <PreferenceItem
                        title="Vibration Enabled"
                        description="Vibrate device for notifications"
                        value={preferences.vibrationEnabled}
                        onValueChange={(value) => handlePreferenceChange('vibrationEnabled', value)}
                        disabled={isUpdatingPreferences}
                    />
                </View>

                {/* Quiet Hours */}
                <View className="mb-6">
                    <Text className="text-lg font-semibold text-gray-900 mb-3">
                        🌙 Quiet Hours
                    </Text>

                    <Text className="text-sm text-gray-600 mb-3">
                        Set times when you don't want to receive non-emergency notifications
                    </Text>

                    <TimePicker
                        title="Quiet Hours Start"
                        value={preferences.quietHoursStart}
                        onValueChange={(value) => handlePreferenceChange('quietHoursStart', value)}
                    />

                    <TimePicker
                        title="Quiet Hours End"
                        value={preferences.quietHoursEnd}
                        onValueChange={(value) => handlePreferenceChange('quietHoursEnd', value)}
                    />
                </View>

                {/* Test Notification */}
                <View className="mb-6">
                    <TouchableOpacity
                        onPress={handleTestNotification}
                        className="bg-green-600 rounded-lg py-4 px-6 items-center"
                        disabled={isUpdatingPreferences}
                    >
                        <Text className="text-white font-semibold text-base">
                            Send Test Notification
                        </Text>
                    </TouchableOpacity>
                </View>

                {/* Information */}
                <View className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <Text className="text-sm text-blue-800 mb-2 font-medium">
                        💡 About Notifications
                    </Text>
                    <Text className="text-sm text-blue-700">
                        • Emergency alerts will always be delivered regardless of your settings{'\n'}
                        • Quiet hours don't apply to critical weather warnings{'\n'}
                        • You can manage system notification permissions in your device settings
                    </Text>
                </View>
            </View>
        </ScrollView>
    );
};

export default NotificationPreferences;