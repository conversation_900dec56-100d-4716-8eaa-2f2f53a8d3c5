# AI Services Configuration
EXPO_PUBLIC_OPENAI_API_KEY=your_openai_api_key_here
EXPO_PUBLIC_GEMINI_API_KEY=your_gemini_api_key_here

# Production Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=https://idcjubbbooeawcsqxobg.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
EXPO_PUBLIC_SUPABASE_ROLE_KEY=your_supabase_service_role_key_here

# Weather API
EXPO_PUBLIC_OPENWEATHER_API_KEY=your_weather_api_key_here

# Cloudinary Configuration
EXPO_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
EXPO_PUBLIC_CLOUDINARY_API_KEY=your_cloudinary_api_key
EXPO_PUBLIC_CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# AdMob Configuration
EXPO_PUBLIC_ADMOB_APP_ID=your_admob_app_id
EXPO_PUBLIC_ADMOB_BANNER_ID=your_admob_banner_id
EXPO_PUBLIC_ADMOB_INTERSTITIAL_ID=your_admob_interstitial_id
EXPO_PUBLIC_ADMOB_REWARDED_ID=your_admob_rewarded_id

# Payment Gateways
EXPO_PUBLIC_FAWRY_MERCHANT_CODE=your_fawry_merchant_code
EXPO_PUBLIC_PAYMOB_API_KEY=your_paymob_api_key

# Production Settings
EXPO_PUBLIC_DEV_MODE=false
EXPO_PUBLIC_MOCK_SERVICES=false
EXPO_PUBLIC_APP_VERSION=1.0.0
EXPO_PUBLIC_APP_URL=https://your-app-domain.com
