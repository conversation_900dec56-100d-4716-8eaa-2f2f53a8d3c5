import { supabase } from './client';
import {
    SubscriptionPlan,
    UserSubscription,
    SubscriptionUsage,
    PaymentMethod,
    Invoice,
    FeatureAccess,
    SUBSCRIPTION_PLANS,
    getFeatureAccess,
    canAccessFeature,
    getAIConsultationLimit,
    getMonthlyPointsAllocation
} from '../../types/subscription';
import { Database } from '../../types/database';

type SubscriptionPlanRow = Database['public']['Tables']['subscription_plans']['Row'];
type UserSubscriptionRow = Database['public']['Tables']['user_subscriptions']['Row'];
type SubscriptionUsageRow = Database['public']['Tables']['subscription_usage']['Row'];

export class SubscriptionService {
    /**
     * Initialize subscription plans in database
     */
    static async initializeSubscriptionPlans(): Promise<{ error: Error | null }> {
        try {
            const { error } = await supabase
                .from('subscription_plans')
                .upsert(
                    SUBSCRIPTION_PLANS.map(plan => ({
                        ...plan,
                        id: plan.name.toLowerCase(),
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString(),
                    })),
                    { onConflict: 'name' }
                );

            return { error };
        } catch (error) {
            return { error: error as Error };
        }
    }

    /**
     * Get all available subscription plans
     */
    static async getSubscriptionPlans(): Promise<{ plans: SubscriptionPlan[]; error: Error | null }> {
        try {
            const { data, error } = await supabase
                .from('subscription_plans')
                .select('*')
                .eq('is_active', true)
                .order('sort_order');

            if (error) {
                throw error;
            }

            return { plans: data as SubscriptionPlan[], error: null };
        } catch (error) {
            return { plans: [], error: error as Error };
        }
    }

    /**
     * Get user's current subscription
     */
    static async getUserSubscription(userId: string): Promise<{ subscription: UserSubscription | null; error: Error | null }> {
        try {
            const { data, error } = await supabase
                .from('user_subscriptions')
                .select(`
                    *,
                    subscription_plans(*)
                `)
                .eq('user_id', userId)
                .eq('status', 'active')
                .single();

            if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
                throw error;
            }

            return { subscription: data as UserSubscription, error: null };
        } catch (error) {
            return { subscription: null, error: error as Error };
        }
    }

    /**
     * Create a new subscription for user
     */
    static async createSubscription(
        userId: string,
        planId: string,
        paymentMethodId?: string
    ): Promise<{ subscription: UserSubscription | null; error: Error | null }> {
        try {
            // Get the plan details
            const { data: plan, error: planError } = await supabase
                .from('subscription_plans')
                .select('*')
                .eq('id', planId)
                .single();

            if (planError) {
                throw planError;
            }

            // Calculate period dates
            const now = new Date();
            const periodEnd = new Date(now);
            if (plan.billing_period === 'monthly') {
                periodEnd.setMonth(periodEnd.getMonth() + 1);
            } else {
                periodEnd.setFullYear(periodEnd.getFullYear() + 1);
            }

            // Create subscription
            const { data: subscription, error: subscriptionError } = await supabase
                .from('user_subscriptions')
                .insert({
                    user_id: userId,
                    plan_id: planId,
                    status: 'active',
                    current_period_start: now.toISOString(),
                    current_period_end: periodEnd.toISOString(),
                    cancel_at_period_end: false,
                    payment_method_id: paymentMethodId,
                })
                .select()
                .single();

            if (subscriptionError) {
                throw subscriptionError;
            }

            // Update user profile with subscription tier
            await supabase
                .from('user_profiles')
                .update({
                    subscription_tier: plan.name.toLowerCase(),
                    subscription_expires_at: periodEnd.toISOString(),
                })
                .eq('id', userId);

            // Create initial usage record
            await this.createUsageRecord(userId, subscription.id, plan);

            // Award subscription bonus points
            if (plan.points_included > 0) {
                // This would integrate with the points service
                // await PointsService.awardPoints(userId, plan.points_included, 'subscription_bonus', `Monthly bonus for ${plan.name} subscription`);
            }

            return { subscription: subscription as UserSubscription, error: null };
        } catch (error) {
            return { subscription: null, error: error as Error };
        }
    }

    /**
     * Cancel user subscription
     */
    static async cancelSubscription(
        userId: string,
        cancelAtPeriodEnd: boolean = true
    ): Promise<{ error: Error | null }> {
        try {
            const updateData: any = {
                cancel_at_period_end: cancelAtPeriodEnd,
                updated_at: new Date().toISOString(),
            };

            if (!cancelAtPeriodEnd) {
                updateData.status = 'cancelled';
                updateData.cancelled_at = new Date().toISOString();
            }

            const { error } = await supabase
                .from('user_subscriptions')
                .update(updateData)
                .eq('user_id', userId)
                .eq('status', 'active');

            if (error) {
                throw error;
            }

            // If immediate cancellation, update user profile
            if (!cancelAtPeriodEnd) {
                await supabase
                    .from('user_profiles')
                    .update({
                        subscription_tier: 'free',
                        subscription_expires_at: null,
                    })
                    .eq('id', userId);
            }

            return { error: null };
        } catch (error) {
            return { error: error as Error };
        }
    }

    /**
     * Upgrade/downgrade subscription
     */
    static async changeSubscription(
        userId: string,
        newPlanId: string,
        paymentMethodId?: string
    ): Promise<{ subscription: UserSubscription | null; error: Error | null }> {
        try {
            // Cancel current subscription
            await this.cancelSubscription(userId, false);

            // Create new subscription
            return await this.createSubscription(userId, newPlanId, paymentMethodId);
        } catch (error) {
            return { subscription: null, error: error as Error };
        }
    }

    /**
     * Get subscription usage for current period
     */
    static async getSubscriptionUsage(userId: string): Promise<{ usage: SubscriptionUsage | null; error: Error | null }> {
        try {
            const { data: subscription } = await supabase
                .from('user_subscriptions')
                .select('id, current_period_start, current_period_end')
                .eq('user_id', userId)
                .eq('status', 'active')
                .single();

            if (!subscription) {
                return { usage: null, error: new Error('No active subscription found') };
            }

            const { data, error } = await supabase
                .from('subscription_usage')
                .select('*')
                .eq('user_id', userId)
                .eq('subscription_id', subscription.id)
                .gte('period_start', subscription.current_period_start)
                .lte('period_end', subscription.current_period_end)
                .single();

            if (error && error.code !== 'PGRST116') {
                throw error;
            }

            return { usage: data as SubscriptionUsage, error: null };
        } catch (error) {
            return { usage: null, error: error as Error };
        }
    }

    /**
     * Update subscription usage
     */
    static async updateUsage(
        userId: string,
        usageType: 'ai_consultations' | 'image_analyses' | 'storage' | 'api_calls',
        increment: number = 1
    ): Promise<{ error: Error | null }> {
        try {
            const { data: subscription } = await supabase
                .from('user_subscriptions')
                .select('id')
                .eq('user_id', userId)
                .eq('status', 'active')
                .single();

            if (!subscription) {
                return { error: new Error('No active subscription found') };
            }

            const updateField = `${usageType}_used`;

            const { error } = await supabase.rpc('increment_usage', {
                p_user_id: userId,
                p_subscription_id: subscription.id,
                p_field: updateField,
                p_increment: increment
            });

            return { error };
        } catch (error) {
            return { error: error as Error };
        }
    }

    /**
     * Check if user can access a feature
     */
    static async checkFeatureAccess(
        userId: string,
        feature: keyof FeatureAccess
    ): Promise<{ hasAccess: boolean; error: Error | null }> {
        try {
            const { data: profile } = await supabase
                .from('user_profiles')
                .select('subscription_tier')
                .eq('id', userId)
                .single();

            if (!profile) {
                return { hasAccess: false, error: new Error('User profile not found') };
            }

            const hasAccess = canAccessFeature(profile.subscription_tier, feature);
            return { hasAccess, error: null };
        } catch (error) {
            return { hasAccess: false, error: error as Error };
        }
    }

    /**
     * Check AI consultation limits
     */
    static async checkAIConsultationLimit(userId: string): Promise<{
        canUse: boolean;
        used: number;
        limit: number;
        error: Error | null
    }> {
        try {
            const { data: profile } = await supabase
                .from('user_profiles')
                .select('subscription_tier')
                .eq('id', userId)
                .single();

            if (!profile) {
                return { canUse: false, used: 0, limit: 0, error: new Error('User profile not found') };
            }

            const limit = getAIConsultationLimit(profile.subscription_tier);

            if (limit === -1) {
                // Unlimited
                return { canUse: true, used: 0, limit: -1, error: null };
            }

            const { usage } = await this.getSubscriptionUsage(userId);
            const used = usage?.ai_consultations_used || 0;

            return {
                canUse: used < limit,
                used,
                limit,
                error: null
            };
        } catch (error) {
            return { canUse: false, used: 0, limit: 0, error: error as Error };
        }
    }

    /**
     * Process subscription renewal
     */
    static async processRenewal(subscriptionId: string): Promise<{ error: Error | null }> {
        try {
            const { data: subscription, error: fetchError } = await supabase
                .from('user_subscriptions')
                .select(`
                    *,
                    subscription_plans(*)
                `)
                .eq('id', subscriptionId)
                .single();

            if (fetchError) {
                throw fetchError;
            }

            const plan = subscription.subscription_plans as SubscriptionPlan;
            const now = new Date();
            const newPeriodEnd = new Date(now);

            if (plan.billing_period === 'monthly') {
                newPeriodEnd.setMonth(newPeriodEnd.getMonth() + 1);
            } else {
                newPeriodEnd.setFullYear(newPeriodEnd.getFullYear() + 1);
            }

            // Update subscription period
            const { error: updateError } = await supabase
                .from('user_subscriptions')
                .update({
                    current_period_start: now.toISOString(),
                    current_period_end: newPeriodEnd.toISOString(),
                    updated_at: now.toISOString(),
                })
                .eq('id', subscriptionId);

            if (updateError) {
                throw updateError;
            }

            // Create new usage record
            await this.createUsageRecord(subscription.user_id, subscriptionId, plan);

            // Award subscription bonus points
            if (plan.points_included > 0) {
                // This would integrate with the points service
                // await PointsService.awardPoints(subscription.user_id, plan.points_included, 'subscription_bonus', `Monthly bonus for ${plan.name} subscription`);
            }

            return { error: null };
        } catch (error) {
            return { error: error as Error };
        }
    }

    /**
     * Handle subscription expiration
     */
    static async handleExpiredSubscriptions(): Promise<{ error: Error | null }> {
        try {
            const now = new Date().toISOString();

            // Find expired subscriptions
            const { data: expiredSubscriptions, error: fetchError } = await supabase
                .from('user_subscriptions')
                .select('*')
                .eq('status', 'active')
                .lt('current_period_end', now);

            if (fetchError) {
                throw fetchError;
            }

            // Update expired subscriptions
            for (const subscription of expiredSubscriptions || []) {
                await supabase
                    .from('user_subscriptions')
                    .update({
                        status: 'expired',
                        updated_at: now,
                    })
                    .eq('id', subscription.id);

                // Update user profile to free tier
                await supabase
                    .from('user_profiles')
                    .update({
                        subscription_tier: 'free',
                        subscription_expires_at: null,
                    })
                    .eq('id', subscription.user_id);
            }

            return { error: null };
        } catch (error) {
            return { error: error as Error };
        }
    }

    /**
     * Create usage record for new subscription period
     */
    private static async createUsageRecord(
        userId: string,
        subscriptionId: string,
        plan: SubscriptionPlan
    ): Promise<void> {
        const now = new Date();
        const periodEnd = new Date(now);

        if (plan.billing_period === 'monthly') {
            periodEnd.setMonth(periodEnd.getMonth() + 1);
        } else {
            periodEnd.setFullYear(periodEnd.getFullYear() + 1);
        }

        await supabase
            .from('subscription_usage')
            .insert({
                user_id: userId,
                subscription_id: subscriptionId,
                period_start: now.toISOString(),
                period_end: periodEnd.toISOString(),
                ai_consultations_used: 0,
                ai_consultations_limit: plan.ai_consultations_limit,
                image_analyses_used: 0,
                storage_used_mb: 0,
                api_calls_used: 0,
            });
    }

    /**
     * Get subscription analytics for admin
     */
    static async getSubscriptionAnalytics(): Promise<{
        analytics: {
            total_subscribers: number;
            active_subscriptions: number;
            revenue_this_month: number;
            churn_rate: number;
            plan_distribution: { [key: string]: number };
        } | null;
        error: Error | null;
    }> {
        try {
            // This would require more complex queries and potentially stored procedures
            // For now, return basic structure
            const analytics = {
                total_subscribers: 0,
                active_subscriptions: 0,
                revenue_this_month: 0,
                churn_rate: 0,
                plan_distribution: {},
            };

            return { analytics, error: null };
        } catch (error) {
            return { analytics: null, error: error as Error };
        }
    }
}