/**
 * React hook for accessibility features
 * Provides comprehensive accessibility support for agricultural users
 */

import { useState, useEffect, useCallback } from 'react';
import { AccessibilityManager, AccessibilityConfig, screenReaderUtils } from '../utils/accessibility';
import { VoiceService } from '../services/voice';

export interface UseAccessibilityReturn {
    config: AccessibilityConfig;
    isScreenReaderEnabled: boolean;
    isVoiceEnabled: boolean;
    isHighContrastEnabled: boolean;
    isLargeTextEnabled: boolean;
    fontScale: number;

    // Actions
    toggleVoiceMode: () => Promise<void>;
    toggleHighContrast: () => Promise<void>;
    toggleLargeText: () => Promise<void>;
    setFontScale: (scale: number) => Promise<void>;
    announceToScreenReader: (message: string, priority?: 'low' | 'high') => void;
    speakText: (text: string) => Promise<void>;

    // Utilities
    getAccessibilityProps: (label: string, hint?: string, role?: string) => object;
    getScaledFontSize: (baseSize: number) => number;
    getScaledTouchTarget: (baseSize: number) => number;
    getHighContrastColors: () => object | null;
}

export const useAccessibility = (): UseAccessibilityReturn => {
    const [config, setConfig] = useState<AccessibilityConfig>(() =>
        AccessibilityManager.getInstance().getConfig()
    );
    const [isScreenReaderEnabled, setIsScreenReaderEnabled] = useState(false);

    useEffect(() => {
        const accessibilityManager = AccessibilityManager.getInstance();

        // Initialize accessibility manager
        accessibilityManager.initialize();

        // Subscribe to config changes
        const unsubscribe = accessibilityManager.subscribe((newConfig) => {
            setConfig(newConfig);
            setIsScreenReaderEnabled(newConfig.screenReaderEnabled);
        });

        // Check initial screen reader status
        screenReaderUtils.isScreenReaderEnabled().then(setIsScreenReaderEnabled);

        return unsubscribe;
    }, []);

    const toggleVoiceMode = useCallback(async () => {
        const voiceService = VoiceService.getInstance();
        const newVoiceEnabled = !config.voiceEnabled;

        await AccessibilityManager.getInstance().updateConfig({
            voiceEnabled: newVoiceEnabled
        });

        if (newVoiceEnabled) {
            await voiceService.enableVoiceMode();
        } else {
            await voiceService.disableVoiceMode();
        }
    }, [config.voiceEnabled]);

    const toggleHighContrast = useCallback(async () => {
        const newHighContrast = !config.highContrast;
        await AccessibilityManager.getInstance().updateConfig({
            highContrast: newHighContrast
        });

        announceToScreenReader(
            newHighContrast ? 'High contrast mode enabled' : 'High contrast mode disabled'
        );
    }, [config.highContrast]);

    const toggleLargeText = useCallback(async () => {
        const newLargeText = !config.largeText;
        const newFontScale = newLargeText ? 1.3 : 1.0;

        await AccessibilityManager.getInstance().updateConfig({
            largeText: newLargeText,
            fontScale: newFontScale
        });

        announceToScreenReader(
            newLargeText ? 'Large text mode enabled' : 'Large text mode disabled'
        );
    }, [config.largeText]);

    const setFontScale = useCallback(async (scale: number) => {
        const clampedScale = Math.max(0.8, Math.min(2.0, scale));
        await AccessibilityManager.getInstance().updateConfig({
            fontScale: clampedScale,
            largeText: clampedScale > 1.0
        });

        announceToScreenReader(`Font size set to ${Math.round(clampedScale * 100)}%`);
    }, []);

    const announceToScreenReader = useCallback((message: string, priority: 'low' | 'high' = 'low') => {
        if (isScreenReaderEnabled) {
            screenReaderUtils.announce(message, priority);
        }
    }, [isScreenReaderEnabled]);

    const speakText = useCallback(async (text: string) => {
        if (config.voiceEnabled) {
            const voiceService = VoiceService.getInstance();
            await voiceService.speak(text);
        }
    }, [config.voiceEnabled]);

    const getAccessibilityProps = useCallback((label: string, hint?: string, role?: string) => {
        return {
            accessibilityLabel: label,
            accessibilityHint: hint,
            accessibilityRole: role as any,
            accessible: true,
            importantForAccessibility: 'yes' as const,
        };
    }, []);

    const getScaledFontSize = useCallback((baseSize: number) => {
        return AccessibilityManager.getInstance().getScaledFontSize(baseSize);
    }, [config.fontScale]);

    const getScaledTouchTarget = useCallback((baseSize: number) => {
        return AccessibilityManager.getInstance().getScaledTouchTarget(baseSize);
    }, [config.largeText]);

    const getHighContrastColors = useCallback(() => {
        return AccessibilityManager.getInstance().getHighContrastColors();
    }, [config.highContrast]);

    return {
        config,
        isScreenReaderEnabled,
        isVoiceEnabled: config.voiceEnabled,
        isHighContrastEnabled: config.highContrast,
        isLargeTextEnabled: config.largeText,
        fontScale: config.fontScale,

        // Actions
        toggleVoiceMode,
        toggleHighContrast,
        toggleLargeText,
        setFontScale,
        announceToScreenReader,
        speakText,

        // Utilities
        getAccessibilityProps,
        getScaledFontSize,
        getScaledTouchTarget,
        getHighContrastColors,
    };
};