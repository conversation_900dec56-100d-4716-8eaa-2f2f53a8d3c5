import { supabase } from './supabase/client';
import {
    NotificationAnalytics,
    NotificationType,
    NotificationData,
} from '../types/notifications';

export interface NotificationMetrics {
    totalSent: number;
    totalDelivered: number;
    totalOpened: number;
    deliveryRate: number;
    openRate: number;
    actionRate: number;
    byType: Record<NotificationType, {
        sent: number;
        delivered: number;
        opened: number;
        actionTaken: number;
    }>;
    byTimeOfDay: Record<string, number>;
    byDayOfWeek: Record<string, number>;
    deviceBreakdown: Record<string, number>;
}

export interface NotificationPerformance {
    bestPerformingTypes: Array<{
        type: NotificationType;
        openRate: number;
        actionRate: number;
    }>;
    optimalSendTimes: Array<{
        hour: number;
        openRate: number;
    }>;
    userEngagementScore: number;
    recommendations: string[];
}

export class NotificationAnalyticsService {
    private static instance: NotificationAnalyticsService;

    static getInstance(): NotificationAnalyticsService {
        if (!NotificationAnalyticsService.instance) {
            NotificationAnalyticsService.instance = new NotificationAnalyticsService();
        }
        return NotificationAnalyticsService.instance;
    }

    async trackNotificationSent(
        notificationId: string,
        userId: string,
        type: NotificationType,
        deviceInfo: {
            platform: string;
            version: string;
            model?: string;
        }
    ): Promise<void> {
        try {
            const analytics: NotificationAnalytics = {
                notificationId,
                userId,
                type,
                sentAt: new Date(),
                deviceInfo,
            };

            await supabase
                .from('notification_analytics')
                .insert(analytics);
        } catch (error) {
            console.error('Failed to track notification sent:', error);
        }
    }

    async trackNotificationDelivered(
        notificationId: string,
        deliveredAt: Date = new Date()
    ): Promise<void> {
        try {
            await supabase
                .from('notification_analytics')
                .update({ deliveredAt })
                .eq('notificationId', notificationId);
        } catch (error) {
            console.error('Failed to track notification delivered:', error);
        }
    }

    async trackNotificationOpened(
        notificationId: string,
        openedAt: Date = new Date(),
        actionTaken: boolean = false
    ): Promise<void> {
        try {
            await supabase
                .from('notification_analytics')
                .update({
                    openedAt,
                    actionTaken
                })
                .eq('notificationId', notificationId);
        } catch (error) {
            console.error('Failed to track notification opened:', error);
        }
    }

    async getNotificationMetrics(
        userId: string,
        startDate?: Date,
        endDate?: Date
    ): Promise<NotificationMetrics> {
        try {
            let query = supabase
                .from('notification_analytics')
                .select('*')
                .eq('userId', userId);

            if (startDate) {
                query = query.gte('sentAt', startDate.toISOString());
            }

            if (endDate) {
                query = query.lte('sentAt', endDate.toISOString());
            }

            const { data: analytics, error } = await query;

            if (error) throw error;

            return this.calculateMetrics(analytics || []);
        } catch (error) {
            console.error('Failed to get notification metrics:', error);
            return this.getEmptyMetrics();
        }
    }

    async getSystemWideMetrics(
        startDate?: Date,
        endDate?: Date
    ): Promise<NotificationMetrics> {
        try {
            let query = supabase
                .from('notification_analytics')
                .select('*');

            if (startDate) {
                query = query.gte('sentAt', startDate.toISOString());
            }

            if (endDate) {
                query = query.lte('sentAt', endDate.toISOString());
            }

            const { data: analytics, error } = await query;

            if (error) throw error;

            return this.calculateMetrics(analytics || []);
        } catch (error) {
            console.error('Failed to get system-wide metrics:', error);
            return this.getEmptyMetrics();
        }
    }

    async getNotificationPerformance(userId: string): Promise<NotificationPerformance> {
        try {
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            const metrics = await this.getNotificationMetrics(userId, thirtyDaysAgo);

            return this.analyzePerformance(metrics);
        } catch (error) {
            console.error('Failed to get notification performance:', error);
            return {
                bestPerformingTypes: [],
                optimalSendTimes: [],
                userEngagementScore: 0,
                recommendations: ['Unable to analyze performance at this time'],
            };
        }
    }

    async getDeliveryConfirmation(notificationId: string): Promise<{
        sent: boolean;
        delivered: boolean;
        opened: boolean;
        actionTaken: boolean;
        timestamps: {
            sentAt?: Date;
            deliveredAt?: Date;
            openedAt?: Date;
        };
    }> {
        try {
            const { data: analytics, error } = await supabase
                .from('notification_analytics')
                .select('*')
                .eq('notificationId', notificationId)
                .single();

            if (error || !analytics) {
                return {
                    sent: false,
                    delivered: false,
                    opened: false,
                    actionTaken: false,
                    timestamps: {},
                };
            }

            return {
                sent: !!analytics.sentAt,
                delivered: !!analytics.deliveredAt,
                opened: !!analytics.openedAt,
                actionTaken: !!analytics.actionTaken,
                timestamps: {
                    sentAt: analytics.sentAt ? new Date(analytics.sentAt) : undefined,
                    deliveredAt: analytics.deliveredAt ? new Date(analytics.deliveredAt) : undefined,
                    openedAt: analytics.openedAt ? new Date(analytics.openedAt) : undefined,
                },
            };
        } catch (error) {
            console.error('Failed to get delivery confirmation:', error);
            return {
                sent: false,
                delivered: false,
                opened: false,
                actionTaken: false,
                timestamps: {},
            };
        }
    }

    async getNotificationHistory(
        userId: string,
        limit: number = 50,
        offset: number = 0
    ): Promise<NotificationAnalytics[]> {
        try {
            const { data: analytics, error } = await supabase
                .from('notification_analytics')
                .select('*')
                .eq('userId', userId)
                .order('sentAt', { ascending: false })
                .range(offset, offset + limit - 1);

            if (error) throw error;

            return analytics || [];
        } catch (error) {
            console.error('Failed to get notification history:', error);
            return [];
        }
    }

    async generateAnalyticsReport(
        userId: string,
        startDate: Date,
        endDate: Date
    ): Promise<{
        summary: NotificationMetrics;
        performance: NotificationPerformance;
        trends: {
            dailyVolume: Array<{ date: string; count: number }>;
            typeDistribution: Array<{ type: NotificationType; percentage: number }>;
            engagementTrend: Array<{ date: string; openRate: number }>;
        };
    }> {
        try {
            const summary = await this.getNotificationMetrics(userId, startDate, endDate);
            const performance = await this.getNotificationPerformance(userId);
            const trends = await this.calculateTrends(userId, startDate, endDate);

            return {
                summary,
                performance,
                trends,
            };
        } catch (error) {
            console.error('Failed to generate analytics report:', error);
            throw error;
        }
    }

    private calculateMetrics(analytics: NotificationAnalytics[]): NotificationMetrics {
        const totalSent = analytics.length;
        const totalDelivered = analytics.filter(a => a.deliveredAt).length;
        const totalOpened = analytics.filter(a => a.openedAt).length;
        const totalActionTaken = analytics.filter(a => a.actionTaken).length;

        const deliveryRate = totalSent > 0 ? (totalDelivered / totalSent) * 100 : 0;
        const openRate = totalDelivered > 0 ? (totalOpened / totalDelivered) * 100 : 0;
        const actionRate = totalOpened > 0 ? (totalActionTaken / totalOpened) * 100 : 0;

        // Group by type
        const byType: Record<string, any> = {};
        analytics.forEach(a => {
            if (!byType[a.type]) {
                byType[a.type] = { sent: 0, delivered: 0, opened: 0, actionTaken: 0 };
            }
            byType[a.type].sent++;
            if (a.deliveredAt) byType[a.type].delivered++;
            if (a.openedAt) byType[a.type].opened++;
            if (a.actionTaken) byType[a.type].actionTaken++;
        });

        // Group by time of day
        const byTimeOfDay: Record<string, number> = {};
        analytics.forEach(a => {
            const hour = new Date(a.sentAt).getHours();
            const timeSlot = `${hour}:00`;
            byTimeOfDay[timeSlot] = (byTimeOfDay[timeSlot] || 0) + 1;
        });

        // Group by day of week
        const byDayOfWeek: Record<string, number> = {};
        const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        analytics.forEach(a => {
            const dayName = dayNames[new Date(a.sentAt).getDay()];
            byDayOfWeek[dayName] = (byDayOfWeek[dayName] || 0) + 1;
        });

        // Group by device
        const deviceBreakdown: Record<string, number> = {};
        analytics.forEach(a => {
            const platform = a.deviceInfo.platform;
            deviceBreakdown[platform] = (deviceBreakdown[platform] || 0) + 1;
        });

        return {
            totalSent,
            totalDelivered,
            totalOpened,
            deliveryRate,
            openRate,
            actionRate,
            byType,
            byTimeOfDay,
            byDayOfWeek,
            deviceBreakdown,
        };
    }

    private analyzePerformance(metrics: NotificationMetrics): NotificationPerformance {
        // Find best performing notification types
        const bestPerformingTypes = Object.entries(metrics.byType)
            .map(([type, stats]) => ({
                type: type as NotificationType,
                openRate: stats.delivered > 0 ? (stats.opened / stats.delivered) * 100 : 0,
                actionRate: stats.opened > 0 ? (stats.actionTaken / stats.opened) * 100 : 0,
            }))
            .sort((a, b) => b.openRate - a.openRate)
            .slice(0, 5);

        // Find optimal send times
        const optimalSendTimes = Object.entries(metrics.byTimeOfDay)
            .map(([hour, count]) => {
                const hourNum = parseInt(hour.split(':')[0]);
                // This is simplified - in reality, you'd calculate open rates for each hour
                return {
                    hour: hourNum,
                    openRate: metrics.openRate, // Placeholder
                };
            })
            .sort((a, b) => b.openRate - a.openRate)
            .slice(0, 3);

        // Calculate user engagement score (0-100)
        const userEngagementScore = Math.round(
            (metrics.openRate * 0.6) + (metrics.actionRate * 0.4)
        );

        // Generate recommendations
        const recommendations: string[] = [];

        if (metrics.deliveryRate < 90) {
            recommendations.push('Consider checking notification permissions and push token validity');
        }

        if (metrics.openRate < 20) {
            recommendations.push('Try improving notification titles and timing');
        }

        if (metrics.actionRate < 10) {
            recommendations.push('Include clearer call-to-action in notification content');
        }

        if (bestPerformingTypes.length > 0) {
            recommendations.push(`Focus on ${bestPerformingTypes[0].type} notifications - they have the highest engagement`);
        }

        return {
            bestPerformingTypes,
            optimalSendTimes,
            userEngagementScore,
            recommendations,
        };
    }

    private async calculateTrends(
        userId: string,
        startDate: Date,
        endDate: Date
    ): Promise<{
        dailyVolume: Array<{ date: string; count: number }>;
        typeDistribution: Array<{ type: NotificationType; percentage: number }>;
        engagementTrend: Array<{ date: string; openRate: number }>;
    }> {
        try {
            const { data: analytics, error } = await supabase
                .from('notification_analytics')
                .select('*')
                .eq('userId', userId)
                .gte('sentAt', startDate.toISOString())
                .lte('sentAt', endDate.toISOString());

            if (error || !analytics) {
                return {
                    dailyVolume: [],
                    typeDistribution: [],
                    engagementTrend: [],
                };
            }

            // Calculate daily volume
            const dailyVolume: Record<string, number> = {};
            analytics.forEach(a => {
                const date = new Date(a.sentAt).toISOString().split('T')[0];
                dailyVolume[date] = (dailyVolume[date] || 0) + 1;
            });

            // Calculate type distribution
            const typeCount: Record<string, number> = {};
            analytics.forEach(a => {
                typeCount[a.type] = (typeCount[a.type] || 0) + 1;
            });

            const totalNotifications = analytics.length;
            const typeDistribution = Object.entries(typeCount).map(([type, count]) => ({
                type: type as NotificationType,
                percentage: (count / totalNotifications) * 100,
            }));

            // Calculate engagement trend (simplified)
            const engagementTrend = Object.entries(dailyVolume).map(([date, count]) => {
                const dayAnalytics = analytics.filter(a =>
                    new Date(a.sentAt).toISOString().split('T')[0] === date
                );
                const opened = dayAnalytics.filter(a => a.openedAt).length;
                const openRate = count > 0 ? (opened / count) * 100 : 0;

                return { date, openRate };
            });

            return {
                dailyVolume: Object.entries(dailyVolume).map(([date, count]) => ({ date, count })),
                typeDistribution,
                engagementTrend,
            };
        } catch (error) {
            console.error('Failed to calculate trends:', error);
            return {
                dailyVolume: [],
                typeDistribution: [],
                engagementTrend: [],
            };
        }
    }

    private getEmptyMetrics(): NotificationMetrics {
        return {
            totalSent: 0,
            totalDelivered: 0,
            totalOpened: 0,
            deliveryRate: 0,
            openRate: 0,
            actionRate: 0,
            byType: {} as any,
            byTimeOfDay: {},
            byDayOfWeek: {},
            deviceBreakdown: {},
        };
    }
}

export const notificationAnalytics = NotificationAnalyticsService.getInstance();
export default notificationAnalytics;