import { supabase } from './supabase/client';
import notificationService from './notifications';
import NotificationTemplateService from './notificationTemplates';
import {
    ScheduledNotification,
    NotificationType,
    NotificationData,
} from '../types/notifications';

export class NotificationScheduler {
    private static instance: NotificationScheduler;
    private scheduledJobs: Map<string, NodeJS.Timeout> = new Map();

    static getInstance(): NotificationScheduler {
        if (!NotificationScheduler.instance) {
            NotificationScheduler.instance = new NotificationScheduler();
        }
        return NotificationScheduler.instance;
    }

    async initialize(): Promise<void> {
        try {
            // Load existing scheduled notifications from database
            await this.loadScheduledNotifications();

            // Set up recurring schedules
            await this.setupRecurringSchedules();

            console.log('Notification scheduler initialized');
        } catch (error) {
            console.error('Failed to initialize notification scheduler:', error);
        }
    }

    private async loadScheduledNotifications(): Promise<void> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return;

            const { data: scheduledNotifications } = await supabase
                .from('scheduled_notifications')
                .select('*')
                .eq('user_id', user.id)
                .gte('scheduled_for', new Date().toISOString());

            if (scheduledNotifications) {
                for (const notification of scheduledNotifications) {
                    await this.scheduleNotificationFromData(notification);
                }
            }
        } catch (error) {
            console.error('Failed to load scheduled notifications:', error);
        }
    }

    private async setupRecurringSchedules(): Promise<void> {
        // Schedule daily task reminders
        this.scheduleRecurringNotification('daily_task_check', {
            hour: 8,
            minute: 0,
        }, this.checkDailyTasks.bind(this));

        // Schedule weather alerts check
        this.scheduleRecurringNotification('weather_check', {
            hour: 6,
            minute: 0,
        }, this.checkWeatherAlerts.bind(this));

        // Schedule crop stage updates check
        this.scheduleRecurringNotification('crop_stage_check', {
            hour: 9,
            minute: 0,
        }, this.checkCropStageUpdates.bind(this));

        // Schedule subscription reminders
        this.scheduleRecurringNotification('subscription_check', {
            hour: 10,
            minute: 0,
        }, this.checkSubscriptionReminders.bind(this));
    }

    private scheduleRecurringNotification(
        id: string,
        time: { hour: number; minute: number },
        callback: () => Promise<void>
    ): void {
        const now = new Date();
        const scheduledTime = new Date();
        scheduledTime.setHours(time.hour, time.minute, 0, 0);

        // If the time has passed today, schedule for tomorrow
        if (scheduledTime <= now) {
            scheduledTime.setDate(scheduledTime.getDate() + 1);
        }

        const timeUntilExecution = scheduledTime.getTime() - now.getTime();

        const timeout = setTimeout(async () => {
            try {
                await callback();
                // Reschedule for next day
                this.scheduleRecurringNotification(id, time, callback);
            } catch (error) {
                console.error(`Error in recurring notification ${id}:`, error);
                // Reschedule even if there was an error
                this.scheduleRecurringNotification(id, time, callback);
            }
        }, timeUntilExecution);

        // Clear existing timeout if any
        if (this.scheduledJobs.has(id)) {
            clearTimeout(this.scheduledJobs.get(id)!);
        }

        this.scheduledJobs.set(id, timeout);
    }

    private async checkDailyTasks(): Promise<void> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return;

            // Get today's incomplete tasks
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);

            const { data: tasks } = await supabase
                .from('tasks')
                .select(`
          *,
          crop_plans (
            crop_type,
            user_id
          )
        `)
                .eq('crop_plans.user_id', user.id)
                .eq('completed', false)
                .gte('due_date', today.toISOString())
                .lt('due_date', tomorrow.toISOString());

            if (tasks && tasks.length > 0) {
                // Group tasks by urgency
                const urgentTasks = tasks.filter(task => {
                    const dueDate = new Date(task.due_date);
                    const hoursUntilDue = (dueDate.getTime() - Date.now()) / (1000 * 60 * 60);
                    return hoursUntilDue <= 2; // Tasks due within 2 hours
                });

                const regularTasks = tasks.filter(task => {
                    const dueDate = new Date(task.due_date);
                    const hoursUntilDue = (dueDate.getTime() - Date.now()) / (1000 * 60 * 60);
                    return hoursUntilDue > 2;
                });

                // Send urgent task notifications immediately
                for (const task of urgentTasks) {
                    const notification = NotificationTemplateService.createTaskReminderNotification(
                        task.title,
                        'in less than 2 hours',
                        task.id,
                        task.crop_plan_id
                    );

                    await notificationService.sendLocalNotification(
                        notification.title,
                        notification.body,
                        notification.data,
                        { priority: 'high' }
                    );
                }

                // Schedule regular task reminders for 1 hour before due time
                for (const task of regularTasks) {
                    const dueDate = new Date(task.due_date);
                    const reminderTime = new Date(dueDate.getTime() - (60 * 60 * 1000)); // 1 hour before

                    if (reminderTime > new Date()) {
                        const notification = NotificationTemplateService.createTaskReminderNotification(
                            task.title,
                            dueDate.toLocaleTimeString(),
                            task.id,
                            task.crop_plan_id
                        );

                        await notificationService.scheduleNotification({
                            id: `task_reminder_${task.id}`,
                            identifier: '',
                            content: {
                                title: notification.title,
                                body: notification.body,
                                data: notification.data,
                            },
                            trigger: {
                                type: 'date',
                                date: reminderTime,
                            },
                        });
                    }
                }
            }
        } catch (error) {
            console.error('Failed to check daily tasks:', error);
        }
    }

    private async checkWeatherAlerts(): Promise<void> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return;

            // Get user's farm locations
            const { data: profile } = await supabase
                .from('user_profiles')
                .select('farm_location')
                .eq('id', user.id)
                .single();

            if (!profile?.farm_location) return;

            // Check for weather alerts (this would integrate with weather service)
            const weatherAlerts = await this.fetchWeatherAlerts(profile.farm_location);

            for (const alert of weatherAlerts) {
                const notification = NotificationTemplateService.createWeatherAlertNotification(alert);

                await notificationService.sendLocalNotification(
                    notification.title,
                    notification.body,
                    notification.data,
                    {
                        priority: notification.priority,
                        sound: notification.sound,
                    }
                );

                // Store alert in database for history
                await supabase
                    .from('weather_alerts')
                    .insert({
                        user_id: user.id,
                        alert_data: alert,
                        sent_at: new Date().toISOString(),
                    });
            }
        } catch (error) {
            console.error('Failed to check weather alerts:', error);
        }
    }

    private async checkCropStageUpdates(): Promise<void> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return;

            // Get active crop plans
            const { data: cropPlans } = await supabase
                .from('crop_plans')
                .select('*')
                .eq('user_id', user.id)
                .eq('status', 'active');

            if (!cropPlans) return;

            for (const plan of cropPlans) {
                const stageUpdate = await this.calculateCropStageUpdate(plan);

                if (stageUpdate) {
                    const notification = NotificationTemplateService.createCropStageNotification(stageUpdate);

                    await notificationService.sendLocalNotification(
                        notification.title,
                        notification.body,
                        notification.data,
                        { priority: notification.priority }
                    );
                }
            }
        } catch (error) {
            console.error('Failed to check crop stage updates:', error);
        }
    }

    private async checkSubscriptionReminders(): Promise<void> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return;

            const { data: profile } = await supabase
                .from('user_profiles')
                .select('subscription_tier, subscription_expires_at')
                .eq('id', user.id)
                .single();

            if (!profile?.subscription_expires_at || profile.subscription_tier === 'free') return;

            const expirationDate = new Date(profile.subscription_expires_at);
            const daysUntilExpiration = Math.ceil((expirationDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24));

            // Send reminders at 7, 3, and 1 day before expiration
            if ([7, 3, 1].includes(daysUntilExpiration)) {
                const notification = NotificationTemplateService.createSubscriptionReminderNotification(
                    profile.subscription_tier,
                    'expires',
                    daysUntilExpiration
                );

                await notificationService.sendLocalNotification(
                    notification.title,
                    notification.body,
                    notification.data,
                    { priority: notification.priority }
                );
            }
        } catch (error) {
            console.error('Failed to check subscription reminders:', error);
        }
    }

    async scheduleTaskReminder(
        taskId: string,
        taskTitle: string,
        dueDate: Date,
        cropPlanId?: string
    ): Promise<void> {
        try {
            // Schedule reminder 1 hour before due date
            const reminderTime = new Date(dueDate.getTime() - (60 * 60 * 1000));

            if (reminderTime <= new Date()) {
                // If reminder time has passed, don't schedule
                return;
            }

            const notification = NotificationTemplateService.createTaskReminderNotification(
                taskTitle,
                dueDate.toLocaleTimeString(),
                taskId,
                cropPlanId
            );

            await notificationService.scheduleNotification({
                id: `task_reminder_${taskId}`,
                identifier: '',
                content: {
                    title: notification.title,
                    body: notification.body,
                    data: notification.data,
                },
                trigger: {
                    type: 'date',
                    date: reminderTime,
                },
            });
        } catch (error) {
            console.error('Failed to schedule task reminder:', error);
        }
    }

    async scheduleWeatherAlert(alert: any): Promise<void> {
        try {
            const notification = NotificationTemplateService.createWeatherAlertNotification(alert);

            await notificationService.sendLocalNotification(
                notification.title,
                notification.body,
                notification.data,
                {
                    priority: notification.priority,
                    sound: notification.sound,
                }
            );
        } catch (error) {
            console.error('Failed to schedule weather alert:', error);
        }
    }

    async scheduleAIInsightNotification(insight: any): Promise<void> {
        try {
            const notification = NotificationTemplateService.createAIInsightNotification(insight);

            await notificationService.sendLocalNotification(
                notification.title,
                notification.body,
                notification.data,
                { priority: notification.priority }
            );
        } catch (error) {
            console.error('Failed to schedule AI insight notification:', error);
        }
    }

    async cancelTaskReminder(taskId: string): Promise<void> {
        try {
            const identifier = `task_reminder_${taskId}`;
            await notificationService.cancelNotification(identifier);
        } catch (error) {
            console.error('Failed to cancel task reminder:', error);
        }
    }

    private async scheduleNotificationFromData(notificationData: any): Promise<void> {
        try {
            const scheduledFor = new Date(notificationData.scheduled_for);

            if (scheduledFor <= new Date()) {
                // Skip past notifications
                return;
            }

            await notificationService.scheduleNotification({
                id: notificationData.id,
                identifier: notificationData.identifier,
                content: notificationData.notification_data.content,
                trigger: {
                    type: 'date',
                    date: scheduledFor,
                },
            });
        } catch (error) {
            console.error('Failed to schedule notification from data:', error);
        }
    }

    private async fetchWeatherAlerts(location: any): Promise<any[]> {
        // This would integrate with the weather service to fetch actual alerts
        // For now, return empty array as placeholder
        return [];
    }

    private async calculateCropStageUpdate(cropPlan: any): Promise<any | null> {
        // This would calculate if a crop is transitioning to a new growth stage
        // Based on planting date, crop type, and current date
        // For now, return null as placeholder
        return null;
    }

    cleanup(): void {
        // Clear all scheduled jobs
        this.scheduledJobs.forEach((timeout) => {
            clearTimeout(timeout);
        });
        this.scheduledJobs.clear();
    }
}

export const notificationScheduler = NotificationScheduler.getInstance();
export default notificationScheduler;