#!/usr/bin/env node

/**
 * Production Database Verification Script
 * This script verifies the production database setup
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.EXPO_PUBLIC_SUPABASE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

console.log('🔧 Production Database Verification');
console.log(`📍 URL: ${SUPABASE_URL}`);

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// Expected tables for the farming app
const expectedTables = [
  'users',
  'user_profiles',
  'crop_plans',
  'tasks',
  'chat_sessions',
  'chat_messages',
  'community_posts',
  'post_comments',
  'products',
  'orders',
  'order_items',
  'points_transactions',
  'points_balances',
  'achievements',
  'user_achievements',
  'subscription_plans',
  'user_subscriptions',
  'notifications',
  'push_tokens',
];

async function checkTable(tableName) {
  try {
    const { data, error } = await supabase.from(tableName).select('*').limit(1);

    if (error) {
      if (error.message.includes('does not exist')) {
        return { exists: false, error: 'Table does not exist' };
      } else if (error.message.includes('permission denied') || error.message.includes('policy')) {
        return { exists: true, error: 'RLS policy blocking (expected)' };
      } else {
        return { exists: false, error: error.message };
      }
    }

    return { exists: true, error: null };
  } catch (error) {
    return { exists: false, error: error.message };
  }
}

async function testAuthConnection() {
  try {
    // Test auth connection by checking if we can access auth users
    const { data, error } = await supabase.auth.admin.listUsers();

    if (error) {
      console.log('⚠️  Auth admin access limited (expected for security)');
      return true; // This is actually expected in production
    }

    console.log(`✅ Auth system accessible (${data.users?.length || 0} users)`);
    return true;
  } catch (error) {
    console.log('⚠️  Auth system check failed (may be expected)');
    return true; // Don't fail on auth check
  }
}

async function testStorageConnection() {
  try {
    const { data, error } = await supabase.storage.listBuckets();

    if (error) {
      console.log('⚠️  Storage access limited:', error.message);
      return false;
    }

    console.log(`✅ Storage accessible (${data?.length || 0} buckets)`);
    return true;
  } catch (error) {
    console.log('⚠️  Storage check failed:', error.message);
    return false;
  }
}

async function main() {
  console.log('\n🚀 Starting production database verification...\n');

  // Test auth connection
  console.log('🔐 Testing Auth connection...');
  await testAuthConnection();

  // Test storage connection
  console.log('\n📦 Testing Storage connection...');
  await testStorageConnection();

  // Check all expected tables
  console.log('\n📋 Checking database tables...');

  let existingTables = 0;
  let missingTables = 0;
  let protectedTables = 0;

  for (const tableName of expectedTables) {
    const result = await checkTable(tableName);

    if (result.exists) {
      if (result.error && result.error.includes('policy')) {
        console.log(`🔒 ${tableName} - Protected by RLS (good)`);
        protectedTables++;
      } else {
        console.log(`✅ ${tableName} - Accessible`);
      }
      existingTables++;
    } else {
      console.log(`❌ ${tableName} - Missing: ${result.error}`);
      missingTables++;
    }
  }

  // Summary
  console.log('\n📊 Verification Summary:');
  console.log(`   ✅ Existing tables: ${existingTables}`);
  console.log(`   🔒 RLS protected: ${protectedTables}`);
  console.log(`   ❌ Missing tables: ${missingTables}`);
  console.log(`   📈 Coverage: ${Math.round((existingTables / expectedTables.length) * 100)}%`);

  // Test basic operations
  console.log('\n🧪 Testing basic operations...');

  // Test reading from achievements (should be public)
  try {
    const { data, error } = await supabase.from('achievements').select('*').limit(3);

    if (!error && data) {
      console.log(`✅ Public table read works (${data.length} achievements found)`);
    } else {
      console.log('⚠️  Public table read limited');
    }
  } catch (error) {
    console.log('⚠️  Public table read failed');
  }

  // Test subscription plans (should be public)
  try {
    const { data, error } = await supabase
      .from('subscription_plans')
      .select('name, price')
      .limit(3);

    if (!error && data) {
      console.log(`✅ Subscription plans accessible (${data.length} plans found)`);
      data.forEach((plan) => {
        console.log(`   - ${plan.name}: $${plan.price}`);
      });
    } else {
      console.log('⚠️  Subscription plans not accessible');
    }
  } catch (error) {
    console.log('⚠️  Subscription plans check failed');
  }

  // Final assessment
  const isHealthy = existingTables >= expectedTables.length * 0.8; // 80% of tables exist

  if (isHealthy) {
    console.log('\n🎉 Database verification completed successfully!');
    console.log('✅ Production database is ready for use.');

    if (missingTables > 0) {
      console.log(
        `⚠️  Note: ${missingTables} tables are missing but core functionality should work.`
      );
    }
  } else {
    console.log('\n⚠️  Database verification found significant issues.');
    console.log('❌ Please review the missing tables above.');
  }

  // Connection info
  console.log('\n📡 Connection Information:');
  console.log(`   🌐 Supabase URL: ${SUPABASE_URL}`);
  console.log(`   🔑 Service Role: ${SUPABASE_SERVICE_ROLE_KEY ? 'Configured' : 'Missing'}`);

  process.exit(isHealthy ? 0 : 1);
}

// Run the verification
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Verification failed:', error);
    process.exit(1);
  });
}
