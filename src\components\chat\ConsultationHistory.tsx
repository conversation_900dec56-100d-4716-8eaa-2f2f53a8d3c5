import React, { useState } from 'react';
import { View, Text, Pressable, ScrollView, FlatList } from 'react-native';

interface HistoryItem {
    id: string;
    category: string;
    question: string;
    summary: string;
    timestamp: Date;
    hasImage: boolean;
    resolved: boolean;
}

interface ConsultationHistoryProps {
    category: string;
    onHistoryItemSelect: (item: HistoryItem) => void;
    voiceEnabled?: boolean;
    onVoiceFeedback?: (text: string) => void;
}

// Mock data for demonstration
const mockHistory: HistoryItem[] = [
    {
        id: '1',
        category: 'plant-health',
        question: 'Yellow leaves on tomato plants',
        summary: 'Diagnosed as nitrogen deficiency. Recommended organic fertilizer application.',
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        hasImage: true,
        resolved: true,
    },
    {
        id: '2',
        category: 'plant-health',
        question: 'Brown spots on cucumber leaves',
        summary: 'Identified as bacterial leaf spot. Suggested copper-based fungicide treatment.',
        timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
        hasImage: true,
        resolved: false,
    },
    {
        id: '3',
        category: 'soil',
        question: 'Soil pH testing methods',
        summary: 'Explained digital pH meter usage and soil sample collection techniques.',
        timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
        hasImage: false,
        resolved: true,
    },
    {
        id: '4',
        category: 'fertilizer',
        question: 'Best fertilizer for corn growth',
        summary: 'Recommended 10-10-10 NPK fertilizer with application schedule.',
        timestamp: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
        hasImage: false,
        resolved: true,
    },
    {
        id: '5',
        category: 'pests',
        question: 'Aphid infestation on peppers',
        summary: 'Suggested neem oil treatment and beneficial insect introduction.',
        timestamp: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), // 2 weeks ago
        hasImage: true,
        resolved: true,
    },
];

export const ConsultationHistory: React.FC<ConsultationHistoryProps> = ({
    category,
    onHistoryItemSelect,
    voiceEnabled = false,
    onVoiceFeedback,
}) => {
    const [selectedFilter, setSelectedFilter] = useState<'all' | 'resolved' | 'pending'>('all');

    const categoryHistory = mockHistory.filter(item => item.category === category);

    const filteredHistory = categoryHistory.filter(item => {
        if (selectedFilter === 'resolved') return item.resolved;
        if (selectedFilter === 'pending') return !item.resolved;
        return true;
    });

    const getCategoryInfo = (categoryId: string) => {
        const categoryMap: Record<string, { name: string; icon: string; color: string }> = {
            'plant-health': { name: 'Plant Health', icon: '🌱', color: 'text-green-600' },
            'soil': { name: 'Soil Analysis', icon: '🌾', color: 'text-amber-600' },
            'fertilizer': { name: 'Fertilizer Help', icon: '💧', color: 'text-blue-600' },
            'pests': { name: 'Pest Control', icon: '🐛', color: 'text-red-600' },
        };
        return categoryMap[categoryId] || { name: 'General', icon: '💬', color: 'text-earth-600' };
    };

    const formatRelativeTime = (date: Date) => {
        const now = new Date();
        const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

        if (diffInHours < 24) {
            return `${diffInHours}h ago`;
        } else {
            const diffInDays = Math.floor(diffInHours / 24);
            return `${diffInDays}d ago`;
        }
    };

    const handleItemPress = (item: HistoryItem) => {
        if (voiceEnabled && onVoiceFeedback) {
            onVoiceFeedback(`Selected consultation: ${item.question}`);
        }
        onHistoryItemSelect(item);
    };

    const handleFilterPress = (filter: 'all' | 'resolved' | 'pending') => {
        setSelectedFilter(filter);
        if (voiceEnabled && onVoiceFeedback) {
            onVoiceFeedback(`Filtered history to show ${filter} consultations`);
        }
    };

    const categoryInfo = getCategoryInfo(category);

    if (categoryHistory.length === 0) {
        return (
            <View className="flex-1 items-center justify-center p-8">
                <Text className="text-4xl mb-4">{categoryInfo.icon}</Text>
                <Text className="text-lg font-semibold text-earth-900 mb-2 text-center">
                    No {categoryInfo.name} History
                </Text>
                <Text className="text-earth-600 text-center">
                    Start a conversation to see your consultation history here
                </Text>
            </View>
        );
    }

    const renderHistoryItem = ({ item }: { item: HistoryItem }) => (
        <Pressable
            onPress={() => handleItemPress(item)}
            className="bg-white border border-earth-200 rounded-2xl p-4 mb-3 mx-4 shadow-sm active:opacity-80"
            accessibilityRole="button"
            accessibilityLabel={`Consultation: ${item.question}`}
            accessibilityHint="Tap to view full conversation">

            <View className="flex-row items-start justify-between mb-2">
                <View className="flex-1 mr-3">
                    <Text className="text-base font-semibold text-earth-900 mb-1" numberOfLines={2}>
                        {item.question}
                    </Text>

                    <Text className="text-sm text-earth-600 leading-5" numberOfLines={2}>
                        {item.summary}
                    </Text>
                </View>

                <View className="items-end">
                    <View className={`
            px-2 py-1 rounded-full mb-1
            ${item.resolved ? 'bg-green-100' : 'bg-yellow-100'}
          `}>
                        <Text className={`text-xs font-medium ${item.resolved ? 'text-green-700' : 'text-yellow-700'
                            }`}>
                            {item.resolved ? '✓ Resolved' : '⏳ Pending'}
                        </Text>
                    </View>

                    <Text className="text-xs text-earth-500">
                        {formatRelativeTime(item.timestamp)}
                    </Text>
                </View>
            </View>

            <View className="flex-row items-center justify-between">
                <View className="flex-row items-center">
                    {item.hasImage && (
                        <View className="flex-row items-center mr-3">
                            <Text className="text-sm text-earth-500 mr-1">📷</Text>
                            <Text className="text-xs text-earth-500">Image</Text>
                        </View>
                    )}

                    <Text className="text-xs text-earth-500">
                        {categoryInfo.name}
                    </Text>
                </View>

                <Text className="text-earth-400 text-lg">›</Text>
            </View>
        </Pressable>
    );

    return (
        <View className="flex-1 bg-earth-50">
            {/* Header */}
            <View className="bg-white border-b border-earth-200 px-4 py-3">
                <View className="flex-row items-center mb-3">
                    <Text className="text-2xl mr-3">{categoryInfo.icon}</Text>
                    <View className="flex-1">
                        <Text className="text-lg font-semibold text-earth-900">
                            {categoryInfo.name} History
                        </Text>
                        <Text className="text-sm text-earth-600">
                            {categoryHistory.length} consultation{categoryHistory.length !== 1 ? 's' : ''}
                        </Text>
                    </View>
                </View>

                {/* Filter Buttons */}
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                    <View className="flex-row gap-2">
                        {(['all', 'resolved', 'pending'] as const).map((filter) => (
                            <Pressable
                                key={filter}
                                onPress={() => handleFilterPress(filter)}
                                className={`
                  px-4 py-2 rounded-full border
                  ${selectedFilter === filter
                                        ? 'bg-primary-600 border-primary-600'
                                        : 'bg-white border-earth-200'
                                    }
                `}
                                accessibilityRole="button"
                                accessibilityLabel={`Filter by ${filter}`}
                                accessibilityState={{ selected: selectedFilter === filter }}>
                                <Text className={`text-sm font-medium ${selectedFilter === filter ? 'text-white' : 'text-earth-700'
                                    }`}>
                                    {filter.charAt(0).toUpperCase() + filter.slice(1)}
                                    {filter === 'all' && ` (${categoryHistory.length})`}
                                    {filter === 'resolved' && ` (${categoryHistory.filter(i => i.resolved).length})`}
                                    {filter === 'pending' && ` (${categoryHistory.filter(i => !i.resolved).length})`}
                                </Text>
                            </Pressable>
                        ))}
                    </View>
                </ScrollView>
            </View>

            {/* History List */}
            <FlatList
                data={filteredHistory}
                renderItem={renderHistoryItem}
                keyExtractor={(item) => item.id}
                contentContainerStyle={{ paddingVertical: 16 }}
                showsVerticalScrollIndicator={false}
                ListEmptyComponent={
                    <View className="flex-1 items-center justify-center p-8">
                        <Text className="text-4xl mb-4">📋</Text>
                        <Text className="text-lg font-semibold text-earth-900 mb-2 text-center">
                            No {selectedFilter} consultations
                        </Text>
                        <Text className="text-earth-600 text-center">
                            {selectedFilter === 'resolved'
                                ? 'No resolved consultations in this category yet'
                                : 'No pending consultations in this category'
                            }
                        </Text>
                    </View>
                }
            />
        </View>
    );
};