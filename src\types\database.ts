export type Json =
    | string
    | number
    | boolean
    | null
    | { [key: string]: Json | undefined }
    | Json[]

export interface Database {
    public: {
        Tables: {
            users: {
                Row: {
                    id: string
                    email: string
                    phone: string
                    first_name: string
                    last_name: string
                    created_at: string
                    updated_at: string
                }
                Insert: {
                    id?: string
                    email: string
                    phone: string
                    first_name: string
                    last_name: string
                    created_at?: string
                    updated_at?: string
                }
                Update: {
                    id?: string
                    email?: string
                    phone?: string
                    first_name?: string
                    last_name?: string
                    created_at?: string
                    updated_at?: string
                }
            }
            user_profiles: {
                Row: {
                    id: string
                    farm_location: string | null
                    crop_types: string[] | null
                    experience_level: 'beginner' | 'intermediate' | 'expert' | null
                    preferred_language: string
                    voice_enabled: boolean
                    points: number
                    subscription_tier: string
                    subscription_expires_at: string | null
                }
                Insert: {
                    id: string
                    farm_location?: string | null
                    crop_types?: string[] | null
                    experience_level?: 'beginner' | 'intermediate' | 'expert' | null
                    preferred_language?: string
                    voice_enabled?: boolean
                    points?: number
                    subscription_tier?: string
                    subscription_expires_at?: string | null
                }
                Update: {
                    id?: string
                    farm_location?: string | null
                    crop_types?: string[] | null
                    experience_level?: 'beginner' | 'intermediate' | 'expert' | null
                    preferred_language?: string
                    voice_enabled?: boolean
                    points?: number
                    subscription_tier?: string
                    subscription_expires_at?: string | null
                }
            }
            crop_plans: {
                Row: {
                    id: string
                    user_id: string
                    crop_type: string
                    planting_date: string | null
                    harvest_date: string | null
                    location: string | null
                    status: string
                    created_at: string
                }
                Insert: {
                    id?: string
                    user_id: string
                    crop_type: string
                    planting_date?: string | null
                    harvest_date?: string | null
                    location?: string | null
                    status?: string
                    created_at?: string
                }
                Update: {
                    id?: string
                    user_id?: string
                    crop_type?: string
                    planting_date?: string | null
                    harvest_date?: string | null
                    location?: string | null
                    status?: string
                    created_at?: string
                }
            }
            tasks: {
                Row: {
                    id: string
                    crop_plan_id: string
                    title: string
                    description: string | null
                    due_date: string | null
                    task_type: string | null
                    completed: boolean
                    points_reward: number
                    created_at: string
                }
                Insert: {
                    id?: string
                    crop_plan_id: string
                    title: string
                    description?: string | null
                    due_date?: string | null
                    task_type?: string | null
                    completed?: boolean
                    points_reward?: number
                    created_at?: string
                }
                Update: {
                    id?: string
                    crop_plan_id?: string
                    title?: string
                    description?: string | null
                    due_date?: string | null
                    task_type?: string | null
                    completed?: boolean
                    points_reward?: number
                    created_at?: string
                }
            }
            chat_sessions: {
                Row: {
                    id: string
                    user_id: string
                    session_type: string
                    created_at: string
                }
                Insert: {
                    id?: string
                    user_id: string
                    session_type?: string
                    created_at?: string
                }
                Update: {
                    id?: string
                    user_id?: string
                    session_type?: string
                    created_at?: string
                }
            }
            chat_messages: {
                Row: {
                    id: string
                    session_id: string
                    message_type: 'user' | 'ai'
                    content: string | null
                    image_url: string | null
                    analysis_result: Json | null
                    created_at: string
                }
                Insert: {
                    id?: string
                    session_id: string
                    message_type: 'user' | 'ai'
                    content?: string | null
                    image_url?: string | null
                    analysis_result?: Json | null
                    created_at?: string
                }
                Update: {
                    id?: string
                    session_id?: string
                    message_type?: 'user' | 'ai'
                    content?: string | null
                    image_url?: string | null
                    analysis_result?: Json | null
                    created_at?: string
                }
            }
            community_posts: {
                Row: {
                    id: string
                    user_id: string
                    title: string
                    content: string | null
                    image_urls: string[] | null
                    location: string | null
                    likes_count: number
                    comments_count: number
                    created_at: string
                }
                Insert: {
                    id?: string
                    user_id: string
                    title: string
                    content?: string | null
                    image_urls?: string[] | null
                    location?: string | null
                    likes_count?: number
                    comments_count?: number
                    created_at?: string
                }
                Update: {
                    id?: string
                    user_id?: string
                    title?: string
                    content?: string | null
                    image_urls?: string[] | null
                    location?: string | null
                    likes_count?: number
                    comments_count?: number
                    created_at?: string
                }
            }
            post_comments: {
                Row: {
                    id: string
                    post_id: string
                    user_id: string
                    content: string
                    created_at: string
                }
                Insert: {
                    id?: string
                    post_id: string
                    user_id: string
                    content: string
                    created_at?: string
                }
                Update: {
                    id?: string
                    post_id?: string
                    user_id?: string
                    content?: string
                    created_at?: string
                }
            }
            products: {
                Row: {
                    id: string
                    name: string
                    description: string | null
                    category: 'seeds' | 'fertilizers' | 'tools' | 'equipment' | 'pesticides' | 'irrigation'
                    price: number
                    original_price: number | null
                    currency: string
                    image_urls: string[]
                    specifications: Json
                    stock_quantity: number
                    rating: number
                    review_count: number
                    tags: string[]
                    brand: string | null
                    is_recommended: boolean
                    is_featured: boolean
                    is_active: boolean
                    search_vector: string | null
                    created_at: string
                    updated_at: string
                }
                Insert: {
                    id?: string
                    name: string
                    description?: string | null
                    category: 'seeds' | 'fertilizers' | 'tools' | 'equipment' | 'pesticides' | 'irrigation'
                    price: number
                    original_price?: number | null
                    currency?: string
                    image_urls?: string[]
                    specifications?: Json
                    stock_quantity?: number
                    rating?: number
                    review_count?: number
                    tags?: string[]
                    brand?: string | null
                    is_recommended?: boolean
                    is_featured?: boolean
                    is_active?: boolean
                    search_vector?: string | null
                    created_at?: string
                    updated_at?: string
                }
                Update: {
                    id?: string
                    name?: string
                    description?: string | null
                    category?: 'seeds' | 'fertilizers' | 'tools' | 'equipment' | 'pesticides' | 'irrigation'
                    price?: number
                    original_price?: number | null
                    currency?: string
                    image_urls?: string[]
                    specifications?: Json
                    stock_quantity?: number
                    rating?: number
                    review_count?: number
                    tags?: string[]
                    brand?: string | null
                    is_recommended?: boolean
                    is_featured?: boolean
                    is_active?: boolean
                    search_vector?: string | null
                    created_at?: string
                    updated_at?: string
                }
            }
            product_reviews: {
                Row: {
                    id: string
                    product_id: string
                    user_id: string
                    rating: number
                    title: string | null
                    comment: string | null
                    helpful_count: number
                    verified_purchase: boolean
                    created_at: string
                    updated_at: string
                }
                Insert: {
                    id?: string
                    product_id: string
                    user_id: string
                    rating: number
                    title?: string | null
                    comment?: string | null
                    helpful_count?: number
                    verified_purchase?: boolean
                    created_at?: string
                    updated_at?: string
                }
                Update: {
                    id?: string
                    product_id?: string
                    user_id?: string
                    rating?: number
                    title?: string | null
                    comment?: string | null
                    helpful_count?: number
                    verified_purchase?: boolean
                    created_at?: string
                    updated_at?: string
                }
            }
            product_variants: {
                Row: {
                    id: string
                    product_id: string
                    name: string
                    price: number
                    stock_quantity: number
                    specifications: Json
                    is_default: boolean
                    created_at: string
                }
                Insert: {
                    id?: string
                    product_id: string
                    name: string
                    price: number
                    stock_quantity?: number
                    specifications?: Json
                    is_default?: boolean
                    created_at?: string
                }
                Update: {
                    id?: string
                    product_id?: string
                    name?: string
                    price?: number
                    stock_quantity?: number
                    specifications?: Json
                    is_default?: boolean
                    created_at?: string
                }
            }
            shopping_carts: {
                Row: {
                    id: string
                    user_id: string
                    created_at: string
                    updated_at: string
                }
                Insert: {
                    id?: string
                    user_id: string
                    created_at?: string
                    updated_at?: string
                }
                Update: {
                    id?: string
                    user_id?: string
                    created_at?: string
                    updated_at?: string
                }
            }
            cart_items: {
                Row: {
                    id: string
                    cart_id: string
                    product_id: string
                    variant_id: string | null
                    quantity: number
                    unit_price: number
                    created_at: string
                    updated_at: string
                }
                Insert: {
                    id?: string
                    cart_id: string
                    product_id: string
                    variant_id?: string | null
                    quantity: number
                    unit_price: number
                    created_at?: string
                    updated_at?: string
                }
                Update: {
                    id?: string
                    cart_id?: string
                    product_id?: string
                    variant_id?: string | null
                    quantity?: number
                    unit_price?: number
                    created_at?: string
                    updated_at?: string
                }
            }
            product_recommendations: {
                Row: {
                    id: string
                    user_id: string
                    product_id: string
                    recommended_product_id: string
                    reason: string
                    confidence: number
                    source: 'ai' | 'collaborative' | 'content' | 'trending'
                    metadata: Json
                    created_at: string
                    expires_at: string
                }
                Insert: {
                    id?: string
                    user_id: string
                    product_id: string
                    recommended_product_id: string
                    reason: string
                    confidence: number
                    source?: 'ai' | 'collaborative' | 'content' | 'trending'
                    metadata?: Json
                    created_at?: string
                    expires_at?: string
                }
                Update: {
                    id?: string
                    user_id?: string
                    product_id?: string
                    recommended_product_id?: string
                    reason?: string
                    confidence?: number
                    source?: 'ai' | 'collaborative' | 'content' | 'trending'
                    metadata?: Json
                    created_at?: string
                    expires_at?: string
                }
            }
            inventory_transactions: {
                Row: {
                    id: string
                    product_id: string
                    variant_id: string | null
                    transaction_type: 'purchase' | 'sale' | 'adjustment' | 'return'
                    quantity_change: number
                    previous_quantity: number
                    new_quantity: number
                    reference_id: string | null
                    reference_type: string | null
                    notes: string | null
                    created_at: string
                }
                Insert: {
                    id?: string
                    product_id: string
                    variant_id?: string | null
                    transaction_type: 'purchase' | 'sale' | 'adjustment' | 'return'
                    quantity_change: number
                    previous_quantity: number
                    new_quantity: number
                    reference_id?: string | null
                    reference_type?: string | null
                    notes?: string | null
                    created_at?: string
                }
                Update: {
                    id?: string
                    product_id?: string
                    variant_id?: string | null
                    transaction_type?: 'purchase' | 'sale' | 'adjustment' | 'return'
                    quantity_change?: number
                    previous_quantity?: number
                    new_quantity?: number
                    reference_id?: string | null
                    reference_type?: string | null
                    notes?: string | null
                    created_at?: string
                }
            }
            wishlists: {
                Row: {
                    id: string
                    user_id: string
                    product_id: string
                    created_at: string
                }
                Insert: {
                    id?: string
                    user_id: string
                    product_id: string
                    created_at?: string
                }
                Update: {
                    id?: string
                    user_id?: string
                    product_id?: string
                    created_at?: string
                }
            }
            product_categories: {
                Row: {
                    id: string
                    name: string
                    description: string | null
                    icon: string | null
                    parent_category_id: string | null
                    sort_order: number
                    is_active: boolean
                    created_at: string
                }
                Insert: {
                    id: string
                    name: string
                    description?: string | null
                    icon?: string | null
                    parent_category_id?: string | null
                    sort_order?: number
                    is_active?: boolean
                    created_at?: string
                }
                Update: {
                    id?: string
                    name?: string
                    description?: string | null
                    icon?: string | null
                    parent_category_id?: string | null
                    sort_order?: number
                    is_active?: boolean
                    created_at?: string
                }
            }
            orders: {
                Row: {
                    id: string
                    user_id: string
                    order_number: string
                    total_amount: number
                    tax_amount: number
                    shipping_amount: number
                    discount_amount: number
                    status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded'
                    payment_status: 'pending' | 'paid' | 'failed' | 'refunded'
                    payment_method: Json | null
                    shipping_address: Json
                    billing_address: Json | null
                    tracking_number: string | null
                    estimated_delivery: string | null
                    delivered_at: string | null
                    notes: string | null
                    created_at: string
                    updated_at: string
                }
                Insert: {
                    id?: string
                    user_id: string
                    order_number?: string
                    total_amount: number
                    tax_amount?: number
                    shipping_amount?: number
                    discount_amount?: number
                    status?: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded'
                    payment_status?: 'pending' | 'paid' | 'failed' | 'refunded'
                    payment_method?: Json | null
                    shipping_address: Json
                    billing_address?: Json | null
                    tracking_number?: string | null
                    estimated_delivery?: string | null
                    delivered_at?: string | null
                    notes?: string | null
                    created_at?: string
                    updated_at?: string
                }
                Update: {
                    id?: string
                    user_id?: string
                    order_number?: string
                    total_amount?: number
                    tax_amount?: number
                    shipping_amount?: number
                    discount_amount?: number
                    status?: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded'
                    payment_status?: 'pending' | 'paid' | 'failed' | 'refunded'
                    payment_method?: Json | null
                    shipping_address?: Json
                    billing_address?: Json | null
                    tracking_number?: string | null
                    estimated_delivery?: string | null
                    delivered_at?: string | null
                    notes?: string | null
                    created_at?: string
                    updated_at?: string
                }
            }
            order_items: {
                Row: {
                    id: string
                    order_id: string
                    product_id: string
                    variant_id: string | null
                    quantity: number
                    unit_price: number
                    total_price: number
                    product_snapshot: Json | null
                    created_at: string
                }
                Insert: {
                    id?: string
                    order_id: string
                    product_id: string
                    variant_id?: string | null
                    quantity: number
                    unit_price: number
                    total_price: number
                    product_snapshot?: Json | null
                    created_at?: string
                }
                Update: {
                    id?: string
                    order_id?: string
                    product_id?: string
                    variant_id?: string | null
                    quantity?: number
                    unit_price?: number
                    total_price?: number
                    product_snapshot?: Json | null
                    created_at?: string
                }
            }
            points_transactions: {
                Row: {
                    id: string
                    user_id: string
                    amount: number
                    transaction_type: 'earned' | 'spent' | 'bonus' | 'refund'
                    source: 'task_completion' | 'daily_checkin' | 'community_post' | 'ai_consultation' | 'subscription_bonus' | 'achievement' | 'referral' | 'admin_adjustment'
                    description: string
                    metadata: Json | null
                    created_at: string
                }
                Insert: {
                    id?: string
                    user_id: string
                    amount: number
                    transaction_type: 'earned' | 'spent' | 'bonus' | 'refund'
                    source: 'task_completion' | 'daily_checkin' | 'community_post' | 'ai_consultation' | 'subscription_bonus' | 'achievement' | 'referral' | 'admin_adjustment'
                    description: string
                    metadata?: Json | null
                    created_at?: string
                }
                Update: {
                    id?: string
                    user_id?: string
                    amount?: number
                    transaction_type?: 'earned' | 'spent' | 'bonus' | 'refund'
                    source?: 'task_completion' | 'daily_checkin' | 'community_post' | 'ai_consultation' | 'subscription_bonus' | 'achievement' | 'referral' | 'admin_adjustment'
                    description?: string
                    metadata?: Json | null
                    created_at?: string
                }
            }
            points_balances: {
                Row: {
                    user_id: string
                    total_points: number
                    available_points: number
                    pending_points: number
                    lifetime_earned: number
                    lifetime_spent: number
                    last_updated: string
                }
                Insert: {
                    user_id: string
                    total_points?: number
                    available_points?: number
                    pending_points?: number
                    lifetime_earned?: number
                    lifetime_spent?: number
                    last_updated?: string
                }
                Update: {
                    user_id?: string
                    total_points?: number
                    available_points?: number
                    pending_points?: number
                    lifetime_earned?: number
                    lifetime_spent?: number
                    last_updated?: string
                }
            }
            achievements: {
                Row: {
                    id: string
                    name: string
                    description: string
                    icon: string
                    category: 'farming' | 'community' | 'learning' | 'consistency' | 'milestone'
                    points_reward: number
                    requirements: Json
                    is_active: boolean
                    created_at: string
                }
                Insert: {
                    id?: string
                    name: string
                    description: string
                    icon: string
                    category: 'farming' | 'community' | 'learning' | 'consistency' | 'milestone'
                    points_reward: number
                    requirements: Json
                    is_active?: boolean
                    created_at?: string
                }
                Update: {
                    id?: string
                    name?: string
                    description?: string
                    icon?: string
                    category?: 'farming' | 'community' | 'learning' | 'consistency' | 'milestone'
                    points_reward?: number
                    requirements?: Json
                    is_active?: boolean
                    created_at?: string
                }
            }
            user_achievements: {
                Row: {
                    id: string
                    user_id: string
                    achievement_id: string
                    earned_at: string
                    points_awarded: number
                }
                Insert: {
                    id?: string
                    user_id: string
                    achievement_id: string
                    earned_at?: string
                    points_awarded: number
                }
                Update: {
                    id?: string
                    user_id?: string
                    achievement_id?: string
                    earned_at?: string
                    points_awarded?: number
                }
            }
            subscription_plans: {
                Row: {
                    id: string
                    name: string
                    description: string
                    price: number
                    currency: string
                    billing_period: 'monthly' | 'yearly'
                    features: Json
                    points_included: number
                    ai_consultations_limit: number
                    priority_support: boolean
                    offline_access: boolean
                    advanced_analytics: boolean
                    api_access: boolean
                    multi_farm_support: boolean
                    team_collaboration: boolean
                    is_active: boolean
                    sort_order: number
                    created_at: string
                    updated_at: string
                }
                Insert: {
                    id?: string
                    name: string
                    description: string
                    price: number
                    currency: string
                    billing_period: 'monthly' | 'yearly'
                    features: Json
                    points_included: number
                    ai_consultations_limit: number
                    priority_support?: boolean
                    offline_access?: boolean
                    advanced_analytics?: boolean
                    api_access?: boolean
                    multi_farm_support?: boolean
                    team_collaboration?: boolean
                    is_active?: boolean
                    sort_order?: number
                    created_at?: string
                    updated_at?: string
                }
                Update: {
                    id?: string
                    name?: string
                    description?: string
                    price?: number
                    currency?: string
                    billing_period?: 'monthly' | 'yearly'
                    features?: Json
                    points_included?: number
                    ai_consultations_limit?: number
                    priority_support?: boolean
                    offline_access?: boolean
                    advanced_analytics?: boolean
                    api_access?: boolean
                    multi_farm_support?: boolean
                    team_collaboration?: boolean
                    is_active?: boolean
                    sort_order?: number
                    created_at?: string
                    updated_at?: string
                }
            }
            user_subscriptions: {
                Row: {
                    id: string
                    user_id: string
                    plan_id: string
                    status: 'active' | 'cancelled' | 'expired' | 'past_due' | 'trialing'
                    current_period_start: string
                    current_period_end: string
                    cancel_at_period_end: boolean
                    cancelled_at: string | null
                    trial_start: string | null
                    trial_end: string | null
                    payment_method_id: string | null
                    created_at: string
                    updated_at: string
                }
                Insert: {
                    id?: string
                    user_id: string
                    plan_id: string
                    status?: 'active' | 'cancelled' | 'expired' | 'past_due' | 'trialing'
                    current_period_start: string
                    current_period_end: string
                    cancel_at_period_end?: boolean
                    cancelled_at?: string | null
                    trial_start?: string | null
                    trial_end?: string | null
                    payment_method_id?: string | null
                    created_at?: string
                    updated_at?: string
                }
                Update: {
                    id?: string
                    user_id?: string
                    plan_id?: string
                    status?: 'active' | 'cancelled' | 'expired' | 'past_due' | 'trialing'
                    current_period_start?: string
                    current_period_end?: string
                    cancel_at_period_end?: boolean
                    cancelled_at?: string | null
                    trial_start?: string | null
                    trial_end?: string | null
                    payment_method_id?: string | null
                    created_at?: string
                    updated_at?: string
                }
            }
            subscription_usage: {
                Row: {
                    id: string
                    user_id: string
                    subscription_id: string
                    period_start: string
                    period_end: string
                    ai_consultations_used: number
                    ai_consultations_limit: number
                    image_analyses_used: number
                    storage_used_mb: number
                    api_calls_used: number
                    created_at: string
                    updated_at: string
                }
                Insert: {
                    id?: string
                    user_id: string
                    subscription_id: string
                    period_start: string
                    period_end: string
                    ai_consultations_used?: number
                    ai_consultations_limit: number
                    image_analyses_used?: number
                    storage_used_mb?: number
                    api_calls_used?: number
                    created_at?: string
                    updated_at?: string
                }
                Update: {
                    id?: string
                    user_id?: string
                    subscription_id?: string
                    period_start?: string
                    period_end?: string
                    ai_consultations_used?: number
                    ai_consultations_limit?: number
                    image_analyses_used?: number
                    storage_used_mb?: number
                    api_calls_used?: number
                    created_at?: string
                    updated_at?: string
                }
            }
            daily_streaks: {
                Row: {
                    user_id: string
                    current_streak: number
                    longest_streak: number
                    last_activity_date: string
                    streak_multiplier: number
                }
                Insert: {
                    user_id: string
                    current_streak?: number
                    longest_streak?: number
                    last_activity_date: string
                    streak_multiplier?: number
                }
                Update: {
                    user_id?: string
                    current_streak?: number
                    longest_streak?: number
                    last_activity_date?: string
                    streak_multiplier?: number
                }
            }
        }
        Views: {
            [_ in never]: never
        }
        Functions: {
            [_ in never]: never
        }
        Enums: {
            [_ in never]: never
        }
        CompositeTypes: {
            [_ in never]: never
        }
    }
}