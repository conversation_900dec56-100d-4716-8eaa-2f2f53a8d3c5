import { CropPlan, CreateCropPlanData, CropRecommendation, PlantingSchedule } from '../types/crops';
import { CropType, cropTypes } from '../data/crops';
import { Task } from '../types/tasks';
import { WeatherService, WeatherData } from './weather';
import { LocationCoordinates } from './location';

export class CropPlanningService {
    private static weatherService = WeatherService.getInstance();

    /**
     * Get crop recommendations based on location, season, user experience, and weather data
     */
    static async getCropRecommendations(
        location: LocationCoordinates,
        userExperience: 'beginner' | 'intermediate' | 'expert',
        currentDate: Date = new Date(),
        weatherData?: WeatherData
    ): Promise<CropRecommendation[]> {
        const currentMonth = currentDate.getMonth() + 1;

        // Get weather data if not provided
        if (!weatherData) {
            weatherData = await this.weatherService.getCompleteWeatherData(location);
        }

        const recommendations = await Promise.all(
            cropTypes
                .filter(crop => {
                    // Filter by user experience level
                    if (userExperience === 'beginner' && crop.difficulty !== 'beginner') {
                        return false;
                    }

                    // Check if current month is suitable for planting
                    return crop.growingSeasonMonths.includes(currentMonth) ||
                        crop.growingSeasonMonths.includes(currentMonth + 1);
                })
                .map(crop => this.generateCropRecommendation(crop, location, currentDate, weatherData))
        );

        return recommendations.sort((a, b) => b.suitabilityScore - a.suitabilityScore);
    }

    /**
     * Generate a crop recommendation for a specific crop with weather integration
     */
    private static async generateCropRecommendation(
        crop: CropType,
        location: LocationCoordinates,
        currentDate: Date,
        weatherData?: WeatherData | null
    ): Promise<CropRecommendation> {
        const currentMonth = currentDate.getMonth() + 1;
        const suitabilityScore = this.calculateSuitabilityScore(crop, location, currentMonth, weatherData);
        const plantingSchedule = await this.getPlantingSchedule(crop, currentDate, location, weatherData);
        const yieldPrediction = await this.predictYield(crop, location, weatherData);

        return {
            cropId: crop.id,
            suitabilityScore,
            reasons: this.generateReasons(crop, currentMonth, suitabilityScore, weatherData),
            reasonsAr: this.generateReasonsAr(crop, currentMonth, suitabilityScore, weatherData),
            optimalPlantingDate: plantingSchedule.optimalStartDate,
            expectedHarvestDate: plantingSchedule.harvestWindow.start,
            estimatedYield: yieldPrediction.estimatedYield,
            difficultyRating: this.getDifficultyRating(crop.difficulty),
            waterRequirement: this.getWaterRequirement(crop),
            sunlightRequirement: 'full',
            soilType: this.getSoilTypes(crop),
        };
    }

    /**
     * Calculate suitability score for a crop with weather integration
     */
    private static calculateSuitabilityScore(
        crop: CropType,
        location: LocationCoordinates,
        currentMonth: number,
        weatherData?: WeatherData | null
    ): number {
        let score = 50; // Base score

        // Season suitability
        if (crop.growingSeasonMonths.includes(currentMonth)) {
            score += 30;
        } else if (crop.growingSeasonMonths.includes(currentMonth + 1)) {
            score += 20;
        }

        // Difficulty bonus for beginners
        if (crop.difficulty === 'beginner') {
            score += 15;
        } else if (crop.difficulty === 'intermediate') {
            score += 10;
        }

        // Climate suitability (enhanced with location data)
        const latitude = Math.abs(location.latitude);
        if (latitude < 30) { // Tropical/subtropical
            if (['tomatoes', 'peppers', 'watermelon', 'rice'].includes(crop.id)) {
                score += 15;
            }
        } else if (latitude < 50) { // Temperate
            if (['wheat', 'corn', 'potatoes', 'soybeans'].includes(crop.id)) {
                score += 15;
            }
        } else { // Cold climate
            if (['wheat', 'potatoes', 'carrots'].includes(crop.id)) {
                score += 10;
            }
        }

        // Weather-based adjustments
        if (weatherData?.current) {
            const weather = weatherData.current;

            // Temperature suitability
            const temp = weather.temperature;
            if (temp >= 20 && temp <= 30) {
                if (['tomatoes', 'peppers', 'corn'].includes(crop.id)) {
                    score += 10;
                }
            } else if (temp < 20) {
                if (['wheat', 'potatoes', 'carrots'].includes(crop.id)) {
                    score += 8;
                }
            } else if (temp > 30) {
                if (['watermelon', 'rice'].includes(crop.id)) {
                    score += 8;
                }
            }

            // Humidity considerations
            if (weather.humidity > 70) {
                if (['rice', 'mint'].includes(crop.id)) {
                    score += 5;
                } else if (['onions', 'carrots'].includes(crop.id)) {
                    score -= 5; // These prefer drier conditions
                }
            }

            // Rainfall predictions from forecast
            if (weatherData.forecast.length > 0) {
                const avgPrecipitation = weatherData.forecast
                    .slice(0, 7) // Next 7 days
                    .reduce((sum, day) => sum + day.precipitation, 0) / 7;

                if (avgPrecipitation > 5) { // High rainfall expected
                    if (['rice', 'mint'].includes(crop.id)) {
                        score += 8;
                    } else if (['onions', 'carrots'].includes(crop.id)) {
                        score -= 8;
                    }
                } else if (avgPrecipitation < 1) { // Low rainfall expected
                    if (['onions', 'carrots'].includes(crop.id)) {
                        score += 5;
                    }
                }
            }
        }

        return Math.min(100, Math.max(0, score));
    }

    /**
     * Get planting schedule for a crop with weather-based timing optimization
     */
    private static async getPlantingSchedule(
        crop: CropType,
        currentDate: Date,
        location: LocationCoordinates,
        weatherData?: WeatherData | null
    ): Promise<PlantingSchedule> {
        const currentYear = currentDate.getFullYear();
        const growingDays = this.getGrowingDays(crop);

        // Find the next optimal planting month
        const currentMonth = currentDate.getMonth() + 1;
        let nextPlantingMonth = crop.growingSeasonMonths.find(month => month >= currentMonth);

        if (!nextPlantingMonth) {
            // If no suitable month this year, use first month of next year
            nextPlantingMonth = crop.growingSeasonMonths[0];
        }

        let optimalStartDate = new Date(currentYear, nextPlantingMonth - 1, 1);
        let optimalEndDate = new Date(currentYear, nextPlantingMonth - 1, 15);

        // Weather-based timing adjustments
        if (weatherData?.forecast && weatherData.forecast.length > 0) {
            const adjustedDates = this.adjustPlantingDatesForWeather(
                optimalStartDate,
                optimalEndDate,
                crop,
                weatherData.forecast
            );
            optimalStartDate = adjustedDates.startDate;
            optimalEndDate = adjustedDates.endDate;
        }

        const earlyStartDate = new Date(optimalStartDate);
        earlyStartDate.setDate(earlyStartDate.getDate() - 14);
        const lateStartDate = new Date(optimalEndDate);
        lateStartDate.setDate(lateStartDate.getDate() + 14);

        const harvestStart = new Date(optimalStartDate);
        harvestStart.setDate(harvestStart.getDate() + growingDays);
        const harvestEnd = new Date(harvestStart);
        harvestEnd.setDate(harvestEnd.getDate() + 14);

        return {
            cropId: crop.id,
            optimalStartDate,
            optimalEndDate,
            earlyStartDate,
            lateStartDate,
            growingDays,
            harvestWindow: {
                start: harvestStart,
                end: harvestEnd,
            },
        };
    }

    /**
     * Adjust planting dates based on weather forecast
     */
    private static adjustPlantingDatesForWeather(
        startDate: Date,
        endDate: Date,
        crop: CropType,
        forecast: any[]
    ): { startDate: Date; endDate: Date } {
        // Look for optimal weather conditions in the next 30 days
        const today = new Date();
        const searchEndDate = new Date(today);
        searchEndDate.setDate(searchEndDate.getDate() + 30);

        let bestDate = startDate;
        let bestScore = 0;

        for (let i = 0; i < forecast.length && i < 30; i++) {
            const forecastDay = forecast[i];
            const checkDate = new Date(today);
            checkDate.setDate(checkDate.getDate() + i);

            if (checkDate < startDate || checkDate > searchEndDate) continue;

            let score = 50; // Base score

            // Temperature scoring
            const avgTemp = (forecastDay.temperature.min + forecastDay.temperature.max) / 2;
            if (avgTemp >= 15 && avgTemp <= 25) {
                score += 20; // Ideal temperature range
            } else if (avgTemp >= 10 && avgTemp <= 30) {
                score += 10; // Acceptable range
            }

            // Precipitation scoring
            if (forecastDay.precipitation < 2) {
                score += 15; // Good for planting (not too wet)
            } else if (forecastDay.precipitation > 10) {
                score -= 20; // Too wet for planting
            }

            // Wind scoring
            if (forecastDay.windSpeed < 15) {
                score += 10; // Calm conditions good for planting
            }

            if (score > bestScore) {
                bestScore = score;
                bestDate = new Date(checkDate);
            }
        }

        // Adjust end date to maintain planting window
        const adjustedEndDate = new Date(bestDate);
        adjustedEndDate.setDate(adjustedEndDate.getDate() + 14);

        return {
            startDate: bestDate,
            endDate: adjustedEndDate,
        };
    }

    /**
     * Predict crop yield based on location, weather, and historical data
     */
    private static async predictYield(
        crop: CropType,
        location: LocationCoordinates,
        weatherData?: WeatherData | null
    ): Promise<{ estimatedYield: number; confidence: number; factors: string[] }> {
        let baseYield = this.estimateYield(crop);
        let confidence = 70; // Base confidence
        const factors: string[] = [];

        // Climate zone adjustments
        const latitude = Math.abs(location.latitude);
        if (latitude < 30) { // Tropical/subtropical
            if (['rice', 'watermelon', 'tomatoes'].includes(crop.id)) {
                baseYield *= 1.2;
                confidence += 10;
                factors.push('Favorable tropical climate');
            }
        } else if (latitude < 50) { // Temperate
            if (['wheat', 'corn', 'potatoes'].includes(crop.id)) {
                baseYield *= 1.15;
                confidence += 8;
                factors.push('Optimal temperate conditions');
            }
        }

        // Weather-based adjustments
        if (weatherData?.current) {
            const weather = weatherData.current;

            // Temperature impact
            if (weather.temperature >= 20 && weather.temperature <= 28) {
                baseYield *= 1.1;
                factors.push('Ideal temperature range');
                confidence += 5;
            } else if (weather.temperature < 10 || weather.temperature > 35) {
                baseYield *= 0.8;
                factors.push('Challenging temperature conditions');
                confidence -= 10;
            }

            // Humidity impact
            if (weather.humidity >= 40 && weather.humidity <= 70) {
                baseYield *= 1.05;
                factors.push('Optimal humidity levels');
            } else if (weather.humidity > 80) {
                if (!['rice', 'mint'].includes(crop.id)) {
                    baseYield *= 0.9;
                    factors.push('High humidity may affect some crops');
                }
            }
        }

        // Forecast-based adjustments
        if (weatherData?.forecast && weatherData.forecast.length > 0) {
            const avgTemp = weatherData.forecast
                .slice(0, 7)
                .reduce((sum, day) => sum + (day.temperature.min + day.temperature.max) / 2, 0) / 7;

            const totalPrecipitation = weatherData.forecast
                .slice(0, 7)
                .reduce((sum, day) => sum + day.precipitation, 0);

            if (avgTemp >= 18 && avgTemp <= 26) {
                baseYield *= 1.08;
                factors.push('Favorable temperature forecast');
                confidence += 5;
            }

            if (totalPrecipitation >= 10 && totalPrecipitation <= 50) {
                baseYield *= 1.1;
                factors.push('Adequate rainfall expected');
                confidence += 8;
            } else if (totalPrecipitation > 100) {
                baseYield *= 0.85;
                factors.push('Excessive rainfall may impact yield');
                confidence -= 15;
            } else if (totalPrecipitation < 5) {
                if (!['onions', 'carrots'].includes(crop.id)) {
                    baseYield *= 0.9;
                    factors.push('Low rainfall may require irrigation');
                    confidence -= 10;
                }
            }
        }

        return {
            estimatedYield: Math.round(baseYield * 100) / 100,
            confidence: Math.min(95, Math.max(30, confidence)),
            factors,
        };
    }

    /**
     * Generate crop growth stage milestones
     */
    static generateGrowthMilestones(cropPlan: CropPlan): Array<{
        stage: string;
        date: Date;
        description: string;
        descriptionAr: string;
        tasks: string[];
    }> {
        const crop = cropTypes.find(c => c.id === cropPlan.cropType);
        if (!crop) return [];

        const plantingDate = new Date(cropPlan.plantingDate);
        const harvestDate = new Date(cropPlan.harvestDate);
        const totalDays = Math.floor((harvestDate.getTime() - plantingDate.getTime()) / (1000 * 60 * 60 * 24));

        const milestones = [];

        // Germination stage (5-10% of growing period)
        const germinationDate = new Date(plantingDate);
        germinationDate.setDate(germinationDate.getDate() + Math.floor(totalDays * 0.08));
        milestones.push({
            stage: 'germination',
            date: germinationDate,
            description: 'Seeds should begin to germinate',
            descriptionAr: 'يجب أن تبدأ البذور في الإنبات',
            tasks: ['Monitor soil moisture', 'Check for pest activity'],
        });

        // Vegetative growth (20-30% of growing period)
        const vegetativeDate = new Date(plantingDate);
        vegetativeDate.setDate(vegetativeDate.getDate() + Math.floor(totalDays * 0.25));
        milestones.push({
            stage: 'vegetative',
            date: vegetativeDate,
            description: 'Plants should show strong vegetative growth',
            descriptionAr: 'يجب أن تظهر النباتات نمواً خضرياً قوياً',
            tasks: ['Apply fertilizer', 'Monitor for diseases', 'Thin plants if needed'],
        });

        // Flowering/reproductive stage (50-60% of growing period)
        const floweringDate = new Date(plantingDate);
        floweringDate.setDate(floweringDate.getDate() + Math.floor(totalDays * 0.55));
        milestones.push({
            stage: 'flowering',
            date: floweringDate,
            description: 'Plants should begin flowering or fruit development',
            descriptionAr: 'يجب أن تبدأ النباتات في الإزهار أو تطوير الثمار',
            tasks: ['Monitor pollination', 'Support heavy branches', 'Increase watering'],
        });

        // Maturation stage (80-90% of growing period)
        const maturationDate = new Date(plantingDate);
        maturationDate.setDate(maturationDate.getDate() + Math.floor(totalDays * 0.85));
        milestones.push({
            stage: 'maturation',
            date: maturationDate,
            description: 'Crops should be nearing maturity',
            descriptionAr: 'يجب أن تقترب المحاصيل من النضج',
            tasks: ['Monitor ripeness', 'Prepare for harvest', 'Reduce watering'],
        });

        return milestones;
    }

    /**
     * Generate tasks for a crop plan with weather-responsive scheduling
     */
    static async generateTasksForCropPlan(
        cropPlan: CropPlan,
        weatherData?: WeatherData
    ): Promise<Task[]> {
        const crop = cropTypes.find(c => c.id === cropPlan.cropType);
        if (!crop) return [];

        const tasks: Task[] = [];
        const plantingDate = new Date(cropPlan.plantingDate);
        const harvestDate = new Date(cropPlan.harvestDate);
        const totalDays = Math.floor((harvestDate.getTime() - plantingDate.getTime()) / (1000 * 60 * 60 * 24));

        // Get weather-adjusted instructions
        const weatherInstructions = this.getWeatherAdjustedInstructions(weatherData);

        // Planting task with weather considerations
        const plantingInstructions = [
            'Prepare the soil by removing weeds and debris',
            'Create furrows at the recommended depth',
            'Plant seeds at proper spacing',
            'Water gently after planting',
        ];

        if (weatherInstructions.planting.length > 0) {
            plantingInstructions.push(...weatherInstructions.planting);
        }

        tasks.push({
            id: `${cropPlan.id}-planting`,
            title: `Plant ${crop.name}`,
            description: `Plant ${crop.name} seeds according to recommended spacing and depth`,
            dueDate: plantingDate,
            type: 'planting',
            completed: false,
            pointsReward: 20,
            priority: 'high',
            cropPlanId: cropPlan.id,
            estimatedDuration: 60,
            instructions: plantingInstructions,
        });

        // Weather-responsive watering tasks
        const wateringFrequency = this.getWateringFrequency(crop, weatherData);
        for (let week = 1; week <= Math.ceil(totalDays / 7); week++) {
            const taskDate = new Date(plantingDate);
            taskDate.setDate(taskDate.getDate() + (week * wateringFrequency));

            if (taskDate < harvestDate) {
                const wateringInstructions = [
                    'Check soil moisture before watering',
                    'Water at base of plants, avoid leaves',
                    'Apply water slowly for deep penetration',
                ];

                if (weatherInstructions.watering.length > 0) {
                    wateringInstructions.push(...weatherInstructions.watering);
                }

                tasks.push({
                    id: `${cropPlan.id}-watering-week-${week}`,
                    title: `Water ${crop.name}`,
                    description: `${wateringFrequency === 7 ? 'Weekly' : 'Bi-weekly'} watering for ${crop.name}`,
                    dueDate: taskDate,
                    type: 'watering',
                    completed: false,
                    pointsReward: 10,
                    priority: this.getWateringPriority(weatherData),
                    cropPlanId: cropPlan.id,
                    estimatedDuration: 30,
                    instructions: wateringInstructions,
                });
            }
        }

        // Fertilizing tasks (every 3 weeks)
        for (let week = 3; week <= Math.ceil(totalDays / 7); week += 3) {
            const taskDate = new Date(plantingDate);
            taskDate.setDate(taskDate.getDate() + (week * 7));

            if (taskDate < harvestDate) {
                tasks.push({
                    id: `${cropPlan.id}-fertilizing-week-${week}`,
                    title: `Fertilize ${crop.name}`,
                    description: `Apply fertilizer to ${crop.name} plants`,
                    dueDate: taskDate,
                    type: 'fertilizing',
                    completed: false,
                    pointsReward: 15,
                    priority: 'medium',
                    cropPlanId: cropPlan.id,
                    estimatedDuration: 45,
                });
            }
        }

        // Monitoring tasks (bi-weekly)
        for (let week = 2; week <= Math.ceil(totalDays / 7); week += 2) {
            const taskDate = new Date(plantingDate);
            taskDate.setDate(taskDate.getDate() + (week * 7));

            if (taskDate < harvestDate) {
                tasks.push({
                    id: `${cropPlan.id}-monitoring-week-${week}`,
                    title: `Monitor ${crop.name}`,
                    description: `Check ${crop.name} for pests, diseases, and growth progress`,
                    dueDate: taskDate,
                    type: 'monitoring',
                    completed: false,
                    pointsReward: 12,
                    priority: 'medium',
                    cropPlanId: cropPlan.id,
                    estimatedDuration: 20,
                });
            }
        }

        // Harvest task
        tasks.push({
            id: `${cropPlan.id}-harvest`,
            title: `Harvest ${crop.name}`,
            description: `Harvest mature ${crop.name}`,
            dueDate: harvestDate,
            type: 'harvesting',
            completed: false,
            pointsReward: 30,
            priority: 'high',
            cropPlanId: cropPlan.id,
            estimatedDuration: 90,
            instructions: [
                'Check for ripeness indicators',
                'Harvest during optimal time of day',
                'Handle produce carefully to avoid damage',
                'Store properly after harvest',
            ],
        });

        return tasks;
    }

    /**
     * Get weather-adjusted instructions for tasks
     */
    private static getWeatherAdjustedInstructions(weatherData?: WeatherData): {
        planting: string[];
        watering: string[];
        fertilizing: string[];
        monitoring: string[];
    } {
        const instructions = {
            planting: [] as string[],
            watering: [] as string[],
            fertilizing: [] as string[],
            monitoring: [] as string[],
        };

        if (!weatherData?.current) return instructions;

        const weather = weatherData.current;
        const forecast = weatherData.forecast;

        // Temperature-based instructions
        if (weather.temperature > 30) {
            instructions.planting.push('Plant in early morning or evening to avoid heat stress');
            instructions.watering.push('Water early morning or late evening to reduce evaporation');
            instructions.monitoring.push('Check for heat stress symptoms');
        } else if (weather.temperature < 15) {
            instructions.planting.push('Consider using row covers for protection');
            instructions.watering.push('Water during warmer parts of the day');
        }

        // Humidity-based instructions
        if (weather.humidity > 80) {
            instructions.monitoring.push('Monitor closely for fungal diseases');
            instructions.watering.push('Reduce watering frequency due to high humidity');
        } else if (weather.humidity < 40) {
            instructions.watering.push('Increase watering frequency due to low humidity');
        }

        // Wind-based instructions
        if (weather.windSpeed > 15) {
            instructions.planting.push('Provide wind protection for young plants');
            instructions.monitoring.push('Check plant supports and stakes');
        }

        // Forecast-based instructions
        if (forecast && forecast.length > 0) {
            const rainExpected = forecast.slice(0, 3).some(day => day.precipitation > 5);
            if (rainExpected) {
                instructions.watering.push('Skip watering if heavy rain is expected');
                instructions.fertilizing.push('Apply fertilizer before expected rain for better absorption');
            }

            const heatWaveExpected = forecast.slice(0, 3).some(day => day.temperature.max > 35);
            if (heatWaveExpected) {
                instructions.watering.push('Increase watering frequency during heat wave');
                instructions.monitoring.push('Provide shade protection during extreme heat');
            }
        }

        return instructions;
    }

    /**
     * Get watering frequency based on crop and weather
     */
    private static getWateringFrequency(crop: CropType, weatherData?: WeatherData): number {
        let baseFrequency = 7; // Default weekly

        // Crop-specific adjustments
        const highWaterCrops = ['rice', 'mint', 'watermelon'];
        const lowWaterCrops = ['onions', 'carrots'];

        if (highWaterCrops.includes(crop.id)) {
            baseFrequency = 3; // Every 3 days
        } else if (lowWaterCrops.includes(crop.id)) {
            baseFrequency = 10; // Every 10 days
        }

        // Weather adjustments
        if (weatherData?.current) {
            const weather = weatherData.current;

            if (weather.temperature > 30 || weather.humidity < 40) {
                baseFrequency = Math.max(3, baseFrequency - 2); // More frequent
            } else if (weather.humidity > 80) {
                baseFrequency = Math.min(14, baseFrequency + 3); // Less frequent
            }

            // Check forecast for rain
            if (weatherData.forecast) {
                const rainExpected = weatherData.forecast
                    .slice(0, 7)
                    .reduce((sum, day) => sum + day.precipitation, 0);

                if (rainExpected > 20) {
                    baseFrequency = Math.min(14, baseFrequency + 4); // Much less frequent
                }
            }
        }

        return baseFrequency;
    }

    /**
     * Get watering priority based on weather conditions
     */
    private static getWateringPriority(weatherData?: WeatherData): 'low' | 'medium' | 'high' {
        if (!weatherData?.current) return 'medium';

        const weather = weatherData.current;

        if (weather.temperature > 35 || weather.humidity < 30) {
            return 'high'; // Critical watering conditions
        } else if (weather.temperature > 30 || weather.humidity < 50) {
            return 'medium'; // Normal priority
        } else {
            return 'low'; // Less urgent
        }
    }

    /**
     * Create a new crop plan with enhanced location and weather integration
     */
    static async createCropPlan(data: CreateCropPlanData): Promise<CropPlan> {
        const crop = cropTypes.find(c => c.id === data.cropType);
        if (!crop) {
            throw new Error('Invalid crop type');
        }

        // Get weather data for location
        const weatherData = await this.weatherService.getCompleteWeatherData(data.location);

        // Get optimized planting schedule
        const plantingSchedule = await this.getPlantingSchedule(
            crop,
            data.plantingDate,
            data.location,
            weatherData
        );

        // Predict yield based on conditions
        const yieldPrediction = await this.predictYield(crop, data.location, weatherData);

        const cropPlan: CropPlan = {
            id: `crop-plan-${Date.now()}`,
            cropType: data.cropType,
            cropName: crop.name,
            plantingDate: plantingSchedule.optimalStartDate,
            harvestDate: plantingSchedule.harvestWindow.start,
            location: data.location,
            status: 'planning',
            tasks: [],
            weatherAlerts: weatherData?.alerts || [],
            notes: data.notes,
            expectedYield: data.expectedYield || yieldPrediction.estimatedYield,
            createdAt: new Date(),
            updatedAt: new Date(),
        };

        return cropPlan;
    }

    // Helper methods
    private static getGrowingDays(crop: CropType): number {
        const growingDaysMap: Record<string, number> = {
            corn: 90,
            wheat: 120,
            rice: 150,
            tomatoes: 80,
            peppers: 75,
            onions: 100,
            carrots: 70,
            potatoes: 90,
            strawberries: 60,
            watermelon: 90,
            soybeans: 100,
            beans: 60,
            basil: 45,
            mint: 30,
        };
        return growingDaysMap[crop.id] || 90;
    }

    private static generateReasons(
        crop: CropType,
        currentMonth: number,
        score: number,
        weatherData?: WeatherData | null
    ): string[] {
        const reasons: string[] = [];

        if (crop.growingSeasonMonths.includes(currentMonth)) {
            reasons.push('Perfect planting season');
        }

        if (crop.difficulty === 'beginner') {
            reasons.push('Easy to grow for beginners');
        }

        if (score > 80) {
            reasons.push('Highly recommended for your location');
        }

        // Weather-based reasons
        if (weatherData?.current) {
            const weather = weatherData.current;

            if (weather.temperature >= 20 && weather.temperature <= 28) {
                reasons.push('Ideal temperature conditions');
            }

            if (weather.humidity >= 40 && weather.humidity <= 70) {
                reasons.push('Optimal humidity levels');
            }

            if (weatherData.forecast) {
                const avgPrecipitation = weatherData.forecast
                    .slice(0, 7)
                    .reduce((sum, day) => sum + day.precipitation, 0) / 7;

                if (avgPrecipitation >= 2 && avgPrecipitation <= 8) {
                    reasons.push('Favorable rainfall forecast');
                }
            }
        }

        return reasons;
    }

    private static generateReasonsAr(
        crop: CropType,
        currentMonth: number,
        score: number,
        weatherData?: WeatherData | null
    ): string[] {
        const reasons: string[] = [];

        if (crop.growingSeasonMonths.includes(currentMonth)) {
            reasons.push('موسم زراعة مثالي');
        }

        if (crop.difficulty === 'beginner') {
            reasons.push('سهل النمو للمبتدئين');
        }

        if (score > 80) {
            reasons.push('موصى به بشدة لموقعك');
        }

        // Weather-based reasons in Arabic
        if (weatherData?.current) {
            const weather = weatherData.current;

            if (weather.temperature >= 20 && weather.temperature <= 28) {
                reasons.push('ظروف درجة حرارة مثالية');
            }

            if (weather.humidity >= 40 && weather.humidity <= 70) {
                reasons.push('مستويات رطوبة مثلى');
            }

            if (weatherData.forecast) {
                const avgPrecipitation = weatherData.forecast
                    .slice(0, 7)
                    .reduce((sum, day) => sum + day.precipitation, 0) / 7;

                if (avgPrecipitation >= 2 && avgPrecipitation <= 8) {
                    reasons.push('توقعات أمطار مناسبة');
                }
            }
        }

        return reasons;
    }

    private static estimateYield(crop: CropType): number {
        // Simplified yield estimation (kg per square meter)
        const yieldMap: Record<string, number> = {
            corn: 2.5,
            wheat: 1.8,
            rice: 2.0,
            tomatoes: 8.0,
            peppers: 3.5,
            onions: 4.0,
            carrots: 5.0,
            potatoes: 6.0,
            strawberries: 2.0,
            watermelon: 15.0,
            soybeans: 1.5,
            beans: 2.0,
            basil: 0.5,
            mint: 0.3,
        };
        return yieldMap[crop.id] || 2.0;
    }

    private static getDifficultyRating(difficulty: string): number {
        const ratingMap: Record<string, number> = {
            beginner: 2,
            intermediate: 3,
            expert: 5,
        };
        return ratingMap[difficulty] || 3;
    }

    private static getWaterRequirement(crop: CropType): 'low' | 'medium' | 'high' {
        const waterMap: Record<string, 'low' | 'medium' | 'high'> = {
            corn: 'high',
            wheat: 'medium',
            rice: 'high',
            tomatoes: 'high',
            peppers: 'medium',
            onions: 'low',
            carrots: 'medium',
            potatoes: 'medium',
            strawberries: 'medium',
            watermelon: 'high',
            soybeans: 'medium',
            beans: 'medium',
            basil: 'medium',
            mint: 'high',
        };
        return waterMap[crop.id] || 'medium';
    }

    private static getSoilTypes(crop: CropType): string[] {
        const soilMap: Record<string, string[]> = {
            corn: ['loamy', 'sandy loam'],
            wheat: ['clay loam', 'loamy'],
            rice: ['clay', 'clay loam'],
            tomatoes: ['loamy', 'sandy loam'],
            peppers: ['loamy', 'sandy loam'],
            onions: ['sandy loam', 'loamy'],
            carrots: ['sandy', 'sandy loam'],
            potatoes: ['sandy loam', 'loamy'],
            strawberries: ['sandy loam', 'loamy'],
            watermelon: ['sandy loam', 'sandy'],
            soybeans: ['loamy', 'clay loam'],
            beans: ['loamy', 'sandy loam'],
            basil: ['loamy', 'sandy loam'],
            mint: ['loamy', 'clay loam'],
        };
        return soilMap[crop.id] || ['loamy'];
    }
}