import React, { useState } from 'react';
import { TextInput, View, Text, Pressable } from 'react-native';
import { useAccessibility } from '../../hooks/useAccessibility';
import { useFocusable } from '../../utils/keyboardNavigation';

export interface InputProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  error?: string;
  disabled?: boolean;
  secureTextEntry?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  multiline?: boolean;
  numberOfLines?: number;
  maxLength?: number;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onRightIconPress?: () => void;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  testID?: string;
  // Enhanced voice input capabilities
  voiceInputEnabled?: boolean;
  onVoiceInput?: () => void;
  voiceFeedbackEnabled?: boolean;
  onVoiceFeedback?: (text: string) => void;
  isVoiceRecording?: boolean;
}

export const Input: React.FC<InputProps> = ({
  label,
  placeholder,
  value,
  onChangeText,
  error,
  disabled = false,
  secureTextEntry = false,
  keyboardType = 'default',
  autoCapitalize = 'sentences',
  multiline = false,
  numberOfLines = 1,
  maxLength,
  leftIcon,
  rightIcon,
  onRightIconPress,
  accessibilityLabel,
  accessibilityHint,
  testID,
  voiceInputEnabled = false,
  onVoiceInput,
  voiceFeedbackEnabled = false,
  onVoiceFeedback,
  isVoiceRecording = false,
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const {
    getScaledFontSize,
    getScaledTouchTarget,
    getHighContrastColors,
    isVoiceEnabled,
    speakText,
    announceToScreenReader,
    getAccessibilityProps
  } = useAccessibility();

  const inputId = testID || `input-${label?.toLowerCase().replace(/\s+/g, '-')}`;
  const { ref: focusableRef } = useFocusable(inputId, 0, {
    disabled,
    onFocus: () => {
      setIsFocused(true);
      if (isVoiceEnabled) {
        speakText(`${label || 'Input'} field focused`);
      }
    },
    onBlur: () => setIsFocused(false),
  });

  const highContrastColors = getHighContrastColors();

  const getBorderColor = () => {
    if (highContrastColors) {
      if (error) return highContrastColors.error;
      if (isFocused) return highContrastColors.primary;
      return highContrastColors.foreground;
    }

    if (error) return '#ef4444';
    if (isFocused) return '#22c55e';
    return '#d6d3d1';
  };

  const getBackgroundColor = () => {
    if (highContrastColors) {
      return disabled ? highContrastColors.background : highContrastColors.background;
    }

    if (disabled) return '#f5f5f4';
    return '#ffffff';
  };

  const getTextColor = () => {
    if (highContrastColors) {
      return disabled ? highContrastColors.foreground + '80' : highContrastColors.foreground;
    }

    return disabled ? '#78716c' : '#1c1917';
  };

  const minHeight = getScaledTouchTarget(multiline ? 100 : 56);
  const fontSize = getScaledFontSize(16);
  const labelFontSize = getScaledFontSize(14);

  return (
    <View className="w-full">
      {label && (
        <Text
          style={{
            fontSize: labelFontSize,
            fontWeight: '500',
            color: getTextColor(),
            marginBottom: 8
          }}
          {...getAccessibilityProps(`${label} field label`, 'Label for input field')}
        >
          {label}
        </Text>
      )}

      <View
        style={{
          borderColor: getBorderColor(),
          backgroundColor: getBackgroundColor(),
          borderWidth: 2,
          borderRadius: 12,
          minHeight,
          paddingHorizontal: 16,
          paddingVertical: 12,
          flexDirection: 'row',
          alignItems: multiline ? 'flex-start' : 'center',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.05,
          shadowRadius: 2,
          elevation: 1,
        }}
      >
        {leftIcon && <View style={{ marginRight: 12 }}>{leftIcon}</View>}

        <TextInput
          ref={focusableRef}
          value={value}
          onChangeText={(text) => {
            onChangeText(text);
            if (voiceFeedbackEnabled && onVoiceFeedback && text) {
              onVoiceFeedback(`Input changed to ${text}`);
            } else if (isVoiceEnabled && text) {
              announceToScreenReader(`Input changed to ${text}`);
            }
          }}
          placeholder={placeholder}
          placeholderTextColor={highContrastColors?.foreground + '60' || '#9ca3af'}
          editable={!disabled}
          secureTextEntry={secureTextEntry}
          keyboardType={keyboardType}
          autoCapitalize={autoCapitalize}
          multiline={multiline}
          numberOfLines={numberOfLines}
          maxLength={maxLength}
          onFocus={() => {
            setIsFocused(true);
            if (voiceFeedbackEnabled && onVoiceFeedback) {
              onVoiceFeedback(`${label || 'Input'} field focused`);
            } else if (isVoiceEnabled) {
              speakText(`${label || 'Input'} field focused`);
            }
          }}
          onBlur={() => setIsFocused(false)}
          {...getAccessibilityProps(
            accessibilityLabel || label || 'Text input',
            accessibilityHint || `Enter ${label?.toLowerCase() || 'text'}`
          )}
          testID={testID}
          style={{
            flex: 1,
            fontSize,
            color: getTextColor(),
            textAlignVertical: multiline ? 'top' : 'center',
            minHeight: multiline ? 80 : undefined,
          }}
        />

        {voiceInputEnabled && onVoiceInput && (
          <Pressable
            onPress={async () => {
              const message = isVoiceRecording ? 'Stopping voice input' : 'Starting voice input';
              if (voiceFeedbackEnabled && onVoiceFeedback) {
                onVoiceFeedback(message);
              } else if (isVoiceEnabled) {
                await speakText(message);
              }
              onVoiceInput();
            }}
            style={{
              marginLeft: 12,
              minHeight: getScaledTouchTarget(44),
              minWidth: getScaledTouchTarget(44),
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 8,
              padding: 12,
              backgroundColor: isVoiceRecording
                ? (highContrastColors?.error + '20' || '#fecaca')
                : (highContrastColors?.primary + '20' || '#dcfce7'),
            }}
            {...getAccessibilityProps(
              isVoiceRecording ? 'Stop voice input' : 'Start voice input',
              'Tap to use voice input for this field'
            )}
          >
            <Text
              style={{
                fontSize: getScaledFontSize(18),
                fontWeight: '500',
                color: isVoiceRecording
                  ? (highContrastColors?.error || '#dc2626')
                  : (highContrastColors?.primary || '#16a34a'),
              }}
            >
              {isVoiceRecording ? '🔴' : '🎤'}
            </Text>
          </Pressable>
        )}

        {rightIcon && (
          <Pressable
            onPress={onRightIconPress}
            style={{ marginLeft: 12 }}
            disabled={!onRightIconPress}
            {...getAccessibilityProps('Additional action', 'Tap for additional options')}
          >
            {rightIcon}
          </Pressable>
        )}
      </View>

      {error && (
        <Text
          style={{
            marginLeft: 4,
            marginTop: 4,
            fontSize: getScaledFontSize(12),
            color: highContrastColors?.error || '#ef4444'
          }}
          {...getAccessibilityProps(`Error: ${error}`, 'Input validation error message')}
        >
          {error}
        </Text>
      )}
    </View>
  );
};
