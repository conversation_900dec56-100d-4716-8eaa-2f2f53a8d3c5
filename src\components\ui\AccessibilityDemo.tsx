/**
 * Accessibility Demo Component
 * Demonstrates and tests all accessibility features
 */

import React, { useState } from 'react';
import { View, Text, ScrollView, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAccessibility } from '../../hooks/useAccessibility';
import { useAccessibilityTesting } from '../../utils/accessibilityTesting';
import { Button } from './Button';
import { Input } from './Input';
import { AccessibilitySettings } from './AccessibilitySettings';

export const AccessibilityDemo: React.FC = () => {
    const [showSettings, setShowSettings] = useState(false);
    const [inputValue, setInputValue] = useState('');
    const [testResults, setTestResults] = useState<string>('');

    const {
        config,
        isVoiceEnabled,
        isHighContrastEnabled,
        isLargeTextEnabled,
        fontScale,
        speakText,
        announceToScreenReader,
        getAccessibilityProps,
        getScaledFontSize,
        getHighContrastColors,
    } = useAccessibility();

    const { testComponent, generateReport } = useAccessibilityTesting();

    const highContrastColors = getHighContrastColors();
    const backgroundColor = highContrastColors?.background || '#ffffff';
    const textColor = highContrastColors?.foreground || '#1c1917';
    const primaryColor = highContrastColors?.primary || '#22c55e';

    const runAccessibilityTests = () => {
        const testSuites = [
            testComponent(
                'Demo Button',
                {
                    accessibilityLabel: 'Test button',
                    accessibilityHint: 'Tap to test button functionality',
                    onPress: () => { },
                },
                { width: 120, height: 48 },
                { foreground: textColor, background: primaryColor },
                getScaledFontSize(16)
            ),
            testComponent(
                'Demo Input',
                {
                    accessibilityLabel: 'Test input field',
                    accessibilityHint: 'Enter test text',
                    value: inputValue,
                    onChangeText: setInputValue,
                },
                { width: 300, height: 56 },
                { foreground: textColor, background: backgroundColor },
                getScaledFontSize(16)
            ),
        ];

        const report = generateReport(testSuites);
        setTestResults(report);

        Alert.alert(
            'Accessibility Test Complete',
            `Tests completed. Overall score: ${Math.round(
                testSuites.reduce((sum, suite) => sum + suite.overallScore, 0) / testSuites.length
            )}%`,
            [{ text: 'OK' }]
        );
    };

    const testVoiceFeatures = async () => {
        await speakText('Testing voice features. This is a comprehensive test of the text-to-speech system.');
        announceToScreenReader('Voice test completed successfully');
    };

    const testScreenReaderFeatures = () => {
        announceToScreenReader('Testing screen reader announcements. This message should be read by your screen reader.', 'high');
    };

    const testKeyboardNavigation = () => {
        announceToScreenReader('Keyboard navigation test. Use Tab to move between elements, Enter to activate.');
    };

    if (showSettings) {
        return <AccessibilitySettings onClose={() => setShowSettings(false)} />;
    }

    return (
        <ScrollView
            style={{ backgroundColor }}
            contentContainerStyle={{ padding: 16 }}
            {...getAccessibilityProps('Accessibility demo screen', 'Test and demonstrate accessibility features')}
        >
            <View className="mb-6">
                <Text
                    style={{
                        fontSize: getScaledFontSize(28),
                        fontWeight: 'bold',
                        color: textColor,
                        marginBottom: 8
                    }}
                    {...getAccessibilityProps('Accessibility Demo', 'Main heading for accessibility demonstration')}
                >
                    🌾 Accessibility Demo
                </Text>
                <Text
                    style={{
                        fontSize: getScaledFontSize(16),
                        color: textColor,
                        opacity: 0.8,
                        marginBottom: 16
                    }}
                    {...getAccessibilityProps('Test and demonstrate all accessibility features', 'Description of demo screen')}
                >
                    Test and demonstrate all accessibility features for agricultural users
                </Text>
            </View>

            {/* Current Settings Display */}
            <View
                className="mb-6 p-4 rounded-xl border-2"
                style={{ borderColor: primaryColor, backgroundColor: backgroundColor }}
            >
                <Text
                    style={{
                        fontSize: getScaledFontSize(18),
                        fontWeight: '600',
                        color: textColor,
                        marginBottom: 12
                    }}
                    {...getAccessibilityProps('Current Settings', 'Display of current accessibility settings')}
                >
                    📊 Current Settings
                </Text>

                <View className="space-y-2">
                    <Text style={{ fontSize: getScaledFontSize(14), color: textColor }}>
                        🗣️ Voice Mode: {isVoiceEnabled ? '✅ Enabled' : '❌ Disabled'}
                    </Text>
                    <Text style={{ fontSize: getScaledFontSize(14), color: textColor }}>
                        🎨 High Contrast: {isHighContrastEnabled ? '✅ Enabled' : '❌ Disabled'}
                    </Text>
                    <Text style={{ fontSize: getScaledFontSize(14), color: textColor }}>
                        📝 Large Text: {isLargeTextEnabled ? '✅ Enabled' : '❌ Disabled'}
                    </Text>
                    <Text style={{ fontSize: getScaledFontSize(14), color: textColor }}>
                        🔍 Font Scale: {Math.round(fontScale * 100)}%
                    </Text>
                </View>
            </View>

            {/* Interactive Test Components */}
            <View
                className="mb-6 p-4 rounded-xl border-2"
                style={{ borderColor: primaryColor, backgroundColor: backgroundColor }}
            >
                <Text
                    style={{
                        fontSize: getScaledFontSize(18),
                        fontWeight: '600',
                        color: textColor,
                        marginBottom: 16
                    }}
                    {...getAccessibilityProps('Interactive Tests', 'Section for testing interactive components')}
                >
                    🧪 Interactive Tests
                </Text>

                <View className="space-y-4">
                    <Button
                        title="Test Voice Features"
                        onPress={testVoiceFeatures}
                        variant="primary"
                        size="medium"
                        fullWidth
                        icon={<Ionicons name="volume-high" size={20} color="white" />}
                        accessibilityLabel="Test voice features"
                        accessibilityHint="Tap to test text-to-speech functionality"
                    />

                    <Button
                        title="Test Screen Reader"
                        onPress={testScreenReaderFeatures}
                        variant="secondary"
                        size="medium"
                        fullWidth
                        icon={<Ionicons name="accessibility" size={20} color="white" />}
                        accessibilityLabel="Test screen reader"
                        accessibilityHint="Tap to test screen reader announcements"
                    />

                    <Button
                        title="Test Keyboard Navigation"
                        onPress={testKeyboardNavigation}
                        variant="outline"
                        size="medium"
                        fullWidth
                        icon={<Ionicons name="keypad" size={20} color={primaryColor} />}
                        accessibilityLabel="Test keyboard navigation"
                        accessibilityHint="Tap to test keyboard navigation features"
                    />

                    <Input
                        label="Test Input Field"
                        placeholder="Type something to test input accessibility..."
                        value={inputValue}
                        onChangeText={setInputValue}
                        voiceInputEnabled={isVoiceEnabled}
                        voiceFeedbackEnabled={isVoiceEnabled}
                        accessibilityLabel="Test input field"
                        accessibilityHint="Enter text to test input accessibility features"
                    />
                </View>
            </View>

            {/* Accessibility Testing */}
            <View
                className="mb-6 p-4 rounded-xl border-2"
                style={{ borderColor: primaryColor, backgroundColor: backgroundColor }}
            >
                <Text
                    style={{
                        fontSize: getScaledFontSize(18),
                        fontWeight: '600',
                        color: textColor,
                        marginBottom: 16
                    }}
                    {...getAccessibilityProps('Accessibility Testing', 'Section for running accessibility tests')}
                >
                    🔍 Accessibility Testing
                </Text>

                <Button
                    title="Run Accessibility Tests"
                    onPress={runAccessibilityTests}
                    variant="primary"
                    size="large"
                    fullWidth
                    icon={<Ionicons name="checkmark-circle" size={20} color="white" />}
                    accessibilityLabel="Run accessibility tests"
                    accessibilityHint="Tap to run comprehensive accessibility validation tests"
                />

                {testResults && (
                    <View
                        className="mt-4 p-3 rounded-lg"
                        style={{ backgroundColor: isHighContrastEnabled ? highContrastColors?.background : '#f5f5f4' }}
                    >
                        <Text
                            style={{
                                fontSize: getScaledFontSize(12),
                                color: textColor,
                                fontFamily: 'monospace'
                            }}
                            {...getAccessibilityProps('Test results', 'Results from accessibility testing')}
                        >
                            {testResults}
                        </Text>
                    </View>
                )}
            </View>

            {/* Touch Target Examples */}
            <View
                className="mb-6 p-4 rounded-xl border-2"
                style={{ borderColor: primaryColor, backgroundColor: backgroundColor }}
            >
                <Text
                    style={{
                        fontSize: getScaledFontSize(18),
                        fontWeight: '600',
                        color: textColor,
                        marginBottom: 16
                    }}
                    {...getAccessibilityProps('Touch Target Examples', 'Examples of different touch target sizes')}
                >
                    👆 Touch Target Examples
                </Text>

                <View className="flex-row justify-around items-center">
                    <Button
                        title="Small"
                        onPress={() => speakText('Small button pressed')}
                        variant="outline"
                        size="small"
                        accessibilityLabel="Small touch target example"
                        accessibilityHint="Example of minimum touch target size"
                    />

                    <Button
                        title="Medium"
                        onPress={() => speakText('Medium button pressed')}
                        variant="outline"
                        size="medium"
                        accessibilityLabel="Medium touch target example"
                        accessibilityHint="Example of comfortable touch target size"
                    />

                    <Button
                        title="Large"
                        onPress={() => speakText('Large button pressed')}
                        variant="outline"
                        size="large"
                        accessibilityLabel="Large touch target example"
                        accessibilityHint="Example of large touch target size for work gloves"
                    />
                </View>
            </View>

            {/* Settings Access */}
            <View className="mb-6">
                <Button
                    title="Open Accessibility Settings"
                    onPress={() => setShowSettings(true)}
                    variant="primary"
                    size="large"
                    fullWidth
                    icon={<Ionicons name="settings" size={20} color="white" />}
                    accessibilityLabel="Open accessibility settings"
                    accessibilityHint="Tap to configure accessibility options"
                />
            </View>

            {/* Agricultural Context Examples */}
            <View
                className="mb-6 p-4 rounded-xl border-2"
                style={{ borderColor: primaryColor, backgroundColor: backgroundColor }}
            >
                <Text
                    style={{
                        fontSize: getScaledFontSize(18),
                        fontWeight: '600',
                        color: textColor,
                        marginBottom: 16
                    }}
                    {...getAccessibilityProps('Agricultural Context', 'Examples in agricultural context')}
                >
                    🚜 Agricultural Context Examples
                </Text>

                <View className="space-y-3">
                    <Button
                        title="🌱 Check Crop Health"
                        onPress={() => speakText('Checking crop health. Please take a photo of your plants.')}
                        variant="primary"
                        size="medium"
                        fullWidth
                        accessibilityLabel="Check crop health"
                        accessibilityHint="Tap to start crop health analysis"
                    />

                    <Button
                        title="🌦️ Weather Alert"
                        onPress={() => speakText('Weather alert: Rain expected tomorrow. Consider covering sensitive crops.')}
                        variant="secondary"
                        size="medium"
                        fullWidth
                        accessibilityLabel="Weather alert"
                        accessibilityHint="Tap to hear weather information"
                    />

                    <Button
                        title="📋 Daily Tasks"
                        onPress={() => speakText('You have 3 tasks today: Water tomatoes, check corn growth, apply fertilizer.')}
                        variant="outline"
                        size="medium"
                        fullWidth
                        accessibilityLabel="Daily tasks"
                        accessibilityHint="Tap to hear today's farming tasks"
                    />
                </View>
            </View>

            <View className="pb-8">
                <Text
                    style={{
                        fontSize: getScaledFontSize(12),
                        color: textColor,
                        opacity: 0.6,
                        textAlign: 'center'
                    }}
                    {...getAccessibilityProps('Demo information', 'Information about the accessibility demo')}
                >
                    This demo showcases accessibility features designed for agricultural users with varying technical literacy levels.
                </Text>
            </View>
        </ScrollView>
    );
};