import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, TextInput, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { CropType, cropTypes } from '../../src/data/crops';
import { CropRecommendation, CreateCropPlanData } from '../../src/types/crops';
import { CropPlanningService } from '../../src/services/cropPlanning';
import CropSelectionGrid from '../../src/components/crops/CropSelectionGrid';
import PlantingDatePicker from '../../src/components/crops/PlantingDatePicker';
import LocationPicker from '../../src/components/crops/LocationPicker';
import { Button } from '../../src/components/ui';

export default function CreateCropPlanScreen() {
    const [currentStep, setCurrentStep] = useState(1);
    const [selectedCrop, setSelectedCrop] = useState<CropType | null>(null);
    const [plantingDate, setPlantingDate] = useState(new Date());
    const [location, setLocation] = useState<{
        latitude: number;
        longitude: number;
        address?: string;
    } | null>(null);
    const [notes, setNotes] = useState('');
    const [expectedYield, setExpectedYield] = useState('');
    const [recommendations, setRecommendations] = useState<CropRecommendation[]>([]);
    const [voiceEnabled, setVoiceEnabled] = useState(false);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        // Load crop recommendations when location is available
        if (location) {
            loadCropRecommendations();
        }
    }, [location]);

    const loadCropRecommendations = () => {
        if (!location) return;

        try {
            const recs = CropPlanningService.getCropRecommendations(
                location,
                'beginner', // TODO: Get from user profile
                new Date()
            );
            setRecommendations(recs);
        } catch (error) {
            console.error('Error loading crop recommendations:', error);
        }
    };

    const handleCropSelect = (crop: CropType) => {
        setSelectedCrop(crop);
    };

    const handleLocationChange = (newLocation: {
        latitude: number;
        longitude: number;
        address?: string;
    }) => {
        setLocation(newLocation);
    };

    const handleVoiceCommand = (command: string) => {
        // TODO: Implement voice commands
        console.log('Voice command:', command);
    };

    const validateStep = (step: number): boolean => {
        switch (step) {
            case 1:
                return selectedCrop !== null;
            case 2:
                return plantingDate !== null;
            case 3:
                return location !== null;
            case 4:
                return true; // Notes and yield are optional
            default:
                return false;
        }
    };

    const nextStep = () => {
        if (!validateStep(currentStep)) {
            Alert.alert(
                'Incomplete Step',
                'Please complete the current step before proceeding.',
                [{ text: 'OK' }]
            );
            return;
        }

        if (currentStep < 4) {
            setCurrentStep(currentStep + 1);
        }
    };

    const previousStep = () => {
        if (currentStep > 1) {
            setCurrentStep(currentStep - 1);
        }
    };

    const createCropPlan = async () => {
        if (!selectedCrop || !location) {
            Alert.alert('Error', 'Please complete all required fields.');
            return;
        }

        setLoading(true);
        try {
            const planData: CreateCropPlanData = {
                cropType: selectedCrop.id,
                plantingDate,
                location,
                notes: notes.trim() || undefined,
                expectedYield: expectedYield ? parseFloat(expectedYield) : undefined,
            };

            const cropPlan = CropPlanningService.createCropPlan(planData);

            // TODO: Save to database/state management
            console.log('Created crop plan:', cropPlan);

            Alert.alert(
                'Success!',
                `Your ${selectedCrop.name} crop plan has been created successfully.`,
                [
                    {
                        text: 'View Plan',
                        onPress: () => router.push(`/crops/plan/${cropPlan.id}`),
                    },
                    {
                        text: 'Create Another',
                        onPress: () => {
                            // Reset form
                            setCurrentStep(1);
                            setSelectedCrop(null);
                            setPlantingDate(new Date());
                            setNotes('');
                            setExpectedYield('');
                        },
                    },
                ]
            );
        } catch (error) {
            console.error('Error creating crop plan:', error);
            Alert.alert('Error', 'Failed to create crop plan. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const getStepTitle = (step: number): string => {
        switch (step) {
            case 1: return 'Choose Your Crop';
            case 2: return 'Select Planting Date';
            case 3: return 'Set Farm Location';
            case 4: return 'Additional Details';
            default: return '';
        }
    };

    const getStepDescription = (step: number): string => {
        switch (step) {
            case 1: return 'Select the crop you want to grow';
            case 2: return 'Choose when to plant for optimal growth';
            case 3: return 'Set your farm location for personalized recommendations';
            case 4: return 'Add notes and expected yield (optional)';
            default: return '';
        }
    };

    const renderStepContent = () => {
        switch (currentStep) {
            case 1:
                return (
                    <CropSelectionGrid
                        selectedCropId={selectedCrop?.id}
                        onCropSelect={handleCropSelect}
                        recommendations={recommendations}
                        showRecommendations={location !== null}
                        voiceEnabled={voiceEnabled}
                        onVoiceCommand={handleVoiceCommand}
                    />
                );

            case 2:
                return (
                    <PlantingDatePicker
                        selectedDate={plantingDate}
                        onDateChange={setPlantingDate}
                        selectedCrop={selectedCrop || undefined}
                        location={location || undefined}
                        voiceEnabled={voiceEnabled}
                        onVoiceCommand={handleVoiceCommand}
                    />
                );

            case 3:
                return (
                    <LocationPicker
                        selectedLocation={location || undefined}
                        onLocationChange={handleLocationChange}
                        voiceEnabled={voiceEnabled}
                        onVoiceCommand={handleVoiceCommand}
                    />
                );

            case 4:
                return (
                    <View className="gap-4">
                        {/* Notes Input */}
                        <View className="bg-white rounded-lg p-4 border border-gray-200">
                            <Text className="text-lg font-semibold text-gray-900 mb-2">
                                Notes (Optional)
                            </Text>
                            <TextInput
                                className="border border-gray-300 rounded-lg p-3 text-base text-gray-900 min-h-[100px]"
                                placeholder="Add any notes about this crop plan..."
                                placeholderTextColor="#9CA3AF"
                                value={notes}
                                onChangeText={setNotes}
                                multiline
                                textAlignVertical="top"
                                accessibilityLabel="Crop plan notes"
                                accessibilityHint="Add any additional notes about your crop plan"
                            />
                        </View>

                        {/* Expected Yield Input */}
                        <View className="bg-white rounded-lg p-4 border border-gray-200">
                            <Text className="text-lg font-semibold text-gray-900 mb-2">
                                Expected Yield (Optional)
                            </Text>
                            <View className="flex-row items-center">
                                <TextInput
                                    className="flex-1 border border-gray-300 rounded-lg p-3 text-base text-gray-900"
                                    placeholder="0"
                                    placeholderTextColor="#9CA3AF"
                                    value={expectedYield}
                                    onChangeText={setExpectedYield}
                                    keyboardType="numeric"
                                    accessibilityLabel="Expected yield"
                                    accessibilityHint="Enter expected yield in kilograms"
                                />
                                <Text className="ml-3 text-base text-gray-600">kg</Text>
                            </View>
                            <Text className="text-sm text-gray-500 mt-2">
                                Estimated yield based on your location and crop type
                            </Text>
                        </View>

                        {/* Plan Summary */}
                        {selectedCrop && location && (
                            <View className="bg-green-50 rounded-lg p-4 border border-green-200">
                                <Text className="text-lg font-semibold text-green-800 mb-3">
                                    📋 Plan Summary
                                </Text>
                                <View className="gap-2">
                                    <View className="flex-row justify-between">
                                        <Text className="text-sm text-green-700">Crop:</Text>
                                        <Text className="text-sm font-medium text-green-800">
                                            {selectedCrop.icon} {selectedCrop.name}
                                        </Text>
                                    </View>
                                    <View className="flex-row justify-between">
                                        <Text className="text-sm text-green-700">Planting Date:</Text>
                                        <Text className="text-sm font-medium text-green-800">
                                            {plantingDate.toLocaleDateString()}
                                        </Text>
                                    </View>
                                    <View className="flex-row justify-between">
                                        <Text className="text-sm text-green-700">Location:</Text>
                                        <Text className="text-sm font-medium text-green-800">
                                            {location.address || 'Custom Location'}
                                        </Text>
                                    </View>
                                    {expectedYield && (
                                        <View className="flex-row justify-between">
                                            <Text className="text-sm text-green-700">Expected Yield:</Text>
                                            <Text className="text-sm font-medium text-green-800">
                                                {expectedYield} kg
                                            </Text>
                                        </View>
                                    )}
                                </View>
                            </View>
                        )}
                    </View>
                );

            default:
                return null;
        }
    };

    return (
        <SafeAreaView className="flex-1 bg-gray-50">
            {/* Header */}
            <View className="bg-white border-b border-gray-200 px-4 py-3">
                <View className="flex-row items-center justify-between">
                    <TouchableOpacity
                        onPress={() => router.back()}
                        className="p-2"
                        accessibilityLabel="Go back"
                    >
                        <Text className="text-2xl">←</Text>
                    </TouchableOpacity>

                    <View className="flex-1 mx-4">
                        <Text className="text-lg font-semibold text-gray-900 text-center">
                            Create Crop Plan
                        </Text>
                        <Text className="text-sm text-gray-500 text-center">
                            Step {currentStep} of 4
                        </Text>
                    </View>

                    <TouchableOpacity
                        onPress={() => setVoiceEnabled(!voiceEnabled)}
                        className={`p-2 rounded-full ${voiceEnabled ? 'bg-green-500' : 'bg-gray-200'}`}
                        accessibilityLabel="Toggle voice mode"
                    >
                        <Text className={`text-sm ${voiceEnabled ? 'text-white' : 'text-gray-600'}`}>
                            🎤
                        </Text>
                    </TouchableOpacity>
                </View>

                {/* Progress Bar */}
                <View className="mt-3 bg-gray-200 rounded-full h-2">
                    <View
                        className="bg-green-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(currentStep / 4) * 100}%` }}
                    />
                </View>
            </View>

            {/* Step Title */}
            <View className="bg-white px-4 py-3 border-b border-gray-100">
                <Text className="text-xl font-bold text-gray-900">
                    {getStepTitle(currentStep)}
                </Text>
                <Text className="text-sm text-gray-600 mt-1">
                    {getStepDescription(currentStep)}
                </Text>
            </View>

            {/* Step Content */}
            <ScrollView className="flex-1 p-4">
                {renderStepContent()}
            </ScrollView>

            {/* Navigation Buttons */}
            <View className="bg-white border-t border-gray-200 p-4">
                <View className="flex-row justify-between">
                    <Button
                        title="Previous"
                        onPress={previousStep}
                        variant="outline"
                        disabled={currentStep === 1}
                        className="flex-1 mr-2"
                    />

                    {currentStep < 4 ? (
                        <Button
                            title="Next"
                            onPress={nextStep}
                            disabled={!validateStep(currentStep)}
                            className="flex-1 ml-2"
                        />
                    ) : (
                        <Button
                            title="Create Plan"
                            onPress={createCropPlan}
                            loading={loading}
                            disabled={!validateStep(currentStep)}
                            className="flex-1 ml-2"
                        />
                    )}
                </View>
            </View>
        </SafeAreaView>
    );
}