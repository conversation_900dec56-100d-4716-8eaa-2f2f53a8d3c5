/**
 * Accessibility Test Screen
 * Comprehensive testing interface for all accessibility features
 */

import React, { useState } from 'react';
import { View, ScrollView, Alert, Share } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAccessibility } from '../../hooks/useAccessibility';
import { accessibilityTestSuite, AccessibilityAuditResult, ComponentTestConfig } from '../../utils/accessibilityTestSuite';
import { AccessibleText } from './AccessibleText';
import { AccessibleView } from './AccessibleView';
import { Button } from './Button';
import { Input } from './Input';
import { AccessibilityValidator } from './AccessibilityValidator';

export const AccessibilityTestScreen: React.FC = () => {
    const [auditResult, setAuditResult] = useState<AccessibilityAuditResult | null>(null);
    const [isRunningAudit, setIsRunningAudit] = useState(false);
    const [testInput, setTestInput] = useState('');

    const {
        config,
        getScaledFontSize,
        getHighContrastColors,
        getAccessibilityProps,
        announceToScreenReader,
        provideFeedback,
        toggleVoiceMode,
        toggleHighContrast,
        toggleLargeText,
    } = useAccessibility();

    const highContrastColors = getHighContrastColors();
    const backgroundColor = highContrastColors?.background || '#ffffff';
    const textColor = highContrastColors?.foreground || '#1c1917';

    const runComprehensiveAudit = async () => {
        setIsRunningAudit(true);
        announceToScreenReader('Starting comprehensive accessibility audit');

        try {
            // Define test components
            const testComponents: ComponentTestConfig[] = [
                {
                    name: 'Primary Button',
                    props: {
                        accessibilityLabel: 'Primary action button',
                        accessibilityHint: 'Tap to perform primary action',
                        onPress: () => { },
                    },
                    dimensions: { width: 200, height: 56 },
                    colors: {
                        foreground: '#ffffff',
                        background: config.highContrast ? '#00FF00' : '#22c55e'
                    },
                    fontSize: getScaledFontSize(16),
                    isInteractive: true,
                    hasText: true,
                },
                {
                    name: 'Text Input',
                    props: {
                        accessibilityLabel: 'Text input field',
                        accessibilityHint: 'Enter text here',
                        value: testInput,
                        onChangeText: setTestInput,
                    },
                    dimensions: { width: 300, height: 56 },
                    colors: { foreground: textColor, background: backgroundColor },
                    fontSize: getScaledFontSize(16),
                    isInteractive: true,
                    isFormElement: true,
                },
                {
                    name: 'Heading Text',
                    props: {
                        accessibilityLabel: 'Main heading',
                        accessibilityRole: 'header',
                    },
                    colors: { foreground: textColor, background: backgroundColor },
                    fontSize: getScaledFontSize(24),
                    hasText: true,
                },
                {
                    name: 'Navigation Item',
                    props: {
                        accessibilityLabel: 'Navigation item',
                        accessibilityHint: 'Tap to navigate',
                        accessibilityRole: 'button',
                        onPress: () => { },
                    },
                    dimensions: { width: 120, height: 48 },
                    colors: { foreground: textColor, background: backgroundColor },
                    fontSize: getScaledFontSize(14),
                    isInteractive: true,
                },
            ];

            // Run full audit
            const result = await accessibilityTestSuite.runFullAudit(testComponents);

            // Test agricultural use cases
            const agriculturalTests = await accessibilityTestSuite.testAgriculturalUseCases();

            setAuditResult(result);

            const message = `Accessibility audit complete. Overall score: ${result.overallScore}%`;
            announceToScreenReader(message);

            if (result.overallScore >= 90) {
                await provideFeedback('success', 'Excellent accessibility score!');
            } else if (result.overallScore >= 70) {
                await provideFeedback('warning', 'Good accessibility with room for improvement');
            } else {
                await provideFeedback('error', 'Accessibility needs significant improvement');
            }

        } catch (error) {
            console.error('Audit failed:', error);
            await provideFeedback('error', 'Accessibility audit failed');
        } finally {
            setIsRunningAudit(false);
        }
    };

    const shareAuditReport = async () => {
        if (!auditResult) return;

        try {
            const report = accessibilityTestSuite.generateDetailedReport(auditResult);

            await Share.share({
                message: report,
                title: 'Accessibility Audit Report',
            });

            announceToScreenReader('Accessibility report shared');
        } catch (error) {
            console.error('Failed to share report:', error);
            await provideFeedback('error', 'Failed to share report');
        }
    };

    const testVoiceFeatures = async () => {
        await provideFeedback('info', 'Testing voice features. This message should be spoken aloud if voice mode is enabled.');
    };

    const testScreenReaderFeatures = () => {
        announceToScreenReader('Testing screen reader announcements. This message should be read by your screen reader.', 'high');
    };

    const testHapticFeedback = async () => {
        await provideFeedback('success', 'Testing haptic feedback');
    };

    return (
        <ScrollView
            style={{ backgroundColor }}
            contentContainerStyle={{ padding: 16 }}
            {...getAccessibilityProps('Accessibility test screen', 'Comprehensive accessibility testing interface')}
        >
            {/* Header */}
            <AccessibleView semanticRole="banner">
                <AccessibleText
                    variant="h1"
                    semanticRole="header"
                    accessibilityLabel="Accessibility Testing Center"
                >
                    🔍 Accessibility Testing
                </AccessibleText>
                <AccessibleText
                    variant="body"
                    style={{ marginTop: 8, opacity: 0.8 }}
                >
                    Comprehensive testing for all accessibility features
                </AccessibleText>
            </AccessibleView>

            {/* Current Settings */}
            <AccessibleView
                style={{
                    marginTop: 24,
                    padding: 16,
                    borderRadius: 12,
                    backgroundColor: config.highContrast ? highContrastColors?.focusBackground : '#f9fafb',
                    borderWidth: 1,
                    borderColor: config.highContrast ? highContrastColors?.foreground : '#e5e7eb',
                }}
                semanticRole="region"
                accessibilityLabel="Current accessibility settings"
            >
                <AccessibleText variant="h3" style={{ marginBottom: 12 }}>
                    📊 Current Settings
                </AccessibleText>

                <View style={{ gap: 8 }}>
                    <AccessibleText variant="bodySmall">
                        🗣️ Voice Mode: {config.voiceEnabled ? '✅ Enabled' : '❌ Disabled'}
                    </AccessibleText>
                    <AccessibleText variant="bodySmall">
                        🎨 High Contrast: {config.highContrast ? '✅ Enabled' : '❌ Disabled'}
                    </AccessibleText>
                    <AccessibleText variant="bodySmall">
                        📝 Large Text: {config.largeText ? '✅ Enabled' : '❌ Disabled'}
                    </AccessibleText>
                    <AccessibleText variant="bodySmall">
                        🔍 Font Scale: {Math.round(config.fontScale * 100)}%
                    </AccessibleText>
                    <AccessibleText variant="bodySmall">
                        ⌨️ Keyboard Navigation: {config.keyboardNavigation ? '✅ Enabled' : '❌ Disabled'}
                    </AccessibleText>
                </View>
            </AccessibleView>

            {/* Quick Settings */}
            <AccessibleView
                style={{ marginTop: 24 }}
                semanticRole="region"
                accessibilityLabel="Quick accessibility settings"
            >
                <AccessibleText variant="h3" style={{ marginBottom: 16 }}>
                    ⚙️ Quick Settings
                </AccessibleText>

                <View style={{ gap: 12 }}>
                    <Button
                        title={config.voiceEnabled ? 'Disable Voice Mode' : 'Enable Voice Mode'}
                        onPress={toggleVoiceMode}
                        variant="outline"
                        size="medium"
                        fullWidth
                        icon={<Ionicons name="volume-high" size={20} color={textColor} />}
                        accessibilityLabel={config.voiceEnabled ? 'Disable voice mode' : 'Enable voice mode'}
                    />

                    <Button
                        title={config.highContrast ? 'Disable High Contrast' : 'Enable High Contrast'}
                        onPress={toggleHighContrast}
                        variant="outline"
                        size="medium"
                        fullWidth
                        icon={<Ionicons name="contrast" size={20} color={textColor} />}
                        accessibilityLabel={config.highContrast ? 'Disable high contrast' : 'Enable high contrast'}
                    />

                    <Button
                        title={config.largeText ? 'Disable Large Text' : 'Enable Large Text'}
                        onPress={toggleLargeText}
                        variant="outline"
                        size="medium"
                        fullWidth
                        icon={<Ionicons name="text" size={20} color={textColor} />}
                        accessibilityLabel={config.largeText ? 'Disable large text' : 'Enable large text'}
                    />
                </View>
            </AccessibleView>

            {/* Feature Tests */}
            <AccessibleView
                style={{ marginTop: 24 }}
                semanticRole="region"
                accessibilityLabel="Accessibility feature tests"
            >
                <AccessibleText variant="h3" style={{ marginBottom: 16 }}>
                    🧪 Feature Tests
                </AccessibleText>

                <View style={{ gap: 12 }}>
                    <Button
                        title="Test Voice Features"
                        onPress={testVoiceFeatures}
                        variant="primary"
                        size="medium"
                        fullWidth
                        icon={<Ionicons name="mic" size={20} color="white" />}
                        accessibilityLabel="Test voice features"
                        accessibilityHint="Tap to test text-to-speech and voice feedback"
                    />

                    <Button
                        title="Test Screen Reader"
                        onPress={testScreenReaderFeatures}
                        variant="secondary"
                        size="medium"
                        fullWidth
                        icon={<Ionicons name="accessibility" size={20} color="white" />}
                        accessibilityLabel="Test screen reader"
                        accessibilityHint="Tap to test screen reader announcements"
                    />

                    <Button
                        title="Test Haptic Feedback"
                        onPress={testHapticFeedback}
                        variant="outline"
                        size="medium"
                        fullWidth
                        icon={<Ionicons name="phone-portrait" size={20} color={textColor} />}
                        accessibilityLabel="Test haptic feedback"
                        accessibilityHint="Tap to test vibration and haptic feedback"
                    />
                </View>
            </AccessibleView>

            {/* Test Input */}
            <AccessibleView
                style={{ marginTop: 24 }}
                semanticRole="region"
                accessibilityLabel="Input testing area"
            >
                <AccessibleText variant="h3" style={{ marginBottom: 16 }}>
                    📝 Input Testing
                </AccessibleText>

                <Input
                    label="Test Input Field"
                    placeholder="Type something to test input accessibility..."
                    value={testInput}
                    onChangeText={setTestInput}
                    voiceInputEnabled={config.voiceEnabled}
                    voiceFeedbackEnabled={config.voiceEnabled}
                    accessibilityLabel="Test input field"
                    accessibilityHint="Enter text to test input accessibility features"
                />
            </AccessibleView>

            {/* Comprehensive Audit */}
            <AccessibleView
                style={{ marginTop: 24 }}
                semanticRole="region"
                accessibilityLabel="Comprehensive accessibility audit"
            >
                <AccessibleText variant="h3" style={{ marginBottom: 16 }}>
                    🔍 Comprehensive Audit
                </AccessibleText>

                <Button
                    title={isRunningAudit ? 'Running Audit...' : 'Run Full Accessibility Audit'}
                    onPress={runComprehensiveAudit}
                    variant="primary"
                    size="large"
                    fullWidth
                    loading={isRunningAudit}
                    icon={<Ionicons name="checkmark-circle" size={20} color="white" />}
                    accessibilityLabel="Run comprehensive accessibility audit"
                    accessibilityHint="Tap to run complete accessibility validation"
                />

                {auditResult && (
                    <View style={{ marginTop: 16 }}>
                        <Button
                            title="Share Audit Report"
                            onPress={shareAuditReport}
                            variant="outline"
                            size="medium"
                            fullWidth
                            icon={<Ionicons name="share" size={20} color={textColor} />}
                            accessibilityLabel="Share audit report"
                            accessibilityHint="Tap to share detailed accessibility report"
                        />
                    </View>
                )}
            </AccessibleView>

            {/* Audit Results */}
            {auditResult && (
                <AccessibilityValidator
                    componentName="Audit Results"
                    showResults={true}
                >
                    <AccessibleView
                        style={{
                            marginTop: 24,
                            padding: 16,
                            borderRadius: 12,
                            backgroundColor: config.highContrast ? highContrastColors?.focusBackground : '#f9fafb',
                            borderWidth: 1,
                            borderColor: config.highContrast ? highContrastColors?.foreground : '#e5e7eb',
                        }}
                        semanticRole="region"
                        accessibilityLabel="Audit results summary"
                    >
                        <AccessibleText variant="h3" style={{ marginBottom: 12 }}>
                            📊 Audit Results
                        </AccessibleText>

                        <View style={{ gap: 8 }}>
                            <AccessibleText variant="bodyLarge" style={{ fontWeight: 'bold' }}>
                                Overall Score: {auditResult.overallScore}%
                            </AccessibleText>
                            <AccessibleText variant="body">
                                Total Tests: {auditResult.totalTests}
                            </AccessibleText>
                            <AccessibleText variant="body" style={{ color: '#22c55e' }}>
                                Passed: {auditResult.passedTests}
                            </AccessibleText>
                            <AccessibleText variant="body" style={{ color: '#ef4444' }}>
                                Failed: {auditResult.failedTests}
                            </AccessibleText>
                            <AccessibleText variant="body" style={{ color: '#f59e0b' }}>
                                Warnings: {auditResult.warnings.length}
                            </AccessibleText>
                        </View>

                        {auditResult.recommendations.length > 0 && (
                            <View style={{ marginTop: 16 }}>
                                <AccessibleText variant="h4" style={{ marginBottom: 8 }}>
                                    💡 Top Recommendations
                                </AccessibleText>
                                {auditResult.recommendations.slice(0, 3).map((rec, index) => (
                                    <AccessibleText
                                        key={index}
                                        variant="bodySmall"
                                        style={{ marginBottom: 4, paddingLeft: 8 }}
                                    >
                                        • {rec}
                                    </AccessibleText>
                                ))}
                            </View>
                        )}
                    </AccessibleView>
                </AccessibilityValidator>
            )}

            <View style={{ height: 32 }} />
        </ScrollView>
    );
};