/**
 * Shopping Cart Service Tests
 * Tests for cart management, checkout, and order processing
 */

import { ShoppingCartService } from '../shoppingCart';
import { supabase } from '../supabase/client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ShippingAddress, PaymentMethod } from '../../types/product';

// Mock dependencies
jest.mock('../supabase/client');
jest.mock('@react-native-async-storage/async-storage');

const mockSupabase = supabase as jest.Mocked<typeof supabase>;
const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

describe('ShoppingCartService', () => {
    let shoppingCartService: ShoppingCartService;

    const mockProduct = {
        id: 'product-1',
        name: 'Test Product',
        description: 'Test description',
        category: 'seeds' as const,
        price: 25.99,
        currency: 'USD',
        imageUrls: ['image1.jpg'],
        specifications: [],
        stockQuantity: 100,
        rating: 4.5,
        reviewCount: 10,
        tags: ['test'],
        isRecommended: false,
        isFeatured: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    };

    const mockShippingAddress: ShippingAddress = {
        fullName: 'John Doe',
        addressLine1: '123 Main St',
        city: 'Anytown',
        state: 'CA',
        postalCode: '12345',
        country: 'US',
        phoneNumber: '+1234567890',
    };

    const mockPaymentMethod: PaymentMethod = {
        type: 'credit_card',
        details: {
            paymentMethodId: 'pm_test123',
            last4: '4242',
            brand: 'visa',
        },
    };

    beforeEach(() => {
        shoppingCartService = new ShoppingCartService();
        jest.clearAllMocks();
    });

    describe('initializeCart', () => {
        it('should initialize cart from local storage', async () => {
            const mockCartData = {
                id: 'local',
                items: [],
                totalAmount: 0,
                itemCount: 0,
                updatedAt: new Date().toISOString(),
            };

            mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify(mockCartData));

            const result = await shoppingCartService.initializeCart();

            expect(result.id).toBe('local');
            expect(result.items).toHaveLength(0);
            expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('@farming_app_cart');
        });

        it('should create empty cart if no local storage data', async () => {
            mockAsyncStorage.getItem.mockResolvedValue(null);

            const result = await shoppingCartService.initializeCart();

            expect(result.items).toHaveLength(0);
            expect(result.totalAmount).toBe(0);
            expect(result.itemCount).toBe(0);
        });

        it('should sync with server when user is provided', async () => {
            mockAsyncStorage.getItem.mockResolvedValue(null);

            // Mock server cart operations
            mockSupabase.from.mockImplementation((table) => {
                if (table === 'shopping_carts') {
                    return {
                        select: jest.fn().mockReturnValue({
                            eq: jest.fn().mockReturnValue({
                                single: jest.fn().mockReturnValue({
                                    data: { id: 'server-cart-1' },
                                    error: null,
                                }),
                            }),
                        }),
                    };
                }
                if (table === 'cart_items') {
                    return {
                        delete: jest.fn().mockReturnValue({
                            eq: jest.fn().mockReturnValue({
                                error: null,
                            }),
                        }),
                        insert: jest.fn().mockReturnValue({
                            error: null,
                        }),
                    };
                }
                return {};
            });

            const result = await shoppingCartService.initializeCart('user-1');

            expect(result).toBeDefined();
        });
    });

    describe('addToCart', () => {
        beforeEach(() => {
            // Mock product fetch
            mockSupabase.from.mockImplementation((table) => {
                if (table === 'products') {
                    return {
                        select: jest.fn().mockReturnValue({
                            eq: jest.fn().mockReturnValue({
                                single: jest.fn().mockReturnValue({
                                    data: {
                                        id: 'product-1',
                                        name: 'Test Product',
                                        description: 'Test description',
                                        category: 'seeds',
                                        price: 25.99,
                                        original_price: null,
                                        currency: 'USD',
                                        image_urls: ['image1.jpg'],
                                        specifications: [],
                                        stock_quantity: 100,
                                        rating: 4.5,
                                        review_count: 10,
                                        tags: ['test'],
                                        brand: null,
                                        is_recommended: false,
                                        is_featured: false,
                                        is_active: true,
                                        created_at: new Date().toISOString(),
                                        updated_at: new Date().toISOString(),
                                    },
                                    error: null,
                                }),
                            }),
                        }),
                    };
                }
                return {};
            });

            mockAsyncStorage.getItem.mockResolvedValue(null);
            mockAsyncStorage.setItem.mockResolvedValue();
        });

        it('should add new item to cart', async () => {
            const result = await shoppingCartService.addToCart('product-1', 2);

            expect(result.items).toHaveLength(1);
            expect(result.items[0].product.id).toBe('product-1');
            expect(result.items[0].quantity).toBe(2);
            expect(result.totalAmount).toBe(51.98); // 25.99 * 2
            expect(result.itemCount).toBe(2);
        });

        it('should update quantity for existing item', async () => {
            // First add item
            await shoppingCartService.addToCart('product-1', 1);

            // Add same item again
            const result = await shoppingCartService.addToCart('product-1', 2);

            expect(result.items).toHaveLength(1);
            expect(result.items[0].quantity).toBe(3);
            expect(result.totalAmount).toBe(77.97); // 25.99 * 3
        });

        it('should throw error for insufficient stock', async () => {
            // Mock product with low stock
            mockSupabase.from.mockImplementation((table) => {
                if (table === 'products') {
                    return {
                        select: jest.fn().mockReturnValue({
                            eq: jest.fn().mockReturnValue({
                                single: jest.fn().mockReturnValue({
                                    data: {
                                        ...mockProduct,
                                        stock_quantity: 1,
                                    },
                                    error: null,
                                }),
                            }),
                        }),
                    };
                }
                return {};
            });

            await expect(
                shoppingCartService.addToCart('product-1', 5)
            ).rejects.toThrow('Insufficient stock');
        });

        it('should throw error for non-existent product', async () => {
            mockSupabase.from.mockImplementation((table) => {
                if (table === 'products') {
                    return {
                        select: jest.fn().mockReturnValue({
                            eq: jest.fn().mockReturnValue({
                                single: jest.fn().mockReturnValue({
                                    data: null,
                                    error: { message: 'Product not found' },
                                }),
                            }),
                        }),
                    };
                }
                return {};
            });

            await expect(
                shoppingCartService.addToCart('nonexistent', 1)
            ).rejects.toThrow('Product not found');
        });
    });

    describe('removeFromCart', () => {
        it('should remove item from cart', async () => {
            // Setup cart with item
            mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify({
                id: 'local',
                items: [{
                    id: 'item-1',
                    product: mockProduct,
                    quantity: 2,
                }],
                totalAmount: 51.98,
                itemCount: 2,
                updatedAt: new Date().toISOString(),
            }));

            const result = await shoppingCartService.removeFromCart('item-1');

            expect(result.items).toHaveLength(0);
            expect(result.totalAmount).toBe(0);
            expect(result.itemCount).toBe(0);
        });

        it('should handle removing non-existent item', async () => {
            mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify({
                id: 'local',
                items: [{
                    id: 'item-1',
                    product: mockProduct,
                    quantity: 1,
                }],
                totalAmount: 25.99,
                itemCount: 1,
                updatedAt: new Date().toISOString(),
            }));

            const result = await shoppingCartService.removeFromCart('nonexistent');

            expect(result.items).toHaveLength(1); // Item should still be there
        });
    });

    describe('updateItemQuantity', () => {
        beforeEach(() => {
            mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify({
                id: 'local',
                items: [{
                    id: 'item-1',
                    product: mockProduct,
                    quantity: 2,
                }],
                totalAmount: 51.98,
                itemCount: 2,
                updatedAt: new Date().toISOString(),
            }));
        });

        it('should update item quantity', async () => {
            const result = await shoppingCartService.updateItemQuantity('item-1', 5);

            expect(result.items[0].quantity).toBe(5);
            expect(result.totalAmount).toBe(129.95); // 25.99 * 5
            expect(result.itemCount).toBe(5);
        });

        it('should remove item when quantity is 0', async () => {
            const result = await shoppingCartService.updateItemQuantity('item-1', 0);

            expect(result.items).toHaveLength(0);
            expect(result.totalAmount).toBe(0);
            expect(result.itemCount).toBe(0);
        });

        it('should throw error for insufficient stock', async () => {
            await expect(
                shoppingCartService.updateItemQuantity('item-1', 150)
            ).rejects.toThrow('Insufficient stock');
        });
    });

    describe('calculateShipping', () => {
        const cartItems = [{
            id: 'item-1',
            product: mockProduct,
            quantity: 2,
        }];

        it('should calculate shipping cost', async () => {
            const result = await shoppingCartService.calculateShipping(cartItems, mockShippingAddress);

            expect(result).toBeGreaterThan(0);
            expect(typeof result).toBe('number');
        });

        it('should return 0 for orders over $100', async () => {
            const expensiveProduct = {
                ...mockProduct,
                price: 60.00,
            };

            const expensiveCartItems = [{
                id: 'item-1',
                product: expensiveProduct,
                quantity: 2, // Total: $120
            }];

            const result = await shoppingCartService.calculateShipping(expensiveCartItems, mockShippingAddress);

            expect(result).toBe(0);
        });
    });

    describe('calculateTax', () => {
        const cartItems = [{
            id: 'item-1',
            product: mockProduct,
            quantity: 2,
        }];

        it('should calculate tax for US addresses', async () => {
            const result = await shoppingCartService.calculateTax(cartItems, mockShippingAddress);

            expect(result).toBeGreaterThan(0);
            expect(typeof result).toBe('number');
        });

        it('should return 0 for non-US addresses', async () => {
            const internationalAddress = {
                ...mockShippingAddress,
                country: 'CA',
            };

            const result = await shoppingCartService.calculateTax(cartItems, internationalAddress);

            expect(result).toBe(0);
        });
    });

    describe('createOrder', () => {
        beforeEach(() => {
            // Mock cart with items
            mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify({
                id: 'local',
                items: [{
                    id: 'item-1',
                    product: mockProduct,
                    quantity: 2,
                }],
                totalAmount: 51.98,
                itemCount: 2,
                updatedAt: new Date().toISOString(),
            }));

            // Mock order creation
            mockSupabase.from.mockImplementation((table) => {
                if (table === 'orders') {
                    return {
                        insert: jest.fn().mockReturnValue({
                            select: jest.fn().mockReturnValue({
                                single: jest.fn().mockReturnValue({
                                    data: {
                                        id: 'order-1',
                                        user_id: 'user-1',
                                        order_number: 'ORD-20240101-000001',
                                        total_amount: 51.98,
                                        tax_amount: 4.16,
                                        shipping_amount: 5.00,
                                        discount_amount: 0,
                                        status: 'pending',
                                        payment_status: 'pending',
                                        payment_method: mockPaymentMethod,
                                        shipping_address: mockShippingAddress,
                                        billing_address: null,
                                        tracking_number: null,
                                        estimated_delivery: '2024-01-08',
                                        delivered_at: null,
                                        notes: 'Order created with 1 items',
                                        created_at: new Date().toISOString(),
                                        updated_at: new Date().toISOString(),
                                    },
                                    error: null,
                                }),
                            }),
                        }),
                    };
                }
                if (table === 'order_items') {
                    return {
                        insert: jest.fn().mockReturnValue({
                            error: null,
                        }),
                    };
                }
                if (table === 'products') {
                    return {
                        update: jest.fn().mockReturnValue({
                            eq: jest.fn().mockReturnValue({
                                error: null,
                            }),
                        }),
                    };
                }
                return {};
            });
        });

        it('should create order successfully', async () => {
            const result = await shoppingCartService.createOrder(
                'user-1',
                mockShippingAddress,
                mockPaymentMethod
            );

            expect(result.id).toBe('order-1');
            expect(result.userId).toBe('user-1');
            expect(result.items).toHaveLength(1);
            expect(result.totalAmount).toBe(51.98);
            expect(result.status).toBe('pending');
        });

        it('should throw error for empty cart', async () => {
            mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify({
                id: 'local',
                items: [],
                totalAmount: 0,
                itemCount: 0,
                updatedAt: new Date().toISOString(),
            }));

            await expect(
                shoppingCartService.createOrder('user-1', mockShippingAddress, mockPaymentMethod)
            ).rejects.toThrow('Cart is empty');
        });

        it('should handle order creation failure', async () => {
            mockSupabase.from.mockImplementation((table) => {
                if (table === 'orders') {
                    return {
                        insert: jest.fn().mockReturnValue({
                            select: jest.fn().mockReturnValue({
                                single: jest.fn().mockReturnValue({
                                    data: null,
                                    error: { message: 'Database error' },
                                }),
                            }),
                        }),
                    };
                }
                return {};
            });

            await expect(
                shoppingCartService.createOrder('user-1', mockShippingAddress, mockPaymentMethod)
            ).rejects.toThrow('Failed to create order: Database error');
        });
    });

    describe('getOrderHistory', () => {
        it('should return user order history', async () => {
            const mockOrders = [{
                id: 'order-1',
                user_id: 'user-1',
                order_number: 'ORD-20240101-000001',
                total_amount: 51.98,
                status: 'delivered',
                shipping_address: mockShippingAddress,
                payment_method: mockPaymentMethod,
                tracking_number: 'TRK123',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                estimated_delivery: '2024-01-08',
                order_items: [{
                    id: 'item-1',
                    quantity: 2,
                    product: {
                        id: 'product-1',
                        name: 'Test Product',
                        price: 25.99,
                        // ... other product fields
                    },
                }],
            }];

            mockSupabase.from.mockReturnValue({
                select: jest.fn().mockReturnValue({
                    eq: jest.fn().mockReturnValue({
                        order: jest.fn().mockReturnValue({
                            limit: jest.fn().mockReturnValue({
                                data: mockOrders,
                                error: null,
                            }),
                        }),
                    }),
                }),
            } as any);

            const result = await shoppingCartService.getOrderHistory('user-1', 10);

            expect(result).toHaveLength(1);
            expect(result[0].id).toBe('order-1');
            expect(result[0].status).toBe('delivered');
        });

        it('should handle database errors gracefully', async () => {
            mockSupabase.from.mockReturnValue({
                select: jest.fn().mockReturnValue({
                    eq: jest.fn().mockReturnValue({
                        order: jest.fn().mockReturnValue({
                            limit: jest.fn().mockReturnValue({
                                data: null,
                                error: { message: 'Database error' },
                            }),
                        }),
                    }),
                }),
            } as any);

            const result = await shoppingCartService.getOrderHistory('user-1');

            expect(result).toEqual([]);
        });
    });

    describe('trackOrder', () => {
        it('should return order tracking information', async () => {
            const mockOrder = {
                id: 'order-1',
                user_id: 'user-1',
                order_number: 'ORD-20240101-000001',
                total_amount: 51.98,
                status: 'shipped',
                shipping_address: mockShippingAddress,
                payment_method: mockPaymentMethod,
                tracking_number: 'TRK123',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                estimated_delivery: '2024-01-08',
                order_items: [{
                    id: 'item-1',
                    quantity: 2,
                    product: mockProduct,
                }],
            };

            mockSupabase.from.mockReturnValue({
                select: jest.fn().mockReturnValue({
                    eq: jest.fn().mockReturnValue({
                        single: jest.fn().mockReturnValue({
                            data: mockOrder,
                            error: null,
                        }),
                    }),
                }),
            } as any);

            const result = await shoppingCartService.trackOrder('order-1');

            expect(result).toBeTruthy();
            expect(result?.id).toBe('order-1');
            expect(result?.status).toBe('shipped');
            expect(result?.trackingNumber).toBe('TRK123');
        });

        it('should return null for non-existent order', async () => {
            mockSupabase.from.mockReturnValue({
                select: jest.fn().mockReturnValue({
                    eq: jest.fn().mockReturnValue({
                        single: jest.fn().mockReturnValue({
                            data: null,
                            error: { code: 'PGRST116' },
                        }),
                    }),
                }),
            } as any);

            const result = await shoppingCartService.trackOrder('nonexistent');

            expect(result).toBeNull();
        });
    });
});