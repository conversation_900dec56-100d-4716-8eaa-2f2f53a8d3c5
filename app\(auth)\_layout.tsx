import { Stack } from 'expo-router';

export default function AuthLayout() {
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="welcome" />
      <Stack.Screen name="register" />
      <Stack.Screen name="personal-info" />
      <Stack.Screen name="farm-setup" />
      <Stack.Screen name="completion" />
      <Stack.Screen name="login" />
      <Stack.Screen name="forgot-password" />
      <Stack.Screen name="verify-reset-code" />
      <Stack.Screen name="new-password" />
    </Stack>
  );
}
