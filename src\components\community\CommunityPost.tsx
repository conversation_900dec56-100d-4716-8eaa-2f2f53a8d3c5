import React, { useState } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    Image,
    ScrollView,
    Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../../design-system';
import { useVoiceStore } from '../../stores/voice';

interface CommunityPostProps {
    post: {
        id: string;
        title: string;
        content: string;
        author: {
            id: string;
            name: string;
            avatar?: string;
            location?: string;
            experienceLevel?: string;
        };
        images?: string[];
        location?: {
            latitude: number;
            longitude: number;
            address?: string;
        };
        likes: number;
        comments: number;
        shares: number;
        isLiked: boolean;
        createdAt: Date;
    };
    onPress: () => void;
    onLike: () => void;
    onComment: () => void;
    onShare: () => void;
}

export function CommunityPost({
    post,
    onPress,
    onLike,
    onComment,
    onShare
}: CommunityPostProps) {
    const [imageIndex, setImageIndex] = useState(0);
    const { speak, isVoiceEnabled } = useVoiceStore();

    const formatTimeAgo = (date: Date) => {
        const now = new Date();
        const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

        if (diffInHours < 1) return 'Just now';
        if (diffInHours < 24) return `${diffInHours}h ago`;
        if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
        return `${Math.floor(diffInHours / 168)}w ago`;
    };

    const handleVoiceRead = async () => {
        if (isVoiceEnabled) {
            const voiceText = `Post by ${post.author.name}. ${post.title}. ${post.content}. ${post.likes} likes, ${post.comments} comments.`;
            await speak(voiceText);
        }
    };

    const handleLike = () => {
        onLike();
        if (isVoiceEnabled) {
            speak(post.isLiked ? 'Post unliked' : 'Post liked');
        }
    };

    const handleComment = () => {
        onComment();
        if (isVoiceEnabled) {
            speak('Opening comments');
        }
    };

    const handleShare = () => {
        onShare();
        if (isVoiceEnabled) {
            speak('Sharing post');
        }
    };

    return (
        <TouchableOpacity
            onPress={onPress}
            style={{
                backgroundColor: 'white',
                marginHorizontal: 16,
                marginVertical: 8,
                borderRadius: 12,
                padding: 16,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 3,
            }}
            accessibilityLabel={`Post by ${post.author.name}: ${post.title}`}
        >
            {/* Header */}
            <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: 12,
            }}>
                {/* Author Avatar */}
                <View style={{
                    width: 48,
                    height: 48,
                    borderRadius: 24,
                    backgroundColor: colors.primary[100],
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginRight: 12,
                }}>
                    {post.author.avatar ? (
                        <Image
                            source={{ uri: post.author.avatar }}
                            style={{
                                width: 48,
                                height: 48,
                                borderRadius: 24,
                            }}
                        />
                    ) : (
                        <Ionicons name="person" size={24} color={colors.primary[500]} />
                    )}
                </View>

                {/* Author Info */}
                <View style={{ flex: 1 }}>
                    <Text style={{
                        fontSize: 16,
                        fontWeight: '600',
                        color: colors.primary[900],
                    }}>
                        {post.author.name}
                    </Text>
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                        {post.author.experienceLevel && (
                            <Text style={{
                                fontSize: 12,
                                color: colors.earth[500],
                                textTransform: 'capitalize',
                            }}>
                                {post.author.experienceLevel} Farmer
                            </Text>
                        )}
                        {post.author.location && (
                            <>
                                <Text style={{ color: colors.earth[300] }}>•</Text>
                                <Text style={{
                                    fontSize: 12,
                                    color: colors.earth[500],
                                }}>
                                    {post.author.location}
                                </Text>
                            </>
                        )}
                        <Text style={{ color: colors.earth[300] }}>•</Text>
                        <Text style={{
                            fontSize: 12,
                            color: colors.earth[500],
                        }}>
                            {formatTimeAgo(post.createdAt)}
                        </Text>
                    </View>
                </View>

                {/* Voice Read Button */}
                {isVoiceEnabled && (
                    <TouchableOpacity
                        onPress={handleVoiceRead}
                        style={{
                            padding: 8,
                        }}
                        accessibilityLabel="Read post aloud"
                    >
                        <Ionicons name="volume-high" size={20} color={colors.primary[500]} />
                    </TouchableOpacity>
                )}
            </View>

            {/* Post Title */}
            <Text style={{
                fontSize: 18,
                fontWeight: '600',
                color: colors.primary[900],
                marginBottom: 8,
                lineHeight: 24,
            }}>
                {post.title}
            </Text>

            {/* Post Content */}
            <Text style={{
                fontSize: 16,
                color: colors.earth[700],
                lineHeight: 22,
                marginBottom: post.images?.length ? 12 : 16,
            }}>
                {post.content}
            </Text>

            {/* Images */}
            {post.images && post.images.length > 0 && (
                <View style={{ marginBottom: 16 }}>
                    <ScrollView
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        pagingEnabled
                        onMomentumScrollEnd={(event) => {
                            const newIndex = Math.round(
                                event.nativeEvent.contentOffset.x / event.nativeEvent.layoutMeasurement.width
                            );
                            setImageIndex(newIndex);
                        }}
                    >
                        {post.images.map((uri, index) => (
                            <Image
                                key={index}
                                source={{ uri }}
                                style={{
                                    width: 300,
                                    height: 200,
                                    borderRadius: 8,
                                    marginRight: index < post.images!.length - 1 ? 8 : 0,
                                }}
                                resizeMode="cover"
                            />
                        ))}
                    </ScrollView>

                    {post.images.length > 1 && (
                        <View style={{
                            flexDirection: 'row',
                            justifyContent: 'center',
                            marginTop: 8,
                            gap: 4,
                        }}>
                            {post.images.map((_, index) => (
                                <View
                                    key={index}
                                    style={{
                                        width: 6,
                                        height: 6,
                                        borderRadius: 3,
                                        backgroundColor: index === imageIndex
                                            ? colors.primary[500]
                                            : colors.earth[300],
                                    }}
                                />
                            ))}
                        </View>
                    )}
                </View>
            )}

            {/* Location */}
            {post.location && (
                <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginBottom: 16,
                    gap: 6,
                }}>
                    <Ionicons name="location" size={16} color={colors.earth[500]} />
                    <Text style={{
                        fontSize: 14,
                        color: colors.earth[500],
                    }}>
                        {post.location.address || `${post.location.latitude.toFixed(4)}, ${post.location.longitude.toFixed(4)}`}
                    </Text>
                </View>
            )}

            {/* Action Buttons */}
            <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingTop: 12,
                borderTopWidth: 1,
                borderTopColor: colors.primary[100],
            }}>
                <TouchableOpacity
                    onPress={handleLike}
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: 6,
                        padding: 8,
                    }}
                    accessibilityLabel={`${post.isLiked ? 'Unlike' : 'Like'} post. ${post.likes} likes`}
                >
                    <Ionicons
                        name={post.isLiked ? "heart" : "heart-outline"}
                        size={20}
                        color={post.isLiked ? colors.status.error : colors.earth[500]}
                    />
                    <Text style={{
                        color: post.isLiked ? colors.status.error : colors.earth[500],
                        fontWeight: '500',
                    }}>
                        {post.likes}
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity
                    onPress={handleComment}
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: 6,
                        padding: 8,
                    }}
                    accessibilityLabel={`Comment on post. ${post.comments} comments`}
                >
                    <Ionicons name="chatbubble-outline" size={20} color={colors.earth[500]} />
                    <Text style={{
                        color: colors.earth[500],
                        fontWeight: '500',
                    }}>
                        {post.comments}
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity
                    onPress={handleShare}
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: 6,
                        padding: 8,
                    }}
                    accessibilityLabel={`Share post. ${post.shares} shares`}
                >
                    <Ionicons name="share-outline" size={20} color={colors.earth[500]} />
                    <Text style={{
                        color: colors.earth[500],
                        fontWeight: '500',
                    }}>
                        {post.shares}
                    </Text>
                </TouchableOpacity>
            </View>
        </TouchableOpacity>
    );
}