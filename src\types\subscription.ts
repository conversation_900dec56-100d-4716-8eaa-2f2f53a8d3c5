export interface SubscriptionPlan {
    id: string;
    name: string;
    description: string;
    price: number;
    currency: string;
    billing_period: 'monthly' | 'yearly';
    features: SubscriptionFeature[];
    points_included: number;
    ai_consultations_limit: number; // -1 for unlimited
    priority_support: boolean;
    offline_access: boolean;
    advanced_analytics: boolean;
    api_access: boolean;
    multi_farm_support: boolean;
    team_collaboration: boolean;
    is_active: boolean;
    sort_order: number;
    created_at: string;
    updated_at: string;
}

export interface SubscriptionFeature {
    id: string;
    name: string;
    description: string;
    category: 'core' | 'ai' | 'analytics' | 'support' | 'collaboration' | 'integration';
    is_premium: boolean;
}

export interface UserSubscription {
    id: string;
    user_id: string;
    plan_id: string;
    status: 'active' | 'cancelled' | 'expired' | 'past_due' | 'trialing';
    current_period_start: string;
    current_period_end: string;
    cancel_at_period_end: boolean;
    cancelled_at?: string;
    trial_start?: string;
    trial_end?: string;
    payment_method_id?: string;
    created_at: string;
    updated_at: string;
}

export interface PaymentMethod {
    id: string;
    user_id: string;
    type: 'card' | 'paypal' | 'apple_pay' | 'google_pay';
    provider: 'stripe' | 'paypal' | 'apple' | 'google';
    provider_payment_method_id: string;
    last_four?: string;
    brand?: string;
    exp_month?: number;
    exp_year?: number;
    is_default: boolean;
    created_at: string;
    updated_at: string;
}

export interface SubscriptionUsage {
    id: string;
    user_id: string;
    subscription_id: string;
    period_start: string;
    period_end: string;
    ai_consultations_used: number;
    ai_consultations_limit: number;
    image_analyses_used: number;
    storage_used_mb: number;
    api_calls_used: number;
    created_at: string;
    updated_at: string;
}

export interface Invoice {
    id: string;
    user_id: string;
    subscription_id: string;
    amount: number;
    currency: string;
    status: 'draft' | 'open' | 'paid' | 'void' | 'uncollectible';
    invoice_number: string;
    invoice_date: string;
    due_date: string;
    paid_at?: string;
    payment_method_id?: string;
    provider_invoice_id?: string;
    invoice_url?: string;
    created_at: string;
    updated_at: string;
}

export interface SubscriptionEvent {
    id: string;
    user_id: string;
    subscription_id: string;
    event_type: 'created' | 'updated' | 'cancelled' | 'renewed' | 'payment_failed' | 'payment_succeeded' | 'trial_started' | 'trial_ended';
    event_data: any;
    created_at: string;
}

// Feature access control
export interface FeatureAccess {
    ai_consultations: boolean;
    image_analysis: boolean;
    advanced_crop_planning: boolean;
    weather_alerts: boolean;
    community_access: boolean;
    offline_mode: boolean;
    priority_support: boolean;
    advanced_analytics: boolean;
    api_access: boolean;
    multi_farm_management: boolean;
    team_collaboration: boolean;
    custom_recommendations: boolean;
    export_data: boolean;
    white_label: boolean;
}

// Subscription tier definitions
export const SUBSCRIPTION_PLANS: Omit<SubscriptionPlan, 'id' | 'created_at' | 'updated_at'>[] = [
    {
        name: 'Free',
        description: 'Perfect for getting started with smart farming',
        price: 0,
        currency: 'USD',
        billing_period: 'monthly',
        features: [
            { id: 'basic_crop_planning', name: 'Basic Crop Planning', description: 'Create and manage basic crop plans', category: 'core', is_premium: false },
            { id: 'weather_alerts', name: 'Weather Alerts', description: 'Receive weather notifications', category: 'core', is_premium: false },
            { id: 'community_access', name: 'Community Access', description: 'Join farmer community discussions', category: 'core', is_premium: false },
            { id: 'limited_ai', name: 'Limited AI Consultations', description: '5 AI consultations per month', category: 'ai', is_premium: false },
        ],
        points_included: 100,
        ai_consultations_limit: 5,
        priority_support: false,
        offline_access: false,
        advanced_analytics: false,
        api_access: false,
        multi_farm_support: false,
        team_collaboration: false,
        is_active: true,
        sort_order: 1,
    },
    {
        name: 'Basic',
        description: 'Enhanced features for serious farmers',
        price: 9.99,
        currency: 'USD',
        billing_period: 'monthly',
        features: [
            { id: 'advanced_crop_planning', name: 'Advanced Crop Planning', description: 'Detailed crop planning with AI recommendations', category: 'core', is_premium: true },
            { id: 'image_analysis', name: 'Image Analysis', description: 'AI-powered plant disease detection', category: 'ai', is_premium: true },
            { id: 'offline_access', name: 'Offline Access', description: 'Access core features without internet', category: 'core', is_premium: true },
            { id: 'priority_support', name: 'Priority Support', description: 'Faster response times for support', category: 'support', is_premium: true },
            { id: 'extended_ai', name: 'Extended AI Consultations', description: '25 AI consultations per month', category: 'ai', is_premium: true },
        ],
        points_included: 500,
        ai_consultations_limit: 25,
        priority_support: true,
        offline_access: true,
        advanced_analytics: false,
        api_access: false,
        multi_farm_support: false,
        team_collaboration: false,
        is_active: true,
        sort_order: 2,
    },
    {
        name: 'Premium',
        description: 'Complete farming solution with unlimited AI',
        price: 19.99,
        currency: 'USD',
        billing_period: 'monthly',
        features: [
            { id: 'unlimited_ai', name: 'Unlimited AI Consultations', description: 'No limits on AI consultations', category: 'ai', is_premium: true },
            { id: 'advanced_analytics', name: 'Advanced Analytics', description: 'Detailed insights and reports', category: 'analytics', is_premium: true },
            { id: 'custom_recommendations', name: 'Custom Recommendations', description: 'Personalized farming recommendations', category: 'ai', is_premium: true },
            { id: 'api_access', name: 'API Access', description: 'Integrate with third-party tools', category: 'integration', is_premium: true },
            { id: 'export_data', name: 'Data Export', description: 'Export your farming data', category: 'core', is_premium: true },
        ],
        points_included: 1000,
        ai_consultations_limit: -1,
        priority_support: true,
        offline_access: true,
        advanced_analytics: true,
        api_access: true,
        multi_farm_support: false,
        team_collaboration: false,
        is_active: true,
        sort_order: 3,
    },
    {
        name: 'Pro',
        description: 'Enterprise solution for large operations',
        price: 49.99,
        currency: 'USD',
        billing_period: 'monthly',
        features: [
            { id: 'multi_farm_management', name: 'Multi-Farm Management', description: 'Manage multiple farm locations', category: 'core', is_premium: true },
            { id: 'team_collaboration', name: 'Team Collaboration', description: 'Share access with team members', category: 'collaboration', is_premium: true },
            { id: 'white_label', name: 'White Label Options', description: 'Custom branding options', category: 'integration', is_premium: true },
            { id: 'priority_development', name: 'Priority Development', description: 'Influence feature development', category: 'support', is_premium: true },
            { id: 'dedicated_support', name: 'Dedicated Support', description: 'Personal account manager', category: 'support', is_premium: true },
        ],
        points_included: 2500,
        ai_consultations_limit: -1,
        priority_support: true,
        offline_access: true,
        advanced_analytics: true,
        api_access: true,
        multi_farm_support: true,
        team_collaboration: true,
        is_active: true,
        sort_order: 4,
    },
];

// Helper functions for feature access
export const getFeatureAccess = (subscriptionTier: string): FeatureAccess => {
    const plan = SUBSCRIPTION_PLANS.find(p => p.name.toLowerCase() === subscriptionTier.toLowerCase());

    if (!plan) {
        // Default to free tier access
        return {
            ai_consultations: true,
            image_analysis: false,
            advanced_crop_planning: false,
            weather_alerts: true,
            community_access: true,
            offline_mode: false,
            priority_support: false,
            advanced_analytics: false,
            api_access: false,
            multi_farm_management: false,
            team_collaboration: false,
            custom_recommendations: false,
            export_data: false,
            white_label: false,
        };
    }

    return {
        ai_consultations: true,
        image_analysis: plan.features.some(f => f.id === 'image_analysis'),
        advanced_crop_planning: plan.features.some(f => f.id === 'advanced_crop_planning'),
        weather_alerts: true,
        community_access: true,
        offline_mode: plan.offline_access,
        priority_support: plan.priority_support,
        advanced_analytics: plan.advanced_analytics,
        api_access: plan.api_access,
        multi_farm_management: plan.multi_farm_support,
        team_collaboration: plan.team_collaboration,
        custom_recommendations: plan.features.some(f => f.id === 'custom_recommendations'),
        export_data: plan.features.some(f => f.id === 'export_data'),
        white_label: plan.features.some(f => f.id === 'white_label'),
    };
};

export const canAccessFeature = (subscriptionTier: string, feature: keyof FeatureAccess): boolean => {
    const access = getFeatureAccess(subscriptionTier);
    return access[feature];
};

export const getAIConsultationLimit = (subscriptionTier: string): number => {
    const plan = SUBSCRIPTION_PLANS.find(p => p.name.toLowerCase() === subscriptionTier.toLowerCase());
    return plan?.ai_consultations_limit ?? 5; // Default to free tier limit
};

export const getMonthlyPointsAllocation = (subscriptionTier: string): number => {
    const plan = SUBSCRIPTION_PLANS.find(p => p.name.toLowerCase() === subscriptionTier.toLowerCase());
    return plan?.points_included ?? 100; // Default to free tier points
};