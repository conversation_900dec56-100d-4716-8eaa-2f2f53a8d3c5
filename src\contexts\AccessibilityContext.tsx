/**
 * Accessibility Context Provider
 * Comprehensive accessibility state management for the entire app
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { AccessibilityInfo, Platform, Appearance, Vibration } from 'react-native';
import { AudioPlayer } from 'expo-audio';
import * as Haptics from 'expo-haptics';
import { AccessibilityManager, AccessibilityConfig } from '../utils/accessibility';
import { KeyboardNavigationManager } from '../utils/keyboardNavigation';

interface AccessibilityContextType {
    config: AccessibilityConfig;
    isInitialized: boolean;

    // State getters
    isScreenReaderEnabled: boolean;
    isVoiceEnabled: boolean;
    isHighContrastEnabled: boolean;
    isLargeTextEnabled: boolean;
    isKeyboardNavigationEnabled: boolean;
    fontScale: number;

    // Actions
    updateConfig: (updates: Partial<AccessibilityConfig>) => Promise<void>;
    toggleVoiceMode: () => Promise<void>;
    toggleHighContrast: () => Promise<void>;
    toggleLargeText: () => Promise<void>;
    toggleKeyboardNavigation: () => Promise<void>;
    setFontScale: (scale: number) => Promise<void>;

    // Feedback methods
    announceToScreenReader: (message: string, priority?: 'low' | 'high') => void;
    provideFeedback: (type: 'success' | 'error' | 'warning' | 'info', message: string) => Promise<void>;
    playSound: (soundType: 'success' | 'error' | 'warning' | 'click') => Promise<void>;
    vibrate: (pattern?: 'light' | 'medium' | 'heavy' | 'success' | 'error') => void;

    // Utilities
    getAccessibilityProps: (label: string, hint?: string, role?: string) => object;
    getScaledFontSize: (baseSize: number) => number;
    getScaledTouchTarget: (baseSize: number) => number;
    getHighContrastColors: () => object | null;
    getColorBlindFriendlyColors: () => object | null;
}

const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined);

interface AccessibilityProviderProps {
    children: ReactNode;
}

export const AccessibilityProvider: React.FC<AccessibilityProviderProps> = ({ children }) => {
    const [config, setConfig] = useState<AccessibilityConfig>(() =>
        AccessibilityManager.getInstance().getConfig()
    );
    const [isInitialized, setIsInitialized] = useState(false);
    const [isScreenReaderEnabled, setIsScreenReaderEnabled] = useState(false);
    const [sounds, setSounds] = useState<{ [key: string]: AudioPlayer }>({});

    useEffect(() => {
        initializeAccessibility();
        loadSounds();

        return () => {
            // Cleanup sounds
            Object.values(sounds).forEach(sound => {
                sound.unloadAsync();
            });
        };
    }, []);

    const initializeAccessibility = async () => {
        try {
            const accessibilityManager = AccessibilityManager.getInstance();
            const keyboardManager = KeyboardNavigationManager.getInstance();

            // Initialize managers
            await accessibilityManager.initialize();

            // Subscribe to config changes
            const unsubscribe = accessibilityManager.subscribe((newConfig) => {
                setConfig(newConfig);
            });

            // Check screen reader status
            const screenReaderEnabled = await AccessibilityInfo.isScreenReaderEnabled();
            setIsScreenReaderEnabled(screenReaderEnabled);

            // Listen for accessibility changes
            AccessibilityInfo.addEventListener('screenReaderChanged', setIsScreenReaderEnabled);
            AccessibilityInfo.addEventListener('reduceMotionChanged', (enabled) => {
                accessibilityManager.updateConfig({ reducedMotion: enabled });
            });

            // Listen for system appearance changes
            const appearanceSubscription = Appearance.addChangeListener(({ colorScheme }) => {
                // Auto-enable high contrast in dark mode if user prefers
                if (colorScheme === 'dark' && config.highContrast) {
                    accessibilityManager.updateConfig({ highContrast: true });
                }
            });

            setIsInitialized(true);

            return () => {
                unsubscribe();
                appearanceSubscription?.remove();
            };
        } catch (error) {
            console.warn('Failed to initialize accessibility:', error);
            setIsInitialized(true); // Still mark as initialized to prevent blocking
        }
    };

    const loadSounds = async () => {
        try {
            // Define sound files with conditional loading
            const soundFiles: { [key: string]: any } = {};

            // Only try to load sounds if files exist
            try {
                soundFiles.success = require('../../assets/sounds/success.mp3');
            } catch (e) {
                console.warn('Success sound file not found');
            }

            try {
                soundFiles.error = require('../../assets/sounds/mixkit-game-show-wrong-answer-buzz-950.wav');
            } catch (e) {
                console.warn('Error sound file not found');
            }

            try {
                soundFiles.warning = require('../../assets/sounds/warning.mp3');
            } catch (e) {
                console.warn('Warning sound file not found');
            }

            try {
                soundFiles.click = require('../../assets/sounds/click.mp3');
            } catch (e) {
                console.warn('Click sound file not found');
            }

            const loadedSounds: { [key: string]: AudioPlayer } = {};

            for (const [key, file] of Object.entries(soundFiles)) {
                if (file) {
                    try {
                        const player = new AudioPlayer(file);
                        loadedSounds[key] = player;
                    } catch (error) {
                        console.warn(`Failed to load sound ${key}:`, error);
                    }
                }
            }

            setSounds(loadedSounds);
        } catch (error) {
            console.warn('Failed to load accessibility sounds:', error);
        }
    };

    const updateConfig = async (updates: Partial<AccessibilityConfig>) => {
        await AccessibilityManager.getInstance().updateConfig(updates);
    };

    const toggleVoiceMode = async () => {
        const newVoiceEnabled = !config.voiceEnabled;
        await updateConfig({ voiceEnabled: newVoiceEnabled });

        if (newVoiceEnabled) {
            await provideFeedback('success', 'Voice mode enabled');
        } else {
            announceToScreenReader('Voice mode disabled');
        }
    };

    const toggleHighContrast = async () => {
        const newHighContrast = !config.highContrast;
        await updateConfig({ highContrast: newHighContrast });

        announceToScreenReader(
            newHighContrast ? 'High contrast mode enabled' : 'High contrast mode disabled'
        );

        vibrate('light');
    };

    const toggleLargeText = async () => {
        const newLargeText = !config.largeText;
        const newFontScale = newLargeText ? 1.3 : 1.0;

        await updateConfig({
            largeText: newLargeText,
            fontScale: newFontScale
        });

        announceToScreenReader(
            newLargeText ? 'Large text mode enabled' : 'Large text mode disabled'
        );

        vibrate('light');
    };

    const toggleKeyboardNavigation = async () => {
        const newKeyboardNav = !config.keyboardNavigation;
        await updateConfig({ keyboardNavigation: newKeyboardNav });

        announceToScreenReader(
            newKeyboardNav ? 'Keyboard navigation enabled' : 'Keyboard navigation disabled'
        );
    };

    const setFontScale = async (scale: number) => {
        const clampedScale = Math.max(0.8, Math.min(2.0, scale));
        await updateConfig({
            fontScale: clampedScale,
            largeText: clampedScale > 1.0
        });

        announceToScreenReader(`Font size set to ${Math.round(clampedScale * 100)}%`);
    };

    const announceToScreenReader = (message: string, priority: 'low' | 'high' = 'low') => {
        if (isScreenReaderEnabled) {
            if (Platform.OS === 'ios') {
                AccessibilityInfo.announceForAccessibility(message);
            } else {
                AccessibilityInfo.announceForAccessibility(message);
            }
        }
    };

    const provideFeedback = async (
        type: 'success' | 'error' | 'warning' | 'info',
        message: string
    ) => {
        // Voice feedback
        if (config.voiceEnabled) {
            // This would integrate with the voice service
            console.log(`Voice: ${message}`);
        }

        // Screen reader announcement
        announceToScreenReader(message, type === 'error' ? 'high' : 'low');

        // Sound feedback
        if (config.soundFeedbackEnabled) {
            await playSound(type === 'info' ? 'click' : type);
        }

        // Haptic feedback
        if (config.hapticFeedbackEnabled) {
            switch (type) {
                case 'success':
                    vibrate('success');
                    break;
                case 'error':
                    vibrate('error');
                    break;
                case 'warning':
                    vibrate('medium');
                    break;
                default:
                    vibrate('light');
            }
        }
    };

    const playSound = async (soundType: 'success' | 'error' | 'warning' | 'click') => {
        if (!config.soundFeedbackEnabled) return;

        const sound = sounds[soundType];
        if (sound) {
            try {
                await sound.play();
            } catch (error) {
                console.warn(`Failed to play sound ${soundType}:`, error);
            }
        }
    };

    const vibrate = (pattern: 'light' | 'medium' | 'heavy' | 'success' | 'error' = 'light') => {
        if (!config.hapticFeedbackEnabled) return;

        try {
            switch (pattern) {
                case 'light':
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                    break;
                case 'medium':
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                    break;
                case 'heavy':
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
                    break;
                case 'success':
                    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
                    break;
                case 'error':
                    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
                    break;
                default:
                    Haptics.selectionAsync();
            }
        } catch (error) {
            // Fallback to basic vibration
            Vibration.vibrate(100);
        }
    };

    const getAccessibilityProps = (label: string, hint?: string, role?: string) => {
        return {
            accessibilityLabel: label,
            accessibilityHint: hint,
            accessibilityRole: role as any,
            accessible: true,
            importantForAccessibility: 'yes' as const,
        };
    };

    const getScaledFontSize = (baseSize: number) => {
        return AccessibilityManager.getInstance().getScaledFontSize(baseSize);
    };

    const getScaledTouchTarget = (baseSize: number) => {
        return AccessibilityManager.getInstance().getScaledTouchTarget(baseSize);
    };

    const getHighContrastColors = () => {
        return AccessibilityManager.getInstance().getHighContrastColors();
    };

    const getColorBlindFriendlyColors = () => {
        return AccessibilityManager.getInstance().getColorBlindFriendlyColors();
    };

    const contextValue: AccessibilityContextType = {
        config,
        isInitialized,
        isScreenReaderEnabled,
        isVoiceEnabled: config.voiceEnabled,
        isHighContrastEnabled: config.highContrast,
        isLargeTextEnabled: config.largeText,
        isKeyboardNavigationEnabled: config.keyboardNavigation,
        fontScale: config.fontScale,

        updateConfig,
        toggleVoiceMode,
        toggleHighContrast,
        toggleLargeText,
        toggleKeyboardNavigation,
        setFontScale,

        announceToScreenReader,
        provideFeedback,
        playSound,
        vibrate,

        getAccessibilityProps,
        getScaledFontSize,
        getScaledTouchTarget,
        getHighContrastColors,
        getColorBlindFriendlyColors,
    };

    return (
        <AccessibilityContext.Provider value={contextValue}>
            {children}
        </AccessibilityContext.Provider>
    );
};

export const useAccessibilityContext = (): AccessibilityContextType => {
    const context = useContext(AccessibilityContext);
    if (!context) {
        throw new Error('useAccessibilityContext must be used within an AccessibilityProvider');
    }
    return context;
};