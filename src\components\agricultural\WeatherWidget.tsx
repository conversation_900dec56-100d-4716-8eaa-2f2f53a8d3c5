import React from 'react';
import { View, Text } from 'react-native';
import { Card } from '../ui';

export interface WeatherData {
  temperature: number;
  humidity: number;
  condition: string;
  windSpeed: number;
  precipitation: number;
  uvIndex: number;
}

export interface WeatherWidgetProps {
  weather: WeatherData;
  location: string;
  onPress?: () => void;
  accessibilityLabel?: string;
}

export const WeatherWidget: React.FC<WeatherWidgetProps> = ({
  weather,
  location,
  onPress,
  accessibilityLabel,
}) => {
  const getWeatherIcon = (condition: string) => {
    const icons: Record<string, string> = {
      sunny: '☀️',
      cloudy: '☁️',
      rainy: '🌧️',
      stormy: '⛈️',
      snowy: '❄️',
      foggy: '🌫️',
    };
    return icons[condition.toLowerCase()] || '🌤️';
  };

  const getTemperatureColor = (temp: number) => {
    if (temp > 85) return 'text-red-600';
    if (temp > 75) return 'text-orange-500';
    if (temp > 65) return 'text-green-600';
    if (temp > 45) return 'text-blue-500';
    return 'text-blue-700';
  };

  return (
    <Card
      onPress={onPress}
      variant="elevated"
      accessibilityLabel={
        accessibilityLabel ||
        `Weather in ${location}: ${weather.temperature} degrees, ${weather.condition}`
      }
      accessibilityHint="Tap for detailed weather information">
      <View className="flex-row items-center justify-between">
        <View className="flex-1">
          <Text className="mb-1 text-sm font-medium text-earth-600">📍 {location}</Text>

          <View className="mb-2 flex-row items-center">
            <Text className="mr-3 text-3xl">{getWeatherIcon(weather.condition)}</Text>
            <View>
              <Text className={`text-2xl font-bold ${getTemperatureColor(weather.temperature)}`}>
                {weather.temperature}°F
              </Text>
              <Text className="text-sm capitalize text-earth-600">{weather.condition}</Text>
            </View>
          </View>

          <View className="flex-row justify-between">
            <View className="items-center">
              <Text className="text-xs text-earth-500">Humidity</Text>
              <Text className="text-sm font-semibold text-earth-700">{weather.humidity}%</Text>
            </View>
            <View className="items-center">
              <Text className="text-xs text-earth-500">Wind</Text>
              <Text className="text-sm font-semibold text-earth-700">{weather.windSpeed} mph</Text>
            </View>
            <View className="items-center">
              <Text className="text-xs text-earth-500">UV Index</Text>
              <Text className="text-sm font-semibold text-earth-700">{weather.uvIndex}</Text>
            </View>
          </View>
        </View>
      </View>
    </Card>
  );
};
