import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, Pressable, Animated, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Button } from '../../src/components/ui/Button';
import { Input } from '../../src/components/ui/Input';
import { AppProvider, useApp } from '../../src/contexts/AppContext';
import { useAuthStore } from '../../src/stores/auth';

const LoginContent: React.FC = () => {
  const { t, isRTL, isVoiceEnabled, speak } = useApp();
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));

  // Form state
  const [loginMethod, setLoginMethod] = useState<'email' | 'phone'>('email');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Form validation
  const [errors, setErrors] = useState<{
    email?: string;
    phone?: string;
    password?: string;
  }>({});

  useEffect(() => {
    // Animate entrance
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();

    // Speak welcome message if voice is enabled
    if (isVoiceEnabled) {
      speak(t('login.title') + '. ' + t('login.description'));
    }
  }, [isVoiceEnabled]);

  const validateForm = () => {
    const newErrors: typeof errors = {};

    if (loginMethod === 'email') {
      if (!email.trim()) {
        newErrors.email = t('login.errors.emailRequired');
      } else if (!/\S+@\S+\.\S+/.test(email)) {
        newErrors.email = t('login.errors.emailInvalid');
      }
    } else {
      if (!phone.trim()) {
        newErrors.phone = t('login.errors.phoneRequired');
      } else if (!/^\+?[\d\s-()]+$/.test(phone)) {
        newErrors.phone = t('login.errors.phoneInvalid');
      }
    }

    if (!password.trim()) {
      newErrors.password = t('login.errors.passwordRequired');
    } else if (password.length < 6) {
      newErrors.password = t('login.errors.passwordTooShort');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validateForm()) {
      if (isVoiceEnabled) {
        const errorMessages = Object.values(errors).join('. ');
        speak(t('login.errors.validationFailed') + '. ' + errorMessages);
      }
      return;
    }

    setIsLoading(true);

    try {
      const ok = await useAuthStore.getState().login({
        email: loginMethod === 'email' ? email : undefined,
        phone: loginMethod === 'phone' ? phone : undefined,
        password,
      });

      if (!ok) {
        Alert.alert(t('login.errors.title'), t('login.errors.loginFailed'));
        return;
      }

      if (isVoiceEnabled) speak(t('login.success'));
      router.replace('/(tabs)');
    } catch (error) {
      if (isVoiceEnabled) speak(t('login.errors.loginFailed'));
      Alert.alert(t('login.errors.title'), t('login.errors.loginFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialLogin = async (provider: 'google' | 'apple') => {
    if (isVoiceEnabled) {
      speak(t('login.socialLogin.' + provider));
    }

    try {
      // TODO: Implement social login
      Alert.alert(t('common.comingSoon'), t('login.socialLogin.comingSoon'));
    } catch (error) {
      if (isVoiceEnabled) {
        speak(t('login.errors.socialLoginFailed'));
      }
      Alert.alert(t('login.errors.title'), t('login.errors.socialLoginFailed'));
    }
  };

  const handleForgotPassword = () => {
    if (isVoiceEnabled) {
      speak(t('login.forgotPassword.title'));
    }
    router.push('/(auth)/forgot-password');
  };

  const handleSignUp = () => {
    if (isVoiceEnabled) {
      speak(t('login.signUp.title'));
    }
    router.push('/(auth)/register');
  };

  const toggleLoginMethod = (method: 'email' | 'phone') => {
    setLoginMethod(method);
    setErrors({});
    if (isVoiceEnabled) {
      speak(t('login.switchTo.' + method));
    }
  };

  return (
    <SafeAreaView
      className={`flex-1 bg-gradient-to-b from-primary-50 to-white ${isRTL ? 'rtl' : 'ltr'}`}>
      <StatusBar style="dark" />

      <ScrollView
        className="flex-1"
        contentContainerStyle={{ flexGrow: 1 }}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled">
        <Animated.View
          className="flex-1 px-6 py-8"
          style={{
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          }}>
          {/* Header */}
          <View className="mb-8 items-center">
            <View className="mb-6 h-20 w-20 items-center justify-center rounded-full bg-primary-100">
              <Text className="text-3xl">🌾</Text>
            </View>

            <Text
              className={`mb-2 text-center text-2xl font-bold text-earth-900 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t('login.title')}
            </Text>

            <Text
              className={`text-center text-base text-earth-600 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t('login.description')}
            </Text>
          </View>

          {/* Login Method Toggle */}
          <View className="mb-6 rounded-xl bg-white p-1 shadow-sm">
            <View className={`flex-row ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Pressable
                onPress={() => toggleLoginMethod('email')}
                className={`flex-1 rounded-lg py-3 ${
                  loginMethod === 'email' ? 'bg-primary-100' : 'bg-transparent'
                }`}
                accessibilityRole="button"
                accessibilityLabel={t('login.loginWithEmail')}
                accessibilityState={{ selected: loginMethod === 'email' }}>
                <Text
                  className={`text-center font-semibold ${
                    loginMethod === 'email' ? 'text-primary-700' : 'text-earth-600'
                  }`}>
                  📧 {t('login.email')}
                </Text>
              </Pressable>

              <Pressable
                onPress={() => toggleLoginMethod('phone')}
                className={`flex-1 rounded-lg py-3 ${
                  loginMethod === 'phone' ? 'bg-primary-100' : 'bg-transparent'
                }`}
                accessibilityRole="button"
                accessibilityLabel={t('login.loginWithPhone')}
                accessibilityState={{ selected: loginMethod === 'phone' }}>
                <Text
                  className={`text-center font-semibold ${
                    loginMethod === 'phone' ? 'text-primary-700' : 'text-earth-600'
                  }`}>
                  📱 {t('login.phone')}
                </Text>
              </Pressable>
            </View>
          </View>

          {/* Login Form */}
          <View className="mb-6 gap-4">
            {loginMethod === 'email' ? (
              <Input
                label={t('login.email')}
                placeholder={t('login.emailPlaceholder')}
                value={email}
                onChangeText={setEmail}
                error={errors.email}
                keyboardType="email-address"
                autoCapitalize="none"
                leftIcon={<Text className="text-lg text-earth-500">📧</Text>}
                voiceInputEnabled={isVoiceEnabled}
                voiceFeedbackEnabled={isVoiceEnabled}
                onVoiceFeedback={speak}
                accessibilityLabel={t('login.email')}
                accessibilityHint={t('login.emailHint')}
              />
            ) : (
              <Input
                label={t('login.phone')}
                placeholder={t('login.phonePlaceholder')}
                value={phone}
                onChangeText={setPhone}
                error={errors.phone}
                keyboardType="phone-pad"
                leftIcon={<Text className="text-lg text-earth-500">📱</Text>}
                voiceInputEnabled={isVoiceEnabled}
                voiceFeedbackEnabled={isVoiceEnabled}
                onVoiceFeedback={speak}
                accessibilityLabel={t('login.phone')}
                accessibilityHint={t('login.phoneHint')}
              />
            )}

            <Input
              label={t('login.password')}
              placeholder={t('login.passwordPlaceholder')}
              value={password}
              onChangeText={setPassword}
              error={errors.password}
              secureTextEntry={!showPassword}
              leftIcon={<Text className="text-lg text-earth-500">🔒</Text>}
              rightIcon={
                <Text className="text-lg text-earth-500">{showPassword ? '👁️' : '🙈'}</Text>
              }
              onRightIconPress={() => setShowPassword(!showPassword)}
              voiceInputEnabled={isVoiceEnabled}
              voiceFeedbackEnabled={isVoiceEnabled}
              onVoiceFeedback={speak}
              accessibilityLabel={t('login.password')}
              accessibilityHint={t('login.passwordHint')}
            />
          </View>

          {/* Remember Me & Forgot Password */}
          <View
            className={`mb-6 flex-row items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Pressable
              onPress={() => {
                setRememberMe(!rememberMe);
                if (isVoiceEnabled) {
                  speak(
                    rememberMe ? t('login.rememberMe.disabled') : t('login.rememberMe.enabled')
                  );
                }
              }}
              className={`flex-row items-center ${isRTL ? 'flex-row-reverse' : ''}`}
              accessibilityRole="checkbox"
              accessibilityState={{ checked: rememberMe }}
              accessibilityLabel={t('login.rememberMe.label')}>
              <View
                className={`mr-2 h-5 w-5 items-center justify-center rounded border-2 ${
                  rememberMe ? 'border-primary-500 bg-primary-500' : 'border-earth-300'
                }`}>
                {rememberMe && <Text className="text-xs text-white">✓</Text>}
              </View>
              <Text className="text-sm text-earth-700">{t('login.rememberMe.label')}</Text>
            </Pressable>

            <Pressable
              onPress={handleForgotPassword}
              accessibilityRole="button"
              accessibilityLabel={t('login.forgotPassword.title')}>
              <Text className="text-sm font-medium text-primary-600">
                {t('login.forgotPassword.title')}
              </Text>
            </Pressable>
          </View>

          {/* Login Button */}
          <Button
            title={t('login.signIn')}
            onPress={handleLogin}
            variant="primary"
            size="large"
            fullWidth
            loading={isLoading}
            disabled={isLoading}
            className="mb-6"
            accessibilityLabel={t('login.signIn')}
            accessibilityHint={t('login.signInHint')}
            voiceFeedbackEnabled={isVoiceEnabled}
            onVoiceFeedback={speak}
          />

          {/* Divider */}
          <View className={`mb-6 flex-row items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <View className="h-px flex-1 bg-earth-200" />
            <Text className="mx-4 text-sm text-earth-500">{t('login.orContinueWith')}</Text>
            <View className="h-px flex-1 bg-earth-200" />
          </View>

          {/* Social Login Buttons */}
          <View className="mb-8 gap-3">
            <Button
              title={t('login.continueWithGoogle')}
              onPress={() => handleSocialLogin('google')}
              variant="outline"
              size="large"
              fullWidth
              icon={<Text className="text-xl">🔍</Text>}
              accessibilityLabel={t('login.continueWithGoogle')}
              accessibilityHint={t('login.socialLoginHint')}
              voiceFeedbackEnabled={isVoiceEnabled}
              onVoiceFeedback={speak}
            />

            <Button
              title={t('login.continueWithApple')}
              onPress={() => handleSocialLogin('apple')}
              variant="outline"
              size="large"
              fullWidth
              icon={<Text className="text-xl">🍎</Text>}
              accessibilityLabel={t('login.continueWithApple')}
              accessibilityHint={t('login.socialLoginHint')}
              voiceFeedbackEnabled={isVoiceEnabled}
              onVoiceFeedback={speak}
            />
          </View>

          {/* Sign Up Link */}
          <View className="items-center">
            <Text
              className={`text-center text-base text-earth-600 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t('login.noAccount')}{' '}
              <Pressable
                onPress={handleSignUp}
                accessibilityRole="button"
                accessibilityLabel={t('login.signUp.title')}>
                <Text className="font-semibold text-primary-600">{t('login.signUp.title')}</Text>
              </Pressable>
            </Text>
          </View>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default function Login() {
  return (
    <AppProvider>
      <LoginContent />
    </AppProvider>
  );
}
