import * as Speech from 'expo-speech';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface VoiceSettings {
    enabled: boolean;
    language: string;
    rate: number;
    pitch: number;
}

const DEFAULT_VOICE_SETTINGS: VoiceSettings = {
    enabled: false,
    language: 'en',
    rate: 0.8,
    pitch: 1.0,
};

const VOICE_SETTINGS_KEY = 'voice_settings';

export class VoiceService {
    private static instance: VoiceService;
    private settings: VoiceSettings = DEFAULT_VOICE_SETTINGS;

    static getInstance(): VoiceService {
        if (!VoiceService.instance) {
            VoiceService.instance = new VoiceService();
        }
        return VoiceService.instance;
    }

    // Static convenience methods
    static async speak(text: string, options?: Partial<Speech.SpeechOptions>): Promise<void> {
        const instance = VoiceService.getInstance();
        return instance.speak(text, options);
    }

    static stop(): void {
        Speech.stop();
    }

    static async isEnabled(): Promise<boolean> {
        const instance = VoiceService.getInstance();
        return instance.isEnabled();
    }

    async initialize(): Promise<void> {
        try {
            const savedSettings = await AsyncStorage.getItem(VOICE_SETTINGS_KEY);
            if (savedSettings) {
                this.settings = { ...DEFAULT_VOICE_SETTINGS, ...JSON.parse(savedSettings) };
            }
        } catch (error) {
            console.warn('Failed to load voice settings:', error);
        }
    }

    async updateSettings(newSettings: Partial<VoiceSettings>): Promise<void> {
        this.settings = { ...this.settings, ...newSettings };
        try {
            await AsyncStorage.setItem(VOICE_SETTINGS_KEY, JSON.stringify(this.settings));
        } catch (error) {
            console.warn('Failed to save voice settings:', error);
        }
    }

    getSettings(): VoiceSettings {
        return { ...this.settings };
    }

    async speak(text: string, options?: Partial<Speech.SpeechOptions>): Promise<void> {
        if (!this.settings.enabled) return;

        const speechOptions: Speech.SpeechOptions = {
            language: this.settings.language,
            rate: this.settings.rate,
            pitch: this.settings.pitch,
            ...options,
        };

        try {
            await Speech.speak(text, speechOptions);
        } catch (error) {
            console.warn('Failed to speak text:', error);
        }
    }

    stop(): void {
        Speech.stop();
    }

    async isEnabled(): Promise<boolean> {
        return this.settings.enabled;
    }

    async enableVoiceMode(): Promise<void> {
        await this.updateSettings({ enabled: true });
        await this.speak('Voice mode enabled. I will now read all interface elements and responses aloud.');
    }

    async disableVoiceMode(): Promise<void> {
        await this.speak('Voice mode disabled.');
        await this.updateSettings({ enabled: false });
    }

    async setLanguage(language: string): Promise<void> {
        await this.updateSettings({ language });
        const message = language === 'ar' ? 'تم تغيير اللغة إلى العربية' : 'Language changed to English';
        await this.speak(message);
    }
}