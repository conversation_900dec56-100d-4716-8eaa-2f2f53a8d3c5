import React, { useEffect, useState } from 'react';
import { View, Text, Animated } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Button } from '../../src/components/ui/Button';
import { AppProvider, useApp } from '../../src/contexts/AppContext';

const CompletionContent: React.FC = () => {
    const { t, isRTL, isVoiceEnabled, speak, currentLanguage } = useApp();
    const [fadeAnim] = useState(new Animated.Value(0));
    const [scaleAnim] = useState(new Animated.Value(0));
    const [slideAnim] = useState(new Animated.Value(50));

    useEffect(() => {
        // Start animations
        Animated.sequence([
            // Fade in the container
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 500,
                useNativeDriver: true,
            }),
            // Scale in the success icon
            Animated.spring(scaleAnim, {
                toValue: 1,
                tension: 50,
                friction: 7,
                useNativeDriver: true,
            }),
            // Slide in the content
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 600,
                useNativeDriver: true,
            }),
        ]).start();

        // Speak welcome message
        if (isVoiceEnabled) {
            setTimeout(() => {
                speak(
                    currentLanguage === 'ar'
                        ? 'مرحباً بك في مساعد الزراعة الذكي! تم إعداد حسابك بنجاح.'
                        : 'Welcome to AI Farming Assistant! Your account has been set up successfully.'
                );
            }, 1000);
        }
    }, [isVoiceEnabled, currentLanguage]);

    const handleGetStarted = () => {
        if (isVoiceEnabled) {
            speak(
                currentLanguage === 'ar'
                    ? 'بدء استخدام التطبيق'
                    : 'Starting the app'
            );
        }

        // In a real app, this would navigate to the main app
        // For now, we'll just show an alert
        router.replace('/'); // This would go to the main app
    };

    return (
        <SafeAreaView className={`flex-1 bg-gradient-to-b from-primary-50 to-white ${isRTL ? 'rtl' : 'ltr'}`}>
            <StatusBar style="dark" />

            <Animated.View
                className="flex-1 items-center justify-center px-6"
                style={{ opacity: fadeAnim }}
            >
                {/* Success Animation */}
                <Animated.View
                    className="mb-8 h-32 w-32 items-center justify-center rounded-full bg-primary-100"
                    style={{ transform: [{ scale: scaleAnim }] }}
                >
                    <Text className="text-6xl">🎉</Text>
                </Animated.View>

                {/* Success Content */}
                <Animated.View
                    className="items-center"
                    style={{ transform: [{ translateY: slideAnim }] }}
                >
                    {/* Title */}
                    <Text className={`mb-4 text-center text-3xl font-bold text-earth-900 ${isRTL ? 'text-right' : 'text-left'}`}>
                        {currentLanguage === 'ar'
                            ? 'مرحباً بك!'
                            : 'Welcome!'
                        }
                    </Text>

                    {/* Subtitle */}
                    <Text className={`mb-6 text-center text-xl text-primary-600 ${isRTL ? 'text-right' : 'text-left'}`}>
                        {currentLanguage === 'ar'
                            ? 'تم إعداد حسابك بنجاح'
                            : 'Your account has been set up successfully'
                        }
                    </Text>

                    {/* Description */}
                    <Text className={`mb-8 text-center text-base leading-relaxed text-earth-700 ${isRTL ? 'text-right' : 'text-left'}`}>
                        {currentLanguage === 'ar'
                            ? 'أنت الآن جاهز لاستكشاف عالم الزراعة الذكية مع الذكاء الاصطناعي. احصل على نصائح مخصصة، وخطط للمحاصيل، وتواصل مع المزارعين الآخرين.'
                            : 'You\'re now ready to explore the world of smart farming with AI. Get personalized advice, plan your crops, and connect with other farmers.'
                        }
                    </Text>

                    {/* Features List */}
                    <View className="mb-8 w-full">
                        <View className={`mb-3 flex-row items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <Text className="mr-3 text-2xl">🤖</Text>
                            <Text className={`flex-1 text-base text-earth-700 ${isRTL ? 'text-right' : 'text-left'}`}>
                                {currentLanguage === 'ar'
                                    ? 'استشارات زراعية مدعومة بالذكاء الاصطناعي'
                                    : 'AI-powered agricultural consultations'
                                }
                            </Text>
                        </View>

                        <View className={`mb-3 flex-row items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <Text className="mr-3 text-2xl">📱</Text>
                            <Text className={`flex-1 text-base text-earth-700 ${isRTL ? 'text-right' : 'text-left'}`}>
                                {currentLanguage === 'ar'
                                    ? 'تحليل الصور للنباتات والتربة'
                                    : 'Image analysis for plants and soil'
                                }
                            </Text>
                        </View>

                        <View className={`mb-3 flex-row items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <Text className="mr-3 text-2xl">🌤️</Text>
                            <Text className={`flex-1 text-base text-earth-700 ${isRTL ? 'text-right' : 'text-left'}`}>
                                {currentLanguage === 'ar'
                                    ? 'توصيات محلية بناءً على الطقس'
                                    : 'Weather-based local recommendations'
                                }
                            </Text>
                        </View>

                        <View className={`mb-3 flex-row items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <Text className="mr-3 text-2xl">👥</Text>
                            <Text className={`flex-1 text-base text-earth-700 ${isRTL ? 'text-right' : 'text-left'}`}>
                                {currentLanguage === 'ar'
                                    ? 'مجتمع المزارعين والدعم'
                                    : 'Farmer community and support'
                                }
                            </Text>
                        </View>
                    </View>

                    {/* Get Started Button */}
                    <Button
                        title={currentLanguage === 'ar' ? 'ابدأ الآن' : 'Get Started'}
                        onPress={handleGetStarted}
                        variant="primary"
                        size="large"
                        fullWidth
                        icon={<Text className="text-xl">🚀</Text>}
                        accessibilityLabel="Start using the app"
                        accessibilityHint="Begin your smart farming journey"
                        voiceFeedbackEnabled={isVoiceEnabled}
                        onVoiceFeedback={speak}
                    />

                    {/* Voice Mode Reminder */}
                    {isVoiceEnabled && (
                        <View className="mt-6 rounded-xl bg-secondary-50 p-4">
                            <View className={`flex-row items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                                <Text className="mr-3 text-2xl">🔊</Text>
                                <Text className={`flex-1 text-sm text-secondary-700 ${isRTL ? 'text-right' : 'text-left'}`}>
                                    {currentLanguage === 'ar'
                                        ? 'الوضع الصوتي مفعل - سأقرأ جميع المعلومات بصوت عالٍ'
                                        : 'Voice mode is active - I\'ll read all information aloud'
                                    }
                                </Text>
                            </View>
                        </View>
                    )}
                </Animated.View>
            </Animated.View>
        </SafeAreaView>
    );
};

export default function Completion() {
    return (
        <AppProvider>
            <CompletionContent />
        </AppProvider>
    );
}