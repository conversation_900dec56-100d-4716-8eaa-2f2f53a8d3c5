import React, { useState } from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    ScrollView,
    Image,
    Alert,
    KeyboardAvoidingView,
    Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as Location from 'expo-location';
import { colors } from '../../src/design-system';
import { VoiceButton } from '../../src/components/ui/VoiceButton';
import { useCommunityStore } from '../../src/stores/community';
import { useVoiceStore } from '../../src/stores/voice';

export default function CreatePostScreen() {
    const [title, setTitle] = useState('');
    const [content, setContent] = useState('');
    const [images, setImages] = useState<string[]>([]);
    const [location, setLocation] = useState<any>(null);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [includeLocation, setIncludeLocation] = useState(false);

    const { createPost } = useCommunityStore();
    const { speak, isVoiceEnabled, startListening } = useVoiceStore();

    const handleImagePicker = async () => {
        try {
            const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
            if (status !== 'granted') {
                Alert.alert('Permission needed', 'Please grant camera roll permissions');
                return;
            }

            const result = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ImagePicker.MediaTypeOptions.Images,
                allowsMultipleSelection: true,
                quality: 0.8,
                aspect: [4, 3],
            });

            if (!result.canceled && result.assets) {
                const newImages = result.assets.map(asset => asset.uri);
                setImages(prev => [...prev, ...newImages].slice(0, 4)); // Max 4 images

                if (isVoiceEnabled) {
                    speak(`Added ${newImages.length} image${newImages.length > 1 ? 's' : ''} to your post`);
                }
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to pick images');
        }
    };

    const handleCamera = async () => {
        try {
            const { status } = await ImagePicker.requestCameraPermissionsAsync();
            if (status !== 'granted') {
                Alert.alert('Permission needed', 'Please grant camera permissions');
                return;
            }

            const result = await ImagePicker.launchCameraAsync({
                quality: 0.8,
                aspect: [4, 3],
            });

            if (!result.canceled && result.assets?.[0]) {
                setImages(prev => [...prev, result.assets[0].uri].slice(0, 4));

                if (isVoiceEnabled) {
                    speak('Photo added to your post');
                }
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to take photo');
        }
    };

    const handleLocationToggle = async () => {
        if (!includeLocation) {
            try {
                const { status } = await Location.requestForegroundPermissionsAsync();
                if (status !== 'granted') {
                    Alert.alert('Permission needed', 'Please grant location permissions');
                    return;
                }

                const currentLocation = await Location.getCurrentPositionAsync({});
                setLocation(currentLocation);
                setIncludeLocation(true);

                if (isVoiceEnabled) {
                    speak('Location added to your post');
                }
            } catch (error) {
                Alert.alert('Error', 'Failed to get location');
            }
        } else {
            setLocation(null);
            setIncludeLocation(false);

            if (isVoiceEnabled) {
                speak('Location removed from your post');
            }
        }
    };

    const handleVoiceInput = async (field: 'title' | 'content') => {
        try {
            if (isVoiceEnabled) {
                speak(`Say your ${field === 'title' ? 'post title' : 'post content'}`);
                const voiceText = await startListening();

                if (voiceText) {
                    if (field === 'title') {
                        setTitle(voiceText);
                    } else {
                        setContent(voiceText);
                    }
                    speak(`${field === 'title' ? 'Title' : 'Content'} added`);
                }
            }
        } catch (error) {
            Alert.alert('Voice Error', 'Failed to process voice input');
        }
    };

    const handleSubmit = async () => {
        if (!title.trim()) {
            Alert.alert('Missing Title', 'Please add a title to your post');
            return;
        }

        if (!content.trim()) {
            Alert.alert('Missing Content', 'Please add some content to your post');
            return;
        }

        setIsSubmitting(true);

        try {
            await createPost({
                title: title.trim(),
                content: content.trim(),
                images,
                location: includeLocation ? location : null,
            });

            if (isVoiceEnabled) {
                speak('Post created successfully');
            }

            router.back();
        } catch (error) {
            Alert.alert('Error', 'Failed to create post');
        } finally {
            setIsSubmitting(false);
        }
    };

    const removeImage = (index: number) => {
        setImages(prev => prev.filter((_, i) => i !== index));
        if (isVoiceEnabled) {
            speak('Image removed');
        }
    };

    return (
        <SafeAreaView style={{ flex: 1, backgroundColor: 'white' }}>
            <KeyboardAvoidingView
                style={{ flex: 1 }}
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            >
                {/* Header */}
                <View style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    paddingHorizontal: 16,
                    paddingVertical: 12,
                    borderBottomWidth: 1,
                    borderBottomColor: colors.primary[100],
                }}>
                    <TouchableOpacity
                        onPress={() => router.back()}
                        accessibilityLabel="Cancel post creation"
                    >
                        <Text style={{ color: colors.earth[500], fontSize: 16 }}>Cancel</Text>
                    </TouchableOpacity>

                    <Text style={{
                        fontSize: 18,
                        fontWeight: '600',
                        color: colors.primary[900],
                    }}>
                        Create Post
                    </Text>

                    <TouchableOpacity
                        onPress={handleSubmit}
                        disabled={isSubmitting || !title.trim() || !content.trim()}
                        style={{
                            backgroundColor: (!title.trim() || !content.trim())
                                ? colors.earth[200]
                                : colors.primary[500],
                            paddingHorizontal: 16,
                            paddingVertical: 6,
                            borderRadius: 16,
                        }}
                        accessibilityLabel="Publish post"
                    >
                        <Text style={{
                            color: 'white',
                            fontWeight: '600',
                        }}>
                            {isSubmitting ? 'Posting...' : 'Post'}
                        </Text>
                    </TouchableOpacity>
                </View>

                <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
                    <View style={{ padding: 16 }}>
                        {/* Title Input */}
                        <View style={{ marginBottom: 20 }}>
                            <View style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                marginBottom: 8,
                            }}>
                                <Text style={{
                                    fontSize: 16,
                                    fontWeight: '600',
                                    color: colors.primary[900],
                                }}>
                                    Post Title
                                </Text>
                                <VoiceButton
                                    onPress={() => handleVoiceInput('title')}
                                    size="small"
                                />
                            </View>
                            <TextInput
                                value={title}
                                onChangeText={setTitle}
                                placeholder="What's happening on your farm?"
                                style={{
                                    borderWidth: 1,
                                    borderColor: colors.primary[200],
                                    borderRadius: 12,
                                    padding: 12,
                                    fontSize: 16,
                                    minHeight: 48,
                                }}
                                maxLength={100}
                                accessibilityLabel="Post title input"
                            />
                            <Text style={{
                                fontSize: 12,
                                color: colors.earth[500],
                                textAlign: 'right',
                                marginTop: 4,
                            }}>
                                {title.length}/100
                            </Text>
                        </View>

                        {/* Content Input */}
                        <View style={{ marginBottom: 20 }}>
                            <View style={{
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                marginBottom: 8,
                            }}>
                                <Text style={{
                                    fontSize: 16,
                                    fontWeight: '600',
                                    color: colors.primary[900],
                                }}>
                                    Content
                                </Text>
                                <VoiceButton
                                    onPress={() => handleVoiceInput('content')}
                                    size="small"
                                />
                            </View>
                            <TextInput
                                value={content}
                                onChangeText={setContent}
                                placeholder="Share your farming experience, ask questions, or give advice..."
                                multiline
                                style={{
                                    borderWidth: 1,
                                    borderColor: colors.primary[200],
                                    borderRadius: 12,
                                    padding: 12,
                                    fontSize: 16,
                                    minHeight: 120,
                                    textAlignVertical: 'top',
                                }}
                                maxLength={500}
                                accessibilityLabel="Post content input"
                            />
                            <Text style={{
                                fontSize: 12,
                                color: colors.earth[500],
                                textAlign: 'right',
                                marginTop: 4,
                            }}>
                                {content.length}/500
                            </Text>
                        </View>

                        {/* Images */}
                        {images.length > 0 && (
                            <View style={{ marginBottom: 20 }}>
                                <Text style={{
                                    fontSize: 16,
                                    fontWeight: '600',
                                    color: colors.primary[900],
                                    marginBottom: 8,
                                }}>
                                    Photos ({images.length}/4)
                                </Text>
                                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                                    <View style={{ flexDirection: 'row', gap: 8 }}>
                                        {images.map((uri, index) => (
                                            <View key={index} style={{ position: 'relative' }}>
                                                <Image
                                                    source={{ uri }}
                                                    style={{
                                                        width: 80,
                                                        height: 80,
                                                        borderRadius: 8,
                                                    }}
                                                />
                                                <TouchableOpacity
                                                    onPress={() => removeImage(index)}
                                                    style={{
                                                        position: 'absolute',
                                                        top: -8,
                                                        right: -8,
                                                        backgroundColor: colors.status.error,
                                                        borderRadius: 12,
                                                        width: 24,
                                                        height: 24,
                                                        justifyContent: 'center',
                                                        alignItems: 'center',
                                                    }}
                                                    accessibilityLabel={`Remove image ${index + 1}`}
                                                >
                                                    <Ionicons name="close" size={16} color="white" />
                                                </TouchableOpacity>
                                            </View>
                                        ))}
                                    </View>
                                </ScrollView>
                            </View>
                        )}

                        {/* Action Buttons */}
                        <View style={{
                            flexDirection: 'row',
                            justifyContent: 'space-around',
                            paddingVertical: 20,
                            borderTopWidth: 1,
                            borderTopColor: colors.primary[100],
                        }}>
                            <TouchableOpacity
                                onPress={handleCamera}
                                style={{
                                    alignItems: 'center',
                                    padding: 12,
                                }}
                                accessibilityLabel="Take photo"
                            >
                                <Ionicons name="camera" size={24} color={colors.primary[500]} />
                                <Text style={{
                                    fontSize: 12,
                                    color: colors.primary[500],
                                    marginTop: 4,
                                }}>
                                    Camera
                                </Text>
                            </TouchableOpacity>

                            <TouchableOpacity
                                onPress={handleImagePicker}
                                style={{
                                    alignItems: 'center',
                                    padding: 12,
                                }}
                                accessibilityLabel="Choose from gallery"
                            >
                                <Ionicons name="images" size={24} color={colors.primary[500]} />
                                <Text style={{
                                    fontSize: 12,
                                    color: colors.primary[500],
                                    marginTop: 4,
                                }}>
                                    Gallery
                                </Text>
                            </TouchableOpacity>

                            <TouchableOpacity
                                onPress={handleLocationToggle}
                                style={{
                                    alignItems: 'center',
                                    padding: 12,
                                }}
                                accessibilityLabel={includeLocation ? "Remove location" : "Add location"}
                            >
                                <Ionicons
                                    name={includeLocation ? "location" : "location-outline"}
                                    size={24}
                                    color={includeLocation ? colors.status.success : colors.primary[500]}
                                />
                                <Text style={{
                                    fontSize: 12,
                                    color: includeLocation ? colors.status.success : colors.primary[500],
                                    marginTop: 4,
                                }}>
                                    Location
                                </Text>
                            </TouchableOpacity>
                        </View>

                        {/* Location Display */}
                        {includeLocation && location && (
                            <View style={{
                                backgroundColor: colors.primary[50],
                                padding: 12,
                                borderRadius: 8,
                                flexDirection: 'row',
                                alignItems: 'center',
                                gap: 8,
                            }}>
                                <Ionicons name="location" size={16} color={colors.status.success} />
                                <Text style={{
                                    fontSize: 14,
                                    color: colors.primary[700],
                                }}>
                                    Location will be shared with your post
                                </Text>
                            </View>
                        )}
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        </SafeAreaView>
    );
}