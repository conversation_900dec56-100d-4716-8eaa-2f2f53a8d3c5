import React, { useState } from 'react';
import { View, ScrollView, Text } from 'react-native';
import { Button, Input, Card, Modal } from '../ui';
// import { PersonalInfoForm, FarmSetupForm } from '../forms';
import {
  TabNavigation,
  HeaderNavigation,
  DrawerNavigation,
  AGRICULTURAL_TABS,
  AGRICULTURAL_DRAWER_SECTIONS,
} from '../navigation';

export const CoreComponentsShowcase: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('home');
  const [voiceEnabled, setVoiceEnabled] = useState(false);
  const [inputValue, setInputValue] = useState('');

  const mockVoiceFeedback = (text: string) => {
    console.log('Voice feedback:', text);
  };

  const mockWeatherData: import('../navigation').HeaderWeatherData = {
    temperature: 72,
    condition: 'Sunny',
    icon: '☀️',
    location: 'Farm Location',
  };

  return (
    <View className="flex-1 bg-earth-50">
      {/* Header */}
      <HeaderNavigation
        title="Core Components"
        showWeather={true}
        weatherData={mockWeatherData}
        showVoiceToggle={true}
        isVoiceEnabled={voiceEnabled}
        onVoiceToggle={() => setVoiceEnabled(!voiceEnabled)}
        voiceFeedbackEnabled={voiceEnabled}
        onVoiceFeedback={mockVoiceFeedback}
        rightActions={
          <Button
            title="Menu"
            onPress={() => setDrawerVisible(true)}
            variant="ghost"
            size="small"
          />
        }
      />

      <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={false}>
        {/* UI Components Section */}
        <Card variant="elevated" className="mb-6">
          <Text className="mb-4 text-lg font-bold text-earth-900">UI Components</Text>

          <View className="gap-4">
            <Input
              label="Sample Input"
              placeholder="Type something..."
              value={inputValue}
              onChangeText={setInputValue}
              voiceInputEnabled={true}
              voiceFeedbackEnabled={voiceEnabled}
              onVoiceFeedback={mockVoiceFeedback}
            />

            <View className="flex-row gap-3">
              <Button
                title="Primary"
                onPress={() => mockVoiceFeedback('Primary button pressed')}
                variant="primary"
                className="flex-1"
                voiceFeedbackEnabled={voiceEnabled}
                onVoiceFeedback={mockVoiceFeedback}
              />
              <Button
                title="Show Modal"
                onPress={() => setModalVisible(true)}
                variant="outline"
                className="flex-1"
                voiceFeedbackEnabled={voiceEnabled}
                onVoiceFeedback={mockVoiceFeedback}
              />
            </View>
          </View>
        </Card>

        {/* Agricultural Cards */}
        <Card
          variant="agricultural"
          dataType="weather"
          priority="high"
          className="mb-4"
          voiceFeedbackEnabled={voiceEnabled}
          onVoiceFeedback={mockVoiceFeedback}
          accessibilityLabel="Weather information card">
          <Text className="mb-2 text-lg font-semibold text-blue-700">🌤️ Weather Alert</Text>
          <Text className="text-blue-600">Perfect conditions for planting today!</Text>
        </Card>

        <Card
          variant="agricultural"
          dataType="task"
          priority="urgent"
          className="mb-4"
          voiceFeedbackEnabled={voiceEnabled}
          onVoiceFeedback={mockVoiceFeedback}
          accessibilityLabel="Urgent task card">
          <Text className="mb-2 text-lg font-semibold text-yellow-700">⚠️ Urgent Task</Text>
          <Text className="text-yellow-600">Water the tomatoes - they&apos;re looking dry!</Text>
        </Card>
      </ScrollView>

      {/* Tab Navigation */}
      <TabNavigation
        tabs={AGRICULTURAL_TABS}
        activeTab={activeTab}
        onTabPress={setActiveTab}
        voiceFeedbackEnabled={voiceEnabled}
        onVoiceFeedback={mockVoiceFeedback}
      />

      {/* Modal */}
      <Modal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        title="Sample Modal"
        type="confirmation"
        voiceFeedbackEnabled={voiceEnabled}
        onVoiceFeedback={mockVoiceFeedback}
        autoAnnounce={true}>
        <Text className="mb-4 text-earth-700">
          This is a sample modal with voice feedback support.
        </Text>
        <Button
          title="Close"
          onPress={() => setModalVisible(false)}
          variant="primary"
          fullWidth
          voiceFeedbackEnabled={voiceEnabled}
          onVoiceFeedback={mockVoiceFeedback}
        />
      </Modal>

      {/* Drawer Navigation */}
      <DrawerNavigation
        visible={drawerVisible}
        onClose={() => setDrawerVisible(false)}
        sections={AGRICULTURAL_DRAWER_SECTIONS}
        userInfo={{
          name: 'John Farmer',
          email: '<EMAIL>',
          avatar: '👨‍🌾',
        }}
        voiceFeedbackEnabled={voiceEnabled}
        onVoiceFeedback={mockVoiceFeedback}
      />
    </View>
  );
};
