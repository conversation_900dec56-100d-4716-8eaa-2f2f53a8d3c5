import React, { useState, useMemo } from 'react';
import { View, Text, ScrollView, TouchableOpacity, TextInput } from 'react-native';
import { CropType, cropTypes, cropCategories } from '../../data/crops';
import { CropRecommendation } from '../../types/crops';

interface CropSelectionGridProps {
    selectedCropId?: string;
    onCropSelect: (crop: CropType) => void;
    recommendations?: CropRecommendation[];
    showRecommendations?: boolean;
    voiceEnabled?: boolean;
    onVoiceCommand?: (command: string) => void;
}

export default function CropSelectionGrid({
    selectedCropId,
    onCropSelect,
    recommendations = [],
    showRecommendations = true,
    voiceEnabled = false,
    onVoiceCommand,
}: CropSelectionGridProps) {
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

    const filteredCrops = useMemo(() => {
        let filtered = cropTypes;

        // Filter by search query
        if (searchQuery.trim()) {
            filtered = filtered.filter(crop =>
                crop.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                crop.nameAr.includes(searchQuery) ||
                crop.description.toLowerCase().includes(searchQuery.toLowerCase())
            );
        }

        // Filter by category
        if (selectedCategory) {
            filtered = filtered.filter(crop => crop.category === selectedCategory);
        }

        // Sort by recommendations if available
        if (showRecommendations && recommendations.length > 0) {
            const recommendationMap = new Map(
                recommendations.map(rec => [rec.cropId, rec.suitabilityScore])
            );

            filtered.sort((a, b) => {
                const scoreA = recommendationMap.get(a.id) || 0;
                const scoreB = recommendationMap.get(b.id) || 0;
                return scoreB - scoreA;
            });
        }

        return filtered;
    }, [searchQuery, selectedCategory, recommendations, showRecommendations]);

    const getRecommendationScore = (cropId: string): number => {
        const recommendation = recommendations.find(rec => rec.cropId === cropId);
        return recommendation?.suitabilityScore || 0;
    };

    const getRecommendationBadge = (score: number) => {
        if (score >= 80) return { text: 'Highly Recommended', color: 'bg-green-500', textColor: 'text-white' };
        if (score >= 60) return { text: 'Recommended', color: 'bg-yellow-500', textColor: 'text-white' };
        if (score >= 40) return { text: 'Suitable', color: 'bg-blue-500', textColor: 'text-white' };
        return null;
    };

    const handleVoiceSearch = () => {
        if (onVoiceCommand) {
            onVoiceCommand('search_crops');
        }
    };

    return (
        <View className="flex-1">
            {/* Search Bar */}
            <View className="px-4 mb-4">
                <View className="flex-row items-center bg-white rounded-lg border border-gray-200 px-3 py-2">
                    <TextInput
                        className="flex-1 text-base text-gray-900"
                        placeholder="Search crops..."
                        placeholderTextColor="#9CA3AF"
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                        accessibilityLabel="Search crops"
                        accessibilityHint="Type to search for specific crops"
                    />
                    {voiceEnabled && (
                        <TouchableOpacity
                            onPress={handleVoiceSearch}
                            className="ml-2 p-2 bg-green-500 rounded-full"
                            accessibilityLabel="Voice search"
                            accessibilityHint="Use voice to search for crops"
                        >
                            <Text className="text-white text-sm">🎤</Text>
                        </TouchableOpacity>
                    )}
                </View>
            </View>

            {/* Category Filter */}
            <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                className="mb-4"
                contentContainerStyle={{ paddingHorizontal: 16 }}
            >
                <TouchableOpacity
                    onPress={() => setSelectedCategory(null)}
                    className={`mr-3 px-4 py-2 rounded-full border ${selectedCategory === null
                            ? 'bg-green-500 border-green-500'
                            : 'bg-white border-gray-300'
                        }`}
                    accessibilityLabel="All categories"
                >
                    <Text className={`font-medium ${selectedCategory === null ? 'text-white' : 'text-gray-700'
                        }`}>
                        All
                    </Text>
                </TouchableOpacity>
                {cropCategories.map(category => (
                    <TouchableOpacity
                        key={category.id}
                        onPress={() => setSelectedCategory(category.id)}
                        className={`mr-3 px-4 py-2 rounded-full border ${selectedCategory === category.id
                                ? 'bg-green-500 border-green-500'
                                : 'bg-white border-gray-300'
                            }`}
                        accessibilityLabel={`Filter by ${category.name}`}
                    >
                        <Text className={`font-medium ${selectedCategory === category.id ? 'text-white' : 'text-gray-700'
                            }`}>
                            {category.icon} {category.name}
                        </Text>
                    </TouchableOpacity>
                ))}
            </ScrollView>

            {/* Crops Grid */}
            <ScrollView className="flex-1 px-4">
                <View className="flex-row flex-wrap justify-between">
                    {filteredCrops.map(crop => {
                        const score = getRecommendationScore(crop.id);
                        const badge = getRecommendationBadge(score);
                        const isSelected = selectedCropId === crop.id;

                        return (
                            <TouchableOpacity
                                key={crop.id}
                                onPress={() => onCropSelect(crop)}
                                className={`w-[48%] mb-4 p-4 rounded-lg border-2 ${isSelected
                                        ? 'border-green-500 bg-green-50'
                                        : 'border-gray-200 bg-white'
                                    }`}
                                accessibilityLabel={`Select ${crop.name}`}
                                accessibilityHint={`${crop.description}. Difficulty: ${crop.difficulty}`}
                            >
                                {/* Recommendation Badge */}
                                {badge && showRecommendations && (
                                    <View className={`absolute -top-2 -right-2 px-2 py-1 rounded-full ${badge.color}`}>
                                        <Text className={`text-xs font-bold ${badge.textColor}`}>
                                            {score}%
                                        </Text>
                                    </View>
                                )}

                                {/* Crop Icon */}
                                <View className="items-center mb-2">
                                    <Text className="text-4xl mb-1">{crop.icon}</Text>
                                    <Text className={`text-lg font-semibold text-center ${isSelected ? 'text-green-700' : 'text-gray-900'
                                        }`}>
                                        {crop.name}
                                    </Text>
                                    <Text className="text-sm text-gray-500 text-center">
                                        {crop.nameAr}
                                    </Text>
                                </View>

                                {/* Difficulty Badge */}
                                <View className={`self-center px-2 py-1 rounded-full ${crop.difficulty === 'beginner' ? 'bg-green-100' :
                                        crop.difficulty === 'intermediate' ? 'bg-yellow-100' :
                                            'bg-red-100'
                                    }`}>
                                    <Text className={`text-xs font-medium ${crop.difficulty === 'beginner' ? 'text-green-700' :
                                            crop.difficulty === 'intermediate' ? 'text-yellow-700' :
                                                'text-red-700'
                                        }`}>
                                        {crop.difficulty}
                                    </Text>
                                </View>

                                {/* Growing Season */}
                                <Text className="text-xs text-gray-500 text-center mt-2">
                                    Season: {crop.growingSeasonMonths.map(m => {
                                        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                                            'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                                        return months[m - 1];
                                    }).join(', ')}
                                </Text>

                                {/* Recommendation Reasons */}
                                {badge && showRecommendations && (
                                    <View className="mt-2">
                                        <Text className="text-xs font-medium text-green-600 text-center">
                                            {badge.text}
                                        </Text>
                                    </View>
                                )}
                            </TouchableOpacity>
                        );
                    })}
                </View>

                {filteredCrops.length === 0 && (
                    <View className="flex-1 items-center justify-center py-12">
                        <Text className="text-6xl mb-4">🔍</Text>
                        <Text className="text-lg font-semibold text-gray-700 mb-2">
                            No crops found
                        </Text>
                        <Text className="text-gray-500 text-center">
                            Try adjusting your search or category filter
                        </Text>
                    </View>
                )}
            </ScrollView>
        </View>
    );
}