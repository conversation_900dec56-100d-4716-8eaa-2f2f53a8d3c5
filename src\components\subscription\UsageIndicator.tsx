import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { calculateUsagePercentage, getUsageStatusColor, formatUsage } from '../../utils/subscription';

interface UsageIndicatorProps {
    label: string;
    used: number;
    limit: number;
    icon?: string;
    showPercentage?: boolean;
}

export const UsageIndicator: React.FC<UsageIndicatorProps> = ({
    label,
    used,
    limit,
    icon,
    showPercentage = true,
}) => {
    const percentage = calculateUsagePercentage(used, limit);
    const statusColor = getUsageStatusColor(percentage);
    const isUnlimited = limit === -1;

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <View style={styles.labelContainer}>
                    {icon && <Text style={styles.icon}>{icon}</Text>}
                    <Text style={styles.label}>{label}</Text>
                </View>
                <Text style={styles.usage}>
                    {formatUsage(used, limit)}
                </Text>
            </View>

            {!isUnlimited && (
                <View style={styles.progressContainer}>
                    <View style={styles.progressBar}>
                        <View
                            style={[
                                styles.progressFill,
                                {
                                    width: `${percentage}%`,
                                    backgroundColor: statusColor,
                                },
                            ]}
                        />
                    </View>
                    {showPercentage && (
                        <Text style={[styles.percentage, { color: statusColor }]}>
                            {percentage.toFixed(0)}%
                        </Text>
                    )}
                </View>
            )}

            {isUnlimited && (
                <View style={styles.unlimitedContainer}>
                    <Text style={styles.unlimitedText}>Unlimited</Text>
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#ffffff',
        borderRadius: 8,
        padding: 16,
        marginVertical: 4,
        borderWidth: 1,
        borderColor: '#e5e7eb',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 12,
    },
    labelContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    icon: {
        fontSize: 16,
        marginRight: 8,
    },
    label: {
        fontSize: 16,
        fontWeight: '500',
        color: '#374151',
        flex: 1,
    },
    usage: {
        fontSize: 16,
        fontWeight: '600',
        color: '#1f2937',
    },
    progressContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    progressBar: {
        flex: 1,
        height: 8,
        backgroundColor: '#f3f4f6',
        borderRadius: 4,
        overflow: 'hidden',
        marginRight: 12,
    },
    progressFill: {
        height: '100%',
        borderRadius: 4,
    },
    percentage: {
        fontSize: 14,
        fontWeight: '600',
        minWidth: 40,
        textAlign: 'right',
    },
    unlimitedContainer: {
        alignItems: 'center',
        paddingVertical: 8,
    },
    unlimitedText: {
        fontSize: 14,
        fontWeight: '600',
        color: '#22c55e',
        textTransform: 'uppercase',
        letterSpacing: 0.5,
    },
});