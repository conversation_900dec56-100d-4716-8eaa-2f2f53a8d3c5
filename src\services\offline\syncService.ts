import { offlineDatabase } from './database';
import { connectivityService } from './connectivity';
import { conflictResolutionService } from './conflictResolution';
import { SyncQueueItem, SyncStatus, ConflictResolution } from '../../types/offline';

export interface SyncProgress {
    total: number;
    completed: number;
    failed: number;
    current?: string;
    percentage: number;
}

export interface SyncResult {
    success: boolean;
    synced: number;
    failed: number;
    conflicts: number;
    errors: string[];
    duration: number;
}

export interface SyncServiceConfig {
    batchSize: number;
    maxConcurrentSyncs: number;
    syncTimeout: number;
    retryDelay: number;
    compressionThreshold: number;
    enableCompression: boolean;
}

export class SyncService {
    private config: SyncServiceConfig = {
        batchSize: 10,
        maxConcurrentSyncs: 3,
        syncTimeout: 30000,
        retryDelay: 2000,
        compressionThreshold: 1024 * 10, // 10KB
        enableCompression: true
    };

    private syncInProgress = false;
    private currentSyncId: string | null = null;
    private syncListeners: ((progress: SyncProgress) => void)[] = [];
    private statusListeners: ((status: SyncStatus) => void)[] = [];
    private lastSyncTime: number | null = null;
    private syncStats = {
        totalSynced: 0,
        totalFailed: 0,
        totalConflicts: 0
    };

    constructor(config?: Partial<SyncServiceConfig>) {
        if (config) {
            this.config = { ...this.config, ...config };
        }
    }

    async initialize(): Promise<void> {
        // Set up periodic sync when online
        connectivityService.addListener((state) => {
            if (state.isConnected && state.isInternetReachable && !this.syncInProgress) {
                // Delay sync to allow for connection stabilization
                setTimeout(() => {
                    this.performIncrementalSync();
                }, 5000);
            }
        });

        console.log('Sync service initialized');
    }

    async performFullSync(): Promise<SyncResult> {
        if (this.syncInProgress) {
            throw new Error('Sync already in progress');
        }

        if (!connectivityService.isOnline()) {
            throw new Error('Cannot sync while offline');
        }

        console.log('Starting full sync...');
        const startTime = Date.now();
        this.syncInProgress = true;
        this.currentSyncId = this.generateSyncId();

        try {
            const result = await this.executeSyncProcess('full');
            this.lastSyncTime = Date.now();

            console.log(`Full sync completed in ${Date.now() - startTime}ms:`, result);
            return result;
        } finally {
            this.syncInProgress = false;
            this.currentSyncId = null;
            this.notifyStatusListeners();
        }
    }

    async performIncrementalSync(): Promise<SyncResult> {
        if (this.syncInProgress) {
            console.log('Sync already in progress, skipping incremental sync');
            return {
                success: true,
                synced: 0,
                failed: 0,
                conflicts: 0,
                errors: [],
                duration: 0
            };
        }

        if (!connectivityService.isOnline()) {
            console.log('Cannot sync while offline');
            return {
                success: false,
                synced: 0,
                failed: 0,
                conflicts: 0,
                errors: ['Offline'],
                duration: 0
            };
        }

        console.log('Starting incremental sync...');
        const startTime = Date.now();
        this.syncInProgress = true;
        this.currentSyncId = this.generateSyncId();

        try {
            const result = await this.executeSyncProcess('incremental');
            this.lastSyncTime = Date.now();

            console.log(`Incremental sync completed in ${Date.now() - startTime}ms:`, result);
            return result;
        } finally {
            this.syncInProgress = false;
            this.currentSyncId = null;
            this.notifyStatusListeners();
        }
    }

    private async executeSyncProcess(type: 'full' | 'incremental'): Promise<SyncResult> {
        const errors: string[] = [];
        let synced = 0;
        let failed = 0;
        let conflicts = 0;

        try {
            // Get pending sync items
            const pendingItems = await offlineDatabase.getSyncQueue('pending');
            const failedItems = type === 'full' ? await offlineDatabase.getSyncQueue('failed') : [];
            const allItems = [...pendingItems, ...failedItems];

            if (allItems.length === 0) {
                return {
                    success: true,
                    synced: 0,
                    failed: 0,
                    conflicts: 0,
                    errors: [],
                    duration: 0
                };
            }

            console.log(`Processing ${allItems.length} sync items...`);

            // Process items in batches
            const batches = this.createBatches(allItems, this.config.batchSize);

            for (let i = 0; i < batches.length; i++) {
                const batch = batches[i];

                this.notifyProgressListeners({
                    total: allItems.length,
                    completed: synced,
                    failed,
                    current: `Batch ${i + 1}/${batches.length}`,
                    percentage: (synced / allItems.length) * 100
                });

                const batchResults = await this.processBatch(batch);
                synced += batchResults.synced;
                failed += batchResults.failed;
                conflicts += batchResults.conflicts;
                errors.push(...batchResults.errors);

                // Add delay between batches to avoid overwhelming the server
                if (i < batches.length - 1) {
                    await this.delay(500);
                }
            }

            // Update stats
            this.syncStats.totalSynced += synced;
            this.syncStats.totalFailed += failed;
            this.syncStats.totalConflicts += conflicts;

            return {
                success: failed === 0,
                synced,
                failed,
                conflicts,
                errors,
                duration: Date.now() - Date.now()
            };

        } catch (error) {
            console.error('Sync process failed:', error);
            errors.push(error instanceof Error ? error.message : 'Unknown sync error');

            return {
                success: false,
                synced,
                failed: failed + 1,
                conflicts,
                errors,
                duration: Date.now() - Date.now()
            };
        }
    }

    private async processBatch(items: SyncQueueItem[]): Promise<{
        synced: number;
        failed: number;
        conflicts: number;
        errors: string[];
    }> {
        const results = {
            synced: 0,
            failed: 0,
            conflicts: 0,
            errors: [] as string[]
        };

        // Process items concurrently with limit
        const semaphore = new Semaphore(this.config.maxConcurrentSyncs);

        const promises = items.map(async (item) => {
            await semaphore.acquire();

            try {
                const result = await this.processSyncItem(item);
                if (result.success) {
                    results.synced++;
                    if (result.hadConflict) {
                        results.conflicts++;
                    }
                } else {
                    results.failed++;
                    results.errors.push(result.error || 'Unknown error');
                }
            } catch (error) {
                results.failed++;
                results.errors.push(error instanceof Error ? error.message : 'Processing error');
            } finally {
                semaphore.release();
            }
        });

        await Promise.all(promises);
        return results;
    }

    private async processSyncItem(item: SyncQueueItem): Promise<{
        success: boolean;
        hadConflict: boolean;
        error?: string;
    }> {
        try {
            // Mark as syncing
            await offlineDatabase.updateSyncQueueItem(item.id, { status: 'syncing' });

            // Compress data if needed
            const processedData = this.config.enableCompression &&
                this.estimateSize(item.data) > this.config.compressionThreshold
                ? await this.compressData(item.data)
                : item.data;

            let hadConflict = false;
            let syncResult;

            switch (item.type) {
                case 'CREATE':
                    syncResult = await this.syncCreate(item, processedData);
                    break;
                case 'UPDATE':
                    const updateResult = await this.syncUpdate(item, processedData);
                    syncResult = updateResult.success;
                    hadConflict = updateResult.hadConflict;
                    break;
                case 'DELETE':
                    syncResult = await this.syncDelete(item, processedData);
                    break;
                default:
                    throw new Error(`Unknown sync type: ${item.type}`);
            }

            if (syncResult) {
                await offlineDatabase.updateSyncQueueItem(item.id, { status: 'completed' });

                // Schedule cleanup of completed item
                setTimeout(() => {
                    offlineDatabase.removeSyncQueueItem(item.id);
                }, 60000); // Remove after 1 minute

                return { success: true, hadConflict };
            } else {
                await this.handleSyncFailure(item);
                return { success: false, hadConflict: false, error: 'Sync operation failed' };
            }

        } catch (error) {
            console.error(`Error processing sync item ${item.id}:`, error);
            await this.handleSyncFailure(item);
            return {
                success: false,
                hadConflict: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }

    private async syncCreate(item: SyncQueueItem, data: any): Promise<boolean> {
        try {
            // Simulate API call - in real implementation, this would call Supabase
            console.log(`Syncing CREATE for ${item.table}:`, data);

            // Simulate network delay
            await this.delay(Math.random() * 1000 + 500);

            // Simulate 95% success rate
            if (Math.random() < 0.95) {
                // Update local cache with server response (including server-generated ID)
                const serverResponse = { ...data, id: `server_${Date.now()}` };
                await offlineDatabase.cacheData(`offline_${item.table}`, serverResponse.id, serverResponse, item.userId);

                return true;
            } else {
                throw new Error('Simulated server error');
            }
        } catch (error) {
            console.error('Create sync failed:', error);
            return false;
        }
    }

    private async syncUpdate(item: SyncQueueItem, data: any): Promise<{
        success: boolean;
        hadConflict: boolean;
    }> {
        try {
            console.log(`Syncing UPDATE for ${item.table}:`, data);

            // Simulate fetching current server data
            await this.delay(Math.random() * 800 + 300);

            // Simulate server data (might be different from local)
            const serverData = {
                ...data,
                server_updated_at: new Date().toISOString(),
                server_field: 'server_value'
            };

            // Check for conflicts
            const hasConflict = Math.random() < 0.1; // 10% chance of conflict
            let resolvedData = data;
            let hadConflict = false;

            if (hasConflict) {
                console.log('Conflict detected, resolving...');
                const resolution = conflictResolutionService.resolveConflict(
                    item.table,
                    data,
                    serverData,
                    'merge'
                );
                resolvedData = resolution.resolvedData;
                hadConflict = true;
            }

            // Simulate server update
            await this.delay(Math.random() * 500 + 200);

            // Simulate 90% success rate
            if (Math.random() < 0.9) {
                // Update local cache with resolved data
                await offlineDatabase.cacheData(`offline_${item.table}`, resolvedData.id, resolvedData, item.userId);
                return { success: true, hadConflict };
            } else {
                throw new Error('Simulated server error');
            }
        } catch (error) {
            console.error('Update sync failed:', error);
            return { success: false, hadConflict: false };
        }
    }

    private async syncDelete(item: SyncQueueItem, data: any): Promise<boolean> {
        try {
            console.log(`Syncing DELETE for ${item.table}:`, data);

            // Simulate network delay
            await this.delay(Math.random() * 400 + 200);

            // Simulate 98% success rate
            if (Math.random() < 0.98) {
                // Remove from local cache
                await offlineDatabase.removeCachedData(`offline_${item.table}`, data.id);
                return true;
            } else {
                throw new Error('Simulated server error');
            }
        } catch (error) {
            console.error('Delete sync failed:', error);
            return false;
        }
    }

    private async handleSyncFailure(item: SyncQueueItem): Promise<void> {
        const newRetryCount = item.retryCount + 1;

        if (newRetryCount >= item.maxRetries) {
            console.log(`Item ${item.id} exceeded max retries, marking as failed`);
            await offlineDatabase.updateSyncQueueItem(item.id, {
                status: 'failed',
                retryCount: newRetryCount
            });
        } else {
            console.log(`Item ${item.id} failed, will retry (${newRetryCount}/${item.maxRetries})`);

            // Calculate exponential backoff delay
            const delay = this.config.retryDelay * Math.pow(2, newRetryCount - 1);

            // Schedule retry
            setTimeout(async () => {
                await offlineDatabase.updateSyncQueueItem(item.id, {
                    status: 'pending',
                    retryCount: newRetryCount
                });
            }, delay);
        }
    }

    // Utility methods
    private createBatches<T>(items: T[], batchSize: number): T[][] {
        const batches: T[][] = [];
        for (let i = 0; i < items.length; i += batchSize) {
            batches.push(items.slice(i, i + batchSize));
        }
        return batches;
    }

    private generateSyncId(): string {
        return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    private estimateSize(data: any): number {
        try {
            return JSON.stringify(data).length * 2; // UTF-16 estimation
        } catch {
            return 1024; // Default size
        }
    }

    private async compressData(data: any): Promise<any> {
        // In a real implementation, you would use a compression library
        // For now, we'll just return the data as-is
        console.log('Compressing data...');
        return data;
    }

    private async delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Event handling
    addProgressListener(listener: (progress: SyncProgress) => void): () => void {
        this.syncListeners.push(listener);
        return () => {
            const index = this.syncListeners.indexOf(listener);
            if (index > -1) {
                this.syncListeners.splice(index, 1);
            }
        };
    }

    addStatusListener(listener: (status: SyncStatus) => void): () => void {
        this.statusListeners.push(listener);
        return () => {
            const index = this.statusListeners.indexOf(listener);
            if (index > -1) {
                this.statusListeners.splice(index, 1);
            }
        };
    }

    private notifyProgressListeners(progress: SyncProgress): void {
        this.syncListeners.forEach(listener => {
            try {
                listener(progress);
            } catch (error) {
                console.error('Error in sync progress listener:', error);
            }
        });
    }

    private notifyStatusListeners(): void {
        const status: SyncStatus = {
            isOnline: connectivityService.isOnline(),
            lastSyncTime: this.lastSyncTime,
            syncInProgress: this.syncInProgress,
            pendingActions: 0, // This would be calculated from database
            failedActions: 0, // This would be calculated from database
            nextSyncAttempt: null // This would be calculated based on retry schedules
        };

        this.statusListeners.forEach(listener => {
            try {
                listener(status);
            } catch (error) {
                console.error('Error in sync status listener:', error);
            }
        });
    }

    // Public API
    isSyncInProgress(): boolean {
        return this.syncInProgress;
    }

    getLastSyncTime(): number | null {
        return this.lastSyncTime;
    }

    getSyncStats() {
        return { ...this.syncStats };
    }

    async cancelSync(): Promise<void> {
        if (this.syncInProgress && this.currentSyncId) {
            console.log(`Cancelling sync ${this.currentSyncId}`);
            // In a real implementation, you would cancel ongoing requests
            this.syncInProgress = false;
            this.currentSyncId = null;
            this.notifyStatusListeners();
        }
    }

    async retryFailedItems(): Promise<SyncResult> {
        const failedItems = await offlineDatabase.getSyncQueue('failed');

        if (failedItems.length === 0) {
            return {
                success: true,
                synced: 0,
                failed: 0,
                conflicts: 0,
                errors: [],
                duration: 0
            };
        }

        // Reset failed items to pending
        for (const item of failedItems) {
            await offlineDatabase.updateSyncQueueItem(item.id, {
                status: 'pending',
                retryCount: 0
            });
        }

        // Perform incremental sync
        return await this.performIncrementalSync();
    }

    updateConfig(config: Partial<SyncServiceConfig>): void {
        this.config = { ...this.config, ...config };
    }
}

// Simple semaphore implementation for concurrency control
class Semaphore {
    private permits: number;
    private waitQueue: (() => void)[] = [];

    constructor(permits: number) {
        this.permits = permits;
    }

    async acquire(): Promise<void> {
        if (this.permits > 0) {
            this.permits--;
            return;
        }

        return new Promise(resolve => {
            this.waitQueue.push(resolve);
        });
    }

    release(): void {
        this.permits++;

        if (this.waitQueue.length > 0) {
            const resolve = this.waitQueue.shift()!;
            this.permits--;
            resolve();
        }
    }
}

export const syncService = new SyncService();