/**
 * Keyboard Navigation Utilities
 * Enhanced keyboard navigation support for agricultural users
 */

import { useEffect, useRef, useCallback } from 'react';
import { Platform, TVEventHandler } from 'react-native';

export interface KeyboardNavigationConfig {
    enabled: boolean;
    focusOnMount: boolean;
    trapFocus: boolean;
    skipLinks: boolean;
}

export interface FocusableElement {
    id: string;
    ref: React.RefObject<any>;
    order: number;
    disabled?: boolean;
    onFocus?: () => void;
    onBlur?: () => void;
    onActivate?: () => void;
}

/**
 * Keyboard Navigation Manager
 */
export class KeyboardNavigationManager {
    private static instance: KeyboardNavigationManager;
    private focusableElements: Map<string, FocusableElement> = new Map();
    private currentFocusId: string | null = null;
    private config: KeyboardNavigationConfig = {
        enabled: true,
        focusOnMount: true,
        trapFocus: true,
        skipLinks: true,
    };

    static getInstance(): KeyboardNavigationManager {
        if (!KeyboardNavigationManager.instance) {
            KeyboardNavigationManager.instance = new KeyboardNavigationManager();
        }
        return KeyboardNavigationManager.instance;
    }

    /**
     * Register a focusable element
     */
    registerElement(element: FocusableElement): () => void {
        this.focusableElements.set(element.id, element);

        // Return cleanup function
        return () => {
            this.focusableElements.delete(element.id);
            if (this.currentFocusId === element.id) {
                this.currentFocusId = null;
            }
        };
    }

    /**
     * Get ordered list of focusable elements
     */
    private getOrderedElements(): FocusableElement[] {
        return Array.from(this.focusableElements.values())
            .filter(element => !element.disabled)
            .sort((a, b) => a.order - b.order);
    }

    /**
     * Focus next element
     */
    focusNext(): boolean {
        const elements = this.getOrderedElements();
        if (elements.length === 0) return false;

        const currentIndex = this.currentFocusId
            ? elements.findIndex(el => el.id === this.currentFocusId)
            : -1;

        const nextIndex = (currentIndex + 1) % elements.length;
        return this.focusElement(elements[nextIndex].id);
    }

    /**
     * Focus previous element
     */
    focusPrevious(): boolean {
        const elements = this.getOrderedElements();
        if (elements.length === 0) return false;

        const currentIndex = this.currentFocusId
            ? elements.findIndex(el => el.id === this.currentFocusId)
            : 0;

        const prevIndex = currentIndex === 0 ? elements.length - 1 : currentIndex - 1;
        return this.focusElement(elements[prevIndex].id);
    }

    /**
     * Focus first element
     */
    focusFirst(): boolean {
        const elements = this.getOrderedElements();
        if (elements.length === 0) return false;
        return this.focusElement(elements[0].id);
    }

    /**
     * Focus last element
     */
    focusLast(): boolean {
        const elements = this.getOrderedElements();
        if (elements.length === 0) return false;
        return this.focusElement(elements[elements.length - 1].id);
    }

    /**
     * Focus specific element
     */
    focusElement(elementId: string): boolean {
        const element = this.focusableElements.get(elementId);
        if (!element || element.disabled) return false;

        // Blur current element
        if (this.currentFocusId && this.currentFocusId !== elementId) {
            const currentElement = this.focusableElements.get(this.currentFocusId);
            if (currentElement?.onBlur) {
                currentElement.onBlur();
            }
        }

        // Focus new element
        this.currentFocusId = elementId;
        if (element.ref.current?.focus) {
            element.ref.current.focus();
        }
        if (element.onFocus) {
            element.onFocus();
        }

        return true;
    }

    /**
     * Activate current element
     */
    activateCurrentElement(): boolean {
        if (!this.currentFocusId) return false;

        const element = this.focusableElements.get(this.currentFocusId);
        if (!element || element.disabled) return false;

        if (element.onActivate) {
            element.onActivate();
            return true;
        }

        // Try to trigger press/click
        if (element.ref.current?.props?.onPress) {
            element.ref.current.props.onPress();
            return true;
        }

        return false;
    }

    /**
     * Update configuration
     */
    updateConfig(newConfig: Partial<KeyboardNavigationConfig>): void {
        this.config = { ...this.config, ...newConfig };
    }

    /**
     * Get current configuration
     */
    getConfig(): KeyboardNavigationConfig {
        return { ...this.config };
    }

    /**
     * Clear all elements (useful for screen changes)
     */
    clearElements(): void {
        this.focusableElements.clear();
        this.currentFocusId = null;
    }
}

/**
 * React hook for keyboard navigation
 */
export const useKeyboardNavigation = (config?: Partial<KeyboardNavigationConfig>) => {
    const manager = KeyboardNavigationManager.getInstance();
    const tvEventHandlerRef = useRef<TVEventHandler | null>(null);

    useEffect(() => {
        if (config) {
            manager.updateConfig(config);
        }

        // Set up TV/keyboard event handler for Android TV, Apple TV, etc.
        if (Platform.isTV) {
            tvEventHandlerRef.current = new TVEventHandler();
            tvEventHandlerRef.current.enable(null, (cmp, evt) => {
                if (evt && evt.eventType === 'focus') {
                    // Handle TV remote navigation
                    switch (evt.eventKeyAction) {
                        case 'up':
                            manager.focusPrevious();
                            break;
                        case 'down':
                            manager.focusNext();
                            break;
                        case 'select':
                            manager.activateCurrentElement();
                            break;
                    }
                }
            });
        }

        return () => {
            if (tvEventHandlerRef.current) {
                tvEventHandlerRef.current.disable();
            }
        };
    }, [config, manager]);

    const registerElement = useCallback((element: FocusableElement) => {
        return manager.registerElement(element);
    }, [manager]);

    const focusNext = useCallback(() => manager.focusNext(), [manager]);
    const focusPrevious = useCallback(() => manager.focusPrevious(), [manager]);
    const focusFirst = useCallback(() => manager.focusFirst(), [manager]);
    const focusLast = useCallback(() => manager.focusLast(), [manager]);
    const focusElement = useCallback((id: string) => manager.focusElement(id), [manager]);
    const activateCurrentElement = useCallback(() => manager.activateCurrentElement(), [manager]);

    return {
        registerElement,
        focusNext,
        focusPrevious,
        focusFirst,
        focusLast,
        focusElement,
        activateCurrentElement,
    };
};

/**
 * React hook for focusable components
 */
export const useFocusable = (
    id: string,
    order: number = 0,
    options?: {
        disabled?: boolean;
        onFocus?: () => void;
        onBlur?: () => void;
        onActivate?: () => void;
        autoFocus?: boolean;
    }
) => {
    const ref = useRef<any>(null);
    const { registerElement } = useKeyboardNavigation();

    useEffect(() => {
        const element: FocusableElement = {
            id,
            ref,
            order,
            disabled: options?.disabled,
            onFocus: options?.onFocus,
            onBlur: options?.onBlur,
            onActivate: options?.onActivate,
        };

        const cleanup = registerElement(element);

        // Auto focus if requested
        if (options?.autoFocus) {
            setTimeout(() => {
                KeyboardNavigationManager.getInstance().focusElement(id);
            }, 100);
        }

        return cleanup;
    }, [id, order, options, registerElement]);

    const focus = useCallback(() => {
        KeyboardNavigationManager.getInstance().focusElement(id);
    }, [id]);

    const blur = useCallback(() => {
        if (options?.onBlur) {
            options.onBlur();
        }
    }, [options]);

    return {
        ref,
        focus,
        blur,
        focusable: true,
        onFocus: options?.onFocus,
        onBlur: blur,
    };
};

/**
 * Keyboard shortcuts for agricultural actions
 */
export const keyboardShortcuts = {
    // Navigation shortcuts
    'h': 'Go to Home',
    'c': 'Open Crops',
    'w': 'Check Weather',
    'a': 'AI Chat',
    's': 'Store',
    'm': 'Community',

    // Action shortcuts
    'p': 'Take Photo',
    't': 'Add Task',
    'v': 'Voice Mode',
    'Escape': 'Go Back',
    'Enter': 'Activate',
    'Space': 'Activate',
    'Tab': 'Next Element',
    'Shift+Tab': 'Previous Element',
    'Home': 'First Element',
    'End': 'Last Element',

    // Accessibility shortcuts
    'Alt+v': 'Toggle Voice Mode',
    'Alt+c': 'Toggle High Contrast',
    'Alt+t': 'Toggle Large Text',
    'Alt+r': 'Read Screen',
} as const;

/**
 * Handle keyboard events for shortcuts
 */
export const handleKeyboardShortcut = (
    key: string,
    modifiers: { alt?: boolean; shift?: boolean; ctrl?: boolean } = {},
    onShortcut: (action: string) => void
) => {
    const shortcutKey = [
        modifiers.ctrl && 'Ctrl',
        modifiers.alt && 'Alt',
        modifiers.shift && 'Shift',
        key,
    ].filter(Boolean).join('+');

    const action = keyboardShortcuts[shortcutKey as keyof typeof keyboardShortcuts];
    if (action) {
        onShortcut(action);
        return true;
    }

    return false;
};