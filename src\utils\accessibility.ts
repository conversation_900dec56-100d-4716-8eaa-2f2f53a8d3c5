/**
 * Accessibility utilities for voice navigation and screen readers
 * Optimized for agricultural users with varying literacy levels
 */

import { AccessibilityInfo, Platform, Appearance } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface AccessibilityConfig {
  voiceEnabled: boolean;
  highContrast: boolean;
  largeText: boolean;
  reducedMotion: boolean;
  screenReaderEnabled: boolean;
  keyboardNavigation: boolean;
  fontScale: number;
  colorBlindnessSupport: boolean;
  autoFocusEnabled: boolean;
  hapticFeedbackEnabled: boolean;
  soundFeedbackEnabled: boolean;
}

export const defaultAccessibilityConfig: AccessibilityConfig = {
  voiceEnabled: false,
  highContrast: false,
  largeText: false,
  reducedMotion: false,
  screenReaderEnabled: false,
  keyboardNavigation: true,
  fontScale: 1.0,
  colorBlindnessSupport: false,
  autoFocusEnabled: true,
  hapticFeedbackEnabled: true,
  soundFeedbackEnabled: true,
};

const ACCESSIBILITY_CONFIG_KEY = 'accessibility_config';

/**
 * Generate accessibility props for components
 */
export const getAccessibilityProps = (label: string, hint?: string, role?: string) => ({
  accessibilityLabel: label,
  accessibilityHint: hint,
  accessibilityRole: role as any,
});

/**
 * Voice-friendly text formatting
 */
export const formatForVoice = (text: string): string => {
  return text
    .replace(/&/g, 'and')
    .replace(/%/g, 'percent')
    .replace(/\$/g, 'dollars')
    .replace(/@/g, 'at')
    .replace(/#/g, 'number')
    .replace(/\+/g, 'plus')
    .replace(/-/g, 'minus')
    .replace(/\*/g, 'times')
    .replace(/\//g, 'divided by');
};

/**
 * Generate semantic labels for agricultural terms
 */
export const getAgriculturalLabel = (term: string): string => {
  const labels: Record<string, string> = {
    temp: 'temperature',
    humidity: 'humidity level',
    ph: 'pH level',
    npk: 'nitrogen phosphorus potassium',
    ppm: 'parts per million',
    ec: 'electrical conductivity',
    tds: 'total dissolved solids',
    gps: 'GPS location',
    ai: 'artificial intelligence',
    ml: 'machine learning',
  };

  return labels[term.toLowerCase()] || term;
};

/**
 * Touch target size validation
 */
export const validateTouchTarget = (size: number): boolean => {
  const MINIMUM_TOUCH_TARGET = 44; // iOS/Android accessibility guidelines
  return size >= MINIMUM_TOUCH_TARGET;
};

/**
 * Color contrast validation (simplified)
 */
export const hasGoodContrast = (foreground: string, background: string): boolean => {
  // Simplified contrast check - in production, use a proper contrast ratio calculator
  const darkColors = ['black', 'dark', 'primary-900', 'earth-900'];
  const lightColors = ['white', 'light', 'primary-50', 'earth-50'];

  const isDarkFg = darkColors.some((color) => foreground.includes(color));
  const isLightBg = lightColors.some((color) => background.includes(color));
  const isLightFg = lightColors.some((color) => foreground.includes(color));
  const isDarkBg = darkColors.some((color) => background.includes(color));

  return (isDarkFg && isLightBg) || (isLightFg && isDarkBg);
};

/**
 * Screen reader announcements
 */
export const announceToScreenReader = (message: string): void => {
  // This would integrate with platform-specific screen reader APIs
  // For now, we'll use a simple console log for development
  console.log(`Screen Reader: ${formatForVoice(message)}`);
};

/**
 * Accessibility Manager Class
 */
export class AccessibilityManager {
  private static instance: AccessibilityManager;
  private config: AccessibilityConfig = defaultAccessibilityConfig;
  private listeners: Array<(config: AccessibilityConfig) => void> = [];

  static getInstance(): AccessibilityManager {
    if (!AccessibilityManager.instance) {
      AccessibilityManager.instance = new AccessibilityManager();
    }
    return AccessibilityManager.instance;
  }

  async initialize(): Promise<void> {
    try {
      // Load saved configuration
      const savedConfig = await AsyncStorage.getItem(ACCESSIBILITY_CONFIG_KEY);
      if (savedConfig) {
        this.config = { ...defaultAccessibilityConfig, ...JSON.parse(savedConfig) };
      }

      // Detect system accessibility settings
      await this.detectSystemSettings();
    } catch (error) {
      console.warn('Failed to initialize accessibility settings:', error);
    }
  }

  private async detectSystemSettings(): Promise<void> {
    try {
      // Check if screen reader is enabled
      const screenReaderEnabled = await AccessibilityInfo.isScreenReaderEnabled();

      // Check if reduce motion is enabled
      const reduceMotionEnabled = await AccessibilityInfo.isReduceMotionEnabled();

      // Update config with system settings
      this.config = {
        ...this.config,
        screenReaderEnabled,
        reducedMotion: reduceMotionEnabled,
      };

      // Listen for accessibility changes
      AccessibilityInfo.addEventListener('screenReaderChanged', (enabled) => {
        this.updateConfig({ screenReaderEnabled: enabled });
      });

      AccessibilityInfo.addEventListener('reduceMotionChanged', (enabled) => {
        this.updateConfig({ reducedMotion: enabled });
      });

    } catch (error) {
      console.warn('Failed to detect system accessibility settings:', error);
    }
  }

  async updateConfig(updates: Partial<AccessibilityConfig>): Promise<void> {
    // Apply font scale limits
    if (updates.fontScale !== undefined) {
      updates.fontScale = Math.max(0.8, Math.min(2.0, updates.fontScale));
    }

    this.config = { ...this.config, ...updates };

    try {
      await AsyncStorage.setItem(ACCESSIBILITY_CONFIG_KEY, JSON.stringify(this.config));
    } catch (error) {
      console.warn('Failed to save accessibility config:', error);
    }

    // Notify listeners
    this.listeners.forEach(listener => listener(this.config));
  }

  getConfig(): AccessibilityConfig {
    return { ...this.config };
  }

  subscribe(listener: (config: AccessibilityConfig) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // High contrast color adjustments
  getHighContrastColors() {
    if (!this.config.highContrast) return null;

    // Enhanced high contrast colors for outdoor visibility
    return {
      background: '#000000',
      foreground: '#FFFFFF',
      primary: '#00FF00',
      secondary: '#FFFF00',
      error: '#FF0000',
      warning: '#FFA500',
      success: '#00FF00',
      info: '#00FFFF',
      // Additional colors for better agricultural context
      soil: '#8B4513',
      plant: '#32CD32',
      water: '#1E90FF',
      sun: '#FFD700',
      // Focus indicators
      focusRing: '#FFFFFF',
      focusBackground: '#333333',
    };
  }

  // Color blindness support
  getColorBlindFriendlyColors() {
    if (!this.config.colorBlindnessSupport) return null;

    return {
      // Deuteranopia-friendly colors
      primary: '#0173B2',
      secondary: '#DE8F05',
      success: '#029E73',
      warning: '#CC78BC',
      error: '#D55E00',
      info: '#56B4E9',
    };
  }

  // Font scaling based on accessibility settings
  getScaledFontSize(baseSize: number): number {
    return Math.round(baseSize * this.config.fontScale);
  }

  // Touch target scaling for better accessibility
  getScaledTouchTarget(baseSize: number): number {
    const minSize = 44; // iOS/Android minimum
    const agriculturalMinSize = 56; // Agricultural recommendation for work gloves
    const scaleFactor = this.config.largeText ? 1.3 : 1.0;
    const scaledSize = baseSize * scaleFactor;

    // Ensure we meet agricultural requirements when large text is enabled
    const targetMinSize = this.config.largeText ? agriculturalMinSize : minSize;
    return Math.max(targetMinSize, scaledSize);
  }
}

/**
 * Screen Reader Utilities
 */
export const screenReaderUtils = {
  announce: (message: string, priority: 'low' | 'high' = 'low') => {
    if (Platform.OS === 'ios') {
      AccessibilityInfo.announceForAccessibility(message);
    } else {
      // Android implementation
      AccessibilityInfo.announceForAccessibility(message);
    }
  },

  setFocus: (reactTag: number) => {
    AccessibilityInfo.setAccessibilityFocus(reactTag);
  },

  isScreenReaderEnabled: async (): Promise<boolean> => {
    return await AccessibilityInfo.isScreenReaderEnabled();
  },
};

/**
 * Keyboard Navigation Utilities
 */
export const keyboardNavigation = {
  // Focus management for keyboard navigation
  focusableElements: new Set<string>(),

  registerFocusable: (id: string) => {
    keyboardNavigation.focusableElements.add(id);
  },

  unregisterFocusable: (id: string) => {
    keyboardNavigation.focusableElements.delete(id);
  },

  getNextFocusable: (currentId: string): string | null => {
    const elements = Array.from(keyboardNavigation.focusableElements);
    const currentIndex = elements.indexOf(currentId);
    const nextIndex = (currentIndex + 1) % elements.length;
    return elements[nextIndex] || null;
  },

  getPreviousFocusable: (currentId: string): string | null => {
    const elements = Array.from(keyboardNavigation.focusableElements);
    const currentIndex = elements.indexOf(currentId);
    const prevIndex = currentIndex === 0 ? elements.length - 1 : currentIndex - 1;
    return elements[prevIndex] || null;
  },
};

/**
 * Color Contrast Utilities
 */
export const colorContrast = {
  // Calculate luminance for contrast ratio
  getLuminance: (hex: string): number => {
    const rgb = colorContrast.hexToRgb(hex);
    if (!rgb) return 0;

    const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  },

  // Calculate contrast ratio between two colors
  getContrastRatio: (color1: string, color2: string): number => {
    const lum1 = colorContrast.getLuminance(color1);
    const lum2 = colorContrast.getLuminance(color2);
    const brightest = Math.max(lum1, lum2);
    const darkest = Math.min(lum1, lum2);
    return (brightest + 0.05) / (darkest + 0.05);
  },

  // Check if contrast meets WCAG standards
  meetsWCAG: (color1: string, color2: string, level: 'AA' | 'AAA' = 'AA'): boolean => {
    const ratio = colorContrast.getContrastRatio(color1, color2);
    return level === 'AA' ? ratio >= 4.5 : ratio >= 7;
  },

  // Convert hex to RGB
  hexToRgb: (hex: string): { r: number; g: number; b: number } | null => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  },
};

/**
 * Voice command patterns for agricultural actions
 */
export const voiceCommands = {
  navigation: [
    'go to home',
    'open crops',
    'show weather',
    'start chat',
    'open store',
    'view community',
    'go back',
    'open settings',
    'show profile',
  ],
  actions: [
    'take photo',
    'analyze plant',
    'check weather',
    'add task',
    'complete task',
    'save plan',
    'start recording',
    'stop recording',
    'send message',
    'call help',
  ],
  queries: [
    'what is the weather',
    'how are my crops',
    'when to water',
    'when to harvest',
    'what to plant',
    'show my tasks',
    'read messages',
    'what time is it',
  ],
  accessibility: [
    'enable voice mode',
    'disable voice mode',
    'increase text size',
    'decrease text size',
    'enable high contrast',
    'disable high contrast',
    'read screen',
    'repeat last message',
  ],
} as const;
