import * as Localization from 'expo-localization';
import AsyncStorage from '@react-native-async-storage/async-storage';

export type SupportedLanguage = 'en' | 'ar';

export interface LanguageSettings {
    current: SupportedLanguage;
    isRTL: boolean;
}

const LANGUAGE_SETTINGS_KEY = 'language_settings';

const translations = {
    en: {
        // Welcome Screen
        welcome: {
            title: 'AI Farming Assistant',
            subtitle: 'Your smart farming companion',
            description: 'Get AI-powered insights, crop planning, and community support to optimize your farming success.',
            enableVoiceMode: 'Enable Voice Mode',
            voiceModeDescription: 'Activate voice navigation and spoken responses for hands-free farming assistance',
            continueWithPhone: 'Continue with Phone',
            continueWithEmail: 'Continue with <PERSON><PERSON>',
            languageSelection: 'Language / اللغة',
            english: 'English',
            arabic: 'العربية',
        },
        // Personal Info Screen
        personalInfo: {
            title: 'Personal Information',
            subtitle: 'Tell us about yourself',
            firstName: 'First Name',
            lastName: 'Last Name',
            phone: 'Phone Number',
            email: 'Email Address',
            fillWithVoice: 'Fill with Voice',
            next: 'Next',
            back: 'Back',
            step: 'Step 1 of 3',
        },
        // Farm Setup Screen
        farmSetup: {
            title: 'Farm Information',
            subtitle: 'Set up your farm profile',
            farmLocation: 'Farm Location',
            useCurrentLocation: 'Use Current Location',
            selectOnMap: 'Select on Map',
            whatDoYouGrow: 'What do you grow?',
            experienceLevel: 'Experience Level',
            beginner: 'Beginner',
            intermediate: 'Intermediate',
            expert: 'Expert',
            complete: 'Complete',
            step: 'Step 2 of 3',
        },
        // Login Screen
        login: {
            title: 'Welcome Back',
            description: 'Sign in to your farming assistant account',
            email: 'Email',
            phone: 'Phone',
            password: 'Password',
            emailPlaceholder: 'Enter your email address',
            phonePlaceholder: 'Enter your phone number',
            passwordPlaceholder: 'Enter your password',
            emailHint: 'Enter the email address associated with your account',
            phoneHint: 'Enter the phone number associated with your account',
            passwordHint: 'Enter your account password',
            loginWithEmail: 'Login with Email',
            loginWithPhone: 'Login with Phone',
            switchTo: {
                email: 'Switched to email login',
                phone: 'Switched to phone login',
            },
            rememberMe: {
                label: 'Remember me',
                enabled: 'Remember me enabled',
                disabled: 'Remember me disabled',
            },
            forgotPassword: {
                title: 'Forgot Password?',
            },
            signIn: 'Sign In',
            signInHint: 'Sign in to access your farming dashboard',
            orContinueWith: 'Or continue with',
            continueWithGoogle: 'Continue with Google',
            continueWithApple: 'Continue with Apple',
            socialLoginHint: 'Sign in using your social media account',
            noAccount: "Don't have an account?",
            signUp: {
                title: 'Sign Up',
            },
            success: 'Login successful! Welcome back to your farming assistant.',
            socialLogin: {
                google: 'Signing in with Google',
                apple: 'Signing in with Apple',
                comingSoon: 'Social login will be available soon!',
            },
            errors: {
                title: 'Login Error',
                emailRequired: 'Email address is required',
                emailInvalid: 'Please enter a valid email address',
                phoneRequired: 'Phone number is required',
                phoneInvalid: 'Please enter a valid phone number',
                passwordRequired: 'Password is required',
                passwordTooShort: 'Password must be at least 6 characters',
                validationFailed: 'Please fix the errors below',
                loginFailed: 'Login failed. Please check your credentials and try again.',
                socialLoginFailed: 'Social login failed. Please try again.',
            },
        },
        // Forgot Password Screen
        forgotPassword: {
            title: 'Reset Password',
            description: 'Enter your email or phone number to receive a password reset code',
            email: 'Email',
            phone: 'Phone',
            emailLabel: 'Email Address',
            phoneLabel: 'Phone Number',
            emailPlaceholder: 'Enter your email address',
            phonePlaceholder: 'Enter your phone number',
            emailHint: 'Enter the email address associated with your account',
            phoneHint: 'Enter the phone number associated with your account',
            resetViaEmail: 'Reset via Email',
            resetViaPhone: 'Reset via Phone',
            switchTo: {
                email: 'Switched to email reset',
                phone: 'Switched to phone reset',
            },
            sendCode: 'Send Reset Code',
            sendCodeHint: 'Send password reset code to your email or phone',
            backToLogin: 'Back to Login',
            backToLoginHint: 'Return to the login screen',
            codeSent: 'Password reset code has been sent successfully',
            instructions: {
                title: 'How it works',
                email: 'We will send a 6-digit verification code to your email address. Enter this code on the next screen to reset your password.',
                phone: 'We will send a 6-digit verification code via SMS to your phone number. Enter this code on the next screen to reset your password.',
            },
            errors: {
                title: 'Reset Error',
                emailRequired: 'Email address is required',
                emailInvalid: 'Please enter a valid email address',
                phoneRequired: 'Phone number is required',
                phoneInvalid: 'Please enter a valid phone number',
                validationFailed: 'Please fix the errors below',
                sendFailed: 'Failed to send reset code. Please try again.',
            },
        },
        // Verify Reset Code Screen
        verifyResetCode: {
            title: 'Enter Verification Code',
            sentToEmail: 'We sent a 6-digit code to {{email}}',
            sentToPhone: 'We sent a 6-digit code to {{phone}}',
            description: 'Enter the code to verify your identity',
            enterCode: 'Enter 6-digit code',
            codeDigit: 'Code digit {{position}}',
            codeDigitHint: 'Enter the digit for position {{position}} of the verification code',
            resendCode: 'Resend Code',
            resending: 'Resending...',
            resendIn: 'Resend code in {{time}}',
            codeResent: 'Verification code has been resent',
            verify: 'Verify Code',
            verifyHint: 'Verify the entered code to proceed',
            codeVerified: 'Code verified successfully',
            tips: {
                title: 'Tips',
                description: 'Check your spam folder if you don\'t see the email. The code expires in 10 minutes.',
            },
            errors: {
                title: 'Verification Error',
                incompleteCode: 'Please enter the complete 6-digit code',
                invalidCode: 'Invalid verification code. Please try again.',
                resendFailed: 'Failed to resend code. Please try again.',
            },
        },
        // New Password Screen
        newPassword: {
            title: 'Create New Password',
            description: 'Choose a strong password for your account',
            newPassword: 'New Password',
            confirmPassword: 'Confirm Password',
            newPasswordPlaceholder: 'Enter your new password',
            confirmPasswordPlaceholder: 'Confirm your new password',
            newPasswordHint: 'Create a strong password with at least 8 characters',
            confirmPasswordHint: 'Re-enter your new password to confirm',
            passwordStrength: 'Password Strength',
            strength: {
                weak: 'Weak',
                medium: 'Medium',
                strong: 'Strong',
            },
            requirements: {
                minLength: 'At least 8 characters',
                uppercase: 'One uppercase letter',
                lowercase: 'One lowercase letter',
                number: 'One number',
            },
            resetPassword: 'Reset Password',
            resetPasswordHint: 'Set your new password and complete the reset process',
            success: 'Password reset successful! You can now sign in with your new password.',
            successTitle: 'Password Reset Complete',
            successMessage: 'Your password has been successfully reset. You can now sign in with your new password.',
            securityTips: {
                title: 'Security Tips',
                description: 'Use a unique password that you don\'t use for other accounts. Consider using a password manager.',
            },
            errors: {
                title: 'Password Reset Error',
                passwordRequired: 'New password is required',
                passwordTooShort: 'Password must be at least 8 characters',
                passwordWeak: 'Password must contain uppercase, lowercase, and number',
                confirmPasswordRequired: 'Please confirm your password',
                passwordMismatch: 'Passwords do not match',
                validationFailed: 'Please fix the errors below',
                resetFailed: 'Failed to reset password. Please try again.',
            },
        },
        common: {
            loading: 'Loading...',
            error: 'Error',
            retry: 'Retry',
            cancel: 'Cancel',
            save: 'Save',
            back: 'Back',
            ok: 'OK',
            comingSoon: 'Coming Soon',
        },
    },
    ar: {
        // Welcome Screen
        welcome: {
            title: 'مساعد الزراعة الذكي',
            subtitle: 'رفيقك الذكي في الزراعة',
            description: 'احصل على رؤى مدعومة بالذكاء الاصطناعي وتخطيط المحاصيل ودعم المجتمع لتحسين نجاحك الزراعي.',
            enableVoiceMode: 'تفعيل الوضع الصوتي',
            voiceModeDescription: 'تفعيل التنقل الصوتي والاستجابات المنطوقة للمساعدة الزراعية بدون استخدام اليدين',
            continueWithPhone: 'المتابعة بالهاتف',
            continueWithEmail: 'المتابعة بالبريد الإلكتروني',
            languageSelection: 'Language / اللغة',
            english: 'English',
            arabic: 'العربية',
        },
        // Personal Info Screen
        personalInfo: {
            title: 'المعلومات الشخصية',
            subtitle: 'أخبرنا عن نفسك',
            firstName: 'الاسم الأول',
            lastName: 'اسم العائلة',
            phone: 'رقم الهاتف',
            email: 'عنوان البريد الإلكتروني',
            fillWithVoice: 'املأ بالصوت',
            next: 'التالي',
            back: 'السابق',
            step: 'الخطوة 1 من 3',
        },
        // Farm Setup Screen
        farmSetup: {
            title: 'معلومات المزرعة',
            subtitle: 'إعداد ملف المزرعة الخاص بك',
            farmLocation: 'موقع المزرعة',
            useCurrentLocation: 'استخدم الموقع الحالي',
            selectOnMap: 'اختر على الخريطة',
            whatDoYouGrow: 'ماذا تزرع؟',
            experienceLevel: 'مستوى الخبرة',
            beginner: 'مبتدئ',
            intermediate: 'متوسط',
            expert: 'خبير',
            complete: 'إكمال',
            step: 'الخطوة 2 من 3',
        },
        // Login Screen
        login: {
            title: 'مرحباً بعودتك',
            description: 'سجل الدخول إلى حساب مساعد الزراعة الخاص بك',
            email: 'البريد الإلكتروني',
            phone: 'الهاتف',
            password: 'كلمة المرور',
            emailPlaceholder: 'أدخل عنوان بريدك الإلكتروني',
            phonePlaceholder: 'أدخل رقم هاتفك',
            passwordPlaceholder: 'أدخل كلمة المرور',
            emailHint: 'أدخل عنوان البريد الإلكتروني المرتبط بحسابك',
            phoneHint: 'أدخل رقم الهاتف المرتبط بحسابك',
            passwordHint: 'أدخل كلمة مرور حسابك',
            loginWithEmail: 'تسجيل الدخول بالبريد الإلكتروني',
            loginWithPhone: 'تسجيل الدخول بالهاتف',
            switchTo: {
                email: 'تم التبديل إلى تسجيل الدخول بالبريد الإلكتروني',
                phone: 'تم التبديل إلى تسجيل الدخول بالهاتف',
            },
            rememberMe: {
                label: 'تذكرني',
                enabled: 'تم تفعيل تذكرني',
                disabled: 'تم إلغاء تذكرني',
            },
            forgotPassword: {
                title: 'نسيت كلمة المرور؟',
            },
            signIn: 'تسجيل الدخول',
            signInHint: 'سجل الدخول للوصول إلى لوحة تحكم الزراعة الخاصة بك',
            orContinueWith: 'أو تابع مع',
            continueWithGoogle: 'المتابعة مع جوجل',
            continueWithApple: 'المتابعة مع آبل',
            socialLoginHint: 'سجل الدخول باستخدام حساب وسائل التواصل الاجتماعي',
            noAccount: 'ليس لديك حساب؟',
            signUp: {
                title: 'إنشاء حساب',
            },
            success: 'تم تسجيل الدخول بنجاح! مرحباً بعودتك إلى مساعد الزراعة الخاص بك.',
            socialLogin: {
                google: 'تسجيل الدخول مع جوجل',
                apple: 'تسجيل الدخول مع آبل',
                comingSoon: 'سيكون تسجيل الدخول الاجتماعي متاحاً قريباً!',
            },
            errors: {
                title: 'خطأ في تسجيل الدخول',
                emailRequired: 'عنوان البريد الإلكتروني مطلوب',
                emailInvalid: 'يرجى إدخال عنوان بريد إلكتروني صحيح',
                phoneRequired: 'رقم الهاتف مطلوب',
                phoneInvalid: 'يرجى إدخال رقم هاتف صحيح',
                passwordRequired: 'كلمة المرور مطلوبة',
                passwordTooShort: 'يجب أن تكون كلمة المرور 6 أحرف على الأقل',
                validationFailed: 'يرجى إصلاح الأخطاء أدناه',
                loginFailed: 'فشل تسجيل الدخول. يرجى التحقق من بيانات الاعتماد والمحاولة مرة أخرى.',
                socialLoginFailed: 'فشل تسجيل الدخول الاجتماعي. يرجى المحاولة مرة أخرى.',
            },
        },
        // Forgot Password Screen
        forgotPassword: {
            title: 'إعادة تعيين كلمة المرور',
            description: 'أدخل بريدك الإلكتروني أو رقم هاتفك لتلقي رمز إعادة تعيين كلمة المرور',
            email: 'البريد الإلكتروني',
            phone: 'الهاتف',
            emailLabel: 'عنوان البريد الإلكتروني',
            phoneLabel: 'رقم الهاتف',
            emailPlaceholder: 'أدخل عنوان بريدك الإلكتروني',
            phonePlaceholder: 'أدخل رقم هاتفك',
            emailHint: 'أدخل عنوان البريد الإلكتروني المرتبط بحسابك',
            phoneHint: 'أدخل رقم الهاتف المرتبط بحسابك',
            resetViaEmail: 'إعادة التعيين عبر البريد الإلكتروني',
            resetViaPhone: 'إعادة التعيين عبر الهاتف',
            switchTo: {
                email: 'تم التبديل إلى إعادة التعيين بالبريد الإلكتروني',
                phone: 'تم التبديل إلى إعادة التعيين بالهاتف',
            },
            sendCode: 'إرسال رمز الإعادة',
            sendCodeHint: 'إرسال رمز إعادة تعيين كلمة المرور إلى بريدك الإلكتروني أو هاتفك',
            backToLogin: 'العودة إلى تسجيل الدخول',
            backToLoginHint: 'العودة إلى شاشة تسجيل الدخول',
            codeSent: 'تم إرسال رمز إعادة تعيين كلمة المرور بنجاح',
            instructions: {
                title: 'كيف يعمل',
                email: 'سنرسل رمز تحقق مكون من 6 أرقام إلى عنوان بريدك الإلكتروني. أدخل هذا الرمز في الشاشة التالية لإعادة تعيين كلمة المرور.',
                phone: 'سنرسل رمز تحقق مكون من 6 أرقام عبر الرسائل النصية إلى رقم هاتفك. أدخل هذا الرمز في الشاشة التالية لإعادة تعيين كلمة المرور.',
            },
            errors: {
                title: 'خطأ في الإعادة',
                emailRequired: 'عنوان البريد الإلكتروني مطلوب',
                emailInvalid: 'يرجى إدخال عنوان بريد إلكتروني صحيح',
                phoneRequired: 'رقم الهاتف مطلوب',
                phoneInvalid: 'يرجى إدخال رقم هاتف صحيح',
                validationFailed: 'يرجى إصلاح الأخطاء أدناه',
                sendFailed: 'فشل في إرسال رمز الإعادة. يرجى المحاولة مرة أخرى.',
            },
        },
        // Verify Reset Code Screen
        verifyResetCode: {
            title: 'أدخل رمز التحقق',
            sentToEmail: 'أرسلنا رمزاً مكوناً من 6 أرقام إلى {{email}}',
            sentToPhone: 'أرسلنا رمزاً مكوناً من 6 أرقام إلى {{phone}}',
            description: 'أدخل الرمز للتحقق من هويتك',
            enterCode: 'أدخل الرمز المكون من 6 أرقام',
            codeDigit: 'رقم الرمز {{position}}',
            codeDigitHint: 'أدخل الرقم للموضع {{position}} من رمز التحقق',
            resendCode: 'إعادة إرسال الرمز',
            resending: 'جاري الإرسال...',
            resendIn: 'إعادة إرسال الرمز خلال {{time}}',
            codeResent: 'تم إعادة إرسال رمز التحقق',
            verify: 'تحقق من الرمز',
            verifyHint: 'تحقق من الرمز المدخل للمتابعة',
            codeVerified: 'تم التحقق من الرمز بنجاح',
            tips: {
                title: 'نصائح',
                description: 'تحقق من مجلد الرسائل غير المرغوب فيها إذا لم تر البريد الإلكتروني. ينتهي صلاحية الرمز خلال 10 دقائق.',
            },
            errors: {
                title: 'خطأ في التحقق',
                incompleteCode: 'يرجى إدخال الرمز الكامل المكون من 6 أرقام',
                invalidCode: 'رمز التحقق غير صحيح. يرجى المحاولة مرة أخرى.',
                resendFailed: 'فشل في إعادة إرسال الرمز. يرجى المحاولة مرة أخرى.',
            },
        },
        // New Password Screen
        newPassword: {
            title: 'إنشاء كلمة مرور جديدة',
            description: 'اختر كلمة مرور قوية لحسابك',
            newPassword: 'كلمة المرور الجديدة',
            confirmPassword: 'تأكيد كلمة المرور',
            newPasswordPlaceholder: 'أدخل كلمة المرور الجديدة',
            confirmPasswordPlaceholder: 'أكد كلمة المرور الجديدة',
            newPasswordHint: 'أنشئ كلمة مرور قوية تحتوي على 8 أحرف على الأقل',
            confirmPasswordHint: 'أعد إدخال كلمة المرور الجديدة للتأكيد',
            passwordStrength: 'قوة كلمة المرور',
            strength: {
                weak: 'ضعيفة',
                medium: 'متوسطة',
                strong: 'قوية',
            },
            requirements: {
                minLength: '8 أحرف على الأقل',
                uppercase: 'حرف كبير واحد',
                lowercase: 'حرف صغير واحد',
                number: 'رقم واحد',
            },
            resetPassword: 'إعادة تعيين كلمة المرور',
            resetPasswordHint: 'تعيين كلمة المرور الجديدة وإكمال عملية الإعادة',
            success: 'تم إعادة تعيين كلمة المرور بنجاح! يمكنك الآن تسجيل الدخول بكلمة المرور الجديدة.',
            successTitle: 'اكتملت إعادة تعيين كلمة المرور',
            successMessage: 'تم إعادة تعيين كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول بكلمة المرور الجديدة.',
            securityTips: {
                title: 'نصائح الأمان',
                description: 'استخدم كلمة مرور فريدة لا تستخدمها للحسابات الأخرى. فكر في استخدام مدير كلمات المرور.',
            },
            errors: {
                title: 'خطأ في إعادة تعيين كلمة المرور',
                passwordRequired: 'كلمة المرور الجديدة مطلوبة',
                passwordTooShort: 'يجب أن تكون كلمة المرور 8 أحرف على الأقل',
                passwordWeak: 'يجب أن تحتوي كلمة المرور على أحرف كبيرة وصغيرة ورقم',
                confirmPasswordRequired: 'يرجى تأكيد كلمة المرور',
                passwordMismatch: 'كلمات المرور غير متطابقة',
                validationFailed: 'يرجى إصلاح الأخطاء أدناه',
                resetFailed: 'فشل في إعادة تعيين كلمة المرور. يرجى المحاولة مرة أخرى.',
            },
        },
        common: {
            loading: 'جاري التحميل...',
            error: 'خطأ',
            retry: 'إعادة المحاولة',
            cancel: 'إلغاء',
            save: 'حفظ',
            back: 'السابق',
            ok: 'موافق',
            comingSoon: 'قريباً',
        },
    },
};

export class LanguageService {
    private static instance: LanguageService;
    private settings: LanguageSettings = {
        current: 'en',
        isRTL: false,
    };

    static getInstance(): LanguageService {
        if (!LanguageService.instance) {
            LanguageService.instance = new LanguageService();
        }
        return LanguageService.instance;
    }

    async initialize(): Promise<void> {
        try {
            const savedSettings = await AsyncStorage.getItem(LANGUAGE_SETTINGS_KEY);
            if (savedSettings) {
                this.settings = JSON.parse(savedSettings);
            } else {
                // Detect system language
                const systemLanguage = Localization.locale.startsWith('ar') ? 'ar' : 'en';
                await this.setLanguage(systemLanguage);
            }
        } catch (error) {
            console.warn('Failed to load language settings:', error);
        }
    }

    async setLanguage(language: SupportedLanguage): Promise<void> {
        this.settings = {
            current: language,
            isRTL: language === 'ar',
        };

        try {
            await AsyncStorage.setItem(LANGUAGE_SETTINGS_KEY, JSON.stringify(this.settings));
        } catch (error) {
            console.warn('Failed to save language settings:', error);
        }
    }

    getSettings(): LanguageSettings {
        return { ...this.settings };
    }

    getCurrentLanguage(): SupportedLanguage {
        return this.settings.current;
    }

    isRTL(): boolean {
        return this.settings.isRTL;
    }

    t(key: string, params?: Record<string, string>): string {
        const keys = key.split('.');
        let value: any = translations[this.settings.current];

        for (const k of keys) {
            value = value?.[k];
        }

        let result = value || key;

        // Replace template variables like {{email}} with actual values
        if (params && typeof result === 'string') {
            Object.entries(params).forEach(([paramKey, paramValue]) => {
                result = result.replace(new RegExp(`{{${paramKey}}}`, 'g'), paramValue);
            });
        }

        return result;
    }
}