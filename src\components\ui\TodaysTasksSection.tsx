import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Modal } from 'react-native';
import { TaskCard } from './TaskCard';
import { Task, DailyTaskSummary } from '../../types/tasks';
import { TaskService } from '../../services/tasks';
import { VoiceService } from '../../services/voice';

interface TodaysTasksSectionProps {
    onViewAllTasks?: () => void;
}

export const TodaysTasksSection: React.FC<TodaysTasksSectionProps> = ({
    onViewAllTasks,
}) => {
    const [taskSummary, setTaskSummary] = useState<DailyTaskSummary | null>(null);
    const [selectedTask, setSelectedTask] = useState<Task | null>(null);
    const [showTaskDetail, setShowTaskDetail] = useState(false);
    const [loading, setLoading] = useState(true);

    const taskService = TaskService.getInstance();
    const voiceService = VoiceService.getInstance();

    useEffect(() => {
        loadTodaysTasks();
    }, []);

    const loadTodaysTasks = async () => {
        try {
            setLoading(true);
            const summary = await taskService.getDailyTaskSummary();
            setTaskSummary(summary);
        } catch (error) {
            console.error('Failed to load today\'s tasks:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleTaskPress = (task: Task) => {
        setSelectedTask(task);
        setShowTaskDetail(true);
    };

    const handleTaskComplete = async (task: Task) => {
        // Reload tasks to reflect changes
        await loadTodaysTasks();
    };

    const handleViewAllTasks = async () => {
        await voiceService.speak('Opening all tasks view');
        onViewAllTasks?.();
    };

    const handleSectionPress = async () => {
        if (!taskSummary) return;

        const summaryText = `Today's tasks: ${taskSummary.completedTasks} of ${taskSummary.totalTasks} completed. ${taskSummary.pointsEarned} points earned today.`;
        await voiceService.speak(summaryText);
    };

    const closeTaskDetail = () => {
        setShowTaskDetail(false);
        setSelectedTask(null);
    };

    if (loading) {
        return (
            <View className="mx-4 mb-6">
                <View className="bg-white rounded-xl p-4 shadow-sm">
                    <View className="h-6 bg-gray-200 rounded mb-3 w-32" />
                    <View className="gap-3">
                        {[1, 2, 3].map((i) => (
                            <View key={i} className="h-16 bg-gray-100 rounded-lg" />
                        ))}
                    </View>
                </View>
            </View>
        );
    }

    if (!taskSummary) {
        return null;
    }

    const completionPercentage = taskSummary.totalTasks > 0
        ? Math.round((taskSummary.completedTasks / taskSummary.totalTasks) * 100)
        : 0;

    return (
        <>
            <View className="mx-4 mb-6">
                <View className="bg-white rounded-xl p-4 shadow-sm">
                    {/* Section Header */}
                    <TouchableOpacity
                        onPress={handleSectionPress}
                        className="flex-row items-center justify-between mb-4"
                        accessibilityLabel={`Today's tasks section. ${taskSummary.completedTasks} of ${taskSummary.totalTasks} completed`}
                        accessibilityRole="button"
                    >
                        <View className="flex-1">
                            <Text className="text-lg font-bold text-gray-900 mb-1">
                                Today's Tasks ({taskSummary.totalTasks})
                            </Text>
                            <View className="flex-row items-center">
                                <Text className="text-sm text-gray-600 mr-3">
                                    {taskSummary.completedTasks} completed
                                </Text>
                                <View className="flex-row items-center">
                                    <Text className="text-sm text-green-600 mr-1">🏆</Text>
                                    <Text className="text-sm text-green-600 font-medium">
                                        {taskSummary.pointsEarned} pts earned
                                    </Text>
                                </View>
                            </View>
                        </View>

                        {taskSummary.totalTasks > 0 && (
                            <View className="items-center">
                                <View className="w-12 h-12 rounded-full border-4 border-gray-200 items-center justify-center relative">
                                    <View
                                        className={`absolute inset-0 rounded-full border-4 ${completionPercentage === 100 ? 'border-green-500' : 'border-blue-500'
                                            }`}
                                        style={{
                                            transform: [{ rotate: '-90deg' }],
                                            borderTopColor: 'transparent',
                                            borderRightColor: 'transparent',
                                            borderBottomColor: 'transparent',
                                        }}
                                    />
                                    <Text className="text-xs font-bold text-gray-700">
                                        {completionPercentage}%
                                    </Text>
                                </View>
                            </View>
                        )}
                    </TouchableOpacity>

                    {/* Progress Bar */}
                    {taskSummary.totalTasks > 0 && (
                        <View className="mb-4">
                            <View className="h-2 bg-gray-200 rounded-full overflow-hidden">
                                <View
                                    className={`h-full rounded-full ${completionPercentage === 100 ? 'bg-green-500' : 'bg-blue-500'
                                        }`}
                                    style={{ width: `${completionPercentage}%` }}
                                />
                            </View>
                        </View>
                    )}

                    {/* Task List */}
                    {taskSummary.tasks.length === 0 ? (
                        <View className="py-8 items-center">
                            <Text className="text-4xl mb-2">🎉</Text>
                            <Text className="text-lg font-medium text-gray-700 mb-1">
                                No tasks for today!
                            </Text>
                            <Text className="text-sm text-gray-500 text-center">
                                Enjoy your free time or check tomorrow's schedule
                            </Text>
                        </View>
                    ) : (
                        <View className="gap-2">
                            {taskSummary.tasks.slice(0, 3).map((task) => (
                                <TaskCard
                                    key={task.id}
                                    task={task}
                                    onTaskPress={handleTaskPress}
                                    onTaskComplete={handleTaskComplete}
                                />
                            ))}

                            {taskSummary.tasks.length > 3 && (
                                <TouchableOpacity
                                    onPress={handleViewAllTasks}
                                    className="bg-gray-50 rounded-lg p-3 items-center border border-gray-200"
                                    accessibilityLabel={`View all ${taskSummary.tasks.length} tasks`}
                                    accessibilityRole="button"
                                >
                                    <Text className="text-blue-600 font-medium">
                                        View All {taskSummary.tasks.length} Tasks
                                    </Text>
                                </TouchableOpacity>
                            )}
                        </View>
                    )}
                </View>
            </View>

            {/* Task Detail Modal */}
            <Modal
                visible={showTaskDetail}
                animationType="slide"
                presentationStyle="pageSheet"
                onRequestClose={closeTaskDetail}
            >
                <View className="flex-1 bg-gray-50">
                    {/* Modal Header */}
                    <View className="bg-white px-4 py-3 border-b border-gray-200">
                        <View className="flex-row items-center justify-between">
                            <Text className="text-lg font-bold text-gray-900">Task Details</Text>
                            <TouchableOpacity
                                onPress={closeTaskDetail}
                                className="w-8 h-8 items-center justify-center"
                                accessibilityLabel="Close task details"
                                accessibilityRole="button"
                            >
                                <Text className="text-gray-500 text-xl">×</Text>
                            </TouchableOpacity>
                        </View>
                    </View>

                    {/* Modal Content */}
                    <ScrollView className="flex-1 p-4">
                        {selectedTask && (
                            <TaskCard
                                task={selectedTask}
                                onTaskComplete={handleTaskComplete}
                                showDetails={true}
                            />
                        )}
                    </ScrollView>
                </View>
            </Modal>
        </>
    );
};