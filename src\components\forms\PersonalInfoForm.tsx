import React, { useState } from 'react';
import { View, Text } from 'react-native';
import { Input } from '../ui/Input';
import { Button } from '../ui/Button';

export interface PersonalInfoData {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
}

export interface PersonalInfoFormProps {
  initialData?: Partial<PersonalInfoData>;
  onSubmit: (data: PersonalInfoData) => void;
  onCancel?: () => void;
  loading?: boolean;
  voiceFeedbackEnabled?: boolean;
  onVoiceFeedback?: (text: string) => void;
  voiceInputEnabled?: boolean;
  onVoiceInput?: (field: keyof PersonalInfoData) => void;
  isVoiceRecording?: keyof PersonalInfoData | null;
}

export const PersonalInfoForm: React.FC<PersonalInfoFormProps> = ({
  initialData = {},
  onSubmit,
  onCancel,
  loading = false,
  voiceFeedbackEnabled = false,
  onVoiceFeedback,
  voiceInputEnabled = false,
  onVoiceInput,
  isVoiceRecording = null,
}) => {
  const [formData, setFormData] = useState<PersonalInfoData>({
    firstName: initialData.firstName || '',
    lastName: initialData.lastName || '',
    phone: initialData.phone || '',
    email: initialData.email || '',
  });

  const [errors, setErrors] = useState<Partial<PersonalInfoData>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<PersonalInfoData> = {};

    // First name validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    } else if (formData.firstName.trim().length < 2) {
      newErrors.firstName = 'First name must be at least 2 characters';
    }

    // Last name validation
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    } else if (formData.lastName.trim().length < 2) {
      newErrors.lastName = 'Last name must be at least 2 characters';
    }

    // Phone validation
    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^\+?[\d\s\-\(\)]{10,}$/.test(formData.phone.trim())) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email address is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email.trim())) {
      newErrors.email = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      if (voiceFeedbackEnabled && onVoiceFeedback) {
        onVoiceFeedback('Personal information form submitted successfully');
      }
      onSubmit(formData);
    } else {
      if (voiceFeedbackEnabled && onVoiceFeedback) {
        const errorCount = Object.keys(errors).length;
        onVoiceFeedback(
          `Form has ${errorCount} error${errorCount > 1 ? 's' : ''}. Please check your input.`
        );
      }
    }
  };

  const updateField = (field: keyof PersonalInfoData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <View className="w-full gap-6">
      <View className="mb-6">
        <Text className="mb-2 text-2xl font-bold text-earth-900">Personal Information</Text>
        <Text className="text-base text-earth-600">
          Please provide your basic information to create your farming profile.
        </Text>
      </View>

      <View className="gap-4">
        <Input
          label="First Name"
          placeholder="Enter your first name"
          value={formData.firstName}
          onChangeText={(value) => updateField('firstName', value)}
          error={errors.firstName}
          autoCapitalize="words"
          accessibilityLabel="First name input"
          accessibilityHint="Enter your first name for your farming profile"
          voiceInputEnabled={voiceInputEnabled}
          onVoiceInput={() => onVoiceInput?.('firstName')}
          voiceFeedbackEnabled={voiceFeedbackEnabled}
          onVoiceFeedback={onVoiceFeedback}
          isVoiceRecording={isVoiceRecording === 'firstName'}
          testID="first-name-input"
        />

        <Input
          label="Last Name"
          placeholder="Enter your last name"
          value={formData.lastName}
          onChangeText={(value) => updateField('lastName', value)}
          error={errors.lastName}
          autoCapitalize="words"
          accessibilityLabel="Last name input"
          accessibilityHint="Enter your last name for your farming profile"
          voiceInputEnabled={voiceInputEnabled}
          onVoiceInput={() => onVoiceInput?.('lastName')}
          voiceFeedbackEnabled={voiceFeedbackEnabled}
          onVoiceFeedback={onVoiceFeedback}
          isVoiceRecording={isVoiceRecording === 'lastName'}
          testID="last-name-input"
        />

        <Input
          label="Phone Number"
          placeholder="Enter your phone number"
          value={formData.phone}
          onChangeText={(value) => updateField('phone', value)}
          error={errors.phone}
          keyboardType="phone-pad"
          autoCapitalize="none"
          accessibilityLabel="Phone number input"
          accessibilityHint="Enter your phone number for account verification"
          voiceInputEnabled={voiceInputEnabled}
          onVoiceInput={() => onVoiceInput?.('phone')}
          voiceFeedbackEnabled={voiceFeedbackEnabled}
          onVoiceFeedback={onVoiceFeedback}
          isVoiceRecording={isVoiceRecording === 'phone'}
          testID="phone-input"
        />

        <Input
          label="Email Address"
          placeholder="Enter your email address"
          value={formData.email}
          onChangeText={(value) => updateField('email', value)}
          error={errors.email}
          keyboardType="email-address"
          autoCapitalize="none"
          accessibilityLabel="Email address input"
          accessibilityHint="Enter your email address for account access"
          voiceInputEnabled={voiceInputEnabled}
          onVoiceInput={() => onVoiceInput?.('email')}
          voiceFeedbackEnabled={voiceFeedbackEnabled}
          onVoiceFeedback={onVoiceFeedback}
          isVoiceRecording={isVoiceRecording === 'email'}
          testID="email-input"
        />
      </View>

      <View className="mt-8 flex-row gap-4">
        {onCancel && (
          <Button
            title="Cancel"
            onPress={onCancel}
            variant="outline"
            size="large"
            className="flex-1"
            accessibilityLabel="Cancel registration"
            accessibilityHint="Go back to the previous step"
            voiceFeedbackEnabled={voiceFeedbackEnabled}
            onVoiceFeedback={onVoiceFeedback}
            testID="cancel-button"
          />
        )}

        <Button
          title="Continue"
          onPress={handleSubmit}
          variant="primary"
          size="large"
          loading={loading}
          className={onCancel ? 'flex-1' : 'w-full'}
          accessibilityLabel="Continue to next step"
          accessibilityHint="Proceed to farm setup after entering personal information"
          voiceFeedbackEnabled={voiceFeedbackEnabled}
          onVoiceFeedback={onVoiceFeedback}
          testID="continue-button"
        />
      </View>
    </View>
  );
};
