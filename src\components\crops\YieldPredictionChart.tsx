import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Dimensions } from 'react-native';
import { CropPlan } from '../../types/crops';

interface YieldData {
    week: number;
    predictedYield: number;
    actualYield?: number;
    confidence: number;
    factors: {
        weather: number;
        soil: number;
        care: number;
        pests: number;
    };
}

interface YieldPredictionChartProps {
    cropPlan: CropPlan;
    yieldData: YieldData[];
    onUpdatePrediction: () => void;
    voiceEnabled?: boolean;
    onVoiceCommand?: (command: string) => void;
}

export default function YieldPredictionChart({
    cropPlan,
    yieldData,
    onUpdatePrediction,
    voiceEnabled = false,
    onVoiceCommand,
}: YieldPredictionChartProps) {
    const [selectedWeek, setSelectedWeek] = useState<number | null>(null);
    const [viewMode, setViewMode] = useState<'chart' | 'factors' | 'comparison'>('chart');

    const screenWidth = Dimensions.get('window').width;
    const chartWidth = screenWidth - 32; // Account for padding
    const chartHeight = 200;

    const maxYield = Math.max(...yieldData.map(d => d.predictedYield));
    const currentWeek = Math.floor((new Date().getTime() - new Date(cropPlan.plantingDate).getTime()) / (7 * 24 * 60 * 60 * 1000));

    const getYieldColor = (confidence: number): string => {
        if (confidence >= 80) return '#22c55e'; // Green
        if (confidence >= 60) return '#eab308'; // Yellow
        return '#ef4444'; // Red
    };

    const getFactorColor = (score: number): string => {
        if (score >= 80) return 'text-green-600 bg-green-50 border-green-200';
        if (score >= 60) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
        return 'text-red-600 bg-red-50 border-red-200';
    };

    const getFactorIcon = (factor: string): string => {
        const icons = {
            weather: '🌤️',
            soil: '🌱',
            care: '👨‍🌾',
            pests: '🐛',
        };
        return icons[factor as keyof typeof icons] || '📊';
    };

    const handleVoiceCommand = () => {
        if (onVoiceCommand) {
            onVoiceCommand('yield_prediction');
        }
    };

    const renderChart = () => (
        <View className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
            <View className="flex-row items-center justify-between mb-4">
                <Text className="text-lg font-semibold text-gray-900">
                    📈 Yield Prediction
                </Text>
                <TouchableOpacity
                    onPress={onUpdatePrediction}
                    className="px-3 py-1 bg-blue-500 rounded-full"
                    accessibilityLabel="Update prediction"
                >
                    <Text className="text-white text-sm font-medium">Update</Text>
                </TouchableOpacity>
            </View>

            {/* Chart Container */}
            <View className="relative mb-4" style={{ height: chartHeight }}>
                {/* Y-axis labels */}
                <View className="absolute left-0 top-0 bottom-0 justify-between w-12">
                    {[maxYield, maxYield * 0.75, maxYield * 0.5, maxYield * 0.25, 0].map((value, index) => (
                        <Text key={index} className="text-xs text-gray-500 text-right">
                            {Math.round(value)}kg
                        </Text>
                    ))}
                </View>

                {/* Chart area */}
                <View className="ml-14 flex-1 border-l border-b border-gray-200 relative">
                    {/* Grid lines */}
                    {[0.25, 0.5, 0.75].map((ratio, index) => (
                        <View
                            key={index}
                            className="absolute left-0 right-0 border-t border-gray-100"
                            style={{ bottom: `${ratio * 100}%` }}
                        />
                    ))}

                    {/* Data points and bars */}
                    <View className="flex-row items-end justify-between h-full px-2">
                        {yieldData.map((data, index) => {
                            const barHeight = (data.predictedYield / maxYield) * (chartHeight - 40);
                            const isSelected = selectedWeek === data.week;
                            const isPast = data.week <= currentWeek;
                            const hasActual = data.actualYield !== undefined;

                            return (
                                <TouchableOpacity
                                    key={data.week}
                                    onPress={() => setSelectedWeek(isSelected ? null : data.week)}
                                    className="items-center flex-1"
                                    accessibilityLabel={`Week ${data.week} prediction`}
                                >
                                    {/* Actual yield bar (if available) */}
                                    {hasActual && (
                                        <View
                                            className="bg-green-600 rounded-t w-3 absolute bottom-0"
                                            style={{
                                                height: (data.actualYield! / maxYield) * (chartHeight - 40),
                                                left: '25%',
                                            }}
                                        />
                                    )}

                                    {/* Predicted yield bar */}
                                    <View
                                        className={`rounded-t w-3 ${isSelected ? 'bg-blue-600' : 'bg-blue-400'
                                            } ${hasActual ? 'opacity-60' : ''}`}
                                        style={{
                                            height: barHeight,
                                            backgroundColor: hasActual ? '#93c5fd' : getYieldColor(data.confidence),
                                        }}
                                    />

                                    {/* Week label */}
                                    <Text className={`text-xs mt-1 ${isPast ? 'text-gray-900 font-medium' : 'text-gray-500'
                                        }`}>
                                        W{data.week}
                                    </Text>

                                    {/* Current week indicator */}
                                    {data.week === currentWeek && (
                                        <View className="absolute -top-2 bg-orange-500 rounded-full w-2 h-2" />
                                    )}
                                </TouchableOpacity>
                            );
                        })}
                    </View>
                </View>
            </View>

            {/* Legend */}
            <View className="flex-row justify-center gap-4">
                <View className="flex-row items-center">
                    <View className="w-3 h-3 bg-blue-400 rounded mr-1" />
                    <Text className="text-xs text-gray-600">Predicted</Text>
                </View>
                <View className="flex-row items-center">
                    <View className="w-3 h-3 bg-green-600 rounded mr-1" />
                    <Text className="text-xs text-gray-600">Actual</Text>
                </View>
                <View className="flex-row items-center">
                    <View className="w-2 h-2 bg-orange-500 rounded-full mr-1" />
                    <Text className="text-xs text-gray-600">Current Week</Text>
                </View>
            </View>

            {/* Selected week details */}
            {selectedWeek !== null && (
                <View className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    {(() => {
                        const weekData = yieldData.find(d => d.week === selectedWeek);
                        if (!weekData) return null;

                        return (
                            <View>
                                <Text className="font-semibold text-blue-800 mb-2">
                                    Week {selectedWeek} Details
                                </Text>
                                <View className="gap-1">
                                    <View className="flex-row justify-between">
                                        <Text className="text-sm text-blue-700">Predicted Yield:</Text>
                                        <Text className="text-sm font-medium text-blue-800">
                                            {weekData.predictedYield.toFixed(1)} kg
                                        </Text>
                                    </View>
                                    {weekData.actualYield && (
                                        <View className="flex-row justify-between">
                                            <Text className="text-sm text-blue-700">Actual Yield:</Text>
                                            <Text className="text-sm font-medium text-green-600">
                                                {weekData.actualYield.toFixed(1)} kg
                                            </Text>
                                        </View>
                                    )}
                                    <View className="flex-row justify-between">
                                        <Text className="text-sm text-blue-700">Confidence:</Text>
                                        <Text className="text-sm font-medium text-blue-800">
                                            {weekData.confidence}%
                                        </Text>
                                    </View>
                                </View>
                            </View>
                        );
                    })()}
                </View>
            )}
        </View>
    );

    const renderFactors = () => (
        <View className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
            <Text className="text-lg font-semibold text-gray-900 mb-4">
                🎯 Yield Factors
            </Text>

            {selectedWeek !== null ? (
                (() => {
                    const weekData = yieldData.find(d => d.week === selectedWeek);
                    if (!weekData) return null;

                    return (
                        <View>
                            <Text className="text-base font-medium text-gray-800 mb-3">
                                Week {selectedWeek} Factors
                            </Text>
                            <View className="gap-3">
                                {Object.entries(weekData.factors).map(([factor, score]) => (
                                    <View key={factor} className="flex-row items-center justify-between">
                                        <View className="flex-row items-center flex-1">
                                            <Text className="text-2xl mr-3">
                                                {getFactorIcon(factor)}
                                            </Text>
                                            <Text className="text-sm font-medium text-gray-700 capitalize">
                                                {factor}
                                            </Text>
                                        </View>

                                        <View className="flex-row items-center">
                                            <View className="w-20 h-2 bg-gray-200 rounded-full mr-2">
                                                <View
                                                    className="h-2 rounded-full"
                                                    style={{
                                                        width: `${score}%`,
                                                        backgroundColor: getYieldColor(score),
                                                    }}
                                                />
                                            </View>
                                            <Text className={`text-sm font-medium w-8 text-right ${score >= 80 ? 'text-green-600' :
                                                score >= 60 ? 'text-yellow-600' : 'text-red-600'
                                                }`}>
                                                {score}%
                                            </Text>
                                        </View>
                                    </View>
                                ))}
                            </View>
                        </View>
                    );
                })()
            ) : (
                <View className="items-center py-8">
                    <Text className="text-4xl mb-2">📊</Text>
                    <Text className="text-gray-600 text-center">
                        Select a week from the chart to see detailed factors
                    </Text>
                </View>
            )}
        </View>
    );

    const renderComparison = () => {
        const totalPredicted = yieldData.reduce((sum, d) => sum + d.predictedYield, 0);
        const totalActual = yieldData.reduce((sum, d) => sum + (d.actualYield || 0), 0);
        const accuracy = totalActual > 0 ? Math.round((totalActual / totalPredicted) * 100) : 0;

        return (
            <View className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
                <Text className="text-lg font-semibold text-gray-900 mb-4">
                    📊 Prediction vs Reality
                </Text>

                <View className="gap-4">
                    <View className="flex-row justify-between items-center p-3 bg-blue-50 rounded-lg">
                        <Text className="text-sm font-medium text-blue-700">Total Predicted</Text>
                        <Text className="text-lg font-bold text-blue-800">
                            {totalPredicted.toFixed(1)} kg
                        </Text>
                    </View>

                    <View className="flex-row justify-between items-center p-3 bg-green-50 rounded-lg">
                        <Text className="text-sm font-medium text-green-700">Total Actual</Text>
                        <Text className="text-lg font-bold text-green-800">
                            {totalActual.toFixed(1)} kg
                        </Text>
                    </View>

                    {totalActual > 0 && (
                        <View className={`flex-row justify-between items-center p-3 rounded-lg ${accuracy >= 90 ? 'bg-green-50' :
                            accuracy >= 70 ? 'bg-yellow-50' : 'bg-red-50'
                            }`}>
                            <Text className={`text-sm font-medium ${accuracy >= 90 ? 'text-green-700' :
                                accuracy >= 70 ? 'text-yellow-700' : 'text-red-700'
                                }`}>
                                Prediction Accuracy
                            </Text>
                            <Text className={`text-lg font-bold ${accuracy >= 90 ? 'text-green-800' :
                                accuracy >= 70 ? 'text-yellow-800' : 'text-red-800'
                                }`}>
                                {accuracy}%
                            </Text>
                        </View>
                    )}

                    <View className="p-3 bg-gray-50 rounded-lg">
                        <Text className="text-sm font-medium text-gray-700 mb-2">
                            Expected vs Actual
                        </Text>
                        <Text className="text-sm text-gray-600">
                            {cropPlan.expectedYield ? (
                                `Original expectation: ${cropPlan.expectedYield} kg`
                            ) : (
                                'No original expectation set'
                            )}
                        </Text>
                        {cropPlan.actualYield && (
                            <Text className="text-sm text-gray-600">
                                Final harvest: {cropPlan.actualYield} kg
                            </Text>
                        )}
                    </View>
                </View>
            </View>
        );
    };

    return (
        <View className="flex-1 bg-gray-50">
            {/* View Mode Toggle */}
            <View className="bg-white border-b border-gray-200 p-4">
                <View className="flex-row items-center justify-between mb-3">
                    <Text className="text-xl font-bold text-gray-900">
                        Yield Predictions
                    </Text>

                    {voiceEnabled && (
                        <TouchableOpacity
                            onPress={handleVoiceCommand}
                            className="p-2 bg-green-500 rounded-full"
                            accessibilityLabel="Voice commands"
                        >
                            <Text className="text-white text-sm">🎤</Text>
                        </TouchableOpacity>
                    )}
                </View>

                <View className="flex-row bg-gray-100 rounded-lg p-1">
                    {[
                        { key: 'chart', label: 'Chart', icon: '📈' },
                        { key: 'factors', label: 'Factors', icon: '🎯' },
                        { key: 'comparison', label: 'Compare', icon: '📊' },
                    ].map(({ key, label, icon }) => (
                        <TouchableOpacity
                            key={key}
                            onPress={() => setViewMode(key as any)}
                            className={`flex-1 px-3 py-2 rounded-md ${viewMode === key ? 'bg-white shadow-sm' : ''
                                }`}
                            accessibilityLabel={`${label} view`}
                        >
                            <Text className={`text-sm font-medium text-center ${viewMode === key ? 'text-gray-900' : 'text-gray-600'
                                }`}>
                                {icon} {label}
                            </Text>
                        </TouchableOpacity>
                    ))}
                </View>
            </View>

            <ScrollView className="flex-1 p-4">
                {viewMode === 'chart' && renderChart()}
                {viewMode === 'factors' && renderFactors()}
                {viewMode === 'comparison' && renderComparison()}

                {/* Tips */}
                <View className="bg-yellow-50 rounded-lg border border-yellow-200 p-4">
                    <Text className="text-lg font-semibold text-yellow-800 mb-2">
                        💡 Tips for Better Yields
                    </Text>
                    <View className="gap-2">
                        <Text className="text-sm text-yellow-700">
                            • Monitor weather conditions and adjust watering accordingly
                        </Text>
                        <Text className="text-sm text-yellow-700">
                            • Regular pest inspections can prevent major losses
                        </Text>
                        <Text className="text-sm text-yellow-700">
                            • Proper fertilization timing improves yield quality
                        </Text>
                        <Text className="text-sm text-yellow-700">
                            • Document actual yields to improve future predictions
                        </Text>
                    </View>
                </View>
            </ScrollView>

            {/* Voice Commands Help */}
            {voiceEnabled && (
                <View className="bg-blue-50 border-t border-blue-200 p-3">
                    <Text className="text-sm font-semibold text-blue-800 mb-1">
                        🗣️ Voice Commands
                    </Text>
                    <Text className="text-xs text-blue-700">
                        "Show chart" • "View factors" • "Compare yields" • "Update prediction"
                    </Text>
                </View>
            )}
        </View>
    );
}