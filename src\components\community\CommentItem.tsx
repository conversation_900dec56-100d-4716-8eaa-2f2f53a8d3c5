import React from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../../design-system';
import { useVoiceStore } from '../../stores/voice';
import { Comment } from '../../stores/community';

interface CommentItemProps {
    comment: Comment;
    onLike: () => void;
    onReply?: () => void;
    level?: number;
}

export function CommentItem({
    comment,
    onLike,
    onReply,
    level = 0
}: CommentItemProps) {
    const { speak, isVoiceEnabled } = useVoiceStore();

    const formatTimeAgo = (date: Date) => {
        const now = new Date();
        const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

        if (diffInMinutes < 1) return 'Just now';
        if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
        if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
        return `${Math.floor(diffInMinutes / 1440)}d ago`;
    };

    const handleVoiceRead = async () => {
        if (isVoiceEnabled) {
            const voiceText = `Comment by ${comment.author.name}. ${comment.content}. ${comment.likes} likes.`;
            await speak(voiceText);
        }
    };

    const handleLike = () => {
        onLike();
        if (isVoiceEnabled) {
            speak(comment.isLiked ? 'Comment unliked' : 'Comment liked');
        }
    };

    return (
        <View style={{
            marginLeft: level * 20,
            marginBottom: 16,
        }}>
            <View style={{
                flexDirection: 'row',
                alignItems: 'flex-start',
                gap: 12,
            }}>
                {/* Author Avatar */}
                <View style={{
                    width: 36,
                    height: 36,
                    borderRadius: 18,
                    backgroundColor: colors.primary[100],
                    justifyContent: 'center',
                    alignItems: 'center',
                }}>
                    {comment.author.avatar ? (
                        <Image
                            source={{ uri: comment.author.avatar }}
                            style={{
                                width: 36,
                                height: 36,
                                borderRadius: 18,
                            }}
                        />
                    ) : (
                        <Ionicons name="person" size={18} color={colors.primary[500]} />
                    )}
                </View>

                {/* Comment Content */}
                <View style={{ flex: 1 }}>
                    {/* Author and Time */}
                    <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginBottom: 4,
                        gap: 8,
                    }}>
                        <Text style={{
                            fontSize: 14,
                            fontWeight: '600',
                            color: colors.primary[900],
                        }}>
                            {comment.author.name}
                        </Text>
                        <Text style={{
                            fontSize: 12,
                            color: colors.earth[500],
                        }}>
                            {formatTimeAgo(comment.createdAt)}
                        </Text>

                        {/* Voice Read Button */}
                        {isVoiceEnabled && (
                            <TouchableOpacity
                                onPress={handleVoiceRead}
                                style={{ padding: 4 }}
                                accessibilityLabel="Read comment aloud"
                            >
                                <Ionicons name="volume-high" size={14} color={colors.primary[500]} />
                            </TouchableOpacity>
                        )}
                    </View>

                    {/* Comment Text */}
                    <Text style={{
                        fontSize: 15,
                        color: colors.earth[700],
                        lineHeight: 20,
                        marginBottom: 8,
                    }}>
                        {comment.content}
                    </Text>

                    {/* Action Buttons */}
                    <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: 16,
                    }}>
                        <TouchableOpacity
                            onPress={handleLike}
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                gap: 4,
                                padding: 4,
                            }}
                            accessibilityLabel={`${comment.isLiked ? 'Unlike' : 'Like'} comment. ${comment.likes} likes`}
                        >
                            <Ionicons
                                name={comment.isLiked ? "heart" : "heart-outline"}
                                size={16}
                                color={comment.isLiked ? colors.status.error : colors.earth[500]}
                            />
                            {comment.likes > 0 && (
                                <Text style={{
                                    fontSize: 12,
                                    color: comment.isLiked ? colors.status.error : colors.earth[500],
                                    fontWeight: '500',
                                }}>
                                    {comment.likes}
                                </Text>
                            )}
                        </TouchableOpacity>

                        {onReply && level < 2 && (
                            <TouchableOpacity
                                onPress={onReply}
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    gap: 4,
                                    padding: 4,
                                }}
                                accessibilityLabel="Reply to comment"
                            >
                                <Ionicons name="return-down-forward" size={16} color={colors.earth[500]} />
                                <Text style={{
                                    fontSize: 12,
                                    color: colors.earth[500],
                                    fontWeight: '500',
                                }}>
                                    Reply
                                </Text>
                            </TouchableOpacity>
                        )}
                    </View>
                </View>
            </View>

            {/* Nested Replies */}
            {comment.replies && comment.replies.length > 0 && (
                <View style={{ marginTop: 12 }}>
                    {comment.replies.map((reply) => (
                        <CommentItem
                            key={reply.id}
                            comment={reply}
                            onLike={() => { }} // Handle reply likes
                            level={level + 1}
                        />
                    ))}
                </View>
            )}
        </View>
    );
}