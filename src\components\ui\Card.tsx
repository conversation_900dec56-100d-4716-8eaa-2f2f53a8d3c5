import React from 'react';
import { View, Pressable } from 'react-native';

export interface CardProps {
  children: React.ReactNode;
  onPress?: () => void;
  variant?: 'default' | 'elevated' | 'outlined' | 'filled' | 'agricultural';
  padding?: 'none' | 'small' | 'medium' | 'large';
  className?: string;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  testID?: string;
  // Agricultural data display features
  dataType?: 'weather' | 'crop' | 'task' | 'analysis' | 'general';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  voiceFeedbackEnabled?: boolean;
  onVoiceFeedback?: (text: string) => void;
}

export const Card: React.FC<CardProps> = ({
  children,
  onPress,
  variant = 'default',
  padding = 'medium',
  className = '',
  accessibilityLabel,
  accessibilityHint,
  testID,
  dataType = 'general',
  priority = 'medium',
  voiceFeedbackEnabled = false,
  onVoiceFeedback,
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'elevated':
        return 'bg-white shadow-lg border-0';
      case 'outlined':
        return 'bg-white border-2 border-earth-200 shadow-none';
      case 'filled':
        return 'bg-earth-50 border-0 shadow-sm';
      case 'agricultural':
        return getAgriculturalStyles();
      default:
        return 'bg-white shadow-md border border-earth-100';
    }
  };

  const getAgriculturalStyles = () => {
    const baseStyles = 'shadow-md border-l-4';
    switch (dataType) {
      case 'weather':
        return `${baseStyles} bg-blue-50 border-l-blue-500`;
      case 'crop':
        return `${baseStyles} bg-green-50 border-l-green-500`;
      case 'task':
        return `${baseStyles} bg-yellow-50 border-l-yellow-500`;
      case 'analysis':
        return `${baseStyles} bg-purple-50 border-l-purple-500`;
      default:
        return `${baseStyles} bg-white border-l-earth-500`;
    }
  };

  const getPriorityIndicator = () => {
    if (priority === 'urgent') return 'border-t-4 border-t-red-500';
    if (priority === 'high') return 'border-t-2 border-t-orange-500';
    return '';
  };

  const getPaddingStyles = () => {
    switch (padding) {
      case 'none':
        return '';
      case 'small':
        return 'p-3';
      case 'medium':
        return 'p-4';
      case 'large':
        return 'p-6';
      default:
        return 'p-4';
    }
  };

  const baseStyles = `
    ${getVariantStyles()}
    ${getPaddingStyles()}
    ${getPriorityIndicator()}
    rounded-2xl
    ${className}
  `;

  const handlePress = () => {
    if (voiceFeedbackEnabled && onVoiceFeedback && accessibilityLabel) {
      onVoiceFeedback(`${accessibilityLabel} card pressed`);
    }
    onPress?.();
  };

  if (onPress) {
    return (
      <Pressable
        onPress={handlePress}
        accessibilityRole="button"
        accessibilityLabel={accessibilityLabel}
        accessibilityHint={accessibilityHint}
        testID={testID}
        className={`${baseStyles} min-h-[56px] active:opacity-80`}>
        {children}
      </Pressable>
    );
  }

  return (
    <View className={baseStyles} accessibilityLabel={accessibilityLabel} testID={testID}>
      {children}
    </View>
  );
};
