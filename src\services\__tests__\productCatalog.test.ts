/**
 * Product Catalog Service Tests
 * Tests for product search, filtering, recommendations, and inventory management
 */

import { ProductCatalogService } from '../productCatalog';
import { supabase } from '../supabase/client';
import { ProductSearchParams, ProductCategory } from '../../types/product';

// Mock Supabase client
jest.mock('../supabase/client', () => ({
    supabase: {
        from: jest.fn(() => ({
            select: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            gte: jest.fn().mockReturnThis(),
            lte: jest.fn().mockReturnThis(),
            gt: jest.fn().mockReturnThis(),
            overlaps: jest.fn().mockReturnThis(),
            textSearch: jest.fn().mockReturnThis(),
            order: jest.fn().mockReturnThis(),
            range: jest.fn().mockReturnThis(),
            limit: jest.fn().mockReturnThis(),
            single: jest.fn().mockReturnThis(),
            insert: jest.fn().mockReturnThis(),
            delete: jest.fn().mockReturnThis(),
        })),
    },
}));

const mockSupabase = supabase as jest.Mocked<typeof supabase>;

describe('ProductCatalogService', () => {
    let productCatalogService: ProductCatalogService;

    beforeEach(() => {
        productCatalogService = new ProductCatalogService();
        jest.clearAllMocks();
    });

    describe('searchProducts', () => {
        const mockProducts = [
            {
                id: '1',
                name: 'Hybrid Corn Seeds',
                description: 'High-yield hybrid corn seeds',
                category: 'seeds',
                price: 25.99,
                original_price: 29.99,
                currency: 'USD',
                image_urls: ['image1.jpg'],
                specifications: [{ name: 'Variety', value: 'Hybrid' }],
                stock_quantity: 150,
                rating: 4.5,
                review_count: 89,
                tags: ['drought-resistant', 'high-yield'],
                brand: 'AgriSeeds Pro',
                is_recommended: true,
                is_featured: true,
                is_active: true,
                search_vector: null,
                created_at: '2024-01-15T00:00:00Z',
                updated_at: '2024-01-20T00:00:00Z',
            },
        ];

        it('should search products with text query', async () => {
            const mockQuery = {
                textSearch: jest.fn().mockReturnThis(),
                order: jest.fn().mockReturnThis(),
                range: jest.fn().mockReturnThis(),
            };

            mockSupabase.from.mockReturnValue({
                ...mockQuery,
                select: jest.fn().mockReturnValue({
                    ...mockQuery,
                    eq: jest.fn().mockReturnValue({
                        ...mockQuery,
                        textSearch: jest.fn().mockReturnValue({
                            ...mockQuery,
                            order: jest.fn().mockReturnValue({
                                ...mockQuery,
                                range: jest.fn().mockReturnValue({
                                    data: mockProducts,
                                    error: null,
                                    count: 1,
                                }),
                            }),
                        }),
                    }),
                }),
            } as any);

            const params: ProductSearchParams = {
                query: 'corn seeds',
                page: 1,
                limit: 20,
            };

            const result = await productCatalogService.searchProducts(params);

            expect(result.products).toHaveLength(1);
            expect(result.products[0].name).toBe('Hybrid Corn Seeds');
            expect(result.totalCount).toBe(1);
            expect(result.hasMore).toBe(false);
        });

        it('should filter products by category', async () => {
            const mockQuery = {
                eq: jest.fn().mockReturnThis(),
                order: jest.fn().mockReturnThis(),
                range: jest.fn().mockReturnThis(),
            };

            mockSupabase.from.mockReturnValue({
                ...mockQuery,
                select: jest.fn().mockReturnValue({
                    ...mockQuery,
                    eq: jest.fn().mockImplementation((field, value) => {
                        if (field === 'category' && value === 'seeds') {
                            return {
                                ...mockQuery,
                                order: jest.fn().mockReturnValue({
                                    ...mockQuery,
                                    range: jest.fn().mockReturnValue({
                                        data: mockProducts,
                                        error: null,
                                        count: 1,
                                    }),
                                }),
                            };
                        }
                        return mockQuery;
                    }),
                }),
            } as any);

            const params: ProductSearchParams = {
                category: 'seeds' as ProductCategory,
            };

            const result = await productCatalogService.searchProducts(params);

            expect(result.products).toHaveLength(1);
            expect(result.products[0].category).toBe('seeds');
        });

        it('should apply price range filter', async () => {
            const mockQuery = {
                eq: jest.fn().mockReturnThis(),
                gte: jest.fn().mockReturnThis(),
                lte: jest.fn().mockReturnThis(),
                order: jest.fn().mockReturnThis(),
                range: jest.fn().mockReturnThis(),
            };

            mockSupabase.from.mockReturnValue({
                ...mockQuery,
                select: jest.fn().mockReturnValue({
                    ...mockQuery,
                    eq: jest.fn().mockReturnValue({
                        ...mockQuery,
                        gte: jest.fn().mockReturnValue({
                            ...mockQuery,
                            lte: jest.fn().mockReturnValue({
                                ...mockQuery,
                                order: jest.fn().mockReturnValue({
                                    ...mockQuery,
                                    range: jest.fn().mockReturnValue({
                                        data: mockProducts,
                                        error: null,
                                        count: 1,
                                    }),
                                }),
                            }),
                        }),
                    }),
                }),
            } as any);

            const params: ProductSearchParams = {
                filters: {
                    priceRange: { min: 20, max: 30 },
                },
            };

            const result = await productCatalogService.searchProducts(params);

            expect(mockSupabase.from().select().eq().gte).toHaveBeenCalledWith('price', 20);
            expect(mockSupabase.from().select().eq().gte().lte).toHaveBeenCalledWith('price', 30);
        });

        it('should handle search errors gracefully', async () => {
            mockSupabase.from.mockReturnValue({
                select: jest.fn().mockReturnValue({
                    eq: jest.fn().mockReturnValue({
                        order: jest.fn().mockReturnValue({
                            range: jest.fn().mockReturnValue({
                                data: null,
                                error: { message: 'Database error' },
                                count: 0,
                            }),
                        }),
                    }),
                }),
            } as any);

            const params: ProductSearchParams = {
                query: 'test',
            };

            await expect(productCatalogService.searchProducts(params)).rejects.toThrow(
                'Failed to search products: Database error'
            );
        });
    });

    describe('getProductById', () => {
        it('should return product by ID', async () => {
            const mockProduct = {
                id: '1',
                name: 'Test Product',
                description: 'Test description',
                category: 'seeds',
                price: 25.99,
                original_price: null,
                currency: 'USD',
                image_urls: [],
                specifications: [],
                stock_quantity: 100,
                rating: 4.0,
                review_count: 10,
                tags: [],
                brand: null,
                is_recommended: false,
                is_featured: false,
                is_active: true,
                search_vector: null,
                created_at: '2024-01-15T00:00:00Z',
                updated_at: '2024-01-20T00:00:00Z',
            };

            mockSupabase.from.mockReturnValue({
                select: jest.fn().mockReturnValue({
                    eq: jest.fn().mockReturnValue({
                        eq: jest.fn().mockReturnValue({
                            single: jest.fn().mockReturnValue({
                                data: mockProduct,
                                error: null,
                            }),
                        }),
                    }),
                }),
            } as any);

            const result = await productCatalogService.getProductById('1');

            expect(result).toBeTruthy();
            expect(result?.id).toBe('1');
            expect(result?.name).toBe('Test Product');
        });

        it('should return null for non-existent product', async () => {
            mockSupabase.from.mockReturnValue({
                select: jest.fn().mockReturnValue({
                    eq: jest.fn().mockReturnValue({
                        eq: jest.fn().mockReturnValue({
                            single: jest.fn().mockReturnValue({
                                data: null,
                                error: { code: 'PGRST116' }, // Not found error
                            }),
                        }),
                    }),
                }),
            } as any);

            const result = await productCatalogService.getProductById('nonexistent');

            expect(result).toBeNull();
        });
    });

    describe('getFeaturedProducts', () => {
        it('should return featured products', async () => {
            const mockFeaturedProducts = [
                {
                    id: '1',
                    name: 'Featured Product 1',
                    is_featured: true,
                    is_active: true,
                    rating: 4.8,
                    // ... other required fields
                },
            ];

            mockSupabase.from.mockReturnValue({
                select: jest.fn().mockReturnValue({
                    eq: jest.fn().mockImplementation((field, value) => {
                        if (field === 'is_featured' && value === true) {
                            return {
                                eq: jest.fn().mockReturnValue({
                                    order: jest.fn().mockReturnValue({
                                        limit: jest.fn().mockReturnValue({
                                            data: mockFeaturedProducts,
                                            error: null,
                                        }),
                                    }),
                                }),
                            };
                        }
                        return { eq: jest.fn().mockReturnThis() };
                    }),
                }),
            } as any);

            const result = await productCatalogService.getFeaturedProducts(10);

            expect(result).toHaveLength(1);
            expect(mockSupabase.from().select().eq).toHaveBeenCalledWith('is_featured', true);
        });
    });

    describe('generateAIRecommendations', () => {
        it('should generate AI recommendations for user', async () => {
            // Mock user orders
            mockSupabase.from.mockImplementation((table) => {
                if (table === 'orders') {
                    return {
                        select: jest.fn().mockReturnValue({
                            eq: jest.fn().mockReturnValue({
                                eq: jest.fn().mockReturnValue({
                                    data: [],
                                    error: null,
                                }),
                            }),
                        }),
                    };
                }
                if (table === 'user_profiles') {
                    return {
                        select: jest.fn().mockReturnValue({
                            eq: jest.fn().mockReturnValue({
                                single: jest.fn().mockReturnValue({
                                    data: {
                                        crop_types: ['corn', 'tomato'],
                                        experience_level: 'intermediate',
                                    },
                                    error: null,
                                }),
                            }),
                        }),
                    };
                }
                if (table === 'product_recommendations') {
                    return {
                        delete: jest.fn().mockReturnValue({
                            eq: jest.fn().mockReturnValue({
                                error: null,
                            }),
                        }),
                        insert: jest.fn().mockReturnValue({
                            error: null,
                        }),
                    };
                }
                return {
                    select: jest.fn().mockReturnValue({
                        data: [],
                        error: null,
                    }),
                };
            });

            await expect(
                productCatalogService.generateAIRecommendations('user-1')
            ).resolves.not.toThrow();
        });
    });

    describe('getProductReviews', () => {
        it('should return product reviews', async () => {
            const mockReviews = [
                {
                    id: '1',
                    product_id: 'product-1',
                    user_id: 'user-1',
                    rating: 5,
                    title: 'Great product',
                    comment: 'Highly recommended',
                    helpful_count: 5,
                    verified_purchase: true,
                    created_at: '2024-01-15T00:00:00Z',
                    updated_at: '2024-01-15T00:00:00Z',
                    user: { first_name: 'John', last_name: 'Doe' },
                },
            ];

            mockSupabase.from.mockReturnValue({
                select: jest.fn().mockReturnValue({
                    eq: jest.fn().mockReturnValue({
                        order: jest.fn().mockReturnValue({
                            limit: jest.fn().mockReturnValue({
                                data: mockReviews,
                                error: null,
                            }),
                        }),
                    }),
                }),
            } as any);

            const result = await productCatalogService.getProductReviews('product-1', 10);

            expect(result).toHaveLength(1);
            expect(result[0].rating).toBe(5);
            expect(result[0].title).toBe('Great product');
        });
    });

    describe('addProductReview', () => {
        it('should add a product review', async () => {
            // Mock order items check
            mockSupabase.from.mockImplementation((table) => {
                if (table === 'order_items') {
                    return {
                        select: jest.fn().mockReturnValue({
                            eq: jest.fn().mockReturnValue({
                                eq: jest.fn().mockReturnValue({
                                    eq: jest.fn().mockReturnValue({
                                        data: [{ id: '1' }], // User has purchased this product
                                        error: null,
                                    }),
                                }),
                            }),
                        }),
                    };
                }
                if (table === 'product_reviews') {
                    return {
                        insert: jest.fn().mockReturnValue({
                            error: null,
                        }),
                    };
                }
                return {};
            });

            await expect(
                productCatalogService.addProductReview('product-1', 'user-1', 5, 'Great!', 'Love it')
            ).resolves.not.toThrow();
        });

        it('should handle review insertion errors', async () => {
            mockSupabase.from.mockImplementation((table) => {
                if (table === 'order_items') {
                    return {
                        select: jest.fn().mockReturnValue({
                            eq: jest.fn().mockReturnValue({
                                eq: jest.fn().mockReturnValue({
                                    eq: jest.fn().mockReturnValue({
                                        data: [],
                                        error: null,
                                    }),
                                }),
                            }),
                        }),
                    };
                }
                if (table === 'product_reviews') {
                    return {
                        insert: jest.fn().mockReturnValue({
                            error: { message: 'Insert failed' },
                        }),
                    };
                }
                return {};
            });

            await expect(
                productCatalogService.addProductReview('product-1', 'user-1', 5)
            ).rejects.toThrow('Failed to add review: Insert failed');
        });
    });

    describe('getCategories', () => {
        it('should return product categories with counts', async () => {
            const mockCategories = [
                {
                    id: 'seeds',
                    name: 'Seeds',
                    description: 'Various crop seeds',
                    icon: '🌱',
                    sort_order: 1,
                    is_active: true,
                    products: [{ id: '1' }, { id: '2' }],
                },
            ];

            mockSupabase.from.mockReturnValue({
                select: jest.fn().mockReturnValue({
                    eq: jest.fn().mockReturnValue({
                        order: jest.fn().mockReturnValue({
                            data: mockCategories,
                            error: null,
                        }),
                    }),
                }),
            } as any);

            const result = await productCatalogService.getCategories();

            expect(result).toHaveLength(1);
            expect(result[0].id).toBe('seeds');
            expect(result[0].name).toBe('Seeds');
            expect(result[0].productCount).toBe(2);
        });
    });

    describe('getInventoryStatus', () => {
        it('should return inventory status for products', async () => {
            const mockInventory = {
                'product-1': {
                    stockQuantity: 100,
                    isInStock: true,
                    lowStock: false,
                },
            };

            mockSupabase.from.mockReturnValue({
                select: jest.fn().mockReturnValue({
                    in: jest.fn().mockReturnValue({
                        data: [
                            {
                                id: 'product-1',
                                stock_quantity: 100,
                            },
                        ],
                        error: null,
                    }),
                }),
            } as any);

            const result = await productCatalogService.getInventoryStatus(['product-1']);

            expect(result['product-1']).toBeDefined();
            expect(result['product-1'].stockQuantity).toBe(100);
            expect(result['product-1'].isInStock).toBe(true);
        });
    });
});