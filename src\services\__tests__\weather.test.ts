import { WeatherService } from '../weather';
import { LocationCoordinates } from '../location';

describe('WeatherService', () => {
    let weatherService: WeatherService;
    const testLocation: LocationCoordinates = {
        latitude: 40.7128,
        longitude: -74.0060,
    };

    beforeEach(() => {
        weatherService = WeatherService.getInstance();
    });

    test('should format temperature correctly', () => {
        expect(weatherService.formatTemperature(25, 'C')).toBe('25°C');
        expect(weatherService.formatTemperature(25, 'F')).toBe('77°F');
    });

    test('should get weather icon', () => {
        expect(weatherService.getWeatherIcon('01d')).toBe('☀️');
        expect(weatherService.getWeatherIcon('10d')).toBe('🌦️');
        expect(weatherService.getWeatherIcon('unknown')).toBe('🌤️');
    });

    test('should provide weather advice', () => {
        const mockWeather = {
            temperature: 35,
            feelsLike: 38,
            humidity: 85,
            pressure: 1013,
            visibility: 10,
            uvIndex: 8,
            windSpeed: 15,
            windDirection: 180,
            conditions: [{ id: 200, main: 'Thunderstorm', description: 'thunderstorm', icon: '11d' }],
            timestamp: new Date(),
        };

        const advice = weatherService.getWeatherAdvice(mockWeather);
        expect(advice).toContain('High temperature - ensure adequate irrigation');
        expect(advice).toContain('High humidity - monitor for fungal diseases');
        expect(advice).toContain('Strong winds - check plant supports');
    });

    test('should generate agricultural alerts', async () => {
        const alerts = await weatherService.generateAgriculturalAlerts(
            testLocation,
            ['tomatoes', 'peppers', 'corn']
        );

        expect(alerts).toBeDefined();
        expect(Array.isArray(alerts)).toBe(true);
        // Alerts may or may not be present depending on current weather
    });

    test('should get task scheduling adjustments', () => {
        const mockForecast = [
            {
                date: new Date(),
                temperature: { min: 15, max: 25 },
                conditions: [],
                humidity: 60,
                windSpeed: 5,
                precipitation: 0,
            },
            {
                date: new Date(Date.now() + 24 * 60 * 60 * 1000),
                temperature: { min: 10, max: 20 },
                conditions: [],
                humidity: 80,
                windSpeed: 25,
                precipitation: 15,
            },
        ];

        const adjustments = weatherService.getTaskSchedulingAdjustments(mockForecast, 'watering');
        expect(adjustments).toHaveProperty('recommendedDays');
        expect(adjustments).toHaveProperty('avoidDays');
        expect(adjustments).toHaveProperty('adjustments');
        expect(adjustments.avoidDays).toContain(1); // Should avoid day 2 due to rain
    });

    test('should analyze weather patterns', async () => {
        const patterns = await weatherService.analyzeWeatherPatterns(testLocation, 1);

        expect(patterns).toBeDefined();
        expect(Array.isArray(patterns)).toBe(true);
        expect(patterns.length).toBeGreaterThan(0);

        if (patterns.length > 0) {
            expect(patterns[0]).toHaveProperty('season');
            expect(patterns[0]).toHaveProperty('avgTemperature');
            expect(patterns[0]).toHaveProperty('avgPrecipitation');
            expect(patterns[0]).toHaveProperty('optimalCrops');
            expect(patterns[0]).toHaveProperty('riskFactors');
        }
    });
});