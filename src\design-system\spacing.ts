/**
 * Spacing system for consistent layout and touch targets
 * Optimized for agricultural use with work gloves
 */

export const spacing = {
  // Base spacing scale
  0: 0,
  1: 4, // 0.25rem
  2: 8, // 0.5rem
  3: 12, // 0.75rem
  4: 16, // 1rem
  5: 20, // 1.25rem
  6: 24, // 1.5rem
  8: 32, // 2rem
  10: 40, // 2.5rem
  12: 48, // 3rem
  16: 64, // 4rem
  20: 80, // 5rem
  24: 96, // 6rem
  32: 128, // 8rem
  40: 160, // 10rem
  48: 192, // 12rem
  56: 224, // 14rem
  64: 256, // 16rem
} as const;

// Touch targets optimized for agricultural use
export const touchTargets = {
  // Minimum touch target size (44px recommended for accessibility)
  minimum: 44,
  // Comfortable touch target for work gloves
  comfortable: 56,
  // Large touch target for critical actions
  large: 72,
} as const;

// Common spacing patterns
export const spacingPatterns = {
  // Container padding
  containerPadding: 'px-4 py-6',
  containerPaddingLarge: 'px-6 py-8',

  // Card spacing
  cardPadding: 'p-4',
  cardPaddingLarge: 'p-6',
  cardGap: 'gap-4',

  // Form spacing
  formFieldGap: 'gap-4',
  formSectionGap: 'gap-6',
  inputPadding: 'px-4 py-3',

  // Button spacing
  buttonPadding: 'px-6 py-3',
  buttonPaddingLarge: 'px-8 py-4',
  buttonGap: 'gap-2',

  // List spacing
  listItemPadding: 'px-4 py-3',
  listGap: 'gap-2',
} as const;
