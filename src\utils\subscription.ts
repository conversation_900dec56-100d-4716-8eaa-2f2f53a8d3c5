import { SubscriptionPlan, UserSubscription, FeatureAccess } from '../types/subscription';

/**
 * Format price for display
 */
export const formatPrice = (price: number, currency: string = 'USD'): string => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency,
    }).format(price);
};

/**
 * Calculate monthly price for yearly plans
 */
export const getMonthlyEquivalent = (plan: SubscriptionPlan): number => {
    if (plan.billing_period === 'monthly') return plan.price;
    return plan.price / 12;
};

/**
 * Calculate yearly savings
 */
export const getYearlySavings = (monthlyPrice: number, yearlyPrice: number): number => {
    return (monthlyPrice * 12) - yearlyPrice;
};

/**
 * Get subscription status color
 */
export const getSubscriptionStatusColor = (status: UserSubscription['status']): string => {
    const colorMap: Record<UserSubscription['status'], string> = {
        active: '#22c55e',      // Green
        trialing: '#3b82f6',    // Blue
        cancelled: '#f59e0b',   // Amber
        expired: '#ef4444',     // Red
        past_due: '#ef4444',    // Red
    };
    return colorMap[status] || '#6b7280';
};

/**
 * Get subscription status label
 */
export const getSubscriptionStatusLabel = (status: UserSubscription['status']): string => {
    const labelMap: Record<UserSubscription['status'], string> = {
        active: 'Active',
        trialing: 'Trial',
        cancelled: 'Cancelled',
        expired: 'Expired',
        past_due: 'Past Due',
    };
    return labelMap[status] || 'Unknown';
};

/**
 * Check if subscription is active
 */
export const isSubscriptionActive = (subscription: UserSubscription | null): boolean => {
    if (!subscription) return false;
    return subscription.status === 'active' || subscription.status === 'trialing';
};

/**
 * Get days remaining in subscription
 */
export const getDaysRemaining = (subscription: UserSubscription | null): number => {
    if (!subscription) return 0;

    const endDate = new Date(subscription.current_period_end);
    const now = new Date();
    const diffTime = endDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return Math.max(0, diffDays);
};

/**
 * Check if subscription is expiring soon
 */
export const isExpiringSoon = (subscription: UserSubscription | null, days: number = 7): boolean => {
    const remaining = getDaysRemaining(subscription);
    return remaining > 0 && remaining <= days;
};

/**
 * Get feature comparison between plans
 */
export const compareFeatures = (plan1: SubscriptionPlan, plan2: SubscriptionPlan): {
    plan1Only: string[];
    plan2Only: string[];
    common: string[];
} => {
    const plan1Features = plan1.features.map(f => f.id);
    const plan2Features = plan2.features.map(f => f.id);

    const plan1Only = plan1Features.filter(f => !plan2Features.includes(f));
    const plan2Only = plan2Features.filter(f => !plan1Features.includes(f));
    const common = plan1Features.filter(f => plan2Features.includes(f));

    return { plan1Only, plan2Only, common };
};

/**
 * Get recommended plan based on usage
 */
export const getRecommendedPlan = (
    plans: SubscriptionPlan[],
    monthlyAIUsage: number,
    needsOffline: boolean,
    needsAnalytics: boolean
): SubscriptionPlan | null => {
    // Sort plans by price
    const sortedPlans = [...plans].sort((a, b) => a.price - b.price);

    for (const plan of sortedPlans) {
        // Check AI consultation limit
        if (plan.ai_consultations_limit !== -1 && monthlyAIUsage > plan.ai_consultations_limit) {
            continue;
        }

        // Check offline access requirement
        if (needsOffline && !plan.offline_access) {
            continue;
        }

        // Check analytics requirement
        if (needsAnalytics && !plan.advanced_analytics) {
            continue;
        }

        return plan;
    }

    // If no plan meets requirements, return the highest tier
    return sortedPlans[sortedPlans.length - 1] || null;
};

/**
 * Calculate usage percentage
 */
export const calculateUsagePercentage = (used: number, limit: number): number => {
    if (limit === -1) return 0; // Unlimited
    if (limit === 0) return 100;
    return Math.min(100, (used / limit) * 100);
};

/**
 * Get usage status color
 */
export const getUsageStatusColor = (percentage: number): string => {
    if (percentage >= 90) return '#ef4444'; // Red
    if (percentage >= 70) return '#f59e0b'; // Amber
    return '#22c55e'; // Green
};

/**
 * Format usage display
 */
export const formatUsage = (used: number, limit: number): string => {
    if (limit === -1) return `${used} (Unlimited)`;
    return `${used} / ${limit}`;
};

/**
 * Get plan tier level
 */
export const getPlanTierLevel = (planName: string): number => {
    const tierMap: Record<string, number> = {
        free: 0,
        basic: 1,
        premium: 2,
        pro: 3,
    };
    return tierMap[planName.toLowerCase()] || 0;
};

/**
 * Check if plan is upgrade
 */
export const isUpgrade = (currentPlan: SubscriptionPlan, newPlan: SubscriptionPlan): boolean => {
    return getPlanTierLevel(newPlan.name) > getPlanTierLevel(currentPlan.name);
};

/**
 * Check if plan is downgrade
 */
export const isDowngrade = (currentPlan: SubscriptionPlan, newPlan: SubscriptionPlan): boolean => {
    return getPlanTierLevel(newPlan.name) < getPlanTierLevel(currentPlan.name);
};

/**
 * Get feature access summary
 */
export const getFeatureAccessSummary = (featureAccess: FeatureAccess): {
    enabled: number;
    total: number;
    percentage: number;
} => {
    const features = Object.values(featureAccess);
    const enabled = features.filter(Boolean).length;
    const total = features.length;
    const percentage = (enabled / total) * 100;

    return { enabled, total, percentage };
};

/**
 * Get subscription renewal date
 */
export const getRenewalDate = (subscription: UserSubscription | null): Date | null => {
    if (!subscription) return null;
    return new Date(subscription.current_period_end);
};

/**
 * Format renewal date
 */
export const formatRenewalDate = (subscription: UserSubscription | null): string => {
    const renewalDate = getRenewalDate(subscription);
    if (!renewalDate) return 'N/A';

    return renewalDate.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
    });
};

/**
 * Calculate prorated amount for plan change
 */
export const calculateProratedAmount = (
    currentPlan: SubscriptionPlan,
    newPlan: SubscriptionPlan,
    daysRemaining: number
): number => {
    const daysInPeriod = currentPlan.billing_period === 'monthly' ? 30 : 365;
    const unusedAmount = (currentPlan.price * daysRemaining) / daysInPeriod;
    const newPeriodAmount = newPlan.price;

    return Math.max(0, newPeriodAmount - unusedAmount);
};

/**
 * Get billing cycle label
 */
export const getBillingCycleLabel = (billingPeriod: 'monthly' | 'yearly'): string => {
    return billingPeriod === 'monthly' ? 'per month' : 'per year';
};

/**
 * Validate subscription change
 */
export const validateSubscriptionChange = (
    currentPlan: SubscriptionPlan | null,
    newPlan: SubscriptionPlan,
    subscription: UserSubscription | null
): { isValid: boolean; error?: string } => {
    if (!currentPlan) {
        return { isValid: true }; // New subscription
    }

    if (currentPlan.id === newPlan.id) {
        return { isValid: false, error: 'Cannot change to the same plan' };
    }

    if (subscription?.status === 'cancelled') {
        return { isValid: false, error: 'Cannot change cancelled subscription' };
    }

    if (subscription?.status === 'past_due') {
        return { isValid: false, error: 'Please resolve payment issues first' };
    }

    return { isValid: true };
};

/**
 * Get subscription benefits summary
 */
export const getSubscriptionBenefits = (plan: SubscriptionPlan): string[] => {
    const benefits: string[] = [];

    if (plan.ai_consultations_limit === -1) {
        benefits.push('Unlimited AI consultations');
    } else {
        benefits.push(`${plan.ai_consultations_limit} AI consultations per month`);
    }

    benefits.push(`${plan.points_included} bonus points monthly`);

    if (plan.priority_support) {
        benefits.push('Priority customer support');
    }

    if (plan.offline_access) {
        benefits.push('Offline access to core features');
    }

    if (plan.advanced_analytics) {
        benefits.push('Advanced analytics and insights');
    }

    if (plan.api_access) {
        benefits.push('API access for integrations');
    }

    if (plan.multi_farm_support) {
        benefits.push('Multi-farm management');
    }

    if (plan.team_collaboration) {
        benefits.push('Team collaboration features');
    }

    return benefits;
};