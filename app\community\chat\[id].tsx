import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { colors } from '../../../src/design-system';
import { VoiceButton } from '../../../src/components/ui/VoiceButton';
import { useVoiceStore } from '../../../src/stores/voice';

interface ChatMessage {
  id: string;
  content: string;
  senderId: string;
  timestamp: Date;
  type: 'text' | 'image' | 'voice';
  imageUrl?: string;
  isRead: boolean;
}

interface ChatParticipant {
  id: string;
  name: string;
  avatar?: string;
  location?: string;
  isOnline: boolean;
  lastSeen?: Date;
}

// Mock data
const mockParticipant: ChatParticipant = {
  id: 'user1',
  name: '<PERSON>',
  location: 'Cairo, Egypt',
  isOnline: true,
};

const mockMessages: ChatMessage[] = [
  {
    id: '1',
    content: 'Hi! I saw your post about the irrigation system. Could you share more details?',
    senderId: 'current-user',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    type: 'text',
    isRead: true,
  },
  {
    id: '2',
    content: 'Of course! I\'m using a drip irrigation system with smart sensors. It has improved my yield by 30%.',
    senderId: 'user1',
    timestamp: new Date(Date.now() - 1.5 * 60 * 60 * 1000),
    type: 'text',
    isRead: true,
  },
  {
    id: '3',
    content: 'Here\'s a photo of my setup:',
    senderId: 'user1',
    timestamp: new Date(Date.now() - 1.4 * 60 * 60 * 1000),
    type: 'text',
    isRead: true,
  },
  {
    id: '4',
    content: '',
    senderId: 'user1',
    timestamp: new Date(Date.now() - 1.4 * 60 * 60 * 1000),
    type: 'image',
    imageUrl: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400',
    isRead: true,
  },
  {
    id: '5',
    content: 'That looks amazing! What brand of sensors do you use?',
    senderId: 'current-user',
    timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
    type: 'text',
    isRead: true,
  },
  {
    id: '6',
    content: 'Thanks for the advice about the irrigation system!',
    senderId: 'current-user',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    type: 'text',
    isRead: false,
  },
];

export default function ChatScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [messageText, setMessageText] = useState('');
  const [participant, setParticipant] = useState<ChatParticipant | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [loading, setLoading] = useState(true);

  const scrollViewRef = useRef<ScrollView>(null);
  const { speak, isVoiceEnabled, startListening } = useVoiceStore();

  useEffect(() => {
    loadChat();
  }, [id]);

  useEffect(() => {
    // Auto-scroll to bottom when new messages arrive
    if (messages.length > 0) {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  const loadChat = async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setParticipant(mockParticipant);
      setMessages(mockMessages);
      setLoading(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to load chat');
      setLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!messageText.trim()) return;

    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      content: messageText.trim(),
      senderId: 'current-user',
      timestamp: new Date(),
      type: 'text',
      isRead: false,
    };

    // Optimistic update
    setMessages(prev => [...prev, newMessage]);
    setMessageText('');

    if (isVoiceEnabled) {
      speak('Message sent');
    }

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      // Simulate typing indicator
      setIsTyping(true);
      setTimeout(() => {
        setIsTyping(false);
        // Simulate response (in real app, this would come from WebSocket/real-time updates)
      }, 2000);
    } catch (error) {
      Alert.alert('Error', 'Failed to send message');
    }
  };

  const handleVoiceMessage = async () => {
    try {
      if (isVoiceEnabled) {
        speak('Say your message');
        const voiceText = await startListening();
        if (voiceText) {
          setMessageText(voiceText);
          speak('Message text added');
        }
      }
    } catch (error) {
      Alert.alert('Voice Error', 'Failed to process voice input');
    }
  };

  const handleImagePicker = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Please grant camera roll permissions');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        quality: 0.8,
        aspect: [4, 3],
      });

      if (!result.canceled && result.assets?.[0]) {
        const imageMessage: ChatMessage = {
          id: Date.now().toString(),
          content: '',
          senderId: 'current-user',
          timestamp: new Date(),
          type: 'image',
          imageUrl: result.assets[0].uri,
          isRead: false,
        };

        setMessages(prev => [...prev, imageMessage]);

        if (isVoiceEnabled) {
          speak('Image sent');
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to send image');
    }
  };

  const handleMessageRead = async (messageId: string) => {
    if (isVoiceEnabled) {
      const message = messages.find(m => m.id === messageId);
      if (message) {
        await speak(`Message from ${participant?.name}: ${message.content}`);
      }
    }
  };

  const formatTimestamp = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDateSeparator = (date: Date) => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }
  };

  const renderMessage = (message: ChatMessage, index: number) => {
    const isCurrentUser = message.senderId === 'current-user';
    const showDateSeparator = index === 0 ||
      messages[index - 1].timestamp.toDateString() !== message.timestamp.toDateString();

    return (
      <View key={message.id}>
        {showDateSeparator && (
          <View className="items-center my-4">
            <Text className="text-gray-500 text-sm bg-gray-100 px-3 py-1 rounded-full">
              {formatDateSeparator(message.timestamp)}
            </Text>
          </View>
        )}

        <TouchableOpacity
          onPress={() => handleMessageRead(message.id)}
          className={`flex-row mb-3 ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
        >
          {!isCurrentUser && (
            <Image
              source={{ uri: participant?.avatar || 'https://via.placeholder.com/40' }}
              className="w-8 h-8 rounded-full mr-2 mt-1"
            />
          )}

          <View className={`max-w-[75%] ${isCurrentUser ? 'items-end' : 'items-start'}`}>
            <View
              className={`px-4 py-2 rounded-2xl ${isCurrentUser
                  ? 'bg-green-500 rounded-br-md'
                  : 'bg-gray-200 rounded-bl-md'
                }`}
            >
              {message.type === 'image' && message.imageUrl ? (
                <Image
                  source={{ uri: message.imageUrl }}
                  className="w-48 h-36 rounded-lg"
                  resizeMode="cover"
                />
              ) : (
                <Text className={`${isCurrentUser ? 'text-white' : 'text-gray-800'}`}>
                  {message.content}
                </Text>
              )}
            </View>

            <Text className="text-xs text-gray-500 mt-1 px-1">
              {formatTimestamp(message.timestamp)}
              {isCurrentUser && (
                <Text className={`ml-1 ${message.isRead ? 'text-blue-500' : 'text-gray-400'}`}>
                  {message.isRead ? '✓✓' : '✓'}
                </Text>
              )}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <View className="flex-1 justify-center items-center">
          <Text className="text-gray-500">Loading chat...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className="flex-row items-center px-4 py-3 border-b border-gray-200">
        <TouchableOpacity
          onPress={() => router.back()}
          className="mr-3"
        >
          <Ionicons name="arrow-back" size={24} color={colors.gray[600]} />
        </TouchableOpacity>

        <Image
          source={{ uri: participant?.avatar || 'https://via.placeholder.com/40' }}
          className="w-10 h-10 rounded-full mr-3"
        />

        <View className="flex-1">
          <Text className="font-semibold text-gray-900">{participant?.name}</Text>
          <Text className="text-sm text-gray-500">
            {participant?.isOnline ? 'Online' : 'Last seen recently'}
          </Text>
        </View>

        <TouchableOpacity className="p-2">
          <Ionicons name="call" size={20} color={colors.green[600]} />
        </TouchableOpacity>

        <TouchableOpacity className="p-2 ml-2">
          <Ionicons name="videocam" size={20} color={colors.green[600]} />
        </TouchableOpacity>
      </View>

      <KeyboardAvoidingView
        className="flex-1"
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Messages */}
        <ScrollView
          ref={scrollViewRef}
          className="flex-1 px-4 py-2"
          showsVerticalScrollIndicator={false}
        >
          {messages.map((message, index) => renderMessage(message, index))}

          {isTyping && (
            <View className="flex-row justify-start mb-3">
              <Image
                source={{ uri: participant?.avatar || 'https://via.placeholder.com/40' }}
                className="w-8 h-8 rounded-full mr-2 mt-1"
              />
              <View className="bg-gray-200 px-4 py-2 rounded-2xl rounded-bl-md">
                <Text className="text-gray-500">Typing...</Text>
              </View>
            </View>
          )}
        </ScrollView>

        {/* Input Area */}
        <View className="flex-row items-end px-4 py-3 border-t border-gray-200">
          <TouchableOpacity
            onPress={handleImagePicker}
            className="p-2 mr-2"
          >
            <Ionicons name="camera" size={24} color={colors.gray[600]} />
          </TouchableOpacity>

          <View className="flex-1 max-h-24 bg-gray-100 rounded-2xl px-4 py-2">
            <TextInput
              value={messageText}
              onChangeText={setMessageText}
              placeholder="Type a message..."
              multiline
              className="text-gray-900 text-base"
              style={{ minHeight: 20 }}
            />
          </View>

          <VoiceButton
            onPress={handleVoiceMessage}
            className="ml-2"
            size="sm"
          />

          <TouchableOpacity
            onPress={handleSendMessage}
            disabled={!messageText.trim()}
            className={`p-2 ml-2 rounded-full ${messageText.trim() ? 'bg-green-500' : 'bg-gray-300'
              }`}
          >
            <Ionicons
              name="send"
              size={20}
              color={messageText.trim() ? 'white' : colors.gray[500]}
            />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}