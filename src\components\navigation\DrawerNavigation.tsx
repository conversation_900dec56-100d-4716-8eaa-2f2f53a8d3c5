import React from 'react';
import { View, Pressable, Text, ScrollView } from 'react-native';
import { Modal } from '../ui/Modal';

export interface DrawerItem {
  id: string;
  label: string;
  icon: string;
  onPress: () => void;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  badge?: number;
  divider?: boolean;
}

export interface DrawerSection {
  title?: string;
  items: DrawerItem[];
}

export interface DrawerNavigationProps {
  visible: boolean;
  onClose: () => void;
  sections: DrawerSection[];
  userInfo?: {
    name: string;
    email: string;
    avatar?: string;
  };
  voiceFeedbackEnabled?: boolean;
  onVoiceFeedback?: (text: string) => void;
  testID?: string;
}

export const DrawerNavigation: React.FC<DrawerNavigationProps> = ({
  visible,
  onClose,
  sections,
  userInfo,
  voiceFeedbackEnabled = false,
  onVoiceFeedback,
  testID,
}) => {
  const handleItemPress = (item: DrawerItem) => {
    if (voiceFeedbackEnabled && onVoiceFeedback) {
      onVoiceFeedback(`${item.label} selected`);
    }
    item.onPress();
    onClose();
  };

  // const announceDrawerOpen = () => {
  //     if (voiceFeedbackEnabled && onVoiceFeedback) {
  //         onVoiceFeedback('Navigation drawer opened');
  //     }
  // };

  return (
    <Modal
      visible={visible}
      onClose={onClose}
      size="large"
      showCloseButton={false}
      closeOnBackdropPress={true}
      voiceFeedbackEnabled={voiceFeedbackEnabled}
      onVoiceFeedback={onVoiceFeedback}
      autoAnnounce={true}
      testID={testID}>
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* User Info Section */}
        {userInfo && (
          <View className="-mx-6 -mt-4 mb-6 bg-primary-50 px-6 pb-6 pt-4">
            <View className="flex-row items-center gap-4">
              <View className="h-16 w-16 items-center justify-center rounded-full bg-primary-200">
                <Text className="text-2xl text-primary-700">{userInfo.avatar || '👤'}</Text>
              </View>
              <View className="flex-1">
                <Text className="text-lg font-bold text-primary-900">{userInfo.name}</Text>
                <Text className="text-sm text-primary-700">{userInfo.email}</Text>
              </View>
            </View>
          </View>
        )}

        {/* Navigation Sections */}
        {sections.map((section, sectionIndex) => (
          <View key={sectionIndex} className="mb-6">
            {section.title && (
              <Text className="mb-3 px-2 text-sm font-semibold uppercase tracking-wide text-earth-500">
                {section.title}
              </Text>
            )}

            {section.items.map((item, itemIndex) => (
              <View key={item.id}>
                <Pressable
                  onPress={() => handleItemPress(item)}
                  className="min-h-[56px] flex-row items-center rounded-xl px-2 py-4 active:bg-earth-50"
                  accessibilityRole="button"
                  accessibilityLabel={item.accessibilityLabel || item.label}
                  accessibilityHint={item.accessibilityHint || `Navigate to ${item.label}`}
                  testID={`drawer-item-${item.id}`}>
                  <View className="mr-4 w-10 items-center">
                    <Text className="text-xl">{item.icon}</Text>
                  </View>

                  <View className="flex-1">
                    <Text className="text-base font-medium text-earth-900">{item.label}</Text>
                  </View>

                  {item.badge && item.badge > 0 && (
                    <View className="mr-2 h-6 min-w-[24px] items-center justify-center rounded-full bg-red-500 px-2">
                      <Text className="text-xs font-bold text-white">
                        {item.badge > 99 ? '99+' : item.badge}
                      </Text>
                    </View>
                  )}

                  <Text className="text-lg text-earth-400">›</Text>
                </Pressable>

                {item.divider && itemIndex < section.items.length - 1 && (
                  <View className="mx-2 my-2 h-px bg-earth-200" />
                )}
              </View>
            ))}
          </View>
        ))}

        {/* Voice Navigation Announcement */}
        {voiceFeedbackEnabled && (
          <View className="mt-4 rounded-xl bg-primary-50 p-4">
            <Text className="text-center text-sm text-primary-700">
              🎤 Voice commands available: &quot;Go to [section name]&quot; or &quot;Open
              [feature]&quot;
            </Text>
          </View>
        )}
      </ScrollView>
    </Modal>
  );
};

// Default agricultural drawer sections
export const AGRICULTURAL_DRAWER_SECTIONS: DrawerSection[] = [
  {
    title: 'Main Features',
    items: [
      {
        id: 'dashboard',
        label: 'Dashboard',
        icon: '📊',
        onPress: () => { },
        accessibilityHint: 'View your farming dashboard and analytics',
      },
      {
        id: 'weather',
        label: 'Weather Forecast',
        icon: '🌤️',
        onPress: () => { },
        accessibilityHint: 'View detailed weather forecast and alerts',
      },
      {
        id: 'notifications',
        label: 'Notifications',
        icon: '🔔',
        onPress: () => { },
        badge: 3,
        accessibilityHint: 'View your farming notifications and alerts',
      },
    ],
  },
  {
    title: 'Tools & Resources',
    items: [
      {
        id: 'calculator',
        label: 'Farm Calculator',
        icon: '🧮',
        onPress: () => { },
        accessibilityHint: 'Access farming calculators and tools',
      },
      {
        id: 'calendar',
        label: 'Farming Calendar',
        icon: '📅',
        onPress: () => { },
        accessibilityHint: 'View your farming calendar and schedule',
      },
      {
        id: 'library',
        label: 'Knowledge Library',
        icon: '📚',
        onPress: () => { },
        accessibilityHint: 'Access farming guides and resources',
      },
      {
        id: 'history',
        label: 'Activity History',
        icon: '📋',
        onPress: () => { },
        accessibilityHint: 'View your farming activity history',
        divider: true,
      },
    ],
  },
  {
    title: 'Account',
    items: [
      {
        id: 'profile',
        label: 'Profile Settings',
        icon: '⚙️',
        onPress: () => { },
        accessibilityHint: 'Manage your profile and account settings',
      },
      {
        id: 'subscription',
        label: 'Subscription',
        icon: '💎',
        onPress: () => { },
        accessibilityHint: 'Manage your subscription and billing',
      },
      {
        id: 'help',
        label: 'Help & Support',
        icon: '❓',
        onPress: () => { },
        accessibilityHint: 'Get help and contact support',
      },
      {
        id: 'logout',
        label: 'Sign Out',
        icon: '🚪',
        onPress: () => { },
        accessibilityHint: 'Sign out of your account',
        divider: true,
      },
    ],
  },
];
