import React, { useState } from 'react';
import { View, Text, Pressable, ScrollView } from 'react-native';
import { cropTypes, cropCategories, CropType } from '../../data/crops';

export interface CropSelectorProps {
    selectedCrops: string[];
    onCropsChange: (cropIds: string[]) => void;
    maxSelections?: number;
    isRTL?: boolean;
    currentLanguage?: 'en' | 'ar';
    voiceFeedbackEnabled?: boolean;
    onVoiceFeedback?: (text: string) => void;
}

export const CropSelector: React.FC<CropSelectorProps> = ({
    selectedCrops,
    onCropsChange,
    maxSelections = 5,
    isRTL = false,
    currentLanguage = 'en',
    voiceFeedbackEnabled = false,
    onVoiceFeedback,
}) => {
    const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

    const filteredCrops = selectedCategory
        ? cropTypes.filter(crop => crop.category === selectedCategory)
        : cropTypes;

    const handleCropToggle = (cropId: string) => {
        const crop = cropTypes.find(c => c.id === cropId);
        if (!crop) return;

        let newSelection: string[];

        if (selectedCrops.includes(cropId)) {
            // Remove crop
            newSelection = selectedCrops.filter(id => id !== cropId);
            if (voiceFeedbackEnabled && onVoiceFeedback) {
                const cropName = currentLanguage === 'ar' ? crop.nameAr : crop.name;
                onVoiceFeedback(`${cropName} removed from selection`);
            }
        } else {
            // Add crop (if under limit)
            if (selectedCrops.length >= maxSelections) {
                if (voiceFeedbackEnabled && onVoiceFeedback) {
                    onVoiceFeedback(`Maximum ${maxSelections} crops can be selected`);
                }
                return;
            }
            newSelection = [...selectedCrops, cropId];
            if (voiceFeedbackEnabled && onVoiceFeedback) {
                const cropName = currentLanguage === 'ar' ? crop.nameAr : crop.name;
                onVoiceFeedback(`${cropName} added to selection`);
            }
        }

        onCropsChange(newSelection);
    };

    const handleCategorySelect = (categoryId: string | null) => {
        setSelectedCategory(categoryId);
        if (voiceFeedbackEnabled && onVoiceFeedback) {
            if (categoryId) {
                const category = cropCategories.find(c => c.id === categoryId);
                const categoryName = currentLanguage === 'ar' ? category?.nameAr : category?.name;
                onVoiceFeedback(`Showing ${categoryName} crops`);
            } else {
                onVoiceFeedback('Showing all crops');
            }
        }
    };

    return (
        <View className="w-full">
            {/* Category Filter */}
            <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                className="mb-6"
                contentContainerStyle={{ paddingHorizontal: 4 }}
            >
                <View className={`flex-row gap-3 ${isRTL ? 'flex-row-reverse gap-reverse' : ''}`}>
                    {/* All Categories Button */}
                    <Pressable
                        onPress={() => handleCategorySelect(null)}
                        className={`rounded-full px-4 py-2 ${selectedCategory === null
                            ? 'bg-primary-500'
                            : 'bg-earth-100'
                            }`}
                        accessibilityRole="button"
                        accessibilityLabel="Show all crop categories"
                    >
                        <Text className={`font-medium ${selectedCategory === null ? 'text-white' : 'text-earth-700'
                            }`}>
                            {currentLanguage === 'ar' ? 'الكل' : 'All'}
                        </Text>
                    </Pressable>

                    {/* Category Buttons */}
                    {cropCategories.map(category => (
                        <Pressable
                            key={category.id}
                            onPress={() => handleCategorySelect(category.id)}
                            className={`flex-row items-center rounded-full px-4 py-2 ${selectedCategory === category.id
                                ? 'bg-primary-500'
                                : 'bg-earth-100'
                                }`}
                            accessibilityRole="button"
                            accessibilityLabel={`Filter by ${currentLanguage === 'ar' ? category.nameAr : category.name}`}
                        >
                            <Text className="mr-2 text-lg">{category.icon}</Text>
                            <Text className={`font-medium ${selectedCategory === category.id ? 'text-white' : 'text-earth-700'
                                }`}>
                                {currentLanguage === 'ar' ? category.nameAr : category.name}
                            </Text>
                        </Pressable>
                    ))}
                </View>
            </ScrollView>

            {/* Selection Counter */}
            <Text className={`mb-4 text-sm text-earth-600 ${isRTL ? 'text-right' : 'text-left'}`}>
                {currentLanguage === 'ar'
                    ? `تم اختيار ${selectedCrops.length} من ${maxSelections} محاصيل`
                    : `${selectedCrops.length} of ${maxSelections} crops selected`
                }
            </Text>

            {/* Crop Grid */}
            <View className="flex-row flex-wrap justify-between">
                {filteredCrops.map(crop => {
                    const isSelected = selectedCrops.includes(crop.id);
                    const isDisabled = !isSelected && selectedCrops.length >= maxSelections;

                    return (
                        <Pressable
                            key={crop.id}
                            onPress={() => handleCropToggle(crop.id)}
                            disabled={isDisabled}
                            className={`mb-4 w-[48%] rounded-xl border-2 p-4 ${isSelected
                                ? 'border-primary-500 bg-primary-50'
                                : isDisabled
                                    ? 'border-earth-200 bg-earth-50 opacity-50'
                                    : 'border-earth-200 bg-white'
                                }`}
                            accessibilityRole="checkbox"
                            accessibilityState={{ checked: isSelected }}
                            accessibilityLabel={`${currentLanguage === 'ar' ? crop.nameAr : crop.name}. ${currentLanguage === 'ar' ? crop.descriptionAr : crop.description
                                }`}
                        >
                            <View className="items-center">
                                <Text className="mb-2 text-3xl">{crop.icon}</Text>
                                <Text className={`mb-1 text-center text-lg font-semibold ${isSelected ? 'text-primary-700' : 'text-earth-800'
                                    }`}>
                                    {currentLanguage === 'ar' ? crop.nameAr : crop.name}
                                </Text>
                                <Text className={`text-center text-xs text-earth-600 ${isRTL ? 'text-right' : 'text-left'}`} numberOfLines={2}>
                                    {currentLanguage === 'ar' ? crop.descriptionAr : crop.description}
                                </Text>

                                {/* Difficulty Badge */}
                                <View className={`mt-2 rounded-full px-2 py-1 ${crop.difficulty === 'beginner' ? 'bg-green-100' :
                                    crop.difficulty === 'intermediate' ? 'bg-yellow-100' : 'bg-red-100'
                                    }`}>
                                    <Text className={`text-xs font-medium ${crop.difficulty === 'beginner' ? 'text-green-700' :
                                        crop.difficulty === 'intermediate' ? 'text-yellow-700' : 'text-red-700'
                                        }`}>
                                        {currentLanguage === 'ar'
                                            ? crop.difficulty === 'beginner' ? 'مبتدئ' : crop.difficulty === 'intermediate' ? 'متوسط' : 'خبير'
                                            : crop.difficulty.charAt(0).toUpperCase() + crop.difficulty.slice(1)
                                        }
                                    </Text>
                                </View>

                                {/* Selection Indicator */}
                                {isSelected && (
                                    <View className="absolute -right-2 -top-2 h-6 w-6 items-center justify-center rounded-full bg-primary-500">
                                        <Text className="text-xs font-bold text-white">✓</Text>
                                    </View>
                                )}
                            </View>
                        </Pressable>
                    );
                })}
            </View>
        </View>
    );
};