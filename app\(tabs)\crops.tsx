import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Button } from '../../src/components/ui';

export default function CropsScreen() {
    const [voiceEnabled, setVoiceEnabled] = useState(false);

    const handleCreatePlan = () => {
        router.push('/crops/create-plan');
    };

    const handleVoiceCommand = () => {
        // TODO: Implement voice commands
        console.log('Voice command activated');
    };

    return (
        <SafeAreaView className="flex-1 bg-gray-50">
            {/* Header */}
            <View className="bg-white border-b border-gray-200 px-4 py-3">
                <View className="flex-row items-center justify-between">
                    <View>
                        <Text className="text-2xl font-bold text-gray-900">🌱 Crop Planning</Text>
                        <Text className="text-sm text-gray-600">Manage your farming plans</Text>
                    </View>

                    <TouchableOpacity
                        onPress={() => setVoiceEnabled(!voiceEnabled)}
                        className={`p-3 rounded-full ${voiceEnabled ? 'bg-green-500' : 'bg-gray-200'}`}
                        accessibilityLabel="Toggle voice mode"
                    >
                        <Text className={`text-lg ${voiceEnabled ? 'text-white' : 'text-gray-600'}`}>
                            🎤
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>

            <ScrollView className="flex-1 p-4">
                {/* Quick Actions */}
                <View className="bg-white rounded-lg p-4 border border-gray-200 mb-6">
                    <Text className="text-lg font-semibold text-gray-900 mb-4">
                        Quick Actions
                    </Text>

                    <View className="gap-3">
                        <Button
                            title="🌱 Create New Crop Plan"
                            onPress={handleCreatePlan}
                            className="w-full"
                            accessibilityLabel="Create new crop plan"
                            accessibilityHint="Start planning a new crop with AI recommendations"
                        />

                        <View className="flex-row gap-3">
                            <Button
                                title="📊 View Progress"
                                onPress={() => router.push('/crops/progress/plan-1')}
                                variant="outline"
                                className="flex-1"
                            />
                            <Button
                                title="📅 Task Calendar"
                                onPress={() => router.push('/crops/tasks')}
                                variant="outline"
                                className="flex-1"
                            />
                        </View>
                    </View>
                </View>

                {/* Current Plans Section */}
                <View className="bg-white rounded-lg p-4 border border-gray-200 mb-6">
                    <Text className="text-lg font-semibold text-gray-900 mb-4">
                        Current Crop Plans
                    </Text>

                    <View className="items-center py-8">
                        <Text className="text-6xl mb-4">🌾</Text>
                        <Text className="text-lg font-medium text-gray-700 mb-2">
                            No crop plans yet
                        </Text>
                        <Text className="text-gray-500 text-center mb-4">
                            Create your first crop plan to get started with AI-powered farming recommendations
                        </Text>
                        <Button
                            title="Get Started"
                            onPress={handleCreatePlan}
                            size="sm"
                        />
                    </View>
                </View>

                {/* Features Overview */}
                <View className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-4 border border-green-200">
                    <Text className="text-lg font-semibold text-green-800 mb-3">
                        🚀 What you can do with Crop Planning
                    </Text>

                    <View className="gap-3">
                        <View className="flex-row items-start">
                            <Text className="text-2xl mr-3">🤖</Text>
                            <View className="flex-1">
                                <Text className="font-medium text-green-700">AI Recommendations</Text>
                                <Text className="text-sm text-green-600">
                                    Get personalized crop suggestions based on your location and climate
                                </Text>
                            </View>
                        </View>

                        <View className="flex-row items-start">
                            <Text className="text-2xl mr-3">📅</Text>
                            <View className="flex-1">
                                <Text className="font-medium text-green-700">Smart Scheduling</Text>
                                <Text className="text-sm text-green-600">
                                    Automated task generation with optimal timing for planting and harvesting
                                </Text>
                            </View>
                        </View>

                        <View className="flex-row items-start">
                            <Text className="text-2xl mr-3">🌤️</Text>
                            <View className="flex-1">
                                <Text className="font-medium text-green-700">Weather Integration</Text>
                                <Text className="text-sm text-green-600">
                                    Plans adapt automatically to weather conditions and forecasts
                                </Text>
                            </View>
                        </View>

                        <View className="flex-row items-start">
                            <Text className="text-2xl mr-3">📊</Text>
                            <View className="flex-1">
                                <Text className="font-medium text-green-700">Progress Tracking</Text>
                                <Text className="text-sm text-green-600">
                                    Monitor crop growth and track yield predictions
                                </Text>
                            </View>
                        </View>
                    </View>
                </View>

                {/* Voice Commands Help */}
                {voiceEnabled && (
                    <View className="bg-blue-50 rounded-lg p-4 border border-blue-200 mt-4">
                        <Text className="text-lg font-semibold text-blue-800 mb-2">
                            🗣️ Voice Commands Available
                        </Text>
                        <Text className="text-sm text-blue-700 mb-2">
                            Try saying:
                        </Text>
                        <View className="gap-1">
                            <Text className="text-sm text-blue-600">• "Create new crop plan"</Text>
                            <Text className="text-sm text-blue-600">• "Show my current plans"</Text>
                            <Text className="text-sm text-blue-600">• "What crops should I plant?"</Text>
                            <Text className="text-sm text-blue-600">• "Check today's tasks"</Text>
                        </View>
                    </View>
                )}
            </ScrollView>
        </SafeAreaView>
    );
}