import React, { useEffect, useState } from 'react';
import {
    View,
    Text,
    FlatList,
    TouchableOpacity,
    RefreshControl,
    Alert,
} from 'react-native';
import { useNotificationStore, useNotificationHistory, useNotificationActions } from '../../stores/notifications';
import { NotificationData, NotificationType } from '../../types/notifications';

interface NotificationItemProps {
    notification: NotificationData;
    onPress: () => void;
    onMarkAsRead: () => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
    notification,
    onPress,
    onMarkAsRead,
}) => {
    const getNotificationIcon = (type: NotificationType): string => {
        const iconMap: Record<NotificationType, string> = {
            weather_alert: '🌦️',
            task_reminder: '📋',
            crop_stage_update: '🌱',
            community_post: '👥',
            emergency_alert: '🚨',
            ai_insight: '🤖',
            subscription_reminder: '💎',
            points_earned: '⭐',
            achievement_unlocked: '🏆',
            system_maintenance: '🔧',
        };
        return iconMap[type] || '📱';
    };

    const getPriorityColor = (priority: string): string => {
        switch (priority) {
            case 'critical':
                return 'border-red-500 bg-red-50';
            case 'high':
                return 'border-orange-500 bg-orange-50';
            case 'normal':
                return 'border-blue-500 bg-blue-50';
            case 'low':
                return 'border-gray-500 bg-gray-50';
            default:
                return 'border-gray-300 bg-white';
        }
    };

    const formatTime = (date: Date): string => {
        const now = new Date();
        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

        if (diffInHours < 1) {
            const minutes = Math.floor(diffInHours * 60);
            return `${minutes}m ago`;
        } else if (diffInHours < 24) {
            return `${Math.floor(diffInHours)}h ago`;
        } else {
            const days = Math.floor(diffInHours / 24);
            return `${days}d ago`;
        }
    };

    const isUnread = !notification.readAt;

    return (
        <TouchableOpacity
            onPress={onPress}
            className={`border-l-4 rounded-lg p-4 mb-3 ${getPriorityColor(notification.priority)} ${isUnread ? 'shadow-md' : 'opacity-75'
                }`}
        >
            <View className="flex-row items-start justify-between">
                <View className="flex-row items-start flex-1">
                    <Text className="text-2xl mr-3">
                        {getNotificationIcon(notification.type)}
                    </Text>
                    <View className="flex-1">
                        <View className="flex-row items-center justify-between mb-1">
                            <Text className={`text-base font-semibold ${isUnread ? 'text-gray-900' : 'text-gray-600'
                                }`}>
                                {notification.title}
                            </Text>
                            {isUnread && (
                                <View className="w-2 h-2 bg-green-500 rounded-full ml-2" />
                            )}
                        </View>
                        <Text className={`text-sm ${isUnread ? 'text-gray-700' : 'text-gray-500'
                            } mb-2`}>
                            {notification.body}
                        </Text>
                        <View className="flex-row items-center justify-between">
                            <Text className="text-xs text-gray-500">
                                {formatTime(new Date(notification.sentAt || Date.now()))}
                            </Text>
                            {isUnread && (
                                <TouchableOpacity
                                    onPress={onMarkAsRead}
                                    className="bg-green-600 px-3 py-1 rounded-full"
                                >
                                    <Text className="text-white text-xs font-medium">
                                        Mark as Read
                                    </Text>
                                </TouchableOpacity>
                            )}
                        </View>
                    </View>
                </View>
            </View>
        </TouchableOpacity>
    );
};

export const NotificationHistory: React.FC = () => {
    const { notifications, unreadCount } = useNotificationHistory();
    const { markAsRead, markAllAsRead, getNotificationHistory } = useNotificationActions();
    const { isLoading } = useNotificationStore();
    const [refreshing, setRefreshing] = useState(false);

    useEffect(() => {
        loadNotifications();
    }, []);

    const loadNotifications = async () => {
        try {
            await getNotificationHistory(50);
        } catch (error) {
            console.error('Failed to load notifications:', error);
        }
    };

    const handleRefresh = async () => {
        setRefreshing(true);
        try {
            await loadNotifications();
        } finally {
            setRefreshing(false);
        }
    };

    const handleNotificationPress = async (notification: NotificationData) => {
        // Mark as read if unread
        if (!notification.readAt) {
            await markAsRead(notification.id);
        }

        // Handle notification-specific actions
        switch (notification.type) {
            case 'weather_alert':
                // Navigate to weather details
                Alert.alert('Weather Alert', 'Navigate to weather details');
                break;
            case 'task_reminder':
                // Navigate to task details
                Alert.alert('Task Reminder', 'Navigate to task details');
                break;
            case 'crop_stage_update':
                // Navigate to crop plan
                Alert.alert('Crop Update', 'Navigate to crop plan');
                break;
            case 'community_post':
                // Navigate to community post
                Alert.alert('Community Post', 'Navigate to community');
                break;
            case 'ai_insight':
                // Navigate to AI chat or analysis
                Alert.alert('AI Insight', 'Navigate to AI analysis');
                break;
            default:
                // Show notification details
                Alert.alert(notification.title, notification.body);
        }
    };

    const handleMarkAsRead = async (notificationId: string) => {
        try {
            await markAsRead(notificationId);
        } catch (error) {
            Alert.alert('Error', 'Failed to mark notification as read');
        }
    };

    const handleMarkAllAsRead = async () => {
        if (unreadCount === 0) return;

        Alert.alert(
            'Mark All as Read',
            `Mark all ${unreadCount} unread notifications as read?`,
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Mark All',
                    onPress: async () => {
                        try {
                            await markAllAsRead();
                        } catch (error) {
                            Alert.alert('Error', 'Failed to mark all notifications as read');
                        }
                    },
                },
            ]
        );
    };

    const renderNotificationItem = ({ item }: { item: NotificationData }) => (
        <NotificationItem
            notification={item}
            onPress={() => handleNotificationPress(item)}
            onMarkAsRead={() => handleMarkAsRead(item.id)}
        />
    );

    const renderEmptyState = () => (
        <View className="flex-1 items-center justify-center py-12">
            <Text className="text-6xl mb-4">📱</Text>
            <Text className="text-xl font-semibold text-gray-900 mb-2">
                No Notifications
            </Text>
            <Text className="text-base text-gray-600 text-center px-8">
                You'll see your farming alerts, task reminders, and other notifications here
            </Text>
        </View>
    );

    const renderHeader = () => (
        <View className="flex-row items-center justify-between mb-4">
            <View>
                <Text className="text-2xl font-bold text-gray-900">
                    Notifications
                </Text>
                {unreadCount > 0 && (
                    <Text className="text-sm text-gray-600">
                        {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}
                    </Text>
                )}
            </View>
            {unreadCount > 0 && (
                <TouchableOpacity
                    onPress={handleMarkAllAsRead}
                    className="bg-green-600 px-4 py-2 rounded-lg"
                >
                    <Text className="text-white font-medium">
                        Mark All Read
                    </Text>
                </TouchableOpacity>
            )}
        </View>
    );

    return (
        <View className="flex-1 bg-gray-50">
            <FlatList
                data={notifications}
                renderItem={renderNotificationItem}
                keyExtractor={(item) => item.id}
                contentContainerStyle={{ padding: 16 }}
                ListHeaderComponent={renderHeader}
                ListEmptyComponent={renderEmptyState}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        tintColor="#22c55e"
                        colors={['#22c55e']}
                    />
                }
                showsVerticalScrollIndicator={false}
            />
        </View>
    );
};

export default NotificationHistory;