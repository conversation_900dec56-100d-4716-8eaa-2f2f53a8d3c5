import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { WeatherService, WeatherData } from '../../services/weather';
import { LocationService, FarmLocation } from '../../services/location';
import { VoiceService } from '../../services/voice';
import { colors } from '../../design-system';

interface WeatherWidgetProps {
    onLocationPress?: () => void;
    onWeatherPress?: () => void;
}

export const WeatherWidget: React.FC<WeatherWidgetProps> = ({
    onLocationPress,
    onWeatherPress,
}) => {
    const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
    const [location, setLocation] = useState<FarmLocation | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const weatherService = WeatherService.getInstance();
    const locationService = LocationService.getInstance();
    const voiceService = VoiceService.getInstance();

    useEffect(() => {
        loadWeatherData();
    }, []);

    const loadWeatherData = async () => {
        try {
            setLoading(true);
            setError(null);

            // Get current location
            const currentLocation = await locationService.getCurrentLocation();
            if (!currentLocation) {
                throw new Error('Unable to get location');
            }

            setLocation(currentLocation);

            // Get weather data
            const weather = await weatherService.getCompleteWeatherData(currentLocation.coordinates);
            if (!weather) {
                throw new Error('Unable to get weather data');
            }

            setWeatherData(weather);
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to load weather data';
            setError(errorMessage);
            console.error('Weather widget error:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleWeatherPress = async () => {
        if (weatherData) {
            const current = weatherData.current;
            const temp = weatherService.formatTemperature(current.temperature);
            const condition = current.conditions[0]?.description || 'Unknown';

            const weatherText = `Current weather: ${temp}, ${condition}. Humidity ${current.humidity}%. Wind speed ${current.windSpeed} meters per second.`;

            await voiceService.speak(weatherText);
        }

        onWeatherPress?.();
    };

    const handleLocationPress = async () => {
        if (location) {
            const locationText = `Current location: ${locationService.formatLocationForDisplay(location)}`;
            await voiceService.speak(locationText);
        }

        onLocationPress?.();
    };

    const handleRefresh = async () => {
        await voiceService.speak('Refreshing weather data');
        await loadWeatherData();
    };

    if (loading) {
        return (
            <View className="bg-white rounded-xl p-4 mx-4 mb-4 shadow-sm">
                <View className="flex-row items-center justify-between">
                    <View className="flex-1">
                        <View className="h-4 bg-gray-200 rounded mb-2" />
                        <View className="h-6 bg-gray-200 rounded w-20" />
                    </View>
                    <View className="h-12 w-12 bg-gray-200 rounded-full" />
                </View>
            </View>
        );
    }

    if (error) {
        return (
            <View className="bg-red-50 rounded-xl p-4 mx-4 mb-4 border border-red-200">
                <Text className="text-red-800 font-medium mb-2">Weather Unavailable</Text>
                <Text className="text-red-600 text-sm mb-3">{error}</Text>
                <TouchableOpacity
                    onPress={handleRefresh}
                    className="bg-red-100 px-3 py-2 rounded-lg self-start"
                    accessibilityLabel="Retry loading weather"
                    accessibilityRole="button"
                >
                    <Text className="text-red-800 font-medium">Retry</Text>
                </TouchableOpacity>
            </View>
        );
    }

    if (!weatherData || !location) {
        return null;
    }

    const current = weatherData.current;
    const condition = current.conditions[0];
    const weatherIcon = weatherService.getWeatherIcon(condition?.icon || '01d');
    const temperature = weatherService.formatTemperature(current.temperature);

    return (
        <View className="bg-white rounded-xl p-4 mx-4 mb-4 shadow-sm">
            {/* Weather Alerts Banner */}
            {weatherData.alerts.length > 0 && (
                <View className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-3">
                    <Text className="text-yellow-800 font-medium text-sm">
                        ⚠️ Weather Alert: {weatherData.alerts[0].title}
                    </Text>
                </View>
            )}

            {/* Main Weather Display */}
            <TouchableOpacity
                onPress={handleWeatherPress}
                className="flex-row items-center justify-between mb-3"
                accessibilityLabel={`Current weather: ${temperature}, ${condition?.description}`}
                accessibilityRole="button"
            >
                <View className="flex-1">
                    <Text className="text-gray-600 text-sm mb-1">Current Weather</Text>
                    <View className="flex-row items-center">
                        <Text className="text-2xl font-bold text-gray-900 mr-2">
                            {temperature}
                        </Text>
                        <Text className="text-gray-600 capitalize">
                            {condition?.description || 'Unknown'}
                        </Text>
                    </View>
                </View>
                <Text className="text-3xl">{weatherIcon}</Text>
            </TouchableOpacity>

            {/* Weather Details */}
            <View className="flex-row justify-between mb-3">
                <View className="flex-1">
                    <Text className="text-xs text-gray-500">Feels like</Text>
                    <Text className="text-sm font-medium text-gray-700">
                        {weatherService.formatTemperature(current.feelsLike)}
                    </Text>
                </View>
                <View className="flex-1">
                    <Text className="text-xs text-gray-500">Humidity</Text>
                    <Text className="text-sm font-medium text-gray-700">{current.humidity}%</Text>
                </View>
                <View className="flex-1">
                    <Text className="text-xs text-gray-500">Wind</Text>
                    <Text className="text-sm font-medium text-gray-700">{current.windSpeed} m/s</Text>
                </View>
            </View>

            {/* Location Display */}
            <TouchableOpacity
                onPress={handleLocationPress}
                className="flex-row items-center justify-between pt-3 border-t border-gray-100"
                accessibilityLabel={`Location: ${locationService.formatLocationForDisplay(location)}`}
                accessibilityRole="button"
            >
                <View className="flex-row items-center flex-1">
                    <Text className="text-green-600 mr-2">📍</Text>
                    <Text className="text-sm text-gray-600 flex-1" numberOfLines={1}>
                        {locationService.formatLocationForDisplay(location)}
                    </Text>
                </View>
                <TouchableOpacity
                    onPress={handleRefresh}
                    className="ml-2 p-1"
                    accessibilityLabel="Refresh weather data"
                    accessibilityRole="button"
                >
                    <Text className="text-gray-400">🔄</Text>
                </TouchableOpacity>
            </TouchableOpacity>

            {/* 7-Day Forecast Preview */}
            {weatherData.forecast.length > 0 && (
                <View className="pt-3 border-t border-gray-100 mt-3">
                    <Text className="text-xs text-gray-500 mb-2">7-Day Forecast</Text>
                    <View className="flex-row justify-between">
                        {weatherData.forecast.slice(0, 4).map((day, index) => {
                            const dayName = day.date.toLocaleDateString('en', { weekday: 'short' });
                            const icon = weatherService.getWeatherIcon(day.conditions[0]?.icon || '01d');

                            return (
                                <View key={index} className="items-center">
                                    <Text className="text-xs text-gray-500 mb-1">{dayName}</Text>
                                    <Text className="text-sm mb-1">{icon}</Text>
                                    <Text className="text-xs font-medium text-gray-700">
                                        {weatherService.formatTemperature(day.temperature.max)}
                                    </Text>
                                    <Text className="text-xs text-gray-500">
                                        {weatherService.formatTemperature(day.temperature.min)}
                                    </Text>
                                </View>
                            );
                        })}
                    </View>
                </View>
            )}
        </View>
    );
};