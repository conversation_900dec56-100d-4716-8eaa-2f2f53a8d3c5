import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    Alert,
    RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../../src/design-system';
import { VoiceButton } from '../../src/components/ui/VoiceButton';
import { SocialService, Event } from '../../src/services/supabase/social';
import { useVoiceStore } from '../../src/stores/voice';

interface EventCardProps {
    event: Event;
    onRSVP: (eventId: string, status: 'going' | 'maybe' | 'not_going') => void;
}

function EventCard({ event, onRSVP }: EventCardProps) {
    const { speak, isVoiceEnabled } = useVoiceStore();

    const formatDate = (date: Date) => {
        return date.toLocaleDateString([], {
            weekday: 'short',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const handleRSVP = (status: 'going' | 'maybe' | 'not_going') => {
        onRSVP(event.id, status);
        if (isVoiceEnabled) {
            speak(`RSVP updated to ${status}`);
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'going': return colors.status.success;
            case 'maybe': return colors.secondary[500];
            case 'not_going': return colors.status.error;
            default: return colors.earth[500];
        }
    };

    return (
        <View style={{
            backgroundColor: 'white',
            marginHorizontal: 16,
            marginVertical: 8,
            borderRadius: 12,
            padding: 16,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
        }}>
            {/* Event Header */}
            <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                marginBottom: 12,
            }}>
                <View style={{ flex: 1 }}>
                    <Text style={{
                        fontSize: 18,
                        fontWeight: '600',
                        color: colors.primary[900],
                        marginBottom: 4,
                    }}>
                        {event.title}
                    </Text>
                    <Text style={{
                        fontSize: 14,
                        color: colors.earth[600],
                        marginBottom: 8,
                    }}>
                        by {event.organizer.name}
                    </Text>
                </View>

                {event.userRSVP && (
                    <View style={{
                        backgroundColor: getStatusColor(event.userRSVP),
                        paddingHorizontal: 8,
                        paddingVertical: 4,
                        borderRadius: 12,
                    }}>
                        <Text style={{
                            color: 'white',
                            fontSize: 12,
                            fontWeight: '600',
                            textTransform: 'capitalize',
                        }}>
                            {event.userRSVP.replace('_', ' ')}
                        </Text>
                    </View>
                )}
            </View>

            {/* Event Details */}
            <View style={{ marginBottom: 12 }}>
                <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginBottom: 8,
                    gap: 8,
                }}>
                    <Ionicons name="calendar" size={16} color={colors.earth[500]} />
                    <Text style={{
                        fontSize: 14,
                        color: colors.earth[600],
                    }}>
                        {formatDate(event.eventDate)}
                    </Text>
                </View>

                {event.location && (
                    <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginBottom: 8,
                        gap: 8,
                    }}>
                        <Ionicons name="location" size={16} color={colors.earth[500]} />
                        <Text style={{
                            fontSize: 14,
                            color: colors.earth[600],
                        }}>
                            {event.location.name || 'Event Location'}
                        </Text>
                    </View>
                )}

                <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    gap: 8,
                }}>
                    <Ionicons name="people" size={16} color={colors.earth[500]} />
                    <Text style={{
                        fontSize: 14,
                        color: colors.earth[600],
                    }}>
                        {event.attendeesCount} attending
                        {event.maxAttendees && ` • ${event.maxAttendees} max`}
                    </Text>
                </View>
            </View>

            {/* Event Description */}
            <Text style={{
                fontSize: 14,
                color: colors.earth[700],
                lineHeight: 20,
                marginBottom: 16,
            }}>
                {event.description}
            </Text>

            {/* RSVP Buttons */}
            <View style={{
                flexDirection: 'row',
                justifyContent: 'space-around',
                paddingTop: 12,
                borderTopWidth: 1,
                borderTopColor: colors.primary[100],
            }}>
                <TouchableOpacity
                    onPress={() => handleRSVP('going')}
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: 4,
                        padding: 8,
                        borderRadius: 16,
                        backgroundColor: event.userRSVP === 'going'
                            ? colors.status.success
                            : 'transparent',
                    }}
                    accessibilityLabel="RSVP as going"
                >
                    <Ionicons
                        name="checkmark-circle"
                        size={20}
                        color={event.userRSVP === 'going' ? 'white' : colors.status.success}
                    />
                    <Text style={{
                        color: event.userRSVP === 'going' ? 'white' : colors.status.success,
                        fontWeight: '500',
                    }}>
                        Going
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity
                    onPress={() => handleRSVP('maybe')}
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: 4,
                        padding: 8,
                        borderRadius: 16,
                        backgroundColor: event.userRSVP === 'maybe'
                            ? colors.secondary[500]
                            : 'transparent',
                    }}
                    accessibilityLabel="RSVP as maybe"
                >
                    <Ionicons
                        name="help-circle"
                        size={20}
                        color={event.userRSVP === 'maybe' ? 'white' : colors.secondary[500]}
                    />
                    <Text style={{
                        color: event.userRSVP === 'maybe' ? 'white' : colors.secondary[500],
                        fontWeight: '500',
                    }}>
                        Maybe
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity
                    onPress={() => handleRSVP('not_going')}
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        gap: 4,
                        padding: 8,
                        borderRadius: 16,
                        backgroundColor: event.userRSVP === 'not_going'
                            ? colors.status.error
                            : 'transparent',
                    }}
                    accessibilityLabel="RSVP as not going"
                >
                    <Ionicons
                        name="close-circle"
                        size={20}
                        color={event.userRSVP === 'not_going' ? 'white' : colors.status.error}
                    />
                    <Text style={{
                        color: event.userRSVP === 'not_going' ? 'white' : colors.status.error,
                        fontWeight: '500',
                    }}>
                        Can't Go
                    </Text>
                </TouchableOpacity>
            </View>
        </View>
    );
}

export default function CommunityEventsScreen() {
    const [events, setEvents] = useState<Event[]>([]);
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);
    const [filter, setFilter] = useState<'all' | 'upcoming' | 'my_events'>('upcoming');

    const { speak, isVoiceEnabled } = useVoiceStore();

    useEffect(() => {
        loadEvents();
    }, [filter]);

    const loadEvents = async () => {
        try {
            setLoading(true);

            // Mock events data
            const mockEvents: Event[] = [
                {
                    id: '1',
                    title: 'Organic Farming Workshop',
                    description: 'Learn the basics of organic farming techniques and sustainable agriculture practices. Perfect for beginners and intermediate farmers.',
                    eventDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
                    location: {
                        latitude: 30.0444,
                        longitude: 31.2357,
                        name: 'Cairo Agricultural Center',
                    },
                    maxAttendees: 50,
                    isPublic: true,
                    status: 'upcoming',
                    organizer: {
                        id: 'org1',
                        name: 'Dr. Amina Hassan',
                    },
                    attendeesCount: 23,
                    userRSVP: 'going',
                },
                {
                    id: '2',
                    title: 'Seed Exchange Market',
                    description: 'Bring your seeds and exchange them with other farmers. Great opportunity to diversify your crops and meet fellow farmers.',
                    eventDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 2 weeks from now
                    location: {
                        latitude: 30.0626,
                        longitude: 31.2497,
                        name: 'Farmers Market Square',
                    },
                    isPublic: true,
                    status: 'upcoming',
                    organizer: {
                        id: 'org2',
                        name: 'Cairo Farmers Association',
                    },
                    attendeesCount: 67,
                },
                {
                    id: '3',
                    title: 'Irrigation Technology Demo',
                    description: 'See the latest in drip irrigation and smart watering systems. Vendors will be demonstrating their products.',
                    eventDate: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000), // 3 weeks from now
                    location: {
                        latitude: 30.0444,
                        longitude: 31.2357,
                        name: 'Agricultural Equipment Center',
                    },
                    maxAttendees: 100,
                    isPublic: true,
                    status: 'upcoming',
                    organizer: {
                        id: 'org3',
                        name: 'TechFarm Solutions',
                    },
                    attendeesCount: 34,
                    userRSVP: 'maybe',
                },
            ];

            setEvents(mockEvents);
        } catch (error) {
            Alert.alert('Error', 'Failed to load events');
        } finally {
            setLoading(false);
        }
    };

    const handleRefresh = async () => {
        setRefreshing(true);
        await loadEvents();
        setRefreshing(false);
    };

    const handleRSVP = async (eventId: string, status: 'going' | 'maybe' | 'not_going') => {
        try {
            // In a real implementation, this would call SocialService.rsvpToEvent
            await new Promise(resolve => setTimeout(resolve, 500));

            setEvents(prev => prev.map(event =>
                event.id === eventId
                    ? {
                        ...event,
                        userRSVP: status,
                        attendeesCount: status === 'going'
                            ? event.attendeesCount + (event.userRSVP === 'going' ? 0 : 1)
                            : event.userRSVP === 'going'
                                ? event.attendeesCount - 1
                                : event.attendeesCount
                    }
                    : event
            ));
        } catch (error) {
            Alert.alert('Error', 'Failed to update RSVP');
        }
    };

    const handleCreateEvent = () => {
        if (isVoiceEnabled) {
            speak('Opening event creation screen');
        }
        Alert.alert('Coming Soon', 'Event creation will be available soon');
    };

    const handleVoiceCommand = async () => {
        try {
            if (isVoiceEnabled) {
                speak('Say create event to make a new event, or say refresh to update the list');
            }
        } catch (error) {
            Alert.alert('Voice Error', 'Failed to process voice command');
        }
    };

    return (
        <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary[50] }}>
            {/* Header */}
            <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingHorizontal: 16,
                paddingVertical: 12,
                backgroundColor: 'white',
                borderBottomWidth: 1,
                borderBottomColor: colors.primary[100],
            }}>
                <TouchableOpacity
                    onPress={() => router.back()}
                    accessibilityLabel="Go back"
                >
                    <Ionicons name="arrow-back" size={24} color={colors.primary[900]} />
                </TouchableOpacity>

                <Text style={{
                    fontSize: 20,
                    fontWeight: 'bold',
                    color: colors.primary[900],
                }}>
                    Community Events
                </Text>

                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 12 }}>
                    <VoiceButton onPress={handleVoiceCommand} />
                    <TouchableOpacity
                        onPress={handleCreateEvent}
                        accessibilityLabel="Create new event"
                    >
                        <Ionicons name="add-circle" size={24} color={colors.primary[500]} />
                    </TouchableOpacity>
                </View>
            </View>

            {/* Filter Tabs */}
            <View style={{
                flexDirection: 'row',
                backgroundColor: 'white',
                paddingHorizontal: 16,
                paddingVertical: 8,
                borderBottomWidth: 1,
                borderBottomColor: colors.primary[100],
            }}>
                {[
                    { key: 'upcoming', label: 'Upcoming' },
                    { key: 'all', label: 'All Events' },
                    { key: 'my_events', label: 'My Events' },
                ].map((filterOption) => (
                    <TouchableOpacity
                        key={filterOption.key}
                        onPress={() => setFilter(filterOption.key as any)}
                        style={{
                            flex: 1,
                            paddingVertical: 8,
                            alignItems: 'center',
                            borderBottomWidth: 2,
                            borderBottomColor: filter === filterOption.key
                                ? colors.primary[500]
                                : 'transparent',
                        }}
                        accessibilityLabel={`Filter by ${filterOption.label}`}
                    >
                        <Text style={{
                            color: filter === filterOption.key
                                ? colors.primary[500]
                                : colors.earth[500],
                            fontWeight: filter === filterOption.key ? '600' : '400',
                        }}>
                            {filterOption.label}
                        </Text>
                    </TouchableOpacity>
                ))}
            </View>

            {/* Events List */}
            <ScrollView
                style={{ flex: 1 }}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        colors={[colors.primary[500]]}
                    />
                }
                showsVerticalScrollIndicator={false}
            >
                {loading && events.length === 0 ? (
                    <View style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        paddingVertical: 40,
                    }}>
                        <Text style={{ color: colors.earth[500] }}>Loading events...</Text>
                    </View>
                ) : events.length === 0 ? (
                    <View style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        paddingVertical: 40,
                        paddingHorizontal: 20,
                    }}>
                        <Ionicons name="calendar-outline" size={64} color={colors.earth[300]} />
                        <Text style={{
                            fontSize: 18,
                            fontWeight: '600',
                            color: colors.earth[600],
                            marginTop: 16,
                            textAlign: 'center',
                        }}>
                            No events found
                        </Text>
                        <Text style={{
                            fontSize: 14,
                            color: colors.earth[500],
                            marginTop: 8,
                            textAlign: 'center',
                        }}>
                            Be the first to create a community event!
                        </Text>
                        <TouchableOpacity
                            onPress={handleCreateEvent}
                            style={{
                                backgroundColor: colors.primary[500],
                                paddingHorizontal: 24,
                                paddingVertical: 12,
                                borderRadius: 24,
                                marginTop: 20,
                            }}
                        >
                            <Text style={{ color: 'white', fontWeight: '600' }}>
                                Create Event
                            </Text>
                        </TouchableOpacity>
                    </View>
                ) : (
                    <View style={{ paddingBottom: 20 }}>
                        {events.map((event) => (
                            <EventCard
                                key={event.id}
                                event={event}
                                onRSVP={handleRSVP}
                            />
                        ))}
                    </View>
                )}
            </ScrollView>
        </SafeAreaView>
    );
}