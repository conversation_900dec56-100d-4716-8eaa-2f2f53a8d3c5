/**
 * Accessibility Utilities Tests
 * Comprehensive testing for accessibility features
 */

import {
    AccessibilityManager,
    colorContrast,
    screenReaderUtils,
    keyboardNavigation,
    formatForVoice,
    getAgriculturalLabel,
    validateTouchTarget,
    hasGoodContrast,
} from '../accessibility';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
    getItem: jest.fn(),
    setItem: jest.fn(),
}));

// Mock AccessibilityInfo
jest.mock('react-native', () => ({
    AccessibilityInfo: {
        isScreenReaderEnabled: jest.fn(() => Promise.resolve(false)),
        isReduceMotionEnabled: jest.fn(() => Promise.resolve(false)),
        announceForAccessibility: jest.fn(),
        setAccessibilityFocus: jest.fn(),
        addEventListener: jest.fn(),
    },
    Platform: {
        OS: 'ios',
    },
}));

describe('AccessibilityManager', () => {
    let manager: AccessibilityManager;

    beforeEach(() => {
        manager = AccessibilityManager.getInstance();
    });

    test('should initialize with default config', () => {
        const config = manager.getConfig();
        expect(config.voiceEnabled).toBe(false);
        expect(config.highContrast).toBe(false);
        expect(config.largeText).toBe(false);
        expect(config.fontScale).toBe(1.0);
    });

    test('should update configuration', async () => {
        await manager.updateConfig({ voiceEnabled: true, fontScale: 1.2 });
        const config = manager.getConfig();
        expect(config.voiceEnabled).toBe(true);
        expect(config.fontScale).toBe(1.2);
    });

    test('should scale font size correctly', () => {
        manager.updateConfig({ fontScale: 1.5 });
        const scaledSize = manager.getScaledFontSize(16);
        expect(scaledSize).toBe(24);
    });

    test('should scale touch targets correctly', () => {
        manager.updateConfig({ largeText: true });
        const scaledTarget = manager.getScaledTouchTarget(40);
        expect(scaledTarget).toBeGreaterThanOrEqual(44); // Minimum touch target
    });

    test('should provide high contrast colors when enabled', () => {
        manager.updateConfig({ highContrast: true });
        const colors = manager.getHighContrastColors();
        expect(colors).toBeTruthy();
        expect(colors?.background).toBe('#000000');
        expect(colors?.foreground).toBe('#FFFFFF');
    });

    test('should notify listeners on config changes', async () => {
        const listener = jest.fn();
        const unsubscribe = manager.subscribe(listener);

        await manager.updateConfig({ voiceEnabled: true });
        expect(listener).toHaveBeenCalledWith(expect.objectContaining({ voiceEnabled: true }));

        unsubscribe();
    });
});

describe('Color Contrast Utilities', () => {
    test('should calculate luminance correctly', () => {
        const whiteLuminance = colorContrast.getLuminance('#FFFFFF');
        const blackLuminance = colorContrast.getLuminance('#000000');

        expect(whiteLuminance).toBeCloseTo(1, 1);
        expect(blackLuminance).toBeCloseTo(0, 1);
    });

    test('should calculate contrast ratio correctly', () => {
        const ratio = colorContrast.getContrastRatio('#FFFFFF', '#000000');
        expect(ratio).toBeCloseTo(21, 0); // Maximum contrast ratio
    });

    test('should validate WCAG compliance', () => {
        // High contrast should pass AA
        expect(colorContrast.meetsWCAG('#FFFFFF', '#000000', 'AA')).toBe(true);

        // Low contrast should fail AA
        expect(colorContrast.meetsWCAG('#FFFFFF', '#EEEEEE', 'AA')).toBe(false);
    });

    test('should convert hex to RGB correctly', () => {
        const rgb = colorContrast.hexToRgb('#FF0000');
        expect(rgb).toEqual({ r: 255, g: 0, b: 0 });
    });
});

describe('Voice Formatting', () => {
    test('should format text for voice correctly', () => {
        const input = 'Temperature: 75°F & humidity: 60%';
        const expected = 'Temperature: 75°F and humidity: 60percent';
        expect(formatForVoice(input)).toBe(expected);
    });

    test('should handle agricultural terms', () => {
        expect(getAgriculturalLabel('pH')).toBe('pH level');
        expect(getAgriculturalLabel('NPK')).toBe('nitrogen phosphorus potassium');
        expect(getAgriculturalLabel('GPS')).toBe('GPS location');
        expect(getAgriculturalLabel('unknown')).toBe('unknown');
    });
});

describe('Touch Target Validation', () => {
    test('should validate minimum touch target size', () => {
        expect(validateTouchTarget(44)).toBe(true);
        expect(validateTouchTarget(40)).toBe(false);
        expect(validateTouchTarget(56)).toBe(true);
    });
});

describe('Contrast Validation', () => {
    test('should validate good contrast combinations', () => {
        expect(hasGoodContrast('black', 'white')).toBe(true);
        expect(hasGoodContrast('primary-900', 'primary-50')).toBe(true);
        expect(hasGoodContrast('dark', 'light')).toBe(true);
    });

    test('should reject poor contrast combinations', () => {
        expect(hasGoodContrast('light', 'light')).toBe(false);
        expect(hasGoodContrast('dark', 'dark')).toBe(false);
    });
});

describe('Keyboard Navigation', () => {
    test('should register and manage focusable elements', () => {
        keyboardNavigation.registerFocusable('element1');
        keyboardNavigation.registerFocusable('element2');

        expect(keyboardNavigation.focusableElements.has('element1')).toBe(true);
        expect(keyboardNavigation.focusableElements.has('element2')).toBe(true);

        keyboardNavigation.unregisterFocusable('element1');
        expect(keyboardNavigation.focusableElements.has('element1')).toBe(false);
    });

    test('should navigate between elements correctly', () => {
        keyboardNavigation.registerFocusable('element1');
        keyboardNavigation.registerFocusable('element2');
        keyboardNavigation.registerFocusable('element3');

        const elements = Array.from(keyboardNavigation.focusableElements);
        expect(elements.length).toBe(3);

        const next = keyboardNavigation.getNextFocusable('element1');
        expect(next).toBeTruthy();

        const prev = keyboardNavigation.getPreviousFocusable('element2');
        expect(prev).toBeTruthy();
    });

    test('should handle circular navigation', () => {
        keyboardNavigation.registerFocusable('element1');
        keyboardNavigation.registerFocusable('element2');

        const nextFromLast = keyboardNavigation.getNextFocusable('element2');
        expect(nextFromLast).toBe('element1');

        const prevFromFirst = keyboardNavigation.getPreviousFocusable('element1');
        expect(prevFromFirst).toBe('element2');
    });
});

describe('Screen Reader Utils', () => {
    test('should announce messages', () => {
        const mockAnnounce = jest.fn();
        (require('react-native').AccessibilityInfo.announceForAccessibility as jest.Mock) = mockAnnounce;

        screenReaderUtils.announce('Test message');
        expect(mockAnnounce).toHaveBeenCalledWith('Test message');
    });

    test('should check screen reader status', async () => {
        const mockIsEnabled = jest.fn(() => Promise.resolve(true));
        (require('react-native').AccessibilityInfo.isScreenReaderEnabled as jest.Mock) = mockIsEnabled;

        const isEnabled = await screenReaderUtils.isScreenReaderEnabled();
        expect(isEnabled).toBe(true);
        expect(mockIsEnabled).toHaveBeenCalled();
    });
});

describe('Agricultural Voice Commands', () => {
    test('should include navigation commands', () => {
        const { voiceCommands } = require('../accessibility');

        expect(voiceCommands.navigation).toContain('go to home');
        expect(voiceCommands.navigation).toContain('open crops');
        expect(voiceCommands.navigation).toContain('show weather');
    });

    test('should include action commands', () => {
        const { voiceCommands } = require('../accessibility');

        expect(voiceCommands.actions).toContain('take photo');
        expect(voiceCommands.actions).toContain('analyze plant');
        expect(voiceCommands.actions).toContain('add task');
    });

    test('should include query commands', () => {
        const { voiceCommands } = require('../accessibility');

        expect(voiceCommands.queries).toContain('what is the weather');
        expect(voiceCommands.queries).toContain('how are my crops');
        expect(voiceCommands.queries).toContain('when to water');
    });

    test('should include accessibility commands', () => {
        const { voiceCommands } = require('../accessibility');

        expect(voiceCommands.accessibility).toContain('enable voice mode');
        expect(voiceCommands.accessibility).toContain('enable high contrast');
        expect(voiceCommands.accessibility).toContain('increase text size');
    });
});