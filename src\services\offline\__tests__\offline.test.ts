import { offlineService } from '../index';
import { offlineDatabase } from '../database';
import { connectivityService } from '../connectivity';
import { syncService } from '../syncService';
import { backgroundSyncService } from '../backgroundSync';
import { OfflineAction } from '../../../types/offline';

// Mock dependencies
jest.mock('@react-native-community/netinfo');
jest.mock('expo-sqlite');
jest.mock('@react-native-async-storage/async-storage');

describe('Offline Service', () => {
    beforeEach(async () => {
        // Reset all mocks
        jest.clearAllMocks();

        // Mock connectivity as online
        (connectivityService as any).isOnline = jest.fn(() => true);
        (connectivityService as any).getCurrentState = jest.fn(() => ({
            isConnected: true,
            isInternetReachable: true,
            type: 'wifi'
        }));
    });

    afterEach(async () => {
        // Clean up
        await offlineService.destroy();
    });

    describe('Initialization', () => {
        it('should initialize all sub-services', async () => {
            const initializeSpy = jest.spyOn(offlineDatabase, 'initialize');

            await offlineService.initialize();

            expect(initializeSpy).toHaveBeenCalled();
        });

        it('should not initialize twice', async () => {
            await offlineService.initialize();
            const initializeSpy = jest.spyOn(offlineDatabase, 'initialize');

            await offlineService.initialize();

            expect(initializeSpy).not.toHaveBeenCalled();
        });
    });

    describe('Data Operations', () => {
        beforeEach(async () => {
            await offlineService.initialize();
        });

        it('should create data and add to sync queue', async () => {
            const testData = { name: 'Test Crop', type: 'tomato' };
            const addToSyncQueueSpy = jest.spyOn(offlineDatabase, 'addToSyncQueue');
            const cacheDataSpy = jest.spyOn(offlineDatabase, 'cacheData');

            const result = await offlineService.createData('crop_plans', testData, 'user123');

            expect(result).toHaveProperty('id');
            expect(result.name).toBe('Test Crop');
            expect(cacheDataSpy).toHaveBeenCalled();
            expect(addToSyncQueueSpy).toHaveBeenCalled();
        });

        it('should update data and add to sync queue', async () => {
            const testData = { name: 'Updated Crop', type: 'tomato' };
            const addToSyncQueueSpy = jest.spyOn(offlineDatabase, 'addToSyncQueue');
            const cacheDataSpy = jest.spyOn(offlineDatabase, 'cacheData');

            const result = await offlineService.updateData('crop_plans', 'test-id', testData, 'user123');

            expect(result.name).toBe('Updated Crop');
            expect(result.id).toBe('test-id');
            expect(cacheDataSpy).toHaveBeenCalled();
            expect(addToSyncQueueSpy).toHaveBeenCalled();
        });

        it('should delete data and add to sync queue', async () => {
            const addToSyncQueueSpy = jest.spyOn(offlineDatabase, 'addToSyncQueue');
            const removeCachedDataSpy = jest.spyOn(offlineDatabase, 'removeCachedData');

            await offlineService.deleteData('crop_plans', 'test-id', 'user123');

            expect(removeCachedDataSpy).toHaveBeenCalledWith('offline_crop_plans', 'test-id');
            expect(addToSyncQueueSpy).toHaveBeenCalled();
        });

        it('should get cached data', async () => {
            const mockData = [{ id: 'test-id', name: 'Test Crop' }];
            jest.spyOn(offlineDatabase, 'getCachedData').mockResolvedValue(mockData);

            const result = await offlineService.getData('crop_plans', undefined, 'user123');

            expect(result).toEqual(mockData);
        });
    });

    describe('Sync Queue Management', () => {
        beforeEach(async () => {
            await offlineService.initialize();
        });

        it('should add actions to sync queue', async () => {
            const action: OfflineAction = {
                id: 'test-action',
                type: 'CREATE',
                table: 'crop_plans',
                data: { name: 'Test' },
                timestamp: Date.now(),
                userId: 'user123',
                retryCount: 0,
                maxRetries: 3,
                status: 'pending'
            };

            const addToSyncQueueSpy = jest.spyOn(offlineDatabase, 'addToSyncQueue');

            await offlineService.addToSyncQueue(action);

            expect(addToSyncQueueSpy).toHaveBeenCalledWith(action);
        });

        it('should process sync queue when online', async () => {
            const performIncrementalSyncSpy = jest.spyOn(syncService, 'performIncrementalSync')
                .mockResolvedValue({
                    success: true,
                    synced: 1,
                    failed: 0,
                    conflicts: 0,
                    errors: [],
                    duration: 1000
                });

            await offlineService.processSyncQueue();

            expect(performIncrementalSyncSpy).toHaveBeenCalled();
        });

        it('should not process sync queue when offline', async () => {
            (connectivityService as any).isOnline = jest.fn(() => false);
            const performIncrementalSyncSpy = jest.spyOn(syncService, 'performIncrementalSync');

            await offlineService.processSyncQueue();

            expect(performIncrementalSyncSpy).not.toHaveBeenCalled();
        });
    });

    describe('Sync Status', () => {
        beforeEach(async () => {
            await offlineService.initialize();
        });

        it('should return correct sync status', () => {
            jest.spyOn(syncService, 'getLastSyncTime').mockReturnValue(Date.now());
            jest.spyOn(syncService, 'isSyncInProgress').mockReturnValue(false);
            jest.spyOn(backgroundSyncService, 'getStatus').mockReturnValue({
                isEnabled: true,
                nextSyncTime: Date.now() + 60000,
                lastSyncAttempt: null,
                lastSuccessfulSync: null,
                consecutiveFailures: 0,
                isInQuietHours: false
            });

            const status = offlineService.getSyncStatus();

            expect(status.isOnline).toBe(true);
            expect(status.syncInProgress).toBe(false);
            expect(status.lastSyncTime).toBeDefined();
            expect(status.nextSyncAttempt).toBeDefined();
        });
    });

    describe('Storage Management', () => {
        beforeEach(async () => {
            await offlineService.initialize();
        });

        it('should clear offline data', async () => {
            const clearCacheSpy = jest.spyOn(offlineDatabase, 'clearCache');
            const clearSyncQueueSpy = jest.spyOn(offlineDatabase, 'clearSyncQueue');

            await offlineService.clearOfflineData();

            expect(clearCacheSpy).toHaveBeenCalled();
            expect(clearSyncQueueSpy).toHaveBeenCalled();
        });

        it('should get storage info', async () => {
            jest.spyOn(offlineDatabase, 'getStorageSize').mockResolvedValue(1024);

            const info = await offlineService.getStorageInfo();

            expect(info).toHaveProperty('databaseSize');
            expect(info).toHaveProperty('cacheStats');
            expect(info).toHaveProperty('syncQueueStats');
        });

        it('should perform maintenance', async () => {
            const vacuumSpy = jest.spyOn(offlineDatabase, 'vacuum');

            await offlineService.performMaintenance();

            expect(vacuumSpy).toHaveBeenCalled();
        });
    });
});

describe('Sync Service', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        (connectivityService as any).isOnline = jest.fn(() => true);
    });

    describe('Sync Operations', () => {
        it('should perform full sync', async () => {
            jest.spyOn(offlineDatabase, 'getSyncQueue').mockResolvedValue([]);

            const result = await syncService.performFullSync();

            expect(result.success).toBe(true);
            expect(result.synced).toBe(0);
        });

        it('should perform incremental sync', async () => {
            jest.spyOn(offlineDatabase, 'getSyncQueue').mockResolvedValue([]);

            const result = await syncService.performIncrementalSync();

            expect(result.success).toBe(true);
            expect(result.synced).toBe(0);
        });

        it('should not sync when offline', async () => {
            (connectivityService as any).isOnline = jest.fn(() => false);

            await expect(syncService.performFullSync()).rejects.toThrow('Cannot sync while offline');
        });

        it('should not sync when already in progress', async () => {
            // Start a sync
            const syncPromise = syncService.performFullSync();

            // Try to start another sync
            await expect(syncService.performFullSync()).rejects.toThrow('Sync already in progress');

            // Wait for first sync to complete
            await syncPromise;
        });
    });

    describe('Progress Tracking', () => {
        it('should notify progress listeners', async () => {
            const progressListener = jest.fn();
            const unsubscribe = syncService.addProgressListener(progressListener);

            // Mock some sync items
            jest.spyOn(offlineDatabase, 'getSyncQueue').mockResolvedValue([
                {
                    id: 'test-1',
                    type: 'CREATE',
                    table: 'crop_plans',
                    data: { name: 'Test' },
                    timestamp: Date.now(),
                    retryCount: 0,
                    maxRetries: 3,
                    status: 'pending'
                }
            ]);

            await syncService.performIncrementalSync();

            expect(progressListener).toHaveBeenCalled();
            unsubscribe();
        });
    });
});

describe('Background Sync Service', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        (connectivityService as any).isOnline = jest.fn(() => true);
    });

    describe('Background Sync Management', () => {
        it('should start background sync', () => {
            backgroundSyncService.start();

            const status = backgroundSyncService.getStatus();
            expect(status.isEnabled).toBe(true);
        });

        it('should stop background sync', () => {
            backgroundSyncService.start();
            backgroundSyncService.stop();

            const status = backgroundSyncService.getStatus();
            expect(status.isEnabled).toBe(false);
        });

        it('should trigger manual sync', async () => {
            const performIncrementalSyncSpy = jest.spyOn(syncService, 'performIncrementalSync')
                .mockResolvedValue({
                    success: true,
                    synced: 1,
                    failed: 0,
                    conflicts: 0,
                    errors: [],
                    duration: 1000
                });

            const result = await backgroundSyncService.triggerSync();

            expect(performIncrementalSyncSpy).toHaveBeenCalled();
            expect(result.success).toBe(true);
        });

        it('should respect quiet hours', () => {
            backgroundSyncService.updateConfig({
                quietHours: {
                    enabled: true,
                    startHour: new Date().getHours(), // Current hour
                    endHour: (new Date().getHours() + 1) % 24
                }
            });

            const status = backgroundSyncService.getStatus();
            expect(status.isInQuietHours).toBe(true);
        });
    });
});