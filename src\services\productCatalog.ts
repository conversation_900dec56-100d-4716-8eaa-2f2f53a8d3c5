/**
 * Product Catalog Service
 * Handles product management, search, filtering, and AI-based recommendations
 */

import { supabase } from './supabase/client';
import {
    Product,
    ProductCategory,
    ProductSearchParams,
    ProductFilter,
    ProductRecommendation,
    ProductSpecification,
} from '../types/product';
import { Database } from '../types/database';

type ProductRow = Database['public']['Tables']['products']['Row'];
type ProductInsert = Database['public']['Tables']['products']['Insert'];
type ProductUpdate = Database['public']['Tables']['products']['Update'];
type ProductReviewRow = Database['public']['Tables']['product_reviews']['Row'];
type ProductRecommendationRow = Database['public']['Tables']['product_recommendations']['Row'];

export class ProductCatalogService {
    /**
     * Search products with advanced filtering and sorting
     */
    async searchProducts(params: ProductSearchParams): Promise<{
        products: Product[];
        totalCount: number;
        hasMore: boolean;
    }> {
        try {
            let query = supabase
                .from('products')
                .select('*', { count: 'exact' })
                .eq('is_active', true);

            // Apply text search
            if (params.query) {
                query = query.textSearch('search_vector', params.query, {
                    type: 'websearch',
                    config: 'english'
                });
            }

            // Apply category filter
            if (params.category) {
                query = query.eq('category', params.category);
            }

            // Apply filters
            if (params.filters) {
                const { priceRange, rating, inStock, brand, tags } = params.filters;

                if (priceRange) {
                    query = query.gte('price', priceRange.min).lte('price', priceRange.max);
                }

                if (rating) {
                    query = query.gte('rating', rating);
                }

                if (inStock) {
                    query = query.gt('stock_quantity', 0);
                }

                if (brand) {
                    query = query.eq('brand', brand);
                }

                if (tags && tags.length > 0) {
                    query = query.overlaps('tags', tags);
                }
            }

            // Apply sorting
            if (params.sortBy) {
                const ascending = params.sortOrder === 'asc';
                switch (params.sortBy) {
                    case 'name':
                        query = query.order('name', { ascending });
                        break;
                    case 'price':
                        query = query.order('price', { ascending });
                        break;
                    case 'rating':
                        query = query.order('rating', { ascending });
                        break;
                    case 'newest':
                        query = query.order('created_at', { ascending });
                        break;
                    default:
                        query = query.order('created_at', { ascending: false });
                }
            } else {
                // Default sorting: featured first, then by rating
                query = query.order('is_featured', { ascending: false })
                    .order('rating', { ascending: false });
            }

            // Apply pagination
            const page = params.page || 1;
            const limit = params.limit || 20;
            const from = (page - 1) * limit;
            const to = from + limit - 1;

            query = query.range(from, to);

            const { data, error, count } = await query;

            if (error) {
                throw new Error(`Failed to search products: ${error.message}`);
            }

            const products = data?.map(this.mapRowToProduct) || [];
            const totalCount = count || 0;
            const hasMore = totalCount > page * limit;

            return {
                products,
                totalCount,
                hasMore,
            };
        } catch (error) {
            console.error('Error searching products:', error);
            throw error;
        }
    }

    /**
     * Get product by ID with full details
     */
    async getProductById(id: string): Promise<Product | null> {
        try {
            const { data, error } = await supabase
                .from('products')
                .select('*')
                .eq('id', id)
                .eq('is_active', true)
                .single();

            if (error) {
                if (error.code === 'PGRST116') {
                    return null; // Product not found
                }
                throw new Error(`Failed to get product: ${error.message}`);
            }

            return this.mapRowToProduct(data);
        } catch (error) {
            console.error('Error getting product by ID:', error);
            throw error;
        }
    }

    /**
     * Get featured products
     */
    async getFeaturedProducts(limit: number = 10): Promise<Product[]> {
        try {
            const { data, error } = await supabase
                .from('products')
                .select('*')
                .eq('is_featured', true)
                .eq('is_active', true)
                .order('rating', { ascending: false })
                .limit(limit);

            if (error) {
                throw new Error(`Failed to get featured products: ${error.message}`);
            }

            return data?.map(this.mapRowToProduct) || [];
        } catch (error) {
            console.error('Error getting featured products:', error);
            throw error;
        }
    }

    /**
     * Get recommended products
     */
    async getRecommendedProducts(limit: number = 10): Promise<Product[]> {
        try {
            const { data, error } = await supabase
                .from('products')
                .select('*')
                .eq('is_recommended', true)
                .eq('is_active', true)
                .order('rating', { ascending: false })
                .limit(limit);

            if (error) {
                throw new Error(`Failed to get recommended products: ${error.message}`);
            }

            return data?.map(this.mapRowToProduct) || [];
        } catch (error) {
            console.error('Error getting recommended products:', error);
            throw error;
        }
    }

    /**
     * Get products by category
     */
    async getProductsByCategory(category: ProductCategory, limit: number = 20): Promise<Product[]> {
        try {
            const { data, error } = await supabase
                .from('products')
                .select('*')
                .eq('category', category)
                .eq('is_active', true)
                .order('rating', { ascending: false })
                .limit(limit);

            if (error) {
                throw new Error(`Failed to get products by category: ${error.message}`);
            }

            return data?.map(this.mapRowToProduct) || [];
        } catch (error) {
            console.error('Error getting products by category:', error);
            throw error;
        }
    }

    /**
     * Get AI-based product recommendations for a user
     */
    async getAIRecommendations(
        userId: string,
        basedOnProductId?: string,
        limit: number = 10
    ): Promise<ProductRecommendation[]> {
        try {
            let query = supabase
                .from('product_recommendations')
                .select(`
                    *,
                    recommended_product:recommended_product_id (*)
                `)
                .eq('user_id', userId)
                .gt('expires_at', new Date().toISOString())
                .order('confidence', { ascending: false })
                .limit(limit);

            if (basedOnProductId) {
                query = query.eq('product_id', basedOnProductId);
            }

            const { data, error } = await query;

            if (error) {
                throw new Error(`Failed to get AI recommendations: ${error.message}`);
            }

            return data?.map(row => ({
                product: this.mapRowToProduct(row.recommended_product as ProductRow),
                reason: row.reason,
                confidence: row.confidence,
                relatedTo: row.product_id,
            })) || [];
        } catch (error) {
            console.error('Error getting AI recommendations:', error);
            // Return empty array on error to not break the UI
            return [];
        }
    }

    /**
     * Generate AI recommendations based on user behavior and product analysis
     */
    async generateAIRecommendations(
        userId: string,
        analysisResults?: any,
        userPreferences?: any
    ): Promise<void> {
        try {
            // Get user's purchase history and preferences
            const { data: userOrders } = await supabase
                .from('orders')
                .select(`
                    order_items (
                        product_id,
                        product:product_id (*)
                    )
                `)
                .eq('user_id', userId)
                .eq('status', 'delivered');

            // Get user's crop types from profile
            const { data: userProfile } = await supabase
                .from('user_profiles')
                .select('crop_types, experience_level')
                .eq('id', userId)
                .single();

            const recommendations: Array<{
                user_id: string;
                product_id: string;
                recommended_product_id: string;
                reason: string;
                confidence: number;
                source: 'ai' | 'collaborative' | 'content' | 'trending';
                metadata: any;
            }> = [];

            // 1. Content-based recommendations based on crop types
            if (userProfile?.crop_types) {
                const cropBasedProducts = await this.getProductsForCrops(userProfile.crop_types);
                cropBasedProducts.forEach(product => {
                    recommendations.push({
                        user_id: userId,
                        product_id: 'general',
                        recommended_product_id: product.id,
                        reason: `Perfect for your ${userProfile.crop_types.join(', ')} crops`,
                        confidence: 0.8,
                        source: 'content',
                        metadata: { crop_types: userProfile.crop_types },
                    });
                });
            }

            // 2. AI analysis-based recommendations
            if (analysisResults) {
                const analysisBasedProducts = await this.getProductsForAnalysis(analysisResults);
                analysisBasedProducts.forEach(product => {
                    recommendations.push({
                        user_id: userId,
                        product_id: analysisResults.productId || 'analysis',
                        recommended_product_id: product.id,
                        reason: `Recommended based on your plant analysis: ${analysisResults.issue}`,
                        confidence: analysisResults.confidence || 0.9,
                        source: 'ai',
                        metadata: { analysis: analysisResults },
                    });
                });
            }

            // 3. Collaborative filtering based on similar users
            const collaborativeProducts = await this.getCollaborativeRecommendations(userId);
            collaborativeProducts.forEach(product => {
                recommendations.push({
                    user_id: userId,
                    product_id: 'collaborative',
                    recommended_product_id: product.id,
                    reason: 'Farmers with similar profiles also bought this',
                    confidence: 0.7,
                    source: 'collaborative',
                    metadata: {},
                });
            });

            // 4. Trending products in user's region/category
            const trendingProducts = await this.getTrendingProducts();
            trendingProducts.forEach(product => {
                recommendations.push({
                    user_id: userId,
                    product_id: 'trending',
                    recommended_product_id: product.id,
                    reason: 'Popular among farmers this season',
                    confidence: 0.6,
                    source: 'trending',
                    metadata: {},
                });
            });

            // Remove duplicates and limit to top recommendations
            const uniqueRecommendations = recommendations
                .filter((rec, index, self) =>
                    index === self.findIndex(r => r.recommended_product_id === rec.recommended_product_id)
                )
                .sort((a, b) => b.confidence - a.confidence)
                .slice(0, 20);

            // Clear old recommendations
            await supabase
                .from('product_recommendations')
                .delete()
                .eq('user_id', userId);

            // Insert new recommendations
            if (uniqueRecommendations.length > 0) {
                const { error } = await supabase
                    .from('product_recommendations')
                    .insert(uniqueRecommendations);

                if (error) {
                    throw new Error(`Failed to save recommendations: ${error.message}`);
                }
            }
        } catch (error) {
            console.error('Error generating AI recommendations:', error);
            // Don't throw error to not break the main flow
        }
    }

    /**
     * Get product reviews
     */
    async getProductReviews(productId: string, limit: number = 10): Promise<ProductReviewRow[]> {
        try {
            const { data, error } = await supabase
                .from('product_reviews')
                .select(`
                    *,
                    user:user_id (first_name, last_name)
                `)
                .eq('product_id', productId)
                .order('created_at', { ascending: false })
                .limit(limit);

            if (error) {
                throw new Error(`Failed to get product reviews: ${error.message}`);
            }

            return data || [];
        } catch (error) {
            console.error('Error getting product reviews:', error);
            return [];
        }
    }

    /**
     * Add product review
     */
    async addProductReview(
        productId: string,
        userId: string,
        rating: number,
        title?: string,
        comment?: string
    ): Promise<void> {
        try {
            // Check if user has purchased this product
            const { data: orderItems } = await supabase
                .from('order_items')
                .select('id, order:order_id!inner(user_id, status)')
                .eq('product_id', productId)
                .eq('order.user_id', userId)
                .eq('order.status', 'delivered');

            const verifiedPurchase = orderItems && orderItems.length > 0;

            const { error } = await supabase
                .from('product_reviews')
                .insert({
                    product_id: productId,
                    user_id: userId,
                    rating,
                    title,
                    comment,
                    verified_purchase: verifiedPurchase,
                });

            if (error) {
                throw new Error(`Failed to add review: ${error.message}`);
            }
        } catch (error) {
            console.error('Error adding product review:', error);
            throw error;
        }
    }

    /**
     * Get product categories
     */
    async getCategories(): Promise<Array<{
        id: ProductCategory;
        name: string;
        description: string | null;
        icon: string | null;
        productCount: number;
    }>> {
        try {
            const { data, error } = await supabase
                .from('product_categories')
                .select(`
                    *,
                    products!inner(id)
                `)
                .eq('is_active', true)
                .order('sort_order');

            if (error) {
                throw new Error(`Failed to get categories: ${error.message}`);
            }

            return data?.map(category => ({
                id: category.id as ProductCategory,
                name: category.name,
                description: category.description,
                icon: category.icon,
                productCount: category.products?.length || 0,
            })) || [];
        } catch (error) {
            console.error('Error getting categories:', error);
            return [];
        }
    }

    /**
     * Get inventory status for products
     */
    async getInventoryStatus(productIds: string[]): Promise<Record<string, {
        stockQuantity: number;
        isInStock: boolean;
        lowStock: boolean;
    }>> {
        try {
            const { data, error } = await supabase
                .from('products')
                .select('id, stock_quantity')
                .in('id', productIds);

            if (error) {
                throw new Error(`Failed to get inventory status: ${error.message}`);
            }

            const inventory: Record<string, any> = {};
            data?.forEach(product => {
                inventory[product.id] = {
                    stockQuantity: product.stock_quantity,
                    isInStock: product.stock_quantity > 0,
                    lowStock: product.stock_quantity <= 10 && product.stock_quantity > 0,
                };
            });

            return inventory;
        } catch (error) {
            console.error('Error getting inventory status:', error);
            return {};
        }
    }

    // Private helper methods

    private mapRowToProduct(row: ProductRow): Product {
        return {
            id: row.id,
            name: row.name,
            description: row.description || '',
            category: row.category as ProductCategory,
            price: row.price,
            originalPrice: row.original_price || undefined,
            currency: row.currency,
            imageUrls: row.image_urls,
            specifications: (row.specifications as ProductSpecification[]) || [],
            stockQuantity: row.stock_quantity,
            rating: row.rating,
            reviewCount: row.review_count,
            tags: row.tags,
            brand: row.brand || undefined,
            isRecommended: row.is_recommended,
            isFeatured: row.is_featured,
            createdAt: new Date(row.created_at),
            updatedAt: new Date(row.updated_at),
        };
    }

    private async getProductsForCrops(cropTypes: string[]): Promise<Product[]> {
        // Get products that are tagged with the user's crop types
        const { data } = await supabase
            .from('products')
            .select('*')
            .overlaps('tags', cropTypes)
            .eq('is_active', true)
            .limit(5);

        return data?.map(this.mapRowToProduct) || [];
    }

    private async getProductsForAnalysis(analysisResults: any): Promise<Product[]> {
        // Based on AI analysis results, recommend relevant products
        const searchTags = [];

        if (analysisResults.issue?.includes('disease')) {
            searchTags.push('fungicide', 'disease-control');
        }
        if (analysisResults.issue?.includes('pest')) {
            searchTags.push('pesticide', 'pest-control');
        }
        if (analysisResults.issue?.includes('nutrient')) {
            searchTags.push('fertilizer', 'nutrients');
        }

        if (searchTags.length === 0) return [];

        const { data } = await supabase
            .from('products')
            .select('*')
            .overlaps('tags', searchTags)
            .eq('is_active', true)
            .limit(3);

        return data?.map(this.mapRowToProduct) || [];
    }

    private async getCollaborativeRecommendations(userId: string): Promise<Product[]> {
        // Find users with similar purchase patterns and recommend their products
        // This is a simplified version - in production, you'd use more sophisticated ML
        const { data } = await supabase
            .from('products')
            .select('*')
            .eq('is_recommended', true)
            .eq('is_active', true)
            .limit(3);

        return data?.map(this.mapRowToProduct) || [];
    }

    private async getTrendingProducts(): Promise<Product[]> {
        // Get products with high recent sales or views
        const { data } = await supabase
            .from('products')
            .select('*')
            .eq('is_active', true)
            .order('rating', { ascending: false })
            .limit(3);

        return data?.map(this.mapRowToProduct) || [];
    }
}

export const productCatalogService = new ProductCatalogService();