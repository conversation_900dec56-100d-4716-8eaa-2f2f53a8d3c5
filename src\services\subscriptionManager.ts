import { SubscriptionService } from './supabase/subscription';
import { PointsService } from './supabase/points';
import { PaymentService } from './payments';
import {
    SubscriptionPlan,
    UserSubscription,
    FeatureAccess,
    getFeatureAccess,
    canAccessFeature,
    getAIConsultationLimit,
    getMonthlyPointsAllocation
} from '../types/subscription';

export interface SubscriptionChangeResult {
    success: boolean;
    subscription?: UserSubscription;
    error?: string;
}

export interface FeatureCheckResult {
    hasAccess: boolean;
    reason?: string;
    upgradeRequired?: boolean;
    recommendedPlan?: SubscriptionPlan;
}

export interface UsageLimitResult {
    canUse: boolean;
    used: number;
    limit: number;
    remaining: number;
    resetDate?: string;
}

/**
 * Comprehensive subscription management service
 * Handles subscription lifecycle, feature access, and usage tracking
 */
export class SubscriptionManager {
    /**
     * Subscribe user to a plan
     */
    static async subscribe(
        userId: string,
        planId: string,
        paymentMethodId?: string
    ): Promise<SubscriptionChangeResult> {
        try {
            // Get the plan details
            const { plans } = await SubscriptionService.getSubscriptionPlans();
            const plan = plans.find(p => p.id === planId);

            if (!plan) {
                return { success: false, error: 'Plan not found' };
            }

            // For paid plans, process payment first
            if (plan.price > 0 && paymentMethodId) {
                const paymentResult = await PaymentService.createSubscription(
                    userId,
                    planId,
                    paymentMethodId
                );

                if (paymentResult.error) {
                    return { success: false, error: paymentResult.error };
                }
            }

            // Create subscription in database
            const { subscription, error } = await SubscriptionService.createSubscription(
                userId,
                planId,
                paymentMethodId
            );

            if (error) {
                return { success: false, error: error.message };
            }

            // Award subscription bonus points
            if (plan.points_included > 0) {
                await PointsService.awardPoints(
                    userId,
                    plan.points_included,
                    'subscription_bonus',
                    `Welcome bonus for ${plan.name} subscription`
                );
            }

            return { success: true, subscription };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Subscription failed'
            };
        }
    }

    /**
     * Cancel user subscription
     */
    static async cancelSubscription(
        userId: string,
        immediate: boolean = false
    ): Promise<SubscriptionChangeResult> {
        try {
            // Get current subscription
            const { subscription: currentSub } = await SubscriptionService.getUserSubscription(userId);

            if (!currentSub) {
                return { success: false, error: 'No active subscription found' };
            }

            // Cancel with payment provider if needed
            if (currentSub.payment_method_id) {
                const cancelResult = await PaymentService.cancelSubscription(currentSub.id);
                if (cancelResult.error) {
                    console.warn('Payment provider cancellation failed:', cancelResult.error);
                    // Continue with database cancellation even if payment provider fails
                }
            }

            // Cancel in database
            const { error } = await SubscriptionService.cancelSubscription(userId, !immediate);

            if (error) {
                return { success: false, error: error.message };
            }

            return { success: true };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Cancellation failed'
            };
        }
    }

    /**
     * Change subscription plan
     */
    static async changePlan(
        userId: string,
        newPlanId: string,
        paymentMethodId?: string
    ): Promise<SubscriptionChangeResult> {
        try {
            // Get current subscription and new plan
            const { subscription: currentSub } = await SubscriptionService.getUserSubscription(userId);
            const { plans } = await SubscriptionService.getSubscriptionPlans();
            const newPlan = plans.find(p => p.id === newPlanId);

            if (!newPlan) {
                return { success: false, error: 'New plan not found' };
            }

            // Handle payment for paid plans
            if (newPlan.price > 0 && paymentMethodId) {
                const paymentResult = await PaymentService.updateSubscription(
                    currentSub?.id || '',
                    newPlanId
                );

                if (paymentResult.error) {
                    return { success: false, error: paymentResult.error };
                }
            }

            // Update subscription in database
            const { subscription, error } = await SubscriptionService.changeSubscription(
                userId,
                newPlanId,
                paymentMethodId
            );

            if (error) {
                return { success: false, error: error.message };
            }

            // Award points for plan upgrade
            if (newPlan.points_included > 0) {
                await PointsService.awardPoints(
                    userId,
                    newPlan.points_included,
                    'subscription_bonus',
                    `Plan change bonus for ${newPlan.name} subscription`
                );
            }

            return { success: true, subscription };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Plan change failed'
            };
        }
    }

    /**
     * Check if user can access a specific feature
     */
    static async checkFeatureAccess(
        userId: string,
        feature: keyof FeatureAccess
    ): Promise<FeatureCheckResult> {
        try {
            const { hasAccess } = await SubscriptionService.checkFeatureAccess(userId, feature);

            if (hasAccess) {
                return { hasAccess: true };
            }

            // Get recommended plan for this feature
            const { plans } = await SubscriptionService.getSubscriptionPlans();
            const recommendedPlan = plans.find(plan => {
                const access = getFeatureAccess(plan.name.toLowerCase());
                return access[feature];
            });

            return {
                hasAccess: false,
                reason: `${feature} requires a subscription upgrade`,
                upgradeRequired: true,
                recommendedPlan,
            };
        } catch (error) {
            return {
                hasAccess: false,
                reason: error instanceof Error ? error.message : 'Feature check failed',
            };
        }
    }

    /**
     * Check AI consultation usage limits
     */
    static async checkAIConsultationLimit(userId: string): Promise<UsageLimitResult> {
        try {
            const { canUse, used, limit } = await SubscriptionService.checkAIConsultationLimit(userId);

            const remaining = limit === -1 ? -1 : Math.max(0, limit - used);

            // Calculate reset date (start of next month)
            const now = new Date();
            const resetDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);

            return {
                canUse,
                used,
                limit,
                remaining,
                resetDate: resetDate.toISOString(),
            };
        } catch (error) {
            return {
                canUse: false,
                used: 0,
                limit: 0,
                remaining: 0,
            };
        }
    }

    /**
     * Record AI consultation usage
     */
    static async recordAIConsultation(userId: string): Promise<{ success: boolean; error?: string }> {
        try {
            // Check if user can use AI consultation
            const limitCheck = await this.checkAIConsultationLimit(userId);

            if (!limitCheck.canUse) {
                return {
                    success: false,
                    error: 'AI consultation limit exceeded. Please upgrade your plan or wait for reset.'
                };
            }

            // Record usage
            await SubscriptionService.updateUsage(userId, 'ai_consultations', 1);

            return { success: true };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to record usage'
            };
        }
    }

    /**
     * Record image analysis usage
     */
    static async recordImageAnalysis(userId: string): Promise<{ success: boolean; error?: string }> {
        try {
            // Check feature access first
            const featureCheck = await this.checkFeatureAccess(userId, 'image_analysis');

            if (!featureCheck.hasAccess) {
                return {
                    success: false,
                    error: featureCheck.reason || 'Image analysis not available in your plan'
                };
            }

            // Record usage
            await SubscriptionService.updateUsage(userId, 'image_analyses', 1);

            return { success: true };
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to record usage'
            };
        }
    }

    /**
     * Get user's subscription summary
     */
    static async getSubscriptionSummary(userId: string): Promise<{
        subscription: UserSubscription | null;
        plan: SubscriptionPlan | null;
        usage: any;
        featureAccess: FeatureAccess;
        daysRemaining: number;
        isExpiringSoon: boolean;
        error?: string;
    }> {
        try {
            const { subscription } = await SubscriptionService.getUserSubscription(userId);
            const { plans } = await SubscriptionService.getSubscriptionPlans();
            const { usage } = await SubscriptionService.getSubscriptionUsage(userId);

            let plan: SubscriptionPlan | null = null;
            let featureAccess: FeatureAccess;

            if (subscription) {
                plan = plans.find(p => p.id === subscription.plan_id) || null;
                featureAccess = getFeatureAccess(plan?.name.toLowerCase() || 'free');
            } else {
                featureAccess = getFeatureAccess('free');
            }

            // Calculate days remaining
            let daysRemaining = 0;
            if (subscription) {
                const endDate = new Date(subscription.current_period_end);
                const now = new Date();
                const diffTime = endDate.getTime() - now.getTime();
                daysRemaining = Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
            }

            const isExpiringSoon = daysRemaining > 0 && daysRemaining <= 7;

            return {
                subscription,
                plan,
                usage,
                featureAccess,
                daysRemaining,
                isExpiringSoon,
            };
        } catch (error) {
            return {
                subscription: null,
                plan: null,
                usage: null,
                featureAccess: getFeatureAccess('free'),
                daysRemaining: 0,
                isExpiringSoon: false,
                error: error instanceof Error ? error.message : 'Failed to get subscription summary',
            };
        }
    }

    /**
     * Process subscription renewals (for background jobs)
     */
    static async processRenewals(): Promise<{ processed: number; errors: string[] }> {
        const errors: string[] = [];
        let processed = 0;

        try {
            // This would typically be called by a background job
            // Get subscriptions expiring today
            const today = new Date();
            today.setHours(23, 59, 59, 999);

            // In a real implementation, you would query for expiring subscriptions
            // and process renewals with the payment provider

            console.log('Processing subscription renewals...');

            return { processed, errors };
        } catch (error) {
            errors.push(error instanceof Error ? error.message : 'Unknown error');
            return { processed, errors };
        }
    }

    /**
     * Handle failed payments (for webhooks)
     */
    static async handleFailedPayment(subscriptionId: string): Promise<void> {
        try {
            // Update subscription status to past_due
            // Send notification to user
            // Implement retry logic

            console.log(`Handling failed payment for subscription: ${subscriptionId}`);
        } catch (error) {
            console.error('Failed to handle payment failure:', error);
        }
    }

    /**
     * Handle successful payments (for webhooks)
     */
    static async handleSuccessfulPayment(subscriptionId: string): Promise<void> {
        try {
            // Update subscription status to active
            // Award subscription bonus points
            // Send confirmation to user

            console.log(`Handling successful payment for subscription: ${subscriptionId}`);
        } catch (error) {
            console.error('Failed to handle successful payment:', error);
        }
    }

    /**
     * Get usage analytics for admin
     */
    static async getUsageAnalytics(userId: string): Promise<{
        monthlyUsage: any;
        yearlyUsage: any;
        trends: any;
        error?: string;
    }> {
        try {
            // This would implement detailed usage analytics
            // For now, return basic structure

            return {
                monthlyUsage: {},
                yearlyUsage: {},
                trends: {},
            };
        } catch (error) {
            return {
                monthlyUsage: {},
                yearlyUsage: {},
                trends: {},
                error: error instanceof Error ? error.message : 'Failed to get analytics',
            };
        }
    }
}