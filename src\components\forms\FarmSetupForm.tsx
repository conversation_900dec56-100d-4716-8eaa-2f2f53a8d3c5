import React, { useState } from 'react';
import { View, Text, Pressable, ScrollView } from 'react-native';
import { Input } from '../ui/Input';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';

export interface FarmSetupData {
  location: {
    address: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  cropTypes: string[];
  experienceLevel: 'beginner' | 'intermediate' | 'expert';
}

export interface FarmSetupFormProps {
  initialData?: Partial<FarmSetupData>;
  onSubmit: (data: FarmSetupData) => void;
  onBack?: () => void;
  loading?: boolean;
  voiceFeedbackEnabled?: boolean;
  onVoiceFeedback?: (text: string) => void;
  voiceInputEnabled?: boolean;
  onVoiceInput?: (field: string) => void;
  onLocationPicker?: () => void;
  onGPSLocation?: () => void;
  isVoiceRecording?: string | null;
}

const CROP_OPTIONS = [
  { id: 'corn', name: 'Corn', icon: '🌽' },
  { id: 'tomatoes', name: 'Tomatoes', icon: '🍅' },
  { id: 'wheat', name: 'Wheat', icon: '🌾' },
  { id: 'peppers', name: 'Peppers', icon: '🌶️' },
  { id: 'soybeans', name: 'Soybeans', icon: '🫘' },
  { id: 'potatoes', name: 'Potatoes', icon: '🥔' },
  { id: 'onions', name: 'Onions', icon: '🧅' },
  { id: 'carrots', name: 'Carrots', icon: '🥕' },
  { id: 'lettuce', name: 'Lettuce', icon: '🥬' },
  { id: 'beans', name: 'Beans', icon: '🫛' },
];

const EXPERIENCE_LEVELS = [
  {
    id: 'beginner' as const,
    name: 'Beginner',
    description: 'New to farming, learning the basics',
    icon: '🌱',
  },
  {
    id: 'intermediate' as const,
    name: 'Intermediate',
    description: 'Some farming experience, growing knowledge',
    icon: '🌿',
  },
  {
    id: 'expert' as const,
    name: 'Expert',
    description: 'Experienced farmer with advanced knowledge',
    icon: '🌳',
  },
];

export const FarmSetupForm: React.FC<FarmSetupFormProps> = ({
  initialData = {},
  onSubmit,
  onBack,
  loading = false,
  voiceFeedbackEnabled = false,
  onVoiceFeedback,
  voiceInputEnabled = false,
  onVoiceInput,
  onLocationPicker,
  onGPSLocation,
  isVoiceRecording = null,
}) => {
  const [formData, setFormData] = useState<FarmSetupData>({
    location: {
      address: initialData.location?.address || '',
      coordinates: initialData.location?.coordinates,
    },
    cropTypes: initialData.cropTypes || [],
    experienceLevel: initialData.experienceLevel || 'beginner',
  });

  const [errors, setErrors] = useState<{
    location?: string;
    cropTypes?: string;
    experienceLevel?: string;
  }>({});

  const validateForm = (): boolean => {
    const newErrors: typeof errors = {};

    // Location validation
    if (!formData.location.address.trim()) {
      newErrors.location = 'Farm location is required';
    }

    // Crop types validation
    if (formData.cropTypes.length === 0) {
      newErrors.cropTypes = 'Please select at least one crop type';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      if (voiceFeedbackEnabled && onVoiceFeedback) {
        onVoiceFeedback('Farm setup completed successfully');
      }
      onSubmit(formData);
    } else {
      if (voiceFeedbackEnabled && onVoiceFeedback) {
        const errorCount = Object.keys(errors).length;
        onVoiceFeedback(
          `Form has ${errorCount} error${errorCount > 1 ? 's' : ''}. Please complete all required fields.`
        );
      }
    }
  };

  const toggleCropType = (cropId: string) => {
    const cropName = CROP_OPTIONS.find((crop) => crop.id === cropId)?.name || cropId;

    setFormData((prev) => {
      const isSelected = prev.cropTypes.includes(cropId);
      const newCropTypes = isSelected
        ? prev.cropTypes.filter((id) => id !== cropId)
        : [...prev.cropTypes, cropId];

      if (voiceFeedbackEnabled && onVoiceFeedback) {
        onVoiceFeedback(isSelected ? `${cropName} deselected` : `${cropName} selected`);
      }

      return { ...prev, cropTypes: newCropTypes };
    });

    // Clear error when user selects crops
    if (errors.cropTypes) {
      setErrors((prev) => ({ ...prev, cropTypes: undefined }));
    }
  };

  const selectExperienceLevel = (level: FarmSetupData['experienceLevel']) => {
    const levelName = EXPERIENCE_LEVELS.find((l) => l.id === level)?.name || level;

    setFormData((prev) => ({ ...prev, experienceLevel: level }));

    if (voiceFeedbackEnabled && onVoiceFeedback) {
      onVoiceFeedback(`Experience level set to ${levelName}`);
    }
  };

  const updateLocation = (address: string) => {
    setFormData((prev) => ({
      ...prev,
      location: { ...prev.location, address },
    }));

    // Clear error when user starts typing
    if (errors.location) {
      setErrors((prev) => ({ ...prev, location: undefined }));
    }
  };

  return (
    <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
      <View className="w-full gap-6 pb-8">
        <View className="mb-6">
          <Text className="mb-2 text-2xl font-bold text-earth-900">Farm Setup</Text>
          <Text className="text-base text-earth-600">
            Tell us about your farm to get personalized recommendations.
          </Text>
        </View>

        {/* Location Section */}
        <View className="gap-4">
          <Text className="text-lg font-semibold text-earth-800">📍 Farm Location</Text>

          <Input
            label="Farm Address"
            placeholder="Enter your farm address"
            value={formData.location.address}
            onChangeText={updateLocation}
            error={errors.location}
            accessibilityLabel="Farm address input"
            accessibilityHint="Enter the address of your farm location"
            voiceInputEnabled={voiceInputEnabled}
            onVoiceInput={() => onVoiceInput?.('location')}
            voiceFeedbackEnabled={voiceFeedbackEnabled}
            onVoiceFeedback={onVoiceFeedback}
            isVoiceRecording={isVoiceRecording === 'location'}
            testID="farm-address-input"
          />

          <View className="flex-row gap-3">
            <Button
              title="📍 Use Current Location"
              onPress={() => {
                if (voiceFeedbackEnabled && onVoiceFeedback) {
                  onVoiceFeedback('Getting current GPS location');
                }
                onGPSLocation?.();
              }}
              variant="outline"
              size="medium"
              className="flex-1"
              accessibilityLabel="Use current GPS location"
              accessibilityHint="Get your current location using GPS"
              voiceFeedbackEnabled={voiceFeedbackEnabled}
              onVoiceFeedback={onVoiceFeedback}
              testID="gps-location-button"
            />

            <Button
              title="🗺️ Select on Map"
              onPress={() => {
                if (voiceFeedbackEnabled && onVoiceFeedback) {
                  onVoiceFeedback('Opening map to select location');
                }
                onLocationPicker?.();
              }}
              variant="outline"
              size="medium"
              className="flex-1"
              accessibilityLabel="Select location on map"
              accessibilityHint="Choose your farm location using the map"
              voiceFeedbackEnabled={voiceFeedbackEnabled}
              onVoiceFeedback={onVoiceFeedback}
              testID="map-picker-button"
            />
          </View>
        </View>

        {/* Crop Types Section */}
        <View className="gap-4">
          <View>
            <Text className="mb-2 text-lg font-semibold text-earth-800">🌱 What do you grow?</Text>
            {errors.cropTypes && (
              <Text className="mb-2 text-sm text-red-500">{errors.cropTypes}</Text>
            )}
          </View>

          <View className="flex-row flex-wrap gap-3">
            {CROP_OPTIONS.map((crop) => {
              const isSelected = formData.cropTypes.includes(crop.id);
              return (
                <Pressable
                  key={crop.id}
                  onPress={() => toggleCropType(crop.id)}
                  className={`
                                        min-h-[56px] items-center justify-center rounded-xl border-2 px-4 py-3
                                        ${isSelected
                      ? 'border-primary-500 bg-primary-100'
                      : 'border-earth-200 bg-white'
                    }
                                        active:opacity-80
                                    `}
                  accessibilityRole="checkbox"
                  accessibilityState={{ checked: isSelected }}
                  accessibilityLabel={`${crop.name} crop option`}
                  accessibilityHint={`Tap to ${isSelected ? 'deselect' : 'select'} ${crop.name}`}
                  testID={`crop-${crop.id}`}>
                  <Text className="mb-1 text-2xl">{crop.icon}</Text>
                  <Text
                    className={`text-sm font-medium ${isSelected ? 'text-primary-700' : 'text-earth-700'
                      }`}>
                    {crop.name}
                  </Text>
                </Pressable>
              );
            })}
          </View>
        </View>

        {/* Experience Level Section */}
        <View className="gap-4">
          <Text className="text-lg font-semibold text-earth-800">🎯 Experience Level</Text>

          <View className="gap-3">
            {EXPERIENCE_LEVELS.map((level) => {
              const isSelected = formData.experienceLevel === level.id;
              return (
                <Card
                  key={level.id}
                  onPress={() => selectExperienceLevel(level.id)}
                  variant={isSelected ? 'filled' : 'outlined'}
                  className={isSelected ? 'border-primary-500 bg-primary-50' : ''}
                  accessibilityLabel={`${level.name} experience level`}
                  accessibilityHint={level.description}
                  voiceFeedbackEnabled={voiceFeedbackEnabled}
                  onVoiceFeedback={onVoiceFeedback}
                  testID={`experience-${level.id}`}>
                  <View className="flex-row items-center gap-4">
                    <Text className="text-3xl">{level.icon}</Text>
                    <View className="flex-1">
                      <Text
                        className={`text-lg font-semibold ${isSelected ? 'text-primary-700' : 'text-earth-800'
                          }`}>
                        {level.name}
                      </Text>
                      <Text
                        className={`text-sm ${isSelected ? 'text-primary-600' : 'text-earth-600'}`}>
                        {level.description}
                      </Text>
                    </View>
                    {isSelected && <Text className="text-xl text-primary-500">✓</Text>}
                  </View>
                </Card>
              );
            })}
          </View>
        </View>

        {/* Action Buttons */}
        <View className="mt-8 flex-row gap-4">
          {onBack && (
            <Button
              title="Back"
              onPress={onBack}
              variant="outline"
              size="large"
              className="flex-1"
              accessibilityLabel="Go back to personal information"
              accessibilityHint="Return to the previous step"
              voiceFeedbackEnabled={voiceFeedbackEnabled}
              onVoiceFeedback={onVoiceFeedback}
              testID="back-button"
            />
          )}

          <Button
            title="Complete Setup"
            onPress={handleSubmit}
            variant="primary"
            size="large"
            loading={loading}
            className={onBack ? 'flex-1' : 'w-full'}
            accessibilityLabel="Complete farm setup"
            accessibilityHint="Finish registration and create your farming profile"
            voiceFeedbackEnabled={voiceFeedbackEnabled}
            onVoiceFeedback={onVoiceFeedback}
            testID="complete-setup-button"
          />
        </View>
      </View>
    </ScrollView>
  );
};
