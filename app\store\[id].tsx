import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, Image, Pressable, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Product } from '../../src/types/product';
import { storeService } from '../../src/services/store';
import { Button } from '../../src/components/ui/Button';

export default function ProductDetailScreen() {
    const { id } = useLocalSearchParams<{ id: string }>();
    const router = useRouter();
    const [product, setProduct] = useState<Product | null>(null);
    const [loading, setLoading] = useState(true);
    const [selectedImageIndex, setSelectedImageIndex] = useState(0);
    const [quantity, setQuantity] = useState(1);

    useEffect(() => {
        loadProduct();
    }, [id]);

    const loadProduct = async () => {
        if (!id) return;

        try {
            setLoading(true);
            const productData = await storeService.getProductById(id);
            setProduct(productData);
        } catch (error) {
            console.error('Error loading product:', error);
            Alert.alert('Error', 'Failed to load product details');
        } finally {
            setLoading(false);
        }
    };

    const handleAddToCart = async () => {
        if (!product) return;

        try {
            await storeService.addToCart(product.id, quantity);
            Alert.alert(
                'Added to Cart',
                `${product.name} has been added to your cart`,
                [
                    { text: 'Continue Shopping', style: 'cancel' },
                    { text: 'View Cart', onPress: () => router.push('/store/cart') },
                ]
            );
        } catch (error) {
            console.error('Error adding to cart:', error);
            Alert.alert('Error', 'Failed to add product to cart');
        }
    };

    const formatPrice = (price: number) => {
        return `$${price.toFixed(2)}`;
    };

    const renderRating = () => {
        if (!product) return null;

        const stars = Math.floor(product.rating);
        const hasHalfStar = product.rating % 1 !== 0;

        return (
            <View className="flex-row items-center gap-2">
                <Text className="text-yellow-500 text-lg">
                    {'★'.repeat(stars)}
                    {hasHalfStar ? '☆' : ''}
                    {'☆'.repeat(5 - stars - (hasHalfStar ? 1 : 0))}
                </Text>
                <Text className="text-base text-gray-600">
                    {product.rating} ({product.reviewCount} reviews)
                </Text>
            </View>
        );
    };

    const renderBadges = () => {
        if (!product) return null;

        const badges = [];

        if (product.isRecommended) {
            badges.push(
                <View key="recommended" className="bg-green-100 px-3 py-1 rounded-full">
                    <Text className="text-sm font-medium text-green-800">✓ Recommended</Text>
                </View>
            );
        }

        if (product.isFeatured) {
            badges.push(
                <View key="featured" className="bg-blue-100 px-3 py-1 rounded-full">
                    <Text className="text-sm font-medium text-blue-800">⭐ Featured</Text>
                </View>
            );
        }

        if (product.originalPrice && product.originalPrice > product.price) {
            const discount = Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);
            badges.push(
                <View key="discount" className="bg-red-100 px-3 py-1 rounded-full">
                    <Text className="text-sm font-medium text-red-800">{discount}% OFF</Text>
                </View>
            );
        }

        return badges.length > 0 ? (
            <View className="flex-row flex-wrap gap-2 mb-4">
                {badges}
            </View>
        ) : null;
    };

    if (loading) {
        return (
            <SafeAreaView className="flex-1 bg-white">
                <View className="flex-1 items-center justify-center">
                    <Text className="text-lg text-gray-600">Loading product...</Text>
                </View>
            </SafeAreaView>
        );
    }

    if (!product) {
        return (
            <SafeAreaView className="flex-1 bg-white">
                <View className="flex-1 items-center justify-center">
                    <Text className="text-lg text-gray-600 mb-4">Product not found</Text>
                    <Button
                        title="Go Back"
                        onPress={() => router.back()}
                        variant="primary"
                    />
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView className="flex-1 bg-white">
            {/* Header */}
            <View className="flex-row items-center justify-between p-4 border-b border-gray-100">
                <Pressable
                    onPress={() => router.back()}
                    className="p-2 active:opacity-80"
                    accessibilityRole="button"
                    accessibilityLabel="Go back"
                >
                    <Text className="text-2xl">←</Text>
                </Pressable>
                <Text className="text-lg font-semibold text-gray-900">Product Details</Text>
                <Pressable
                    onPress={() => {/* TODO: Add to favorites */ }}
                    className="p-2 active:opacity-80"
                    accessibilityRole="button"
                    accessibilityLabel="Add to favorites"
                >
                    <Text className="text-2xl">♡</Text>
                </Pressable>
            </View>

            <ScrollView className="flex-1">
                {/* Product Images */}
                <View className="bg-gray-50">
                    <ScrollView
                        horizontal
                        pagingEnabled
                        showsHorizontalScrollIndicator={false}
                        onMomentumScrollEnd={(event) => {
                            const index = Math.round(event.nativeEvent.contentOffset.x / event.nativeEvent.layoutMeasurement.width);
                            setSelectedImageIndex(index);
                        }}
                    >
                        {product.imageUrls.map((imageUrl, index) => (
                            <Image
                                key={index}
                                source={{ uri: imageUrl }}
                                className="w-screen h-80 bg-gray-100"
                                resizeMode="cover"
                            />
                        ))}
                    </ScrollView>

                    {product.imageUrls.length > 1 && (
                        <View className="flex-row justify-center gap-2 py-4">
                            {product.imageUrls.map((_, index) => (
                                <View
                                    key={index}
                                    className={`w-2 h-2 rounded-full ${index === selectedImageIndex ? 'bg-primary-600' : 'bg-gray-300'
                                        }`}
                                />
                            ))}
                        </View>
                    )}
                </View>

                <View className="p-4">
                    {renderBadges()}

                    {/* Product Info */}
                    <Text className="text-2xl font-bold text-gray-900 mb-2">
                        {product.name}
                    </Text>

                    <Text className="text-base text-gray-600 mb-4 leading-6">
                        {product.description}
                    </Text>

                    {renderRating()}

                    {/* Price */}
                    <View className="flex-row items-center gap-3 my-4">
                        <Text className="text-3xl font-bold text-primary-600">
                            {formatPrice(product.price)}
                        </Text>
                        {product.originalPrice && product.originalPrice > product.price && (
                            <Text className="text-xl text-gray-500 line-through">
                                {formatPrice(product.originalPrice)}
                            </Text>
                        )}
                    </View>

                    {/* Stock Status */}
                    <View className="flex-row items-center gap-2 mb-4">
                        <View className={`w-3 h-3 rounded-full ${product.stockQuantity > 0 ? 'bg-green-500' : 'bg-red-500'
                            }`} />
                        <Text className={`font-medium ${product.stockQuantity > 0 ? 'text-green-700' : 'text-red-700'
                            }`}>
                            {product.stockQuantity > 0
                                ? `In Stock (${product.stockQuantity} available)`
                                : 'Out of Stock'
                            }
                        </Text>
                    </View>

                    {/* Specifications */}
                    {product.specifications.length > 0 && (
                        <View className="mb-6">
                            <Text className="text-lg font-semibold text-gray-900 mb-3">
                                Specifications
                            </Text>
                            <View className="bg-gray-50 rounded-xl p-4">
                                {product.specifications.map((spec, index) => (
                                    <View
                                        key={index}
                                        className={`flex-row justify-between py-2 ${index < product.specifications.length - 1 ? 'border-b border-gray-200' : ''
                                            }`}
                                    >
                                        <Text className="text-gray-700 font-medium">{spec.name}</Text>
                                        <Text className="text-gray-900">
                                            {spec.value} {spec.unit || ''}
                                        </Text>
                                    </View>
                                ))}
                            </View>
                        </View>
                    )}

                    {/* Tags */}
                    {product.tags.length > 0 && (
                        <View className="mb-6">
                            <Text className="text-lg font-semibold text-gray-900 mb-3">
                                Features
                            </Text>
                            <View className="flex-row flex-wrap gap-2">
                                {product.tags.map((tag, index) => (
                                    <View key={index} className="bg-primary-100 px-3 py-2 rounded-full">
                                        <Text className="text-primary-800 font-medium capitalize">
                                            {tag.replace('-', ' ')}
                                        </Text>
                                    </View>
                                ))}
                            </View>
                        </View>
                    )}

                    {/* Brand */}
                    {product.brand && (
                        <View className="mb-6">
                            <Text className="text-lg font-semibold text-gray-900 mb-2">
                                Brand
                            </Text>
                            <Text className="text-gray-700">{product.brand}</Text>
                        </View>
                    )}
                </View>
            </ScrollView>

            {/* Bottom Action Bar */}
            <View className="p-4 border-t border-gray-100 bg-white">
                {product.stockQuantity > 0 ? (
                    <View className="gap-3">
                        {/* Quantity Selector */}
                        <View className="flex-row items-center justify-center gap-4">
                            <Text className="text-base font-medium text-gray-700">Quantity:</Text>
                            <View className="flex-row items-center gap-3">
                                <Pressable
                                    onPress={() => setQuantity(Math.max(1, quantity - 1))}
                                    className="w-10 h-10 bg-gray-100 rounded-lg items-center justify-center active:opacity-80"
                                    accessibilityRole="button"
                                    accessibilityLabel="Decrease quantity"
                                >
                                    <Text className="text-xl font-bold text-gray-600">−</Text>
                                </Pressable>
                                <Text className="text-lg font-semibold text-gray-900 min-w-[40px] text-center">
                                    {quantity}
                                </Text>
                                <Pressable
                                    onPress={() => setQuantity(Math.min(product.stockQuantity, quantity + 1))}
                                    className="w-10 h-10 bg-gray-100 rounded-lg items-center justify-center active:opacity-80"
                                    accessibilityRole="button"
                                    accessibilityLabel="Increase quantity"
                                >
                                    <Text className="text-xl font-bold text-gray-600">+</Text>
                                </Pressable>
                            </View>
                        </View>

                        <Button
                            title={`Add to Cart - ${formatPrice(product.price * quantity)}`}
                            onPress={handleAddToCart}
                            variant="primary"
                            size="large"
                            fullWidth
                        />
                    </View>
                ) : (
                    <View className="items-center py-4">
                        <Text className="text-lg font-medium text-red-600 mb-2">
                            Out of Stock
                        </Text>
                        <Text className="text-gray-600 text-center">
                            This product is currently unavailable
                        </Text>
                    </View>
                )}
            </View>
        </SafeAreaView>
    );
}