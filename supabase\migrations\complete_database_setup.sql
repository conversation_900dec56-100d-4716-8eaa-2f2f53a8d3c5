-- Complete Database Setup for Smart Farming App
-- This file contains all necessary database structures and sample data

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- ==========================================
-- CORE TABLES
-- ==========================================

-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR UNIQUE NOT NULL,
  phone VARCHAR UNIQUE NOT NULL,
  first_name VARCHAR NOT NULL,
  last_name VARCHAR NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
  farm_location POINT,
  crop_types TEXT[],
  experience_level VARCHAR CHECK (experience_level IN ('beginner', 'intermediate', 'expert')),
  preferred_language VARCHAR DEFAULT 'en',
  voice_enabled BOOLEAN DEFAULT false,
  points INTEGER DEFAULT 0,
  subscription_tier VARCHAR DEFAULT 'free',
  subscription_expires_at TIMESTAMP
);

-- Create crop_plans table
CREATE TABLE IF NOT EXISTS crop_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  crop_type VARCHAR NOT NULL,
  planting_date DATE,
  harvest_date DATE,
  location POINT,
  status VARCHAR DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW()
);

-- Create tasks table
CREATE TABLE IF NOT EXISTS tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  crop_plan_id UUID REFERENCES crop_plans(id) ON DELETE CASCADE,
  title VARCHAR NOT NULL,
  description TEXT,
  due_date TIMESTAMP,
  task_type VARCHAR,
  completed BOOLEAN DEFAULT false,
  points_reward INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Create chat_sessions table
CREATE TABLE IF NOT EXISTS chat_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  session_type VARCHAR DEFAULT 'general',
  created_at TIMESTAMP DEFAULT NOW()
);

-- Create chat_messages table
CREATE TABLE IF NOT EXISTS chat_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID REFERENCES chat_sessions(id) ON DELETE CASCADE,
  message_type VARCHAR CHECK (message_type IN ('user', 'ai')),
  content TEXT,
  image_url VARCHAR,
  analysis_result JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);

-- ==========================================
-- COMMUNITY FEATURES
-- ==========================================

-- Create community_posts table
CREATE TABLE IF NOT EXISTS community_posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR NOT NULL,
  content TEXT,
  image_urls TEXT[],
  location POINT,
  likes_count INTEGER DEFAULT 0,
  comments_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Create post_comments table
CREATE TABLE IF NOT EXISTS post_comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  post_id UUID REFERENCES community_posts(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Post likes table
CREATE TABLE IF NOT EXISTS post_likes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    post_id UUID REFERENCES community_posts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(post_id, user_id)
);

-- Post reports table for moderation
CREATE TABLE IF NOT EXISTS post_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    post_id UUID REFERENCES community_posts(id) ON DELETE CASCADE,
    reporter_id UUID REFERENCES users(id) ON DELETE CASCADE,
    reason VARCHAR NOT NULL,
    description TEXT,
    status VARCHAR DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'resolved', 'dismissed')),
    reviewed_by UUID REFERENCES users(id),
    reviewed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Post views table for engagement tracking
CREATE TABLE IF NOT EXISTS post_views (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    post_id UUID REFERENCES community_posts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    viewed_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(post_id, user_id)
);

-- Post shares table for engagement tracking
CREATE TABLE IF NOT EXISTS post_shares (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    post_id UUID REFERENCES community_posts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    platform VARCHAR, -- 'whatsapp', 'facebook', 'twitter', etc.
    shared_at TIMESTAMP DEFAULT NOW()
);

-- User following table for social features
CREATE TABLE IF NOT EXISTS user_follows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    follower_id UUID REFERENCES users(id) ON DELETE CASCADE,
    following_id UUID REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(follower_id, following_id),
    CHECK (follower_id != following_id)
);

-- Direct messages table
CREATE TABLE IF NOT EXISTS direct_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sender_id UUID REFERENCES users(id) ON DELETE CASCADE,
    recipient_id UUID REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    read_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Community events table
CREATE TABLE IF NOT EXISTS community_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organizer_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR NOT NULL,
    description TEXT,
    event_date TIMESTAMP NOT NULL,
    location POINT,
    location_name VARCHAR,
    max_attendees INTEGER,
    is_public BOOLEAN DEFAULT true,
    status VARCHAR DEFAULT 'upcoming' CHECK (status IN ('upcoming', 'ongoing', 'completed', 'cancelled')),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Event RSVPs table
CREATE TABLE IF NOT EXISTS event_rsvps (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_id UUID REFERENCES community_events(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR DEFAULT 'going' CHECK (status IN ('going', 'maybe', 'not_going')),
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(event_id, user_id)
);

-- ==========================================
-- E-COMMERCE FEATURES
-- ==========================================

-- Create enhanced products table
CREATE TABLE IF NOT EXISTS products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR NOT NULL,
  description TEXT,
  category VARCHAR NOT NULL CHECK (category IN ('seeds', 'fertilizers', 'tools', 'equipment', 'pesticides', 'irrigation')),
  price DECIMAL(10,2) NOT NULL,
  original_price DECIMAL(10,2), -- For showing discounts
  currency VARCHAR DEFAULT 'USD',
  image_urls TEXT[] DEFAULT '{}',
  specifications JSONB DEFAULT '[]', -- Array of {name, value, unit}
  stock_quantity INTEGER DEFAULT 0,
  rating DECIMAL(3,2) DEFAULT 0.0,
  review_count INTEGER DEFAULT 0,
  tags TEXT[] DEFAULT '{}',
  brand VARCHAR,
  is_recommended BOOLEAN DEFAULT false,
  is_featured BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  search_vector tsvector, -- For full-text search
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create product reviews table
CREATE TABLE IF NOT EXISTS product_reviews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  title VARCHAR,
  comment TEXT,
  helpful_count INTEGER DEFAULT 0,
  verified_purchase BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Create product variants table
CREATE TABLE IF NOT EXISTS product_variants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  name VARCHAR NOT NULL,
  attributes JSONB NOT NULL, -- {size, color, etc.}
  price_adjustment DECIMAL(10,2) DEFAULT 0,
  stock_quantity INTEGER DEFAULT 0,
  sku VARCHAR,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Create shopping carts table
CREATE TABLE IF NOT EXISTS shopping_carts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create cart items table
CREATE TABLE IF NOT EXISTS cart_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cart_id UUID REFERENCES shopping_carts(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  variant_id UUID REFERENCES product_variants(id) ON DELETE SET NULL,
  quantity INTEGER NOT NULL,
  added_at TIMESTAMP DEFAULT NOW()
);

-- Create enhanced orders table
CREATE TABLE IF NOT EXISTS orders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  order_number VARCHAR UNIQUE NOT NULL,
  status VARCHAR NOT NULL CHECK (status IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded')),
  total_amount DECIMAL(10,2) NOT NULL,
  subtotal DECIMAL(10,2) NOT NULL,
  tax DECIMAL(10,2) DEFAULT 0,
  shipping_fee DECIMAL(10,2) DEFAULT 0,
  discount DECIMAL(10,2) DEFAULT 0,
  shipping_address JSONB NOT NULL,
  billing_address JSONB,
  payment_method VARCHAR,
  payment_status VARCHAR DEFAULT 'pending',
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create enhanced order items table
CREATE TABLE IF NOT EXISTS order_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  variant_id UUID REFERENCES product_variants(id) ON DELETE SET NULL,
  quantity INTEGER NOT NULL,
  unit_price DECIMAL(10,2) NOT NULL,
  total_price DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Create product recommendations table (AI-based)
CREATE TABLE IF NOT EXISTS product_recommendations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  recommended_product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  reason VARCHAR NOT NULL,
  confidence DECIMAL(3,2) CHECK (confidence >= 0 AND confidence <= 1),
  source VARCHAR DEFAULT 'ai' CHECK (source IN ('ai', 'collaborative', 'content', 'trending')),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP DEFAULT (NOW() + INTERVAL '30 days')
);

-- Create inventory tracking table
CREATE TABLE IF NOT EXISTS inventory_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  variant_id UUID REFERENCES product_variants(id) ON DELETE SET NULL,
  transaction_type VARCHAR NOT NULL CHECK (transaction_type IN ('purchase', 'sale', 'adjustment', 'return')),
  quantity_change INTEGER NOT NULL,
  previous_quantity INTEGER NOT NULL,
  new_quantity INTEGER NOT NULL,
  reference_id UUID, -- Order ID, adjustment ID, etc.
  reference_type VARCHAR, -- 'order', 'adjustment', 'return'
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Create wishlist table
CREATE TABLE IF NOT EXISTS wishlists (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR DEFAULT 'Default',
  is_public BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create wishlist items table
CREATE TABLE IF NOT EXISTS wishlist_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  wishlist_id UUID REFERENCES wishlists(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  variant_id UUID REFERENCES product_variants(id) ON DELETE SET NULL,
  added_at TIMESTAMP DEFAULT NOW()
);

-- ==========================================
-- POINTS AND SUBSCRIPTION SYSTEM
-- ==========================================

-- Create points_transactions table
CREATE TABLE IF NOT EXISTS points_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    amount INTEGER NOT NULL,
    transaction_type VARCHAR(10) CHECK (transaction_type IN ('earned', 'spent', 'bonus', 'refund')),
    source VARCHAR(50) CHECK (source IN ('task_completion', 'daily_checkin', 'community_post', 'ai_consultation', 'subscription_bonus', 'achievement', 'referral', 'admin_adjustment')),
    description TEXT NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create points_balances table
CREATE TABLE IF NOT EXISTS points_balances (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    total_points INTEGER DEFAULT 0,
    available_points INTEGER DEFAULT 0,
    pending_points INTEGER DEFAULT 0,
    lifetime_earned INTEGER DEFAULT 0,
    lifetime_spent INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create achievements table
CREATE TABLE IF NOT EXISTS achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    icon VARCHAR(10) NOT NULL,
    category VARCHAR(20) CHECK (category IN ('farming', 'community', 'learning', 'consistency', 'milestone')),
    points_reward INTEGER NOT NULL,
    requirements JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_achievements table
CREATE TABLE IF NOT EXISTS user_achievements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    achievement_id UUID REFERENCES achievements(id) ON DELETE CASCADE,
    earned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    points_awarded INTEGER NOT NULL,
    UNIQUE(user_id, achievement_id)
);

-- Create subscription_plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    billing_period VARCHAR(10) CHECK (billing_period IN ('monthly', 'yearly')),
    features JSONB NOT NULL,
    points_included INTEGER DEFAULT 0,
    ai_consultations_limit INTEGER DEFAULT 5, -- -1 for unlimited
    priority_support BOOLEAN DEFAULT false,
    offline_access BOOLEAN DEFAULT false,
    advanced_analytics BOOLEAN DEFAULT false,
    api_access BOOLEAN DEFAULT false,
    multi_farm_support BOOLEAN DEFAULT false,
    team_collaboration BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_subscriptions table
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    plan_id UUID REFERENCES subscription_plans(id),
    status VARCHAR(20) CHECK (status IN ('active', 'cancelled', 'expired', 'past_due', 'trialing')),
    current_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    current_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    cancel_at_period_end BOOLEAN DEFAULT false,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    trial_start TIMESTAMP WITH TIME ZONE,
    trial_end TIMESTAMP WITH TIME ZONE,
    payment_method_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create subscription_usage table
CREATE TABLE IF NOT EXISTS subscription_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    subscription_id UUID REFERENCES user_subscriptions(id) ON DELETE CASCADE,
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    ai_consultations_used INTEGER DEFAULT 0,
    ai_consultations_limit INTEGER NOT NULL,
    image_analyses_used INTEGER DEFAULT 0,
    storage_used_mb INTEGER DEFAULT 0,
    api_calls_used INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create daily_streaks table
CREATE TABLE IF NOT EXISTS daily_streaks (
    user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
    current_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    last_activity_date TIMESTAMP WITH TIME ZONE NOT NULL,
    streak_multiplier DECIMAL(3,2) DEFAULT 1.0
);

-- ==========================================
-- NOTIFICATION SYSTEM
-- ==========================================

-- User notification preferences
CREATE TABLE IF NOT EXISTS user_notification_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    preferences JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Push tokens for devices
CREATE TABLE IF NOT EXISTS push_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    token TEXT NOT NULL,
    platform TEXT NOT NULL CHECK (platform IN ('ios', 'android', 'web')),
    device_id TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, device_id)
);

-- Notifications sent to users
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    type TEXT NOT NULL,
    title TEXT NOT NULL,
    body TEXT NOT NULL,
    data JSONB DEFAULT '{}',
    priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'critical')),
    category TEXT DEFAULT 'system' CHECK (category IN ('agricultural', 'weather', 'community', 'system', 'commercial')),
    status TEXT DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'read', 'failed')),
    scheduled_for TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    delivered_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Scheduled notifications
CREATE TABLE IF NOT EXISTS scheduled_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    identifier TEXT NOT NULL,
    notification_data JSONB NOT NULL,
    scheduled_for TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notification analytics
CREATE TABLE IF NOT EXISTS notification_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    notification_id UUID REFERENCES notifications(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    type TEXT NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE NOT NULL,
    delivered_at TIMESTAMP WITH TIME ZONE,
    opened_at TIMESTAMP WITH TIME ZONE,
    action_taken BOOLEAN DEFAULT false,
    device_info JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Weather alerts
CREATE TABLE IF NOT EXISTS weather_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    alert_data JSONB NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ==========================================
-- INDEXES
-- ==========================================

-- Core tables indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(id);
CREATE INDEX IF NOT EXISTS idx_crop_plans_user_id ON crop_plans(user_id);
CREATE INDEX IF NOT EXISTS idx_tasks_crop_plan_id ON tasks(crop_plan_id);
CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON tasks(due_date);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_user_id ON chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_session_id ON chat_messages(session_id);

-- Community features indexes
CREATE INDEX IF NOT EXISTS idx_community_posts_user_id ON community_posts(user_id);
CREATE INDEX IF NOT EXISTS idx_community_posts_location ON community_posts USING GIST(location);
CREATE INDEX IF NOT EXISTS idx_post_comments_post_id ON post_comments(post_id);
CREATE INDEX IF NOT EXISTS idx_post_likes_post_id ON post_likes(post_id);
CREATE INDEX IF NOT EXISTS idx_post_likes_user_id ON post_likes(user_id);
CREATE INDEX IF NOT EXISTS idx_post_reports_post_id ON post_reports(post_id);
CREATE INDEX IF NOT EXISTS idx_post_reports_status ON post_reports(status);
CREATE INDEX IF NOT EXISTS idx_post_views_post_id ON post_views(post_id);
CREATE INDEX IF NOT EXISTS idx_post_shares_post_id ON post_shares(post_id);
CREATE INDEX IF NOT EXISTS idx_user_follows_follower ON user_follows(follower_id);
CREATE INDEX IF NOT EXISTS idx_user_follows_following ON user_follows(following_id);
CREATE INDEX IF NOT EXISTS idx_direct_messages_sender ON direct_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_direct_messages_recipient ON direct_messages(recipient_id);
CREATE INDEX IF NOT EXISTS idx_community_events_date ON community_events(event_date);
CREATE INDEX IF NOT EXISTS idx_community_events_location ON community_events USING GIST(location);
CREATE INDEX IF NOT EXISTS idx_event_rsvps_event ON event_rsvps(event_id);

-- E-commerce indexes
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_products_price ON products(price);
CREATE INDEX IF NOT EXISTS idx_products_rating ON products(rating);
CREATE INDEX IF NOT EXISTS idx_products_is_featured ON products(is_featured);
CREATE INDEX IF NOT EXISTS idx_products_is_recommended ON products(is_recommended);
CREATE INDEX IF NOT EXISTS idx_products_is_active ON products(is_active);
CREATE INDEX IF NOT EXISTS idx_products_search_vector ON products USING gin(search_vector);
CREATE INDEX IF NOT EXISTS idx_products_tags ON products USING gin(tags);

CREATE INDEX IF NOT EXISTS idx_product_reviews_product_id ON product_reviews(product_id);
CREATE INDEX IF NOT EXISTS idx_product_reviews_user_id ON product_reviews(user_id);
CREATE INDEX IF NOT EXISTS idx_product_reviews_rating ON product_reviews(rating);

CREATE INDEX IF NOT EXISTS idx_product_variants_product_id ON product_variants(product_id);

CREATE INDEX IF NOT EXISTS idx_shopping_carts_user_id ON shopping_carts(user_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_cart_id ON cart_items(cart_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_product_id ON cart_items(product_id);

CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_orders_order_number ON orders(order_number);

CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);

CREATE INDEX IF NOT EXISTS idx_product_recommendations_user_id ON product_recommendations(user_id);
CREATE INDEX IF NOT EXISTS idx_product_recommendations_product_id ON product_recommendations(product_id);
CREATE INDEX IF NOT EXISTS idx_product_recommendations_expires_at ON product_recommendations(expires_at);

CREATE INDEX IF NOT EXISTS idx_inventory_transactions_product_id ON inventory_transactions(product_id);

CREATE INDEX IF NOT EXISTS idx_wishlists_user_id ON wishlists(user_id);
CREATE INDEX IF NOT EXISTS idx_wishlist_items_wishlist_id ON wishlist_items(wishlist_id);
CREATE INDEX IF NOT EXISTS idx_wishlist_items_product_id ON wishlist_items(product_id);

-- Points and subscription indexes
CREATE INDEX IF NOT EXISTS idx_points_transactions_user_id ON points_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_points_transactions_created_at ON points_transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_points_transactions_source ON points_transactions(source);

CREATE INDEX IF NOT EXISTS idx_user_achievements_user_id ON user_achievements(user_id);
CREATE INDEX IF NOT EXISTS idx_user_achievements_achievement_id ON user_achievements(achievement_id);

CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_period_end ON user_subscriptions(current_period_end);

CREATE INDEX IF NOT EXISTS idx_subscription_usage_user_id ON subscription_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_subscription_usage_subscription_id ON subscription_usage(subscription_id);

-- Notification indexes
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);
CREATE INDEX IF NOT EXISTS idx_notifications_status ON notifications(status);
CREATE INDEX IF NOT EXISTS idx_notifications_sent_at ON notifications(sent_at);
CREATE INDEX IF NOT EXISTS idx_notifications_read_at ON notifications(read_at);

CREATE INDEX IF NOT EXISTS idx_push_tokens_user_id ON push_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_push_tokens_is_active ON push_tokens(is_active);

CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_user_id ON scheduled_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_scheduled_for ON scheduled_notifications(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_is_active ON scheduled_notifications(is_active);

CREATE INDEX IF NOT EXISTS idx_notification_analytics_user_id ON notification_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_analytics_type ON notification_analytics(type);
CREATE INDEX IF NOT EXISTS idx_notification_analytics_sent_at ON notification_analytics(sent_at);

-- ==========================================
-- FUNCTIONS AND TRIGGERS
-- ==========================================

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for users table
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to update points balance
CREATE OR REPLACE FUNCTION update_points_balance(
    p_user_id UUID,
    p_amount INTEGER,
    p_transaction_type VARCHAR
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO points_balances (user_id, total_points, available_points, lifetime_earned, lifetime_spent, last_updated)
    VALUES (p_user_id, 0, 0, 0, 0, NOW())
    ON CONFLICT (user_id) DO NOTHING;

    IF p_transaction_type = 'earned' THEN
        UPDATE points_balances 
        SET 
            total_points = total_points + p_amount,
            available_points = available_points + p_amount,
            lifetime_earned = lifetime_earned + p_amount,
            last_updated = NOW()
        WHERE user_id = p_user_id;
    ELSIF p_transaction_type = 'spent' THEN
        UPDATE points_balances 
        SET 
            total_points = total_points + p_amount, -- p_amount is negative for spending
            available_points = available_points + p_amount,
            lifetime_spent = lifetime_spent + ABS(p_amount),
            last_updated = NOW()
        WHERE user_id = p_user_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create function to increment usage
CREATE OR REPLACE FUNCTION increment_usage(
    p_user_id UUID,
    p_subscription_id UUID,
    p_field VARCHAR,
    p_increment INTEGER
)
RETURNS VOID AS $$
DECLARE
    sql_query TEXT;
BEGIN
    sql_query := format('
        UPDATE subscription_usage 
        SET %I = %I + $1, updated_at = NOW()
        WHERE user_id = $2 AND subscription_id = $3
    ', p_field, p_field);
    
    EXECUTE sql_query USING p_increment, p_user_id, p_subscription_id;
END;
$$ LANGUAGE plpgsql;

-- Add triggers to update post engagement counts
CREATE OR REPLACE FUNCTION update_post_likes_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE community_posts 
        SET likes_count = likes_count + 1 
        WHERE id = NEW.post_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE community_posts 
        SET likes_count = likes_count - 1 
        WHERE id = OLD.post_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_post_likes_count
    AFTER INSERT OR DELETE ON post_likes
    FOR EACH ROW EXECUTE FUNCTION update_post_likes_count();

-- Function to update comments count
CREATE OR REPLACE FUNCTION update_post_comments_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE community_posts 
        SET comments_count = comments_count + 1 
        WHERE id = NEW.post_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE community_posts 
        SET comments_count = comments_count - 1 
        WHERE id = OLD.post_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_post_comments_count
    AFTER INSERT OR DELETE ON post_comments
    FOR EACH ROW EXECUTE FUNCTION update_post_comments_count();

-- Function to update product ratings
CREATE OR REPLACE FUNCTION update_product_rating()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE products SET 
        rating = (SELECT AVG(rating)::numeric(3,2) FROM product_reviews WHERE product_id = NEW.product_id),
        review_count = (SELECT COUNT(*) FROM product_reviews WHERE product_id = NEW.product_id),
        updated_at = NOW()
    WHERE id = NEW.product_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_product_rating
    AFTER INSERT OR UPDATE OR DELETE ON product_reviews
    FOR EACH ROW EXECUTE FUNCTION update_product_rating();

-- Function to update inventory on order
CREATE OR REPLACE FUNCTION update_inventory_on_order()
RETURNS TRIGGER AS $$
DECLARE
    v_previous_quantity INTEGER;
    v_new_quantity INTEGER;
BEGIN
    -- Get current stock quantity
    SELECT stock_quantity INTO v_previous_quantity FROM products WHERE id = NEW.product_id;
    
    -- Calculate new quantity
    v_new_quantity := v_previous_quantity - NEW.quantity;
    
    -- Update product stock
    UPDATE products SET stock_quantity = v_new_quantity, updated_at = NOW() WHERE id = NEW.product_id;
    
    -- Record inventory transaction
    INSERT INTO inventory_transactions (
        product_id,
        variant_id,
        transaction_type,
        quantity_change,
        previous_quantity,
        new_quantity,
        reference_id,
        reference_type
    ) VALUES (
        NEW.product_id,
        NEW.variant_id,
        'sale',
        -NEW.quantity,
        v_previous_quantity,
        v_new_quantity,
        NEW.order_id,
        'order'
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_inventory_on_order
    AFTER INSERT ON order_items
    FOR EACH ROW EXECUTE FUNCTION update_inventory_on_order();

-- ==========================================
-- ROW LEVEL SECURITY POLICIES
-- ==========================================

-- Enable Row Level Security on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE crop_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE community_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_follows ENABLE ROW LEVEL SECURITY;
ALTER TABLE direct_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE community_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE event_rsvps ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_variants ENABLE ROW LEVEL SECURITY;
ALTER TABLE shopping_carts ENABLE ROW LEVEL SECURITY;
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE wishlists ENABLE ROW LEVEL SECURITY;
ALTER TABLE wishlist_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE points_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE points_balances ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_streaks ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_notification_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE push_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE scheduled_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE weather_alerts ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view their own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

-- User profiles policies
CREATE POLICY "Users can access their own profile" ON user_profiles
  FOR ALL USING (auth.uid() = id);

-- Crop plans policies
CREATE POLICY "Users can access their own crop plans" ON crop_plans
  FOR ALL USING (auth.uid() = user_id);

-- Tasks policies
CREATE POLICY "Users can access tasks for their crop plans" ON tasks
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM crop_plans 
      WHERE crop_plans.id = tasks.crop_plan_id 
      AND crop_plans.user_id = auth.uid()
    )
  );

-- Chat sessions policies
CREATE POLICY "Users can access their own chat sessions" ON chat_sessions
  FOR ALL USING (auth.uid() = user_id);

-- Chat messages policies
CREATE POLICY "Users can access messages from their sessions" ON chat_messages
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM chat_sessions 
      WHERE chat_sessions.id = chat_messages.session_id 
      AND chat_sessions.user_id = auth.uid()
    )
  );

-- Community posts policies
CREATE POLICY "Community posts are publicly readable" ON community_posts
  FOR SELECT USING (true);

CREATE POLICY "Users can create their own posts" ON community_posts
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own posts" ON community_posts
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own posts" ON community_posts
  FOR DELETE USING (auth.uid() = user_id);

-- Post comments policies
CREATE POLICY "Comments are publicly readable" ON post_comments
  FOR SELECT USING (true);

CREATE POLICY "Users can create comments" ON post_comments
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own comments" ON post_comments
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own comments" ON post_comments
  FOR DELETE USING (auth.uid() = user_id);

-- Products policies (public read access for e-commerce)
CREATE POLICY "Products are publicly readable" ON products
  FOR SELECT USING (true);

-- Orders policies
CREATE POLICY "Users can access their own orders" ON orders
  FOR ALL USING (auth.uid() = user_id);

-- Order items policies
CREATE POLICY "Users can access items from their orders" ON order_items
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM orders 
      WHERE orders.id = order_items.order_id 
      AND orders.user_id = auth.uid()
    )
  );

-- Post likes policies
CREATE POLICY "Users can view all post likes" ON post_likes FOR SELECT USING (true);
CREATE POLICY "Users can manage their own likes" ON post_likes FOR ALL USING (auth.uid() = user_id);

-- Post reports policies
CREATE POLICY "Users can create reports" ON post_reports FOR INSERT WITH CHECK (auth.uid() = reporter_id);
CREATE POLICY "Users can view their own reports" ON post_reports FOR SELECT USING (auth.uid() = reporter_id);

-- Post views policies
CREATE POLICY "Users can view all post views" ON post_views FOR SELECT USING (true);
CREATE POLICY "Users can track their own views" ON post_views FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Post shares policies
CREATE POLICY "Users can view all post shares" ON post_shares FOR SELECT USING (true);
CREATE POLICY "Users can manage their own shares" ON post_shares FOR ALL USING (auth.uid() = user_id);

-- User follows policies
CREATE POLICY "Users can view all follows" ON user_follows FOR SELECT USING (true);
CREATE POLICY "Users can manage their own follows" ON user_follows FOR ALL USING (auth.uid() = follower_id);

-- Direct messages policies
CREATE POLICY "Users can view their messages" ON direct_messages FOR SELECT USING (
    auth.uid() = sender_id OR auth.uid() = recipient_id
);
CREATE POLICY "Users can send messages" ON direct_messages FOR INSERT WITH CHECK (auth.uid() = sender_id);
CREATE POLICY "Users can update their received messages" ON direct_messages FOR UPDATE USING (
    auth.uid() = recipient_id
);

-- Community events policies
CREATE POLICY "Users can view public events" ON community_events FOR SELECT USING (is_public = true);
CREATE POLICY "Users can view their own events" ON community_events FOR SELECT USING (auth.uid() = organizer_id);
CREATE POLICY "Users can create events" ON community_events FOR INSERT WITH CHECK (auth.uid() = organizer_id);
CREATE POLICY "Users can update their own events" ON community_events FOR UPDATE USING (auth.uid() = organizer_id);
CREATE POLICY "Users can delete their own events" ON community_events FOR DELETE USING (auth.uid() = organizer_id);

-- Event RSVPs policies
CREATE POLICY "Users can view event RSVPs" ON event_rsvps FOR SELECT USING (true);
CREATE POLICY "Users can manage their own RSVPs" ON event_rsvps FOR ALL USING (auth.uid() = user_id);

-- Notifications RLS policies
CREATE POLICY "Users can view their own notifications" ON notifications FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update their own notifications" ON notifications FOR UPDATE USING (auth.uid() = user_id);

-- ==========================================
-- SAMPLE DATA
-- ==========================================

-- Insert default subscription plans
INSERT INTO subscription_plans (id, name, description, price, currency, billing_period, features, points_included, ai_consultations_limit, priority_support, offline_access, advanced_analytics, api_access, multi_farm_support, team_collaboration, is_active, sort_order)
VALUES 
    ('free', 'Free', 'Perfect for getting started with smart farming', 0, 'USD', 'monthly', 
     '[{"id": "basic_crop_planning", "name": "Basic Crop Planning", "description": "Create and manage basic crop plans", "category": "core", "is_premium": false}, {"id": "weather_alerts", "name": "Weather Alerts", "description": "Receive weather notifications", "category": "core", "is_premium": false}, {"id": "community_access", "name": "Community Access", "description": "Join farmer community discussions", "category": "core", "is_premium": false}, {"id": "limited_ai", "name": "Limited AI Consultations", "description": "5 AI consultations per month", "category": "ai", "is_premium": false}]'::jsonb,
     100, 5, false, false, false, false, false, false, true, 1),
    
    ('basic', 'Basic', 'Enhanced features for serious farmers', 9.99, 'USD', 'monthly',
     '[{"id": "advanced_crop_planning", "name": "Advanced Crop Planning", "description": "Detailed crop planning with AI recommendations", "category": "core", "is_premium": true}, {"id": "image_analysis", "name": "Image Analysis", "description": "AI-powered plant disease detection", "category": "ai", "is_premium": true}, {"id": "offline_access", "name": "Offline Access", "description": "Access core features without internet", "category": "core", "is_premium": true}, {"id": "priority_support", "name": "Priority Support", "description": "Faster response times for support", "category": "support", "is_premium": true}, {"id": "extended_ai", "name": "Extended AI Consultations", "description": "25 AI consultations per month", "category": "ai", "is_premium": true}]'::jsonb,
     500, 25, true, true, false, false, false, false, true, 2),
    
    ('premium', 'Premium', 'Complete farming solution with unlimited AI', 19.99, 'USD', 'monthly',
     '[{"id": "unlimited_ai", "name": "Unlimited AI Consultations", "description": "No limits on AI consultations", "category": "ai", "is_premium": true}, {"id": "advanced_analytics", "name": "Advanced Analytics", "description": "Detailed insights and reports", "category": "analytics", "is_premium": true}, {"id": "custom_recommendations", "name": "Custom Recommendations", "description": "Personalized farming recommendations", "category": "ai", "is_premium": true}, {"id": "api_access", "name": "API Access", "description": "Integrate with third-party tools", "category": "integration", "is_premium": true}, {"id": "export_data", "name": "Data Export", "description": "Export your farming data", "category": "core", "is_premium": true}]'::jsonb,
     1000, -1, true, true, true, true, false, false, true, 3),
    
    ('pro', 'Pro', 'Enterprise solution for large operations', 49.99, 'USD', 'monthly',
     '[{"id": "multi_farm_management", "name": "Multi-Farm Management", "description": "Manage multiple farm locations", "category": "core", "is_premium": true}, {"id": "team_collaboration", "name": "Team Collaboration", "description": "Share access with team members", "category": "collaboration", "is_premium": true}, {"id": "white_label", "name": "White Label Options", "description": "Custom branding options", "category": "integration", "is_premium": true}, {"id": "priority_development", "name": "Priority Development", "description": "Influence feature development", "category": "support", "is_premium": true}, {"id": "dedicated_support", "name": "Dedicated Support", "description": "Personal account manager", "category": "support", "is_premium": true}]'::jsonb,
     2500, -1, true, true, true, true, true, true, true, 4)
ON CONFLICT (name) DO NOTHING;

-- Insert sample products
INSERT INTO products (
    name,
    description,
    category,
    price,
    original_price,
    currency,
    image_urls,
    specifications,
    stock_quantity,
    rating,
    review_count,
    tags,
    brand,
    is_recommended,
    is_featured,
    is_active
) VALUES
-- Seeds Category
(
    'Hybrid Corn Seeds - Premium Variety',
    'High-yield hybrid corn seeds suitable for various soil types. Drought resistant with excellent germination rate of 95%. Perfect for commercial farming and home gardens.',
    'seeds',
    25.99,
    29.99,
    'USD',
    ARRAY['https://images.unsplash.com/photo-**********-deb4988cc6c0?w=400', 'https://images.unsplash.com/photo-*************-f5e1ad6d020b?w=400'],
    '[
        {"name": "Variety", "value": "Hybrid", "unit": ""},
        {"name": "Maturity", "value": "90-95", "unit": "days"},
        {"name": "Yield", "value": "8-10", "unit": "tons/hectare"},
        {"name": "Planting Season", "value": "Spring/Summer", "unit": ""},
        {"name": "Germination Rate", "value": "95", "unit": "%"}
    ]'::jsonb,
    150,
    4.5,
    89,
    ARRAY['drought-resistant', 'high-yield', 'hybrid', 'corn', 'commercial'],
    'AgriSeeds Pro',
    true,
    true,
    true
),
(
    'Organic Tomato Seeds - Cherry Variety',
    'Organic cherry tomato seeds with excellent disease resistance. Sweet flavor and high productivity. Ideal for greenhouse and outdoor cultivation.',
    'seeds',
    12.50,
    null,
    'USD',
    ARRAY['https://images.unsplash.com/photo-1592841200221-a6898f307baa?w=400'],
    '[
        {"name": "Type", "value": "Organic", "unit": ""},
        {"name": "Variety", "value": "Cherry", "unit": ""},
        {"name": "Fruit Size", "value": "Small", "unit": ""},
        {"name": "Days to Harvest", "value": "75-80", "unit": "days"},
        {"name": "Plant Height", "value": "1.5-2", "unit": "meters"}
    ]'::jsonb,
    200,
    4.7,
    156,
    ARRAY['organic', 'disease-resistant', 'greenhouse', 'tomato', 'cherry', 'sweet'],
    'EcoGrow',
    false,
    false,
    true
),

-- Fertilizers Category
(
    'NPK Fertilizer 20-20-20 - All Purpose',
    'Balanced NPK fertilizer suitable for all crops. Water-soluble formula for easy application. Promotes healthy growth and maximum yield.',
    'fertilizers',
    45.00,
    null,
    'USD',
    ARRAY['https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400'],
    '[
        {"name": "N-P-K Ratio", "value": "20-20-20", "unit": ""},
        {"name": "Form", "value": "Water Soluble", "unit": ""},
        {"name": "Package Size", "value": "25", "unit": "kg"},
        {"name": "Application Rate", "value": "2-3", "unit": "kg/hectare"},
        {"name": "Coverage", "value": "8-12", "unit": "hectares"}
    ]'::jsonb,
    75,
    4.3,
    67,
    ARRAY['balanced', 'water-soluble', 'all-crops', 'npk', 'growth-booster'],
    'FertMax',
    true,
    false,
    true
),

-- Tools Category
(
    'Garden Hand Trowel - Stainless Steel',
    'Durable stainless steel hand trowel with ergonomic handle. Perfect for planting, weeding, and transplanting. Rust-resistant and long-lasting.',
    'tools',
    18.99,
    null,
    'USD',
    ARRAY['https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400'],
    '[
        {"name": "Material", "value": "Stainless Steel", "unit": ""},
        {"name": "Handle", "value": "Ergonomic Grip", "unit": ""},
        {"name": "Length", "value": "30", "unit": "cm"},
        {"name": "Weight", "value": "200", "unit": "g"},
        {"name": "Warranty", "value": "2", "unit": "years"}
    ]'::jsonb,
    120,
    4.6,
    234,
    ARRAY['durable', 'ergonomic', 'stainless-steel', 'hand-tool', 'rust-resistant'],
    'GardenPro',
    false,
    true,
    true
),

-- Equipment Category
(
    'Drip Irrigation Kit - 50m Coverage',
    'Complete drip irrigation system for efficient water management. Includes drip lines, emitters, connectors, and timer. Covers up to 50 square meters.',
    'irrigation',
    89.99,
    109.99,
    'USD',
    ARRAY['https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400'],
    '[
        {"name": "Coverage", "value": "50", "unit": "m²"},
        {"name": "Drip Line Length", "value": "100", "unit": "m"},
        {"name": "Emitter Spacing", "value": "30", "unit": "cm"},
        {"name": "Flow Rate", "value": "2", "unit": "L/h per emitter"},
        {"name": "Timer Included", "value": "Yes", "unit": ""}
    ]'::jsonb,
    45,
    4.2,
    92,
    ARRAY['drip-irrigation', 'water-efficient', 'timer', 'complete-kit', 'water-management'],
    'IrriTech',
    true,
    true,
    true
);

-- Insert sample product reviews
INSERT INTO product_reviews (
    product_id,
    user_id,
    rating,
    title,
    comment,
    helpful_count,
    verified_purchase
) VALUES
-- Reviews for Hybrid Corn Seeds (assuming we have at least one user in the database)
(
    (SELECT id FROM products WHERE name = 'Hybrid Corn Seeds - Premium Variety' LIMIT 1),
    (SELECT id FROM users LIMIT 1),
    5,
    'Excellent germination rate!',
    'These seeds had a fantastic germination rate of over 95%. My corn crop was the best I''ve had in years. Highly recommend for commercial farming.',
    12,
    true
);

-- Insert sample achievements
INSERT INTO achievements (name, description, icon, category, points_reward, requirements, is_active) VALUES
('First Harvest', 'Complete your first crop harvest', '🌾', 'farming', 100, '{"crops_harvested": 1}', true),
('Community Builder', 'Create 5 posts in the community', '👥', 'community', 150, '{"posts_created": 5}', true),
('Weather Watcher', 'Check weather forecasts for 7 consecutive days', '☁️', 'consistency', 75, '{"weather_checks": 7}', true),
('Green Thumb', 'Successfully grow 3 different crop types', '🌱', 'farming', 200, '{"unique_crops": 3}', true),
('Knowledge Seeker', 'Complete 10 AI consultations', '🧠', 'learning', 125, '{"ai_consultations": 10}', true);

-- ==========================================
-- FULL TEXT SEARCH CONFIGURATION
-- ==========================================

-- Update products table to include search vector
CREATE OR REPLACE FUNCTION products_search_vector_update() RETURNS trigger AS $$
BEGIN
  NEW.search_vector := 
     setweight(to_tsvector('english', coalesce(NEW.name,'')), 'A') ||
     setweight(to_tsvector('english', coalesce(NEW.description,'')), 'B') ||
     setweight(to_tsvector('english', coalesce(NEW.category,'')), 'C') ||
     setweight(to_tsvector('english', coalesce(array_to_string(NEW.tags, ' '),'')), 'C');
  RETURN NEW;
END
$$ LANGUAGE plpgsql;

CREATE TRIGGER products_search_vector_update_trigger
BEFORE INSERT OR UPDATE ON products
FOR EACH ROW EXECUTE FUNCTION products_search_vector_update();

-- Create search function for products
CREATE OR REPLACE FUNCTION search_products(search_term TEXT)
RETURNS TABLE (id UUID, name VARCHAR, description TEXT, category VARCHAR, price DECIMAL, image_urls TEXT[], rank REAL) AS $$
BEGIN
  RETURN QUERY
  SELECT p.id, p.name, p.description, p.category, p.price, p.image_urls,
         ts_rank(p.search_vector, to_tsquery('english', search_term)) AS rank
  FROM products p
  WHERE p.search_vector @@ to_tsquery('english', search_term)
  AND p.is_active = true
  ORDER BY rank DESC;
END
$$ LANGUAGE plpgsql;

-- ==========================================
-- VIEWS
-- ==========================================

-- Create view for active products with reviews
CREATE OR REPLACE VIEW active_products_with_reviews AS
SELECT 
    p.id,
    p.name,
    p.description,
    p.category,
    p.price,
    p.original_price,
    p.image_urls,
    p.specifications,
    p.stock_quantity,
    p.rating,
    p.review_count,
    p.tags,
    p.brand,
    p.is_recommended,
    p.is_featured,
    COALESCE(json_agg(json_build_object(
        'id', pr.id,
        'rating', pr.rating,
        'title', pr.title,
        'comment', pr.comment,
        'helpful_count', pr.helpful_count,
        'created_at', pr.created_at
    )) FILTER (WHERE pr.id IS NOT NULL), '[]'::json) as reviews
FROM products p
LEFT JOIN product_reviews pr ON p.id = pr.product_id
WHERE p.is_active = true
GROUP BY p.id;

-- Create view for user activity summary
CREATE OR REPLACE VIEW user_activity_summary AS
SELECT 
    u.id,
    u.first_name,
    u.last_name,
    u.email,
    up.farm_location,
    up.crop_types,
    up.experience_level,
    up.points,
    up.subscription_tier,
    COUNT(DISTINCT cp.id) AS crop_plans_count,
    COUNT(DISTINCT t.id) AS tasks_count,
    COUNT(DISTINCT t.id) FILTER (WHERE t.completed = true) AS completed_tasks_count,
    COUNT(DISTINCT cp.id) AS community_posts_count,
    COUNT(DISTINCT o.id) AS orders_count,
    COALESCE(SUM(o.total_amount), 0) AS total_spent,
    MAX(o.created_at) AS last_order_date,
    MAX(u.created_at) AS member_since
FROM users u
LEFT JOIN user_profiles up ON u.id = up.id
LEFT JOIN crop_plans cp ON u.id = cp.user_id
LEFT JOIN tasks t ON cp.id = t.crop_plan_id
LEFT JOIN community_posts cp ON u.id = cp.user_id
LEFT JOIN orders o ON u.id = o.user_id
GROUP BY u.id, up.farm_location, up.crop_types, up.experience_level, up.points, up.subscription_tier;

-- ==========================================
-- FINAL SETUP
-- ==========================================

-- Refresh all materialized views
REFRESH MATERIALIZED VIEW IF EXISTS active_products_summary;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO anon;

-- Grant select permissions on public tables
GRANT SELECT ON products TO anon;
GRANT SELECT ON products TO authenticated;
GRANT SELECT ON community_posts TO anon;
GRANT SELECT ON community_posts TO authenticated;
GRANT SELECT ON post_comments TO anon;
GRANT SELECT ON post_comments TO authenticated;

-- Grant all permissions on user-specific tables to authenticated users
GRANT ALL ON users TO authenticated;
GRANT ALL ON user_profiles TO authenticated;
GRANT ALL ON crop_plans TO authenticated;
GRANT ALL ON tasks TO authenticated;
GRANT ALL ON chat_sessions TO authenticated;
GRANT ALL ON chat_messages TO authenticated;
GRANT ALL ON orders TO authenticated;
GRANT ALL ON order_items TO authenticated;
GRANT ALL ON shopping_carts TO authenticated;
GRANT ALL ON cart_items TO authenticated;
GRANT ALL ON wishlists TO authenticated;
GRANT ALL ON wishlist_items TO authenticated;
GRANT ALL ON points_transactions TO authenticated;
GRANT ALL ON points_balances TO authenticated;
GRANT ALL ON user_achievements TO authenticated;
GRANT ALL ON user_subscriptions TO authenticated;
GRANT ALL ON subscription_usage TO authenticated;
GRANT ALL ON daily_streaks TO authenticated;
GRANT ALL ON user_notification_preferences TO authenticated;
GRANT ALL ON push_tokens TO authenticated;
GRANT ALL ON notifications TO authenticated;
GRANT ALL ON scheduled_notifications TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION update_points_balance TO authenticated;
GRANT EXECUTE ON FUNCTION increment_usage TO authenticated;
GRANT EXECUTE ON FUNCTION search_products TO authenticated;
GRANT EXECUTE ON FUNCTION search_products TO anon;

-- Comment on database
COMMENT ON DATABASE postgres IS 'Smart Farming App Database';