import { create } from 'zustand';
import { ProfileService, GeoLocation, ProfileUpdateData } from '../services/supabase/profile';
import { Database } from '../types/database';

type UserProfile = Database['public']['Tables']['user_profiles']['Row'];

interface ProfileState {
    profile: UserProfile | null;
    isLoading: boolean;
    error: string | null;

    // Actions
    fetchProfile: (userId: string) => Promise<void>;
    updateProfile: (userId: string, updates: ProfileUpdateData) => Promise<boolean>;
    updateFarmLocation: (userId: string, location: GeoLocation) => Promise<boolean>;
    updateCropTypes: (userId: string, cropTypes: string[]) => Promise<boolean>;
    updateExperienceLevel: (userId: string, level: 'beginner' | 'intermediate' | 'expert') => Promise<boolean>;
    toggleVoiceMode: (userId: string, enabled: boolean) => Promise<boolean>;
    updateLanguage: (userId: string, language: string) => Promise<boolean>;
    addPoints: (userId: string, points: number) => Promise<boolean>;
    deductPoints: (userId: string, points: number) => Promise<boolean>;
    updateSubscription: (userId: string, tier: string, expiresAt?: Date) => Promise<boolean>;
    uploadProfileImage: (userId: string, imageUri: string, imageType: string) => Promise<string | null>;
    clearError: () => void;
    clearProfile: () => void;
}

export const useProfileStore = create<ProfileState>((set, get) => ({
    profile: null,
    isLoading: false,
    error: null,

    fetchProfile: async (userId: string) => {
        set({ isLoading: true, error: null });

        try {
            const { profile, error } = await ProfileService.getProfile(userId);

            if (error) {
                set({ error, isLoading: false });
                return;
            }

            set({ profile, isLoading: false, error: null });
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to fetch profile',
                isLoading: false,
            });
        }
    },

    updateProfile: async (userId: string, updates: ProfileUpdateData) => {
        set({ isLoading: true, error: null });

        try {
            const { profile, error } = await ProfileService.updateProfile(userId, updates);

            if (error) {
                set({ error, isLoading: false });
                return false;
            }

            set({ profile, isLoading: false, error: null });
            return true;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to update profile',
                isLoading: false,
            });
            return false;
        }
    },

    updateFarmLocation: async (userId: string, location: GeoLocation) => {
        set({ isLoading: true, error: null });

        try {
            const { success, error } = await ProfileService.updateFarmLocation(userId, location);

            if (error) {
                set({ error, isLoading: false });
                return false;
            }

            // Update local state
            const currentProfile = get().profile;
            if (currentProfile) {
                set({
                    profile: {
                        ...currentProfile,
                        farm_location: `POINT(${location.longitude} ${location.latitude})`,
                    },
                    isLoading: false,
                    error: null,
                });
            }

            return success;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to update farm location',
                isLoading: false,
            });
            return false;
        }
    },

    updateCropTypes: async (userId: string, cropTypes: string[]) => {
        set({ isLoading: true, error: null });

        try {
            const { success, error } = await ProfileService.updateCropTypes(userId, cropTypes);

            if (error) {
                set({ error, isLoading: false });
                return false;
            }

            // Update local state
            const currentProfile = get().profile;
            if (currentProfile) {
                set({
                    profile: { ...currentProfile, crop_types: cropTypes },
                    isLoading: false,
                    error: null,
                });
            }

            return success;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to update crop types',
                isLoading: false,
            });
            return false;
        }
    },

    updateExperienceLevel: async (userId: string, level: 'beginner' | 'intermediate' | 'expert') => {
        set({ isLoading: true, error: null });

        try {
            const { success, error } = await ProfileService.updateExperienceLevel(userId, level);

            if (error) {
                set({ error, isLoading: false });
                return false;
            }

            // Update local state
            const currentProfile = get().profile;
            if (currentProfile) {
                set({
                    profile: { ...currentProfile, experience_level: level },
                    isLoading: false,
                    error: null,
                });
            }

            return success;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to update experience level',
                isLoading: false,
            });
            return false;
        }
    },

    toggleVoiceMode: async (userId: string, enabled: boolean) => {
        set({ isLoading: true, error: null });

        try {
            const { success, error } = await ProfileService.toggleVoiceMode(userId, enabled);

            if (error) {
                set({ error, isLoading: false });
                return false;
            }

            // Update local state
            const currentProfile = get().profile;
            if (currentProfile) {
                set({
                    profile: { ...currentProfile, voice_enabled: enabled },
                    isLoading: false,
                    error: null,
                });
            }

            return success;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to toggle voice mode',
                isLoading: false,
            });
            return false;
        }
    },

    updateLanguage: async (userId: string, language: string) => {
        set({ isLoading: true, error: null });

        try {
            const { success, error } = await ProfileService.updateLanguage(userId, language);

            if (error) {
                set({ error, isLoading: false });
                return false;
            }

            // Update local state
            const currentProfile = get().profile;
            if (currentProfile) {
                set({
                    profile: { ...currentProfile, preferred_language: language },
                    isLoading: false,
                    error: null,
                });
            }

            return success;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to update language',
                isLoading: false,
            });
            return false;
        }
    },

    addPoints: async (userId: string, points: number) => {
        try {
            const { success, error } = await ProfileService.addPoints(userId, points);

            if (error) {
                set({ error });
                return false;
            }

            // Update local state
            const currentProfile = get().profile;
            if (currentProfile) {
                set({
                    profile: { ...currentProfile, points: currentProfile.points + points },
                    error: null,
                });
            }

            return success;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to add points',
            });
            return false;
        }
    },

    deductPoints: async (userId: string, points: number) => {
        try {
            const { success, error } = await ProfileService.deductPoints(userId, points);

            if (error) {
                set({ error });
                return false;
            }

            // Update local state only if deduction was successful
            if (success) {
                const currentProfile = get().profile;
                if (currentProfile) {
                    set({
                        profile: { ...currentProfile, points: Math.max(0, currentProfile.points - points) },
                        error: null,
                    });
                }
            }

            return success;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to deduct points',
            });
            return false;
        }
    },

    updateSubscription: async (userId: string, tier: string, expiresAt?: Date) => {
        set({ isLoading: true, error: null });

        try {
            const { success, error } = await ProfileService.updateSubscription(userId, tier, expiresAt);

            if (error) {
                set({ error, isLoading: false });
                return false;
            }

            // Update local state
            const currentProfile = get().profile;
            if (currentProfile) {
                set({
                    profile: {
                        ...currentProfile,
                        subscription_tier: tier,
                        subscription_expires_at: expiresAt?.toISOString() || null,
                    },
                    isLoading: false,
                    error: null,
                });
            }

            return success;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to update subscription',
                isLoading: false,
            });
            return false;
        }
    },

    uploadProfileImage: async (userId: string, imageUri: string, imageType: string) => {
        set({ isLoading: true, error: null });

        try {
            const { imageUrl, error } = await ProfileService.uploadProfileImage(userId, {
                uri: imageUri,
                type: imageType,
                name: `profile-${userId}`,
            });

            if (error) {
                set({ error, isLoading: false });
                return null;
            }

            set({ isLoading: false, error: null });
            return imageUrl;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to upload profile image',
                isLoading: false,
            });
            return null;
        }
    },

    clearError: () => {
        set({ error: null });
    },

    clearProfile: () => {
        set({ profile: null, error: null, isLoading: false });
    },
}));