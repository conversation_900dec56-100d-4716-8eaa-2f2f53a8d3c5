import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from './supabase/client';
import {
    NotificationData,
    NotificationPreferences,
    ScheduledNotification,
    NotificationAnalytics,
    PushToken,
    NotificationType,
    NotificationPriority,
    NotificationCategory,
} from '../types/notifications';

// Configure notification behavior
Notifications.setNotificationHandler({
    handleNotification: async (notification) => {
        const preferences = await getNotificationPreferences();
        const notificationType = notification.request.content.data?.type as NotificationType;

        // Check if notifications are enabled for this type
        const isEnabled = isNotificationTypeEnabled(notificationType, preferences);

        // Check quiet hours
        const isQuietHours = isInQuietHours(preferences);

        return {
            shouldShowAlert: isEnabled && !isQuietHours,
            shouldPlaySound: isEnabled && preferences.soundEnabled && !isQuietHours,
            shouldSetBadge: true,
        };
    },
});

class NotificationService {
    private static instance: NotificationService;
    private pushToken: string | null = null;
    private isInitialized = false;

    static getInstance(): NotificationService {
        if (!NotificationService.instance) {
            NotificationService.instance = new NotificationService();
        }
        return NotificationService.instance;
    }

    async initialize(): Promise<void> {
        if (this.isInitialized) return;

        try {
            // Request permissions
            await this.requestPermissions();

            // Get push token
            await this.registerForPushNotifications();

            // Set up notification listeners
            this.setupNotificationListeners();

            // Sync notification preferences
            await this.syncNotificationPreferences();

            this.isInitialized = true;
            console.log('Notification service initialized successfully');
        } catch (error) {
            console.error('Failed to initialize notification service:', error);
            throw error;
        }
    }

    async requestPermissions(): Promise<boolean> {
        if (!Device.isDevice) {
            console.warn('Push notifications only work on physical devices');
            return false;
        }

        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        let finalStatus = existingStatus;

        if (existingStatus !== 'granted') {
            const { status } = await Notifications.requestPermissionsAsync();
            finalStatus = status;
        }

        if (finalStatus !== 'granted') {
            console.warn('Push notification permissions not granted');
            return false;
        }

        return true;
    }

    async registerForPushNotifications(): Promise<string | null> {
        try {
            if (!Device.isDevice) {
                return null;
            }

            const token = await Notifications.getExpoPushTokenAsync({
                projectId: process.env.EXPO_PUBLIC_PROJECT_ID,
            });

            this.pushToken = token.data;

            // Store token in database
            await this.storePushToken(token.data);

            // Configure platform-specific settings
            if (Platform.OS === 'android') {
                await Notifications.setNotificationChannelAsync('default', {
                    name: 'Default',
                    importance: Notifications.AndroidImportance.MAX,
                    vibrationPattern: [0, 250, 250, 250],
                    lightColor: '#22c55e',
                });

                // Create specific channels for different notification types
                await this.createNotificationChannels();
            }

            return token.data;
        } catch (error) {
            console.error('Failed to register for push notifications:', error);
            return null;
        }
    }

    private async createNotificationChannels(): Promise<void> {
        const channels = [
            {
                id: 'weather_alerts',
                name: 'Weather Alerts',
                importance: Notifications.AndroidImportance.HIGH,
                description: 'Critical weather alerts for farming',
                sound: 'weather_alert.wav',
            },
            {
                id: 'task_reminders',
                name: 'Task Reminders',
                importance: Notifications.AndroidImportance.DEFAULT,
                description: 'Daily farming task reminders',
            },
            {
                id: 'crop_updates',
                name: 'Crop Updates',
                importance: Notifications.AndroidImportance.DEFAULT,
                description: 'Crop growth stage updates',
            },
            {
                id: 'emergency_alerts',
                name: 'Emergency Alerts',
                importance: Notifications.AndroidImportance.MAX,
                description: 'Critical emergency notifications',
                sound: 'emergency_alert.wav',
                vibrationPattern: [0, 500, 200, 500],
            },
            {
                id: 'community',
                name: 'Community',
                importance: Notifications.AndroidImportance.LOW,
                description: 'Community posts and interactions',
            },
        ];

        for (const channel of channels) {
            await Notifications.setNotificationChannelAsync(channel.id, channel);
        }
    }

    private setupNotificationListeners(): void {
        // Handle notification received while app is in foreground
        Notifications.addNotificationReceivedListener((notification) => {
            this.handleNotificationReceived(notification);
        });

        // Handle notification tapped
        Notifications.addNotificationResponseReceivedListener((response) => {
            this.handleNotificationResponse(response);
        });
    }

    private async handleNotificationReceived(notification: Notifications.Notification): Promise<void> {
        try {
            const notificationData = notification.request.content.data as any;

            // Track analytics
            await this.trackNotificationAnalytics({
                notificationId: notificationData.id || notification.request.identifier,
                userId: notificationData.userId,
                type: notificationData.type,
                sentAt: new Date(),
                deliveredAt: new Date(),
                deviceInfo: {
                    platform: Platform.OS,
                    version: Platform.Version.toString(),
                },
            });

            // Update notification status in database
            if (notificationData.id) {
                await this.updateNotificationStatus(notificationData.id, 'delivered');
            }
        } catch (error) {
            console.error('Error handling notification received:', error);
        }
    }

    private async handleNotificationResponse(response: Notifications.NotificationResponse): Promise<void> {
        try {
            const notificationData = response.notification.request.content.data as any;

            // Track analytics
            await this.trackNotificationAnalytics({
                notificationId: notificationData.id || response.notification.request.identifier,
                userId: notificationData.userId,
                type: notificationData.type,
                sentAt: new Date(response.notification.date),
                deliveredAt: new Date(response.notification.date),
                openedAt: new Date(),
                actionTaken: response.actionIdentifier !== Notifications.DEFAULT_ACTION_IDENTIFIER,
                deviceInfo: {
                    platform: Platform.OS,
                    version: Platform.Version.toString(),
                },
            });

            // Update notification status in database
            if (notificationData.id) {
                await this.updateNotificationStatus(notificationData.id, 'read');
            }

            // Handle specific notification actions
            await this.handleNotificationAction(notificationData, response.actionIdentifier);
        } catch (error) {
            console.error('Error handling notification response:', error);
        }
    }

    private async handleNotificationAction(notificationData: any, actionIdentifier: string): Promise<void> {
        // Handle different notification actions based on type and action identifier
        switch (notificationData.type) {
            case 'weather_alert':
                if (actionIdentifier === 'view_details') {
                    // Navigate to weather details
                } else if (actionIdentifier === 'dismiss') {
                    // Mark as dismissed
                }
                break;
            case 'task_reminder':
                if (actionIdentifier === 'mark_complete') {
                    // Mark task as complete
                } else if (actionIdentifier === 'snooze') {
                    // Snooze for 1 hour
                    await this.snoozeNotification(notificationData.id, 3600);
                }
                break;
            // Add more cases as needed
        }
    }

    async sendLocalNotification(
        title: string,
        body: string,
        data?: Record<string, any>,
        options?: {
            sound?: string;
            badge?: number;
            priority?: NotificationPriority;
            categoryId?: string;
        }
    ): Promise<string> {
        try {
            const identifier = await Notifications.scheduleNotificationAsync({
                content: {
                    title,
                    body,
                    data: data || {},
                    sound: options?.sound || 'default',
                    badge: options?.badge,
                    priority: this.mapPriorityToAndroid(options?.priority || 'normal'),
                    categoryIdentifier: options?.categoryId,
                },
                trigger: null, // Send immediately
            });

            return identifier;
        } catch (error) {
            console.error('Failed to send local notification:', error);
            throw error;
        }
    }

    async scheduleNotification(notification: ScheduledNotification): Promise<string> {
        try {
            const trigger = this.buildNotificationTrigger(notification.trigger);

            const identifier = await Notifications.scheduleNotificationAsync({
                content: {
                    title: notification.content.title,
                    body: notification.content.body,
                    data: notification.content.data || {},
                    sound: notification.content.sound || 'default',
                    badge: notification.content.badge,
                },
                trigger,
            });

            // Store scheduled notification in database
            await this.storeScheduledNotification({
                ...notification,
                identifier,
            });

            return identifier;
        } catch (error) {
            console.error('Failed to schedule notification:', error);
            throw error;
        }
    }

    private buildNotificationTrigger(trigger: ScheduledNotification['trigger']): any {
        switch (trigger.type) {
            case 'date':
                return {
                    date: trigger.date,
                };
            case 'timeInterval':
                return {
                    seconds: trigger.seconds,
                    repeats: trigger.repeats || false,
                };
            case 'calendar':
                return {
                    hour: trigger.hour,
                    minute: trigger.minute,
                    weekday: trigger.weekday,
                    repeats: trigger.repeats || false,
                };
            default:
                return null;
        }
    }

    async cancelNotification(identifier: string): Promise<void> {
        try {
            await Notifications.cancelScheduledNotificationAsync(identifier);
            await this.removeScheduledNotification(identifier);
        } catch (error) {
            console.error('Failed to cancel notification:', error);
        }
    }

    async cancelAllNotifications(): Promise<void> {
        try {
            await Notifications.cancelAllScheduledNotificationsAsync();
            await this.clearAllScheduledNotifications();
        } catch (error) {
            console.error('Failed to cancel all notifications:', error);
        }
    }

    async getScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
        try {
            return await Notifications.getAllScheduledNotificationsAsync();
        } catch (error) {
            console.error('Failed to get scheduled notifications:', error);
            return [];
        }
    }

    async updateNotificationPreferences(preferences: Partial<NotificationPreferences>): Promise<void> {
        try {
            const currentPreferences = await getNotificationPreferences();
            const updatedPreferences = { ...currentPreferences, ...preferences };

            await AsyncStorage.setItem('notification_preferences', JSON.stringify(updatedPreferences));

            // Sync with backend
            const { data: { user } } = await supabase.auth.getUser();
            if (user) {
                await supabase
                    .from('user_notification_preferences')
                    .upsert({
                        user_id: user.id,
                        preferences: updatedPreferences,
                        updated_at: new Date().toISOString(),
                    });
            }
        } catch (error) {
            console.error('Failed to update notification preferences:', error);
            throw error;
        }
    }

    async getNotificationHistory(limit: number = 50): Promise<NotificationData[]> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return [];

            const { data, error } = await supabase
                .from('notifications')
                .select('*')
                .eq('user_id', user.id)
                .order('created_at', { ascending: false })
                .limit(limit);

            if (error) throw error;
            return data || [];
        } catch (error) {
            console.error('Failed to get notification history:', error);
            return [];
        }
    }

    async markNotificationAsRead(notificationId: string): Promise<void> {
        try {
            await this.updateNotificationStatus(notificationId, 'read');
        } catch (error) {
            console.error('Failed to mark notification as read:', error);
        }
    }

    private async storePushToken(token: string): Promise<void> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return;

            const deviceId = await this.getDeviceId();

            await supabase
                .from('push_tokens')
                .upsert({
                    user_id: user.id,
                    token,
                    platform: Platform.OS as 'ios' | 'android',
                    device_id: deviceId,
                    is_active: true,
                    updated_at: new Date().toISOString(),
                });
        } catch (error) {
            console.error('Failed to store push token:', error);
        }
    }

    private async getDeviceId(): Promise<string> {
        try {
            let deviceId = await AsyncStorage.getItem('device_id');
            if (!deviceId) {
                deviceId = `${Platform.OS}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                await AsyncStorage.setItem('device_id', deviceId);
            }
            return deviceId;
        } catch (error) {
            console.error('Failed to get device ID:', error);
            return `${Platform.OS}_${Date.now()}`;
        }
    }

    private async syncNotificationPreferences(): Promise<void> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return;

            const { data } = await supabase
                .from('user_notification_preferences')
                .select('preferences')
                .eq('user_id', user.id)
                .single();

            if (data?.preferences) {
                await AsyncStorage.setItem('notification_preferences', JSON.stringify(data.preferences));
            }
        } catch (error) {
            console.error('Failed to sync notification preferences:', error);
        }
    }

    private async storeScheduledNotification(notification: ScheduledNotification): Promise<void> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return;

            await supabase
                .from('scheduled_notifications')
                .insert({
                    user_id: user.id,
                    identifier: notification.identifier,
                    notification_data: notification,
                    created_at: new Date().toISOString(),
                });
        } catch (error) {
            console.error('Failed to store scheduled notification:', error);
        }
    }

    private async removeScheduledNotification(identifier: string): Promise<void> {
        try {
            await supabase
                .from('scheduled_notifications')
                .delete()
                .eq('identifier', identifier);
        } catch (error) {
            console.error('Failed to remove scheduled notification:', error);
        }
    }

    private async clearAllScheduledNotifications(): Promise<void> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return;

            await supabase
                .from('scheduled_notifications')
                .delete()
                .eq('user_id', user.id);
        } catch (error) {
            console.error('Failed to clear all scheduled notifications:', error);
        }
    }

    private async updateNotificationStatus(notificationId: string, status: 'sent' | 'delivered' | 'read'): Promise<void> {
        try {
            const updateData: any = { status };

            if (status === 'delivered') {
                updateData.delivered_at = new Date().toISOString();
            } else if (status === 'read') {
                updateData.read_at = new Date().toISOString();
            }

            await supabase
                .from('notifications')
                .update(updateData)
                .eq('id', notificationId);
        } catch (error) {
            console.error('Failed to update notification status:', error);
        }
    }

    private async trackNotificationAnalytics(analytics: NotificationAnalytics): Promise<void> {
        try {
            await supabase
                .from('notification_analytics')
                .insert(analytics);
        } catch (error) {
            console.error('Failed to track notification analytics:', error);
        }
    }

    private async snoozeNotification(notificationId: string, seconds: number): Promise<void> {
        try {
            // Get original notification data
            const { data } = await supabase
                .from('notifications')
                .select('*')
                .eq('id', notificationId)
                .single();

            if (data) {
                // Schedule a new notification
                await this.scheduleNotification({
                    id: `${notificationId}_snoozed`,
                    identifier: '',
                    content: {
                        title: data.title,
                        body: data.body,
                        data: data.data,
                    },
                    trigger: {
                        type: 'timeInterval',
                        seconds,
                        repeats: false,
                    },
                });
            }
        } catch (error) {
            console.error('Failed to snooze notification:', error);
        }
    }

    private mapPriorityToAndroid(priority: NotificationPriority): Notifications.AndroidImportance {
        switch (priority) {
            case 'low':
                return Notifications.AndroidImportance.LOW;
            case 'normal':
                return Notifications.AndroidImportance.DEFAULT;
            case 'high':
                return Notifications.AndroidImportance.HIGH;
            case 'critical':
                return Notifications.AndroidImportance.MAX;
            default:
                return Notifications.AndroidImportance.DEFAULT;
        }
    }

    getPushToken(): string | null {
        return this.pushToken;
    }

    isInitialized(): boolean {
        return this.isInitialized;
    }
}

// Helper functions
export async function getNotificationPreferences(): Promise<NotificationPreferences> {
    try {
        const stored = await AsyncStorage.getItem('notification_preferences');
        if (stored) {
            return JSON.parse(stored);
        }
    } catch (error) {
        console.error('Failed to get notification preferences:', error);
    }

    // Return default preferences
    return {
        weatherAlerts: true,
        taskReminders: true,
        cropStageUpdates: true,
        communityUpdates: true,
        emergencyAlerts: true,
        aiInsights: true,
        marketingPromotions: false,
        soundEnabled: true,
        vibrationEnabled: true,
        quietHoursStart: '22:00',
        quietHoursEnd: '07:00',
    };
}

export function isNotificationTypeEnabled(type: NotificationType, preferences: NotificationPreferences): boolean {
    switch (type) {
        case 'weather_alert':
            return preferences.weatherAlerts;
        case 'task_reminder':
            return preferences.taskReminders;
        case 'crop_stage_update':
            return preferences.cropStageUpdates;
        case 'community_post':
            return preferences.communityUpdates;
        case 'emergency_alert':
            return preferences.emergencyAlerts;
        case 'ai_insight':
            return preferences.aiInsights;
        case 'subscription_reminder':
        case 'points_earned':
        case 'achievement_unlocked':
            return preferences.marketingPromotions;
        default:
            return true;
    }
}

export function isInQuietHours(preferences: NotificationPreferences): boolean {
    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

    const { quietHoursStart, quietHoursEnd } = preferences;

    // Handle cases where quiet hours span midnight
    if (quietHoursStart > quietHoursEnd) {
        return currentTime >= quietHoursStart || currentTime <= quietHoursEnd;
    } else {
        return currentTime >= quietHoursStart && currentTime <= quietHoursEnd;
    }
}

export const notificationService = NotificationService.getInstance();
export default notificationService;