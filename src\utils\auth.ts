import { AuthError } from '@supabase/supabase-js';

/**
 * Validates email format
 */
export const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};

/**
 * Validates phone number format (international format)
 */
export const isValidPhone = (phone: string): boolean => {
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    return phoneRegex.test(phone);
};

/**
 * Validates password strength
 */
export const isValidPassword = (password: string): boolean => {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
    return passwordRegex.test(password);
};

/**
 * Gets password strength score (0-4)
 */
export const getPasswordStrength = (password: string): number => {
    let score = 0;

    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/\d/.test(password)) score++;
    if (/[@$!%*?&]/.test(password)) score++;

    return Math.min(score, 4);
};

/**
 * Gets password strength label
 */
export const getPasswordStrengthLabel = (score: number): string => {
    const labels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
    return labels[score] || 'Very Weak';
};

/**
 * Formats phone number for display
 */
export const formatPhoneNumber = (phone: string): string => {
    // Remove all non-digit characters except +
    const cleaned = phone.replace(/[^\d+]/g, '');

    // Add + if not present and starts with country code
    if (!cleaned.startsWith('+') && cleaned.length > 10) {
        return `+${cleaned}`;
    }

    return cleaned;
};

/**
 * Validates OTP code format
 */
export const isValidOTP = (otp: string): boolean => {
    const otpRegex = /^\d{6}$/;
    return otpRegex.test(otp);
};

/**
 * Formats authentication error messages for user display
 */
export const formatAuthError = (error: AuthError | Error | string): string => {
    if (typeof error === 'string') {
        return error;
    }

    const message = error.message || 'An unexpected error occurred';

    // Map common Supabase auth errors to user-friendly messages
    const errorMappings: Record<string, string> = {
        'Invalid login credentials': 'Invalid email/phone or password. Please check your credentials and try again.',
        'Email not confirmed': 'Please check your email and click the confirmation link before signing in.',
        'Phone not confirmed': 'Please verify your phone number with the OTP code sent to you.',
        'User already registered': 'An account with this email/phone already exists. Please sign in instead.',
        'Signup not allowed for this instance': 'New registrations are currently disabled. Please contact support.',
        'Password should be at least 6 characters': 'Password must be at least 6 characters long.',
        'Unable to validate email address: invalid format': 'Please enter a valid email address.',
        'Phone number is not valid': 'Please enter a valid phone number with country code.',
        'Token has expired or is invalid': 'The verification code has expired. Please request a new one.',
        'Too many requests': 'Too many attempts. Please wait a few minutes before trying again.',
        'Network request failed': 'Network error. Please check your internet connection and try again.',
    };

    return errorMappings[message] || message;
};

/**
 * Checks if user session is expired
 */
export const isSessionExpired = (expiresAt?: number): boolean => {
    if (!expiresAt) return true;
    return Date.now() / 1000 > expiresAt;
};

/**
 * Gets time until session expires (in minutes)
 */
export const getSessionTimeRemaining = (expiresAt?: number): number => {
    if (!expiresAt) return 0;
    const remainingSeconds = expiresAt - Date.now() / 1000;
    return Math.max(0, Math.floor(remainingSeconds / 60));
};

/**
 * Generates a secure random password
 */
export const generateSecurePassword = (length: number = 12): string => {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@$!%*?&';
    let password = '';

    // Ensure at least one character from each required category
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '@$!%*?&';

    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];

    // Fill the rest randomly
    for (let i = password.length; i < length; i++) {
        password += charset[Math.floor(Math.random() * charset.length)];
    }

    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
};

/**
 * Sanitizes user input to prevent XSS
 */
export const sanitizeInput = (input: string): string => {
    return input
        .replace(/[<>]/g, '') // Remove < and > characters
        .trim(); // Remove leading/trailing whitespace
};

/**
 * Validates form data for registration
 */
export const validateRegistrationData = (data: {
    firstName: string;
    lastName: string;
    email?: string;
    phone?: string;
    password: string;
    confirmPassword: string;
}): string[] => {
    const errors: string[] = [];

    if (!data.firstName.trim()) {
        errors.push('First name is required');
    }

    if (!data.lastName.trim()) {
        errors.push('Last name is required');
    }

    if (!data.email && !data.phone) {
        errors.push('Either email or phone number is required');
    }

    if (data.email && !isValidEmail(data.email)) {
        errors.push('Please enter a valid email address');
    }

    if (data.phone && !isValidPhone(data.phone)) {
        errors.push('Please enter a valid phone number with country code');
    }

    if (!isValidPassword(data.password)) {
        errors.push('Password must be at least 8 characters with uppercase, lowercase, and number');
    }

    if (data.password !== data.confirmPassword) {
        errors.push('Passwords do not match');
    }

    return errors;
};

/**
 * Validates form data for login
 */
export const validateLoginData = (data: {
    identifier: string;
    password: string;
}): string[] => {
    const errors: string[] = [];

    if (!data.identifier.trim()) {
        errors.push('Email or phone number is required');
    }

    if (!data.password) {
        errors.push('Password is required');
    }

    // Check if identifier is email or phone
    const isEmail = data.identifier.includes('@');
    const isPhone = data.identifier.startsWith('+');

    if (isEmail && !isValidEmail(data.identifier)) {
        errors.push('Please enter a valid email address');
    }

    if (isPhone && !isValidPhone(data.identifier)) {
        errors.push('Please enter a valid phone number');
    }

    if (!isEmail && !isPhone) {
        errors.push('Please enter a valid email address or phone number');
    }

    return errors;
};