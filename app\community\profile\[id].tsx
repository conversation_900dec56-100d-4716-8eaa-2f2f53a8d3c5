import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    Image,
    Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../../../src/design-system';
import { CommunityPost } from '../../../src/components/community/CommunityPost';
import { VoiceButton } from '../../../src/components/ui/VoiceButton';
import { useCommunityStore } from '../../../src/stores/community';
import { useVoiceStore } from '../../../src/stores/voice';

interface FarmerProfile {
    id: string;
    name: string;
    avatar?: string;
    location: string;
    experienceLevel: 'beginner' | 'intermediate' | 'expert';
    joinedDate: Date;
    bio: string;
    cropTypes: string[];
    farmSize: string;
    achievements: string[];
    stats: {
        posts: number;
        likes: number;
        helpful: number;
        followers: number;
        following: number;
    };
    isFollowing: boolean;
}

// Mock profile data
const mockProfile: FarmerProfile = {
    id: 'user1',
    name: 'Ahmed Hassan',
    avatar: undefined,
    location: 'Cairo, Egypt',
    experienceLevel: 'intermediate',
    joinedDate: new Date('2023-01-15'),
    bio: 'Passionate farmer with 8 years of experience in sustainable agriculture. Specializing in corn and wheat production with modern irrigation techniques.',
    cropTypes: ['Corn', 'Wheat', 'Tomatoes'],
    farmSize: '25 acres',
    achievements: ['Top Contributor', 'Helpful Farmer', 'Sustainability Champion'],
    stats: {
        posts: 45,
        likes: 234,
        helpful: 89,
        followers: 156,
        following: 78,
    },
    isFollowing: false,
};

export default function FarmerProfileScreen() {
    const { id } = useLocalSearchParams<{ id: string }>();
    const [profile, setProfile] = useState<FarmerProfile | null>(null);
    const [userPosts, setUserPosts] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);

    const { posts, likePost, sharePost } = useCommunityStore();
    const { speak, isVoiceEnabled } = useVoiceStore();

    useEffect(() => {
        loadProfile();
    }, [id]);

    const loadProfile = async () => {
        try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000));

            setProfile(mockProfile);

            // Filter posts by user
            const filteredPosts = posts.filter(post => post.author.id === id);
            setUserPosts(filteredPosts);

            setLoading(false);
        } catch (error) {
            Alert.alert('Error', 'Failed to load profile');
            setLoading(false);
        }
    };

    const handleFollow = async () => {
        if (!profile) return;

        try {
            // Optimistic update
            setProfile(prev => prev ? {
                ...prev,
                isFollowing: !prev.isFollowing,
                stats: {
                    ...prev.stats,
                    followers: prev.isFollowing
                        ? prev.stats.followers - 1
                        : prev.stats.followers + 1,
                },
            } : null);

            if (isVoiceEnabled) {
                speak(profile.isFollowing ? 'Unfollowed farmer' : 'Following farmer');
            }

            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 500));
        } catch (error) {
            // Revert on error
            setProfile(prev => prev ? {
                ...prev,
                isFollowing: !prev.isFollowing,
                stats: {
                    ...prev.stats,
                    followers: prev.isFollowing
                        ? prev.stats.followers + 1
                        : prev.stats.followers - 1,
                },
            } : null);
            Alert.alert('Error', 'Failed to update follow status');
        }
    };

    const handleMessage = () => {
        if (isVoiceEnabled) {
            speak('Opening message conversation');
        }
        router.push(`/community/chat/${id}`);
    };

    const handleVoiceProfile = async () => {
        if (!profile || !isVoiceEnabled) return;

        const voiceText = `Profile of ${profile.name}, ${profile.experienceLevel} farmer from ${profile.location}. ${profile.bio}. Grows ${profile.cropTypes.join(', ')}. Has ${profile.stats.posts} posts and ${profile.stats.followers} followers.`;
        await speak(voiceText);
    };

    const formatJoinDate = (date: Date) => {
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long'
        });
    };

    if (loading) {
        return (
            <SafeAreaView style={{ flex: 1, backgroundColor: 'white' }}>
                <View style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                }}>
                    <Text style={{ color: colors.earth[500] }}>Loading profile...</Text>
                </View>
            </SafeAreaView>
        );
    }

    if (!profile) {
        return (
            <SafeAreaView style={{ flex: 1, backgroundColor: 'white' }}>
                <View style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                }}>
                    <Text style={{ color: colors.earth[500] }}>Profile not found</Text>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary[50] }}>
            {/* Header */}
            <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                paddingHorizontal: 16,
                paddingVertical: 12,
                backgroundColor: 'white',
                borderBottomWidth: 1,
                borderBottomColor: colors.primary[100],
            }}>
                <TouchableOpacity
                    onPress={() => router.back()}
                    style={{ marginRight: 16 }}
                    accessibilityLabel="Go back"
                >
                    <Ionicons name="arrow-back" size={24} color={colors.primary[900]} />
                </TouchableOpacity>

                <Text style={{
                    fontSize: 18,
                    fontWeight: '600',
                    color: colors.primary[900],
                    flex: 1,
                }}>
                    Farmer Profile
                </Text>

                <VoiceButton onPress={handleVoiceProfile} />
            </View>

            <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
                {/* Profile Header */}
                <View style={{
                    backgroundColor: 'white',
                    padding: 20,
                    alignItems: 'center',
                }}>
                    {/* Avatar */}
                    <View style={{
                        width: 100,
                        height: 100,
                        borderRadius: 50,
                        backgroundColor: colors.primary[100],
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginBottom: 16,
                    }}>
                        {profile.avatar ? (
                            <Image
                                source={{ uri: profile.avatar }}
                                style={{
                                    width: 100,
                                    height: 100,
                                    borderRadius: 50,
                                }}
                            />
                        ) : (
                            <Ionicons name="person" size={50} color={colors.primary[500]} />
                        )}
                    </View>

                    {/* Name and Location */}
                    <Text style={{
                        fontSize: 24,
                        fontWeight: 'bold',
                        color: colors.primary[900],
                        marginBottom: 4,
                    }}>
                        {profile.name}
                    </Text>

                    <View style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginBottom: 8,
                        gap: 8,
                    }}>
                        <Ionicons name="location" size={16} color={colors.earth[500]} />
                        <Text style={{
                            fontSize: 16,
                            color: colors.earth[600],
                        }}>
                            {profile.location}
                        </Text>
                    </View>

                    {/* Experience Level */}
                    <View style={{
                        backgroundColor: colors.primary[100],
                        paddingHorizontal: 12,
                        paddingVertical: 4,
                        borderRadius: 12,
                        marginBottom: 16,
                    }}>
                        <Text style={{
                            fontSize: 14,
                            color: colors.primary[700],
                            fontWeight: '600',
                            textTransform: 'capitalize',
                        }}>
                            {profile.experienceLevel} Farmer
                        </Text>
                    </View>

                    {/* Action Buttons */}
                    <View style={{
                        flexDirection: 'row',
                        gap: 12,
                        marginBottom: 16,
                    }}>
                        <TouchableOpacity
                            onPress={handleFollow}
                            style={{
                                backgroundColor: profile.isFollowing
                                    ? colors.earth[200]
                                    : colors.primary[500],
                                paddingHorizontal: 24,
                                paddingVertical: 10,
                                borderRadius: 20,
                                flex: 1,
                                alignItems: 'center',
                            }}
                            accessibilityLabel={profile.isFollowing ? 'Unfollow farmer' : 'Follow farmer'}
                        >
                            <Text style={{
                                color: profile.isFollowing ? colors.earth[700] : 'white',
                                fontWeight: '600',
                            }}>
                                {profile.isFollowing ? 'Following' : 'Follow'}
                            </Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            onPress={handleMessage}
                            style={{
                                backgroundColor: colors.secondary[500],
                                paddingHorizontal: 24,
                                paddingVertical: 10,
                                borderRadius: 20,
                                flex: 1,
                                alignItems: 'center',
                            }}
                            accessibilityLabel="Send message"
                        >
                            <Text style={{
                                color: 'white',
                                fontWeight: '600',
                            }}>
                                Message
                            </Text>
                        </TouchableOpacity>
                    </View>

                    {/* Stats */}
                    <View style={{
                        flexDirection: 'row',
                        justifyContent: 'space-around',
                        width: '100%',
                        paddingTop: 16,
                        borderTopWidth: 1,
                        borderTopColor: colors.primary[100],
                    }}>
                        {[
                            { label: 'Posts', value: profile.stats.posts },
                            { label: 'Likes', value: profile.stats.likes },
                            { label: 'Helpful', value: profile.stats.helpful },
                            { label: 'Followers', value: profile.stats.followers },
                        ].map((stat, index) => (
                            <View key={index} style={{ alignItems: 'center' }}>
                                <Text style={{
                                    fontSize: 20,
                                    fontWeight: 'bold',
                                    color: colors.primary[900],
                                }}>
                                    {stat.value}
                                </Text>
                                <Text style={{
                                    fontSize: 12,
                                    color: colors.earth[500],
                                    marginTop: 2,
                                }}>
                                    {stat.label}
                                </Text>
                            </View>
                        ))}
                    </View>
                </View>

                {/* Bio and Details */}
                <View style={{
                    backgroundColor: 'white',
                    marginTop: 8,
                    padding: 20,
                }}>
                    <Text style={{
                        fontSize: 18,
                        fontWeight: '600',
                        color: colors.primary[900],
                        marginBottom: 12,
                    }}>
                        About
                    </Text>

                    <Text style={{
                        fontSize: 16,
                        color: colors.earth[700],
                        lineHeight: 22,
                        marginBottom: 16,
                    }}>
                        {profile.bio}
                    </Text>

                    {/* Farm Details */}
                    <View style={{ gap: 12 }}>
                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            gap: 8,
                        }}>
                            <Ionicons name="leaf" size={16} color={colors.primary[500]} />
                            <Text style={{ color: colors.earth[600] }}>
                                Grows: {profile.cropTypes.join(', ')}
                            </Text>
                        </View>

                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            gap: 8,
                        }}>
                            <Ionicons name="resize" size={16} color={colors.primary[500]} />
                            <Text style={{ color: colors.earth[600] }}>
                                Farm Size: {profile.farmSize}
                            </Text>
                        </View>

                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            gap: 8,
                        }}>
                            <Ionicons name="calendar" size={16} color={colors.primary[500]} />
                            <Text style={{ color: colors.earth[600] }}>
                                Joined: {formatJoinDate(profile.joinedDate)}
                            </Text>
                        </View>
                    </View>

                    {/* Achievements */}
                    {profile.achievements.length > 0 && (
                        <View style={{ marginTop: 16 }}>
                            <Text style={{
                                fontSize: 16,
                                fontWeight: '600',
                                color: colors.primary[900],
                                marginBottom: 8,
                            }}>
                                Achievements
                            </Text>
                            <View style={{
                                flexDirection: 'row',
                                flexWrap: 'wrap',
                                gap: 8,
                            }}>
                                {profile.achievements.map((achievement, index) => (
                                    <View
                                        key={index}
                                        style={{
                                            backgroundColor: colors.secondary[100],
                                            paddingHorizontal: 12,
                                            paddingVertical: 6,
                                            borderRadius: 16,
                                        }}
                                    >
                                        <Text style={{
                                            fontSize: 12,
                                            color: colors.secondary[700],
                                            fontWeight: '500',
                                        }}>
                                            🏆 {achievement}
                                        </Text>
                                    </View>
                                ))}
                            </View>
                        </View>
                    )}
                </View>

                {/* User Posts */}
                <View style={{
                    backgroundColor: 'white',
                    marginTop: 8,
                    padding: 20,
                }}>
                    <Text style={{
                        fontSize: 18,
                        fontWeight: '600',
                        color: colors.primary[900],
                        marginBottom: 16,
                    }}>
                        Recent Posts ({userPosts.length})
                    </Text>

                    {userPosts.length === 0 ? (
                        <View style={{
                            alignItems: 'center',
                            paddingVertical: 20,
                        }}>
                            <Ionicons name="document-outline" size={48} color={colors.earth[300]} />
                            <Text style={{
                                fontSize: 16,
                                color: colors.earth[500],
                                marginTop: 8,
                            }}>
                                No posts yet
                            </Text>
                        </View>
                    ) : (
                        <View>
                            {userPosts.map((post) => (
                                <CommunityPost
                                    key={post.id}
                                    post={post}
                                    onPress={() => router.push(`/community/post/${post.id}`)}
                                    onLike={() => likePost(post.id)}
                                    onShare={() => sharePost(post.id)}
                                    onComment={() => router.push(`/community/post/${post.id}`)}
                                />
                            ))}
                        </View>
                    )}
                </View>
            </ScrollView>
        </SafeAreaView>
    );
}