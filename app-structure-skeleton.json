{"appInfo": {"name": "Smart Farming - المزرعة الذكية", "version": "1.0.0", "type": "React Native Expo App", "platform": ["iOS", "Android", "Web"], "languages": ["Arabic", "English"], "orientation": "portrait"}, "architecture": {"framework": "React Native with Expo", "router": "Expo Router", "styling": "NativeWind (Tailwind CSS)", "stateManagement": "Zustand (planned)", "navigation": "Stack + Tab Navigation"}, "features": {"authentication": {"registration": {"personalInfo": ["firstName", "lastName", "phone", "email"], "farmSetup": ["location", "cropTypes", "experienceLevel"], "voiceMode": true}, "login": {"methods": ["email", "phone", "social"], "socialProviders": ["Google", "Apple"], "features": ["rememberMe", "forgotPassword"]}}, "dashboard": {"weather": {"currentConditions": true, "forecast": true, "alerts": true, "voiceAnnouncements": true}, "tasks": {"dailyTasks": true, "completion": true, "pointsReward": true, "voiceReading": true}, "quickActions": ["analyzePlant", "voiceCommand", "viewProgress"]}, "aiAssistant": {"chat": {"textInput": true, "voiceInput": true, "imageCapture": true, "voicePlayback": true}, "consultationCategories": ["plantHealth", "soil", "fertilizer", "pests"], "imageAnalysis": {"captureTypes": ["plant", "soil", "fertilizer"], "analysisResults": true, "recommendations": true, "confidenceScores": true}}, "cropPlanning": {"planCreation": {"cropSelection": true, "plantingDates": true, "locationRecommendations": true}, "taskManagement": {"calendar": ["daily", "weekly"], "taskDetails": true, "completion": true, "photoEvidence": true}, "progressTracking": {"growthTimeline": true, "yieldPrediction": true, "harvestPlanning": true}}, "ecommerce": {"catalog": {"categories": ["seeds", "fertilizers", "tools"], "search": true, "filters": true, "voiceSearch": true}, "shopping": {"cart": true, "checkout": true, "orderTracking": true, "recommendations": true}}, "community": {"feed": {"posts": true, "images": true, "locationTags": true, "interactions": ["like", "comment", "share"]}, "messaging": {"directMessages": true, "userProfiles": true, "voiceInteractions": true}}, "subscription": {"plans": {"comparison": true, "purchase": true, "status": true}, "points": {"balance": true, "earning": true, "redemption": true, "history": true}}, "accessibility": {"voiceMode": {"tts": true, "voiceNavigation": true, "speedControl": true, "voiceSelection": true}, "visual": {"highContrast": true, "fontSize": true, "rtlSupport": true}, "navigation": {"keyboard": true, "screenReader": true}}, "offline": {"indicators": {"connectivity": true, "syncStatus": true, "pendingActions": true}, "cachedData": {"cropPlans": true, "tasks": true, "weather": true, "consultationHistory": true}}}, "fileStructure": {"root": {"config": ["app.json", "package.json", "tsconfig.json", "tailwind.config.js", "babel.config.js", "metro.config.js", "eslint.config.js", "prettier.config.js"], "environment": [".env", ".giti<PERSON>re"], "assets": ["icon.png", "splash.png", "adaptive-icon.png", "favicon.png"]}, "app": {"layout": "_layout.tsx", "entry": "index.tsx", "auth": {"screens": ["welcome.tsx", "register/personal-info.tsx", "register/farm-setup.tsx", "login.tsx", "forgot-password.tsx"]}, "main": {"tabs": ["(tabs)/home.tsx", "(tabs)/chat.tsx", "(tabs)/camera.tsx", "(tabs)/planning.tsx", "(tabs)/store.tsx"], "modals": ["task-detail.tsx", "analysis-results.tsx", "product-detail.tsx"]}}, "src": {"components": {"ui": ["Button.tsx", "Input.tsx", "Card.tsx", "Modal.tsx"], "forms": ["PersonalInfoForm.tsx", "FarmSetupForm.tsx"], "navigation": ["TabNavigation.tsx", "HeaderNavigation.tsx"], "chat": ["MessageBubble.tsx", "ChatInput.tsx", "VoiceRecorder.tsx"], "camera": ["CameraInterface.tsx", "AnalysisLoader.tsx", "ResultsDisplay.tsx"]}, "contexts": ["AuthContext.tsx", "VoiceModeContext.tsx", "LocationContext.tsx"], "hooks": ["useAuth.ts", "useVoice.ts", "useLocation.ts", "useCamera.ts", "useOffline.ts"], "services": ["api.ts", "auth.ts", "weather.ts", "ai.ts", "storage.ts"], "types": ["auth.ts", "weather.ts", "crops.ts", "chat.ts", "store.ts"], "utils": ["validation.ts", "formatting.ts", "voice.ts", "offline.ts"], "data": ["crops.ts", "tasks.ts", "products.ts"], "design-system": {"tokens": ["colors.ts", "typography.ts", "spacing.ts"], "components": ["theme.ts", "styles.ts"]}}}, "dependencies": {"core": ["expo", "react", "react-native", "expo-router"], "ui": ["nativewind", "react-native-safe-area-context", "react-native-reanimated"], "functionality": ["expo-location", "expo-speech", "@react-native-async-storage/async-storage", "expo-constants", "expo-localization"]}, "permissions": {"location": "Always and when in use", "camera": "Required for plant analysis", "microphone": "Required for voice features", "storage": "Required for offline functionality"}, "buildTargets": {"development": {"expo": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "production": {"prebuild": "expo prebuild", "build": "eas build"}}}