import { supabase } from './client';
import {
    PointsTransaction,
    PointsBalance,
    Achievement,
    UserAchievement,
    LeaderboardEntry,
    PointsRedemption,
    DailyStreak,
    POINTS_EARNING_RULES,
    DEFAULT_ACHIEVEMENTS
} from '../../types/points';
import { Database } from '../../types/database';

type PointsTransactionRow = Database['public']['Tables']['points_transactions']['Row'];
type PointsBalanceRow = Database['public']['Tables']['points_balances']['Row'];
type AchievementRow = Database['public']['Tables']['achievements']['Row'];
type UserAchievementRow = Database['public']['Tables']['user_achievements']['Row'];
type DailyStreakRow = Database['public']['Tables']['daily_streaks']['Row'];

export class PointsService {
    /**
     * Initialize points system for a new user
     */
    static async initializeUserPoints(userId: string): Promise<{ error: Error | null }> {
        try {
            // Create initial points balance
            const { error: balanceError } = await supabase
                .from('points_balances')
                .insert({
                    user_id: userId,
                    total_points: 0,
                    available_points: 0,
                    pending_points: 0,
                    lifetime_earned: 0,
                    lifetime_spent: 0,
                    last_updated: new Date().toISOString(),
                });

            if (balanceError) {
                throw balanceError;
            }

            // Initialize daily streak
            const { error: streakError } = await supabase
                .from('daily_streaks')
                .insert({
                    user_id: userId,
                    current_streak: 0,
                    longest_streak: 0,
                    last_activity_date: new Date().toISOString(),
                    streak_multiplier: 1.0,
                });

            if (streakError) {
                throw streakError;
            }

            // Award welcome bonus
            await this.awardPoints(userId, 50, 'subscription_bonus', 'Welcome bonus for new users');

            return { error: null };
        } catch (error) {
            return { error: error as Error };
        }
    }

    /**
     * Award points to a user
     */
    static async awardPoints(
        userId: string,
        amount: number,
        source: PointsTransaction['source'],
        description: string,
        metadata?: PointsTransaction['metadata']
    ): Promise<{ transaction: PointsTransaction | null; error: Error | null }> {
        try {
            // Check daily/weekly/monthly limits
            const canAward = await this.checkPointsLimits(userId, source, amount);
            if (!canAward) {
                throw new Error(`Points limit exceeded for source: ${source}`);
            }

            // Get current streak multiplier
            const { data: streakData } = await supabase
                .from('daily_streaks')
                .select('streak_multiplier')
                .eq('user_id', userId)
                .single();

            const multiplier = streakData?.streak_multiplier || 1.0;
            const finalAmount = Math.floor(amount * multiplier);

            // Create transaction
            const { data: transaction, error: transactionError } = await supabase
                .from('points_transactions')
                .insert({
                    user_id: userId,
                    amount: finalAmount,
                    transaction_type: 'earned',
                    source,
                    description,
                    metadata,
                })
                .select()
                .single();

            if (transactionError) {
                throw transactionError;
            }

            // Update points balance
            const { error: balanceError } = await supabase.rpc('update_points_balance', {
                p_user_id: userId,
                p_amount: finalAmount,
                p_transaction_type: 'earned'
            });

            if (balanceError) {
                throw balanceError;
            }

            // Check for achievements
            await this.checkAndAwardAchievements(userId);

            // Update daily streak if it's a daily activity
            if (['task_completion', 'daily_checkin', 'community_post'].includes(source)) {
                await this.updateDailyStreak(userId);
            }

            return { transaction: transaction as PointsTransaction, error: null };
        } catch (error) {
            return { transaction: null, error: error as Error };
        }
    }

    /**
     * Spend points for a user
     */
    static async spendPoints(
        userId: string,
        amount: number,
        source: PointsTransaction['source'],
        description: string,
        metadata?: PointsTransaction['metadata']
    ): Promise<{ transaction: PointsTransaction | null; error: Error | null }> {
        try {
            // Check if user has enough points
            const { data: balance } = await supabase
                .from('points_balances')
                .select('available_points')
                .eq('user_id', userId)
                .single();

            if (!balance || balance.available_points < amount) {
                throw new Error('Insufficient points');
            }

            // Create transaction
            const { data: transaction, error: transactionError } = await supabase
                .from('points_transactions')
                .insert({
                    user_id: userId,
                    amount: -amount,
                    transaction_type: 'spent',
                    source,
                    description,
                    metadata,
                })
                .select()
                .single();

            if (transactionError) {
                throw transactionError;
            }

            // Update points balance
            const { error: balanceError } = await supabase.rpc('update_points_balance', {
                p_user_id: userId,
                p_amount: -amount,
                p_transaction_type: 'spent'
            });

            if (balanceError) {
                throw balanceError;
            }

            return { transaction: transaction as PointsTransaction, error: null };
        } catch (error) {
            return { transaction: null, error: error as Error };
        }
    }

    /**
     * Get user's points balance
     */
    static async getPointsBalance(userId: string): Promise<{ balance: PointsBalance | null; error: Error | null }> {
        try {
            const { data, error } = await supabase
                .from('points_balances')
                .select('*')
                .eq('user_id', userId)
                .single();

            if (error) {
                throw error;
            }

            return { balance: data as PointsBalance, error: null };
        } catch (error) {
            return { balance: null, error: error as Error };
        }
    }

    /**
     * Get user's points transaction history
     */
    static async getPointsHistory(
        userId: string,
        limit: number = 50,
        offset: number = 0
    ): Promise<{ transactions: PointsTransaction[]; error: Error | null }> {
        try {
            const { data, error } = await supabase
                .from('points_transactions')
                .select('*')
                .eq('user_id', userId)
                .order('created_at', { ascending: false })
                .range(offset, offset + limit - 1);

            if (error) {
                throw error;
            }

            return { transactions: data as PointsTransaction[], error: null };
        } catch (error) {
            return { transactions: [], error: error as Error };
        }
    }

    /**
     * Get leaderboard
     */
    static async getLeaderboard(
        limit: number = 50,
        timeframe: 'weekly' | 'monthly' | 'all_time' = 'all_time'
    ): Promise<{ leaderboard: LeaderboardEntry[]; error: Error | null }> {
        try {
            let query = supabase
                .from('points_balances')
                .select(`
                    user_id,
                    total_points,
                    users!inner(first_name, last_name)
                `)
                .order('total_points', { ascending: false })
                .limit(limit);

            // Add time filtering for weekly/monthly
            if (timeframe !== 'all_time') {
                const startDate = new Date();
                if (timeframe === 'weekly') {
                    startDate.setDate(startDate.getDate() - 7);
                } else if (timeframe === 'monthly') {
                    startDate.setMonth(startDate.getMonth() - 1);
                }

                // This would require a more complex query with points_transactions
                // For now, we'll use all_time data
            }

            const { data, error } = await query;

            if (error) {
                throw error;
            }

            const leaderboard: LeaderboardEntry[] = data.map((entry: any, index: number) => ({
                user_id: entry.user_id,
                first_name: entry.users.first_name,
                last_name: entry.users.last_name,
                total_points: entry.total_points,
                rank: index + 1,
                achievements_count: 0, // Would need separate query
                completed_tasks: 0, // Would need separate query
                days_active: 0, // Would need separate query
            }));

            return { leaderboard, error: null };
        } catch (error) {
            return { leaderboard: [], error: error as Error };
        }
    }

    /**
     * Get user achievements
     */
    static async getUserAchievements(userId: string): Promise<{ achievements: UserAchievement[]; error: Error | null }> {
        try {
            const { data, error } = await supabase
                .from('user_achievements')
                .select(`
                    *,
                    achievements(*)
                `)
                .eq('user_id', userId)
                .order('earned_at', { ascending: false });

            if (error) {
                throw error;
            }

            return { achievements: data as UserAchievement[], error: null };
        } catch (error) {
            return { achievements: [], error: error as Error };
        }
    }

    /**
     * Get available achievements
     */
    static async getAvailableAchievements(): Promise<{ achievements: Achievement[]; error: Error | null }> {
        try {
            const { data, error } = await supabase
                .from('achievements')
                .select('*')
                .eq('is_active', true)
                .order('points_reward', { ascending: false });

            if (error) {
                throw error;
            }

            return { achievements: data as Achievement[], error: null };
        } catch (error) {
            return { achievements: [], error: error as Error };
        }
    }

    /**
     * Check and award achievements
     */
    private static async checkAndAwardAchievements(userId: string): Promise<void> {
        try {
            // Get user's current achievements
            const { data: userAchievements } = await supabase
                .from('user_achievements')
                .select('achievement_id')
                .eq('user_id', userId);

            const earnedAchievementIds = userAchievements?.map(ua => ua.achievement_id) || [];

            // Get all available achievements
            const { data: achievements } = await supabase
                .from('achievements')
                .select('*')
                .eq('is_active', true);

            if (!achievements) return;

            // Check each achievement
            for (const achievement of achievements) {
                if (earnedAchievementIds.includes(achievement.id)) {
                    continue; // Already earned
                }

                const requirements = achievement.requirements as Achievement['requirements'];
                const isEarned = await this.checkAchievementRequirements(userId, requirements);

                if (isEarned) {
                    // Award achievement
                    await supabase
                        .from('user_achievements')
                        .insert({
                            user_id: userId,
                            achievement_id: achievement.id,
                            points_awarded: achievement.points_reward,
                        });

                    // Award points for achievement
                    await this.awardPoints(
                        userId,
                        achievement.points_reward,
                        'achievement',
                        `Achievement earned: ${achievement.name}`,
                        { achievement_id: achievement.id }
                    );
                }
            }
        } catch (error) {
            console.error('Error checking achievements:', error);
        }
    }

    /**
     * Check if achievement requirements are met
     */
    private static async checkAchievementRequirements(
        userId: string,
        requirements: Achievement['requirements']
    ): Promise<boolean> {
        try {
            const { type, target_value, timeframe } = requirements;

            let query;
            let currentValue = 0;

            switch (type) {
                case 'task_count':
                    query = supabase
                        .from('tasks')
                        .select('id', { count: 'exact' })
                        .eq('completed', true)
                        .in('crop_plan_id',
                            supabase
                                .from('crop_plans')
                                .select('id')
                                .eq('user_id', userId)
                        );
                    break;

                case 'community_posts':
                    query = supabase
                        .from('community_posts')
                        .select('id', { count: 'exact' })
                        .eq('user_id', userId);
                    break;

                case 'ai_consultations':
                    query = supabase
                        .from('chat_messages')
                        .select('id', { count: 'exact' })
                        .eq('message_type', 'user')
                        .in('session_id',
                            supabase
                                .from('chat_sessions')
                                .select('id')
                                .eq('user_id', userId)
                        );
                    break;

                case 'points_earned':
                    const { data: balance } = await supabase
                        .from('points_balances')
                        .select('lifetime_earned')
                        .eq('user_id', userId)
                        .single();
                    currentValue = balance?.lifetime_earned || 0;
                    return currentValue >= target_value;

                case 'days_active':
                    const { data: streak } = await supabase
                        .from('daily_streaks')
                        .select('current_streak')
                        .eq('user_id', userId)
                        .single();
                    currentValue = streak?.current_streak || 0;
                    return currentValue >= target_value;

                default:
                    return false;
            }

            if (query) {
                const { count } = await query;
                currentValue = count || 0;
            }

            return currentValue >= target_value;
        } catch (error) {
            console.error('Error checking achievement requirements:', error);
            return false;
        }
    }

    /**
     * Update daily streak
     */
    private static async updateDailyStreak(userId: string): Promise<void> {
        try {
            const today = new Date().toISOString().split('T')[0];

            const { data: streak } = await supabase
                .from('daily_streaks')
                .select('*')
                .eq('user_id', userId)
                .single();

            if (!streak) return;

            const lastActivityDate = new Date(streak.last_activity_date).toISOString().split('T')[0];
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);
            const yesterdayStr = yesterday.toISOString().split('T')[0];

            let newStreak = streak.current_streak;
            let newLongestStreak = streak.longest_streak;

            if (lastActivityDate === today) {
                // Already active today, no change
                return;
            } else if (lastActivityDate === yesterdayStr) {
                // Consecutive day, increment streak
                newStreak += 1;
            } else {
                // Streak broken, reset to 1
                newStreak = 1;
            }

            if (newStreak > newLongestStreak) {
                newLongestStreak = newStreak;
            }

            // Calculate streak multiplier (max 2.0x at 30+ days)
            const multiplier = Math.min(1.0 + (newStreak * 0.033), 2.0);

            await supabase
                .from('daily_streaks')
                .update({
                    current_streak: newStreak,
                    longest_streak: newLongestStreak,
                    last_activity_date: new Date().toISOString(),
                    streak_multiplier: multiplier,
                })
                .eq('user_id', userId);
        } catch (error) {
            console.error('Error updating daily streak:', error);
        }
    }

    /**
     * Check points earning limits
     */
    private static async checkPointsLimits(
        userId: string,
        source: PointsTransaction['source'],
        amount: number
    ): Promise<boolean> {
        try {
            const rule = POINTS_EARNING_RULES.find(r => r.source === source);
            if (!rule) return true; // No limits defined

            const now = new Date();
            const today = now.toISOString().split('T')[0];

            // Check daily limit
            if (rule.daily_limit) {
                const { data: dailyTransactions } = await supabase
                    .from('points_transactions')
                    .select('amount')
                    .eq('user_id', userId)
                    .eq('source', source)
                    .gte('created_at', `${today}T00:00:00.000Z`)
                    .lt('created_at', `${today}T23:59:59.999Z`);

                const dailyTotal = dailyTransactions?.reduce((sum, t) => sum + Math.max(0, t.amount), 0) || 0;
                if (dailyTotal + amount > rule.daily_limit) {
                    return false;
                }
            }

            // Check weekly limit
            if (rule.weekly_limit) {
                const weekStart = new Date(now);
                weekStart.setDate(now.getDate() - now.getDay());
                weekStart.setHours(0, 0, 0, 0);

                const { data: weeklyTransactions } = await supabase
                    .from('points_transactions')
                    .select('amount')
                    .eq('user_id', userId)
                    .eq('source', source)
                    .gte('created_at', weekStart.toISOString());

                const weeklyTotal = weeklyTransactions?.reduce((sum, t) => sum + Math.max(0, t.amount), 0) || 0;
                if (weeklyTotal + amount > rule.weekly_limit) {
                    return false;
                }
            }

            // Check monthly limit
            if (rule.monthly_limit) {
                const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

                const { data: monthlyTransactions } = await supabase
                    .from('points_transactions')
                    .select('amount')
                    .eq('user_id', userId)
                    .eq('source', source)
                    .gte('created_at', monthStart.toISOString());

                const monthlyTotal = monthlyTransactions?.reduce((sum, t) => sum + Math.max(0, t.amount), 0) || 0;
                if (monthlyTotal + amount > rule.monthly_limit) {
                    return false;
                }
            }

            return true;
        } catch (error) {
            console.error('Error checking points limits:', error);
            return false;
        }
    }

    /**
     * Initialize default achievements in database
     */
    static async initializeDefaultAchievements(): Promise<{ error: Error | null }> {
        try {
            const { error } = await supabase
                .from('achievements')
                .upsert(
                    DEFAULT_ACHIEVEMENTS.map(achievement => ({
                        ...achievement,
                        id: achievement.name.toLowerCase().replace(/\s+/g, '_'),
                        created_at: new Date().toISOString(),
                    })),
                    { onConflict: 'name' }
                );

            return { error };
        } catch (error) {
            return { error: error as Error };
        }
    }
}