import { AIService, ImageAnalysisService } from './index';
import { createChatService } from './chatService';
import { createImageAnalysisService } from './imageAnalysis';
import { ChatMessage, ChatContext, ImageAnalysisResult } from '../../types/ai';
import { ComprehensiveAnalysisResult } from './comprehensiveImageAnalysis';

interface RateLimitConfig {
    maxRequestsPerMinute: number;
    maxRequestsPerHour: number;
    maxRequestsPerDay: number;
}

interface ServiceUsage {
    requestsThisMinute: number;
    requestsThisHour: number;
    requestsThisDay: number;
    lastMinuteReset: number;
    lastHourReset: number;
    lastDayReset: number;
}

export class AIServiceManager {
    private chatService: AIService;
    private imageAnalysisService: ImageAnalysisService;
    private rateLimitConfig: RateLimitConfig;
    private serviceUsage: ServiceUsage;

    constructor(rateLimitConfig?: Partial<RateLimitConfig>) {
        this.chatService = createChatService();
        this.imageAnalysisService = createImageAnalysisService();

        this.rateLimitConfig = {
            maxRequestsPerMinute: 10,
            maxRequestsPerHour: 100,
            maxRequestsPerDay: 500,
            ...rateLimitConfig
        };

        this.serviceUsage = {
            requestsThisMinute: 0,
            requestsThisHour: 0,
            requestsThisDay: 0,
            lastMinuteReset: Date.now(),
            lastHourReset: Date.now(),
            lastDayReset: Date.now()
        };
    }

    async sendChatMessage(message: string, context?: ChatContext): Promise<string> {
        await this.checkRateLimit();

        try {
            this.incrementUsage();
            const response = await this.chatService.sendMessage(message, context);
            return response;
        } catch (error) {
            console.error('Chat service error:', error);
            throw new Error('خدمة الذكاء الاصطناعي غير متاحة حالياً. يرجى المحاولة لاحقاً.');
        }
    }

    async analyzeImage(imageUri: string, category: 'plant' | 'soil' | 'fertilizer'): Promise<ImageAnalysisResult> {
        await this.checkRateLimit();

        try {
            this.incrementUsage();
            const result = await this.imageAnalysisService.analyzeImage(imageUri, category);
            return result;
        } catch (error) {
            console.error('Image analysis service error:', error);
            throw new Error('خدمة تحليل الصور غير متاحة حالياً. يرجى المحاولة لاحقاً.');
        }
    }

    async analyzeImageComprehensive(imageUri: string, category: 'plant' | 'soil' | 'fertilizer'): Promise<ComprehensiveAnalysisResult> {
        await this.checkRateLimit();

        try {
            this.incrementUsage();

            // Check if the service supports comprehensive analysis
            if (this.imageAnalysisService.analyzeImageComprehensive) {
                const result = await this.imageAnalysisService.analyzeImageComprehensive(imageUri, category);
                return result;
            } else {
                // Fallback to basic analysis and convert to comprehensive format
                const basicResult = await this.imageAnalysisService.analyzeImage(imageUri, category);
                return {
                    ...basicResult,
                    detailedAnalysis: {},
                    productRecommendations: [],
                    shoppingList: {
                        id: `list-${Date.now()}`,
                        items: [],
                        totalCost: 0,
                        estimatedDelivery: '2-3 أيام عمل',
                        recommendations: []
                    },
                    actionPlan: {
                        priority1: [],
                        priority2: [],
                        priority3: [],
                        timeline: '1-2 أسبوع',
                        totalCost: 0
                    },
                    followUpSchedule: {
                        immediate: [],
                        weekly: [],
                        monthly: []
                    }
                };
            }
        } catch (error) {
            console.error('Comprehensive image analysis service error:', error);
            throw new Error('خدمة التحليل الشامل للصور غير متاحة حالياً. يرجى المحاولة لاحقاً.');
        }
    }

    private async checkRateLimit(): Promise<void> {
        this.resetCountersIfNeeded();

        if (this.serviceUsage.requestsThisMinute >= this.rateLimitConfig.maxRequestsPerMinute) {
            const waitTime = 60 - Math.floor((Date.now() - this.serviceUsage.lastMinuteReset) / 1000);
            throw new Error(`تم تجاوز الحد الأقصى للطلبات في الدقيقة. يرجى الانتظار ${waitTime} ثانية.`);
        }

        if (this.serviceUsage.requestsThisHour >= this.rateLimitConfig.maxRequestsPerHour) {
            const waitTime = Math.ceil((60 * 60 * 1000 - (Date.now() - this.serviceUsage.lastHourReset)) / (60 * 1000));
            throw new Error(`تم تجاوز الحد الأقصى للطلبات في الساعة. يرجى الانتظار ${waitTime} دقيقة.`);
        }

        if (this.serviceUsage.requestsThisDay >= this.rateLimitConfig.maxRequestsPerDay) {
            const waitTime = Math.ceil((24 * 60 * 60 * 1000 - (Date.now() - this.serviceUsage.lastDayReset)) / (60 * 60 * 1000));
            throw new Error(`تم تجاوز الحد الأقصى للطلبات اليومية. يرجى الانتظار ${waitTime} ساعة.`);
        }
    }

    private resetCountersIfNeeded(): void {
        const now = Date.now();

        // Reset minute counter
        if (now - this.serviceUsage.lastMinuteReset >= 60 * 1000) {
            this.serviceUsage.requestsThisMinute = 0;
            this.serviceUsage.lastMinuteReset = now;
        }

        // Reset hour counter
        if (now - this.serviceUsage.lastHourReset >= 60 * 60 * 1000) {
            this.serviceUsage.requestsThisHour = 0;
            this.serviceUsage.lastHourReset = now;
        }

        // Reset day counter
        if (now - this.serviceUsage.lastDayReset >= 24 * 60 * 60 * 1000) {
            this.serviceUsage.requestsThisDay = 0;
            this.serviceUsage.lastDayReset = now;
        }
    }

    private incrementUsage(): void {
        this.serviceUsage.requestsThisMinute++;
        this.serviceUsage.requestsThisHour++;
        this.serviceUsage.requestsThisDay++;
    }

    // Get current usage statistics
    getUsageStats() {
        this.resetCountersIfNeeded();
        return {
            minute: {
                used: this.serviceUsage.requestsThisMinute,
                limit: this.rateLimitConfig.maxRequestsPerMinute,
                remaining: this.rateLimitConfig.maxRequestsPerMinute - this.serviceUsage.requestsThisMinute
            },
            hour: {
                used: this.serviceUsage.requestsThisHour,
                limit: this.rateLimitConfig.maxRequestsPerHour,
                remaining: this.rateLimitConfig.maxRequestsPerHour - this.serviceUsage.requestsThisHour
            },
            day: {
                used: this.serviceUsage.requestsThisDay,
                limit: this.rateLimitConfig.maxRequestsPerDay,
                remaining: this.rateLimitConfig.maxRequestsPerDay - this.serviceUsage.requestsThisDay
            }
        };
    }

    // Check if service is available
    isServiceAvailable(): boolean {
        this.resetCountersIfNeeded();
        return this.serviceUsage.requestsThisMinute < this.rateLimitConfig.maxRequestsPerMinute &&
            this.serviceUsage.requestsThisHour < this.rateLimitConfig.maxRequestsPerHour &&
            this.serviceUsage.requestsThisDay < this.rateLimitConfig.maxRequestsPerDay;
    }
}

// Singleton instance
let aiServiceManager: AIServiceManager | null = null;

export function getAIServiceManager(): AIServiceManager {
    if (!aiServiceManager) {
        aiServiceManager = new AIServiceManager();
    }
    return aiServiceManager;
}