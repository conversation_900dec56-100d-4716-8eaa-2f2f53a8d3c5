/**
 * AI-Based Product Recommendations Service
 * Integrates with AI analysis results to provide intelligent product suggestions
 */

import { supabase } from './supabase/client';
import { productCatalogService } from './productCatalog';
import {
    Product,
    ProductRecommendation,
    ProductCategory,
} from '../types/product';

export interface AIAnalysisResult {
    category: 'plant' | 'soil' | 'fertilizer';
    issues: Array<{
        type: string;
        severity: 'low' | 'medium' | 'high' | 'critical';
        confidence: number;
        description: string;
        treatment?: string;
    }>;
    recommendations: Array<{
        action: string;
        priority: 'low' | 'medium' | 'high';
        timeframe: string;
        products?: string[];
    }>;
    confidence: number;
    metadata?: any;
}

export interface RecommendationContext {
    userId: string;
    cropTypes?: string[];
    experienceLevel?: 'beginner' | 'intermediate' | 'expert';
    farmLocation?: { lat: number; lng: number };
    seasonalContext?: {
        season: 'spring' | 'summer' | 'fall' | 'winter';
        plantingPhase: 'preparation' | 'planting' | 'growing' | 'harvesting';
    };
    previousPurchases?: string[];
    budget?: {
        min: number;
        max: number;
    };
}

export class AIRecommendationsService {
    /**
     * Generate product recommendations based on AI analysis results
     */
    async generateRecommendationsFromAnalysis(
        analysisResult: AIAnalysisResult,
        context: RecommendationContext
    ): Promise<ProductRecommendation[]> {
        try {
            const recommendations: ProductRecommendation[] = [];

            // Process each issue identified in the analysis
            for (const issue of analysisResult.issues) {
                const issueRecommendations = await this.getProductsForIssue(
                    issue,
                    analysisResult.category,
                    context
                );
                recommendations.push(...issueRecommendations);
            }

            // Add preventive recommendations
            const preventiveRecommendations = await this.getPreventiveRecommendations(
                analysisResult,
                context
            );
            recommendations.push(...preventiveRecommendations);

            // Add complementary products
            const complementaryRecommendations = await this.getComplementaryProducts(
                recommendations.map(r => r.product.id),
                context
            );
            recommendations.push(...complementaryRecommendations);

            // Remove duplicates and sort by confidence
            const uniqueRecommendations = this.deduplicateRecommendations(recommendations);
            const sortedRecommendations = uniqueRecommendations
                .sort((a, b) => b.confidence - a.confidence)
                .slice(0, 10); // Limit to top 10 recommendations

            // Save recommendations to database for future reference
            await this.saveRecommendationsToDatabase(
                context.userId,
                sortedRecommendations,
                analysisResult
            );

            return sortedRecommendations;
        } catch (error) {
            console.error('Error generating recommendations from analysis:', error);
            return [];
        }
    }

    /**
     * Get seasonal product recommendations
     */
    async getSeasonalRecommendations(
        context: RecommendationContext
    ): Promise<ProductRecommendation[]> {
        try {
            const recommendations: ProductRecommendation[] = [];
            const season = context.seasonalContext?.season || this.getCurrentSeason();
            const phase = context.seasonalContext?.plantingPhase || 'preparation';

            // Get season-specific products
            const seasonalProducts = await this.getProductsBySeasonAndPhase(season, phase);

            for (const product of seasonalProducts) {
                const relevanceScore = this.calculateSeasonalRelevance(
                    product,
                    season,
                    phase,
                    context
                );

                if (relevanceScore > 0.5) {
                    recommendations.push({
                        product,
                        reason: this.generateSeasonalReason(season, phase, product.category),
                        confidence: relevanceScore,
                        relatedTo: 'seasonal',
                    });
                }
            }

            return recommendations.sort((a, b) => b.confidence - a.confidence).slice(0, 8);
        } catch (error) {
            console.error('Error getting seasonal recommendations:', error);
            return [];
        }
    }

    /**
     * Get recommendations based on user's crop planning
     */
    async getCropPlanningRecommendations(
        context: RecommendationContext
    ): Promise<ProductRecommendation[]> {
        try {
            if (!context.cropTypes || context.cropTypes.length === 0) {
                return [];
            }

            const recommendations: ProductRecommendation[] = [];

            for (const cropType of context.cropTypes) {
                const cropRecommendations = await this.getProductsForCrop(cropType, context);
                recommendations.push(...cropRecommendations);
            }

            return this.deduplicateRecommendations(recommendations)
                .sort((a, b) => b.confidence - a.confidence)
                .slice(0, 6);
        } catch (error) {
            console.error('Error getting crop planning recommendations:', error);
            return [];
        }
    }

    /**
     * Get trending products in user's region
     */
    async getTrendingRecommendations(
        context: RecommendationContext
    ): Promise<ProductRecommendation[]> {
        try {
            // Get products with high recent sales in the region
            const { data: trendingProducts, error } = await supabase
                .from('products')
                .select(`
                    *,
                    order_items!inner(
                        order:order_id!inner(
                            user:user_id!inner(
                                user_profiles!inner(farm_location)
                            ),
                            created_at
                        )
                    )
                `)
                .eq('is_active', true)
                .gte('order_items.order.created_at', this.getDateDaysAgo(30))
                .limit(10);

            if (error) {
                console.error('Error fetching trending products:', error);
                return [];
            }

            const recommendations: ProductRecommendation[] = [];

            for (const productData of trendingProducts || []) {
                const product = this.mapDatabaseProductToProduct(productData);
                const trendingScore = this.calculateTrendingScore(productData, context);

                if (trendingScore > 0.6) {
                    recommendations.push({
                        product,
                        reason: 'Popular among farmers in your area this month',
                        confidence: trendingScore,
                        relatedTo: 'trending',
                    });
                }
            }

            return recommendations.sort((a, b) => b.confidence - a.confidence).slice(0, 5);
        } catch (error) {
            console.error('Error getting trending recommendations:', error);
            return [];
        }
    }

    /**
     * Get personalized recommendations based on user behavior
     */
    async getPersonalizedRecommendations(
        context: RecommendationContext
    ): Promise<ProductRecommendation[]> {
        try {
            const recommendations: ProductRecommendation[] = [];

            // Get user's purchase history
            const { data: purchaseHistory } = await supabase
                .from('order_items')
                .select(`
                    product_id,
                    quantity,
                    product:product_id!inner(*),
                    order:order_id!inner(user_id, created_at, status)
                `)
                .eq('order.user_id', context.userId)
                .eq('order.status', 'delivered')
                .order('order.created_at', { ascending: false })
                .limit(20);

            if (purchaseHistory && purchaseHistory.length > 0) {
                // Find frequently purchased categories
                const categoryFrequency = this.analyzeCategoryFrequency(purchaseHistory);

                // Recommend new products in frequently purchased categories
                for (const [category, frequency] of Object.entries(categoryFrequency)) {
                    if (frequency >= 2) { // User has bought from this category at least twice
                        const categoryProducts = await productCatalogService.getProductsByCategory(
                            category as ProductCategory,
                            5
                        );

                        // Filter out already purchased products
                        const purchasedProductIds = new Set(
                            purchaseHistory.map(item => item.product_id)
                        );

                        const newProducts = categoryProducts.filter(
                            product => !purchasedProductIds.has(product.id)
                        );

                        for (const product of newProducts) {
                            recommendations.push({
                                product,
                                reason: `Based on your ${category} purchases`,
                                confidence: Math.min(0.9, frequency * 0.2),
                                relatedTo: 'purchase_history',
                            });
                        }
                    }
                }

                // Recommend complementary products to recent purchases
                const recentPurchases = purchaseHistory.slice(0, 5);
                for (const purchase of recentPurchases) {
                    const complementary = await this.getComplementaryProducts(
                        [purchase.product_id],
                        context
                    );
                    recommendations.push(...complementary);
                }
            }

            return this.deduplicateRecommendations(recommendations)
                .sort((a, b) => b.confidence - a.confidence)
                .slice(0, 8);
        } catch (error) {
            console.error('Error getting personalized recommendations:', error);
            return [];
        }
    }

    // Private helper methods

    private async getProductsForIssue(
        issue: AIAnalysisResult['issues'][0],
        category: AIAnalysisResult['category'],
        context: RecommendationContext
    ): Promise<ProductRecommendation[]> {
        const searchTags = this.getTagsForIssue(issue.type, category);
        const products = await this.searchProductsByTags(searchTags);

        return products.map(product => ({
            product,
            reason: `Recommended for ${issue.description.toLowerCase()}`,
            confidence: issue.confidence * this.calculateContextRelevance(product, context),
            relatedTo: 'ai_analysis',
        }));
    }

    private async getPreventiveRecommendations(
        analysisResult: AIAnalysisResult,
        context: RecommendationContext
    ): Promise<ProductRecommendation[]> {
        const preventiveTags = ['preventive', 'maintenance', 'health-boost'];
        const products = await this.searchProductsByTags(preventiveTags);

        return products.slice(0, 3).map(product => ({
            product,
            reason: 'Preventive care to maintain plant health',
            confidence: 0.7 * this.calculateContextRelevance(product, context),
            relatedTo: 'preventive',
        }));
    }

    private async getComplementaryProducts(
        productIds: string[],
        context: RecommendationContext
    ): Promise<ProductRecommendation[]> {
        // Find products frequently bought together
        const { data: coOccurrences } = await supabase
            .from('order_items')
            .select(`
                product_id,
                order:order_id!inner(
                    order_items!inner(product_id, product:product_id!inner(*))
                )
            `)
            .in('product_id', productIds);

        const complementaryProductIds = new Set<string>();
        coOccurrences?.forEach(item => {
            item.order?.order_items?.forEach(orderItem => {
                if (!productIds.includes(orderItem.product_id)) {
                    complementaryProductIds.add(orderItem.product_id);
                }
            });
        });

        const complementaryProducts = await Promise.all(
            Array.from(complementaryProductIds).slice(0, 5).map(id =>
                productCatalogService.getProductById(id)
            )
        );

        return complementaryProducts
            .filter((product): product is Product => product !== null)
            .map(product => ({
                product,
                reason: 'Frequently bought together',
                confidence: 0.6,
                relatedTo: 'complementary',
            }));
    }

    private async getProductsBySeasonAndPhase(
        season: string,
        phase: string
    ): Promise<Product[]> {
        const seasonalTags = this.getSeasonalTags(season, phase);
        return this.searchProductsByTags(seasonalTags);
    }

    private async getProductsForCrop(
        cropType: string,
        context: RecommendationContext
    ): Promise<ProductRecommendation[]> {
        const cropTags = [cropType.toLowerCase(), `${cropType}-specific`];
        const products = await this.searchProductsByTags(cropTags);

        return products.slice(0, 4).map(product => ({
            product,
            reason: `Perfect for your ${cropType} crops`,
            confidence: 0.8 * this.calculateContextRelevance(product, context),
            relatedTo: cropType,
        }));
    }

    private async searchProductsByTags(tags: string[]): Promise<Product[]> {
        const { data: products } = await supabase
            .from('products')
            .select('*')
            .overlaps('tags', tags)
            .eq('is_active', true)
            .order('rating', { ascending: false })
            .limit(10);

        return products?.map(this.mapDatabaseProductToProduct) || [];
    }

    private getTagsForIssue(issueType: string, category: string): string[] {
        const tagMap: Record<string, string[]> = {
            'disease': ['fungicide', 'disease-control', 'treatment'],
            'pest': ['pesticide', 'pest-control', 'insecticide'],
            'nutrient_deficiency': ['fertilizer', 'nutrients', 'supplement'],
            'water_stress': ['irrigation', 'water-management'],
            'soil_ph': ['ph-adjuster', 'soil-amendment'],
            'fungal': ['fungicide', 'antifungal'],
            'bacterial': ['bactericide', 'antibacterial'],
            'viral': ['plant-health', 'immunity-booster'],
        };

        return tagMap[issueType] || ['general-treatment'];
    }

    private getSeasonalTags(season: string, phase: string): string[] {
        const seasonalTagMap: Record<string, Record<string, string[]>> = {
            spring: {
                preparation: ['soil-prep', 'seeds', 'planting-tools'],
                planting: ['seeds', 'seedlings', 'planting-fertilizer'],
                growing: ['growth-fertilizer', 'pest-prevention'],
                harvesting: ['harvesting-tools'],
            },
            summer: {
                preparation: ['irrigation', 'shade-cloth', 'mulch'],
                planting: ['heat-resistant-seeds', 'summer-crops'],
                growing: ['water-management', 'heat-stress-relief'],
                harvesting: ['preservation-tools'],
            },
            fall: {
                preparation: ['cover-crop-seeds', 'soil-amendment'],
                planting: ['winter-crops', 'cold-hardy-seeds'],
                growing: ['cold-protection', 'winter-fertilizer'],
                harvesting: ['storage-solutions'],
            },
            winter: {
                preparation: ['greenhouse-supplies', 'heating'],
                planting: ['indoor-growing', 'greenhouse-seeds'],
                growing: ['supplemental-lighting', 'heating-systems'],
                harvesting: ['winter-harvesting-tools'],
            },
        };

        return seasonalTagMap[season]?.[phase] || [];
    }

    private calculateContextRelevance(product: Product, context: RecommendationContext): number {
        let relevance = 1.0;

        // Adjust based on experience level
        if (context.experienceLevel === 'beginner' && product.tags.includes('advanced')) {
            relevance *= 0.7;
        } else if (context.experienceLevel === 'expert' && product.tags.includes('beginner')) {
            relevance *= 0.8;
        }

        // Adjust based on budget
        if (context.budget) {
            if (product.price < context.budget.min || product.price > context.budget.max) {
                relevance *= 0.5;
            }
        }

        // Adjust based on crop types
        if (context.cropTypes) {
            const hasRelevantCrop = context.cropTypes.some(crop =>
                product.tags.includes(crop.toLowerCase())
            );
            if (hasRelevantCrop) {
                relevance *= 1.2;
            }
        }

        return Math.min(1.0, relevance);
    }

    private calculateSeasonalRelevance(
        product: Product,
        season: string,
        phase: string,
        context: RecommendationContext
    ): number {
        const seasonalTags = this.getSeasonalTags(season, phase);
        const hasSeasonalTag = seasonalTags.some(tag => product.tags.includes(tag));

        let relevance = hasSeasonalTag ? 0.8 : 0.3;
        return relevance * this.calculateContextRelevance(product, context);
    }

    private calculateTrendingScore(productData: any, context: RecommendationContext): number {
        // Simplified trending score calculation
        const baseScore = 0.7;
        const contextRelevance = this.calculateContextRelevance(
            this.mapDatabaseProductToProduct(productData),
            context
        );
        return baseScore * contextRelevance;
    }

    private analyzeCategoryFrequency(purchaseHistory: any[]): Record<string, number> {
        const frequency: Record<string, number> = {};

        purchaseHistory.forEach(item => {
            const category = (item.product as any)?.category;
            if (category) {
                frequency[category] = (frequency[category] || 0) + 1;
            }
        });

        return frequency;
    }

    private deduplicateRecommendations(
        recommendations: ProductRecommendation[]
    ): ProductRecommendation[] {
        const seen = new Set<string>();
        return recommendations.filter(rec => {
            if (seen.has(rec.product.id)) {
                return false;
            }
            seen.add(rec.product.id);
            return true;
        });
    }

    private async saveRecommendationsToDatabase(
        userId: string,
        recommendations: ProductRecommendation[],
        analysisResult: AIAnalysisResult
    ): Promise<void> {
        try {
            const recommendationData = recommendations.map(rec => ({
                user_id: userId,
                product_id: 'analysis',
                recommended_product_id: rec.product.id,
                reason: rec.reason,
                confidence: rec.confidence,
                source: 'ai' as const,
                metadata: {
                    analysis_result: analysisResult,
                    related_to: rec.relatedTo,
                },
            }));

            await supabase
                .from('product_recommendations')
                .insert(recommendationData);
        } catch (error) {
            console.error('Error saving recommendations to database:', error);
        }
    }

    private mapDatabaseProductToProduct(productData: any): Product {
        return {
            id: productData.id,
            name: productData.name,
            description: productData.description || '',
            category: productData.category,
            price: productData.price,
            originalPrice: productData.original_price,
            currency: productData.currency,
            imageUrls: productData.image_urls || [],
            specifications: productData.specifications || [],
            stockQuantity: productData.stock_quantity,
            rating: productData.rating,
            reviewCount: productData.review_count,
            tags: productData.tags || [],
            brand: productData.brand,
            isRecommended: productData.is_recommended,
            isFeatured: productData.is_featured,
            createdAt: new Date(productData.created_at),
            updatedAt: new Date(productData.updated_at),
        };
    }

    private getCurrentSeason(): 'spring' | 'summer' | 'fall' | 'winter' {
        const month = new Date().getMonth();
        if (month >= 2 && month <= 4) return 'spring';
        if (month >= 5 && month <= 7) return 'summer';
        if (month >= 8 && month <= 10) return 'fall';
        return 'winter';
    }

    private getDateDaysAgo(days: number): string {
        const date = new Date();
        date.setDate(date.getDate() - days);
        return date.toISOString();
    }

    private generateSeasonalReason(
        season: string,
        phase: string,
        category: ProductCategory
    ): string {
        const reasonMap: Record<string, Record<string, Record<ProductCategory, string>>> = {
            spring: {
                preparation: {
                    seeds: 'Perfect for spring planting season',
                    tools: 'Essential for spring garden preparation',
                    fertilizers: 'Boost soil nutrients for spring growth',
                    equipment: 'Prepare your equipment for the growing season',
                    pesticides: 'Prevent early season pest problems',
                    irrigation: 'Set up irrigation for the growing season',
                },
            },
            // Add more seasonal reasons as needed
        };

        return reasonMap[season]?.[phase]?.[category] ||
            `Recommended for ${season} ${phase} activities`;
    }
}

export const aiRecommendationsService = new AIRecommendationsService();