import React, { useState, useRef, useEffect } from 'react';
import { View, Text, FlatList, Pressable, Animated } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { ChatInput } from '../src/components/chat/ChatInput';
import { ConsultationCategories } from '../src/components/chat/ConsultationCategories';
import { createChatService } from '../src/services/ai/chatService';
import { createVoiceService } from '../src/services/voice/voiceService';
import { ChatMessage, Category } from '../src/types/ai';



export default function AIChatScreen() {
    const [messages, setMessages] = useState<ChatMessage[]>([
        {
            id: '1',
            type: 'ai',
            content: 'مرحباً! أنا مساعدك الذكي للزراعة. كيف يمكنني مساعدتك اليوم؟ يمكنك سؤالي عن صحة النباتات، تحليل التربة، الأسمدة، أو مكافحة الآفات.',
            timestamp: new Date(),
        }
    ]);
    const [isTyping, setIsTyping] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedCategory, setSelectedCategory] = useState<string>('');
    const [showCategories, setShowCategories] = useState(false);
    const [voiceEnabled, setVoiceEnabled] = useState(false);
    const [isRecording, setIsRecording] = useState(false);
    const flatListRef = useRef<FlatList>(null);
    const chatService = createChatService();
    const voiceService = createVoiceService();

    // Animation for typing indicator
    const typingAnimation = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        if (isTyping) {
            Animated.loop(
                Animated.sequence([
                    Animated.timing(typingAnimation, {
                        toValue: 1,
                        duration: 600,
                        useNativeDriver: true,
                    }),
                    Animated.timing(typingAnimation, {
                        toValue: 0,
                        duration: 600,
                        useNativeDriver: true,
                    }),
                ])
            ).start();
        } else {
            typingAnimation.setValue(0);
        }
    }, [isTyping, typingAnimation]);

    const handleSendMessage = async (message: string) => {
        const newMessage: ChatMessage = {
            id: Date.now().toString(),
            type: 'user',
            content: message,
            timestamp: new Date(),
            category: selectedCategory,
        };

        setMessages(prev => [...prev, newMessage]);
        setIsLoading(true);
        setIsTyping(true);

        try {
            const response = await chatService.sendMessage(message, {
                category: selectedCategory,
                previousMessages: messages.slice(-5), // Last 5 messages for context
            });

            const aiResponse: ChatMessage = {
                id: (Date.now() + 1).toString(),
                type: 'ai',
                content: response,
                timestamp: new Date(),
            };
            setMessages(prev => [...prev, aiResponse]);
            setIsTyping(false);
            setIsLoading(false);
        } catch (error) {
            console.error('Error sending message:', error);
            setIsTyping(false);
            setIsLoading(false);
        }
    };

    const handleCategorySelect = (category: Category) => {
        setSelectedCategory(category.id);
        setShowCategories(false);

        const systemMessage: ChatMessage = {
            id: Date.now().toString(),
            type: 'ai',
            content: `ممتاز! أنا الآن مركز على ${category.name}. ${category.description}. ما هي أسئلتك المحددة؟`,
            timestamp: new Date(),
        };
        setMessages(prev => [...prev, systemMessage]);
    };

    const handleVoiceInput = async () => {
        if (isRecording) {
            voiceService.stopListening();
            setIsRecording(false);
            return;
        }

        try {
            setIsRecording(true);
            const voiceText = await voiceService.startListening();
            setIsRecording(false);

            if (voiceText.trim()) {
                handleSendMessage(voiceText);
            }
        } catch (error) {
            console.error('Voice input error:', error);
            setIsRecording(false);
        }
    };

    const renderMessage = ({ item }: { item: ChatMessage }) => {
        const isUser = item.type === 'user';

        return (
            <View className={`mb-4 px-4 ${isUser ? 'items-end' : 'items-start'}`}>
                <View
                    className={`
                        max-w-[80%] rounded-2xl px-4 py-3
                        ${isUser
                            ? 'bg-primary-600 rounded-br-md'
                            : 'bg-white border border-earth-200 rounded-bl-md shadow-sm'
                        }
                    `}>
                    <Text className={`text-base leading-6 ${isUser ? 'text-white' : 'text-earth-900'}`}>
                        {item.content}
                    </Text>

                    {!isUser && (
                        <View className="flex-row items-center justify-between mt-2">
                            <Text className="text-xs text-earth-500">
                                {item.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                            </Text>

                            <Pressable
                                onPress={() => voiceService.speak(item.content)}
                                className="ml-3 p-1 rounded-full bg-primary-100 active:bg-primary-200"
                                accessibilityRole="button"
                                accessibilityLabel="تشغيل الرسالة صوتياً">
                                <Text className="text-primary-600 text-sm">🔊</Text>
                            </Pressable>
                        </View>
                    )}
                </View>

                {isUser && (
                    <Text className="text-xs text-earth-500 mt-1">
                        {item.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </Text>
                )}
            </View>
        );
    };

    const renderTypingIndicator = () => {
        if (!isTyping) return null;

        return (
            <View className="mb-4 px-4 items-start">
                <View className="bg-white border border-earth-200 rounded-2xl rounded-bl-md px-4 py-3 shadow-sm">
                    <View className="flex-row items-center">
                        <Text className="text-earth-600 mr-2">الذكاء الاصطناعي يفكر</Text>
                        <Animated.View
                            style={{
                                opacity: typingAnimation,
                            }}>
                            <Text className="text-primary-600">●●●</Text>
                        </Animated.View>
                    </View>
                </View>
            </View>
        );
    };

    return (
        <SafeAreaView className="flex-1 bg-earth-50">
            {/* Header */}
            <View className="bg-white border-b border-earth-200 px-4 py-3 shadow-sm">
                <View className="flex-row items-center justify-between">
                    <View className="flex-row items-center flex-1">
                        <Pressable
                            onPress={() => router.back()}
                            className="w-10 h-10 bg-earth-100 rounded-full items-center justify-center mr-3"
                            accessibilityRole="button"
                            accessibilityLabel="العودة">
                            <Text className="text-earth-600 text-lg">←</Text>
                        </Pressable>

                        <View className="flex-1">
                            <Text className="text-lg font-semibold text-earth-900">محادثة الذكاء الاصطناعي</Text>
                            <Text className="text-sm text-earth-600">
                                {isTyping ? 'يحلل سؤالك...' : 'جاهز لتقديم النصائح الزراعية'}
                            </Text>
                        </View>
                    </View>

                    <View className="flex-row gap-2">
                        <Pressable
                            onPress={() => {
                                setVoiceEnabled(!voiceEnabled);
                                if (!voiceEnabled) {
                                    voiceService.enableVoiceMode();
                                } else {
                                    voiceService.disableVoiceMode();
                                }
                            }}
                            className="w-10 h-10 bg-primary-100 rounded-full items-center justify-center active:bg-primary-200"
                            accessibilityRole="button"
                            accessibilityLabel="تفعيل/إلغاء الصوت">
                            <Text className="text-primary-600 text-lg">
                                {voiceEnabled ? '🔊' : '🔇'}
                            </Text>
                        </Pressable>

                        <Pressable
                            onPress={() => setShowCategories(!showCategories)}
                            className="w-10 h-10 bg-secondary-100 rounded-full items-center justify-center active:bg-secondary-200"
                            accessibilityRole="button"
                            accessibilityLabel="اختيار فئة الاستشارة">
                            <Text className="text-secondary-600 text-lg">📂</Text>
                        </Pressable>

                        <Pressable
                            onPress={() => router.push('/ai-diagnosis')}
                            className="w-10 h-10 bg-primary-100 rounded-full items-center justify-center active:bg-primary-200"
                            accessibilityRole="button"
                            accessibilityLabel="تشخيص الصور">
                            <Text className="text-primary-600 text-lg">📷</Text>
                        </Pressable>
                    </View>
                </View>
            </View>

            {/* Categories Panel */}
            {showCategories && (
                <View className="bg-white border-b border-earth-200">
                    <ConsultationCategories
                        onCategorySelect={handleCategorySelect}
                        selectedCategory={selectedCategory}
                        voiceEnabled={voiceEnabled}
                        onVoiceFeedback={() => { }}
                    />
                </View>
            )}

            {/* Messages List */}
            <FlatList
                ref={flatListRef}
                data={messages}
                renderItem={renderMessage}
                keyExtractor={(item) => item.id}
                className="flex-1"
                contentContainerStyle={{ paddingVertical: 16 }}
                showsVerticalScrollIndicator={false}
                onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
                ListFooterComponent={renderTypingIndicator}
            />

            {/* Chat Input */}
            <ChatInput
                onSendMessage={handleSendMessage}
                onSendImage={() => router.push('/ai-diagnosis')}
                onVoiceInput={handleVoiceInput}
                disabled={isLoading}
                voiceEnabled={voiceEnabled}
                isRecording={isRecording}
                isLoading={isLoading}
                placeholder="اكتب رسالتك هنا..."
            />
        </SafeAreaView>
    );
}