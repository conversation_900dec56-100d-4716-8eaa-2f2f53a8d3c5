export interface CropPlan {
    id: string;
    cropType: string;
    cropName: string;
    plantingDate: Date;
    harvestDate: Date;
    location: {
        latitude: number;
        longitude: number;
        address?: string;
    };
    status: 'planning' | 'active' | 'completed' | 'cancelled';
    tasks: string[]; // Task IDs
    weatherAlerts: WeatherAlert[];
    notes?: string;
    expectedYield?: number;
    actualYield?: number;
    createdAt: Date;
    updatedAt: Date;
}

export interface WeatherAlert {
    id: string;
    type: 'frost' | 'drought' | 'heavy_rain' | 'high_wind' | 'extreme_heat';
    severity: 'low' | 'medium' | 'high';
    message: string;
    messageAr: string;
    startDate: Date;
    endDate: Date;
    recommendations: string[];
    recommendationsAr: string[];
}

export interface CropRecommendation {
    cropId: string;
    suitabilityScore: number; // 0-100
    reasons: string[];
    reasonsAr: string[];
    optimalPlantingDate: Date;
    expectedHarvestDate: Date;
    estimatedYield: number;
    difficultyRating: number; // 1-5
    waterRequirement: 'low' | 'medium' | 'high';
    sunlightRequirement: 'partial' | 'full';
    soilType: string[];
}

export interface PlantingSchedule {
    cropId: string;
    optimalStartDate: Date;
    optimalEndDate: Date;
    lateStartDate: Date;
    earlyStartDate: Date;
    growingDays: number;
    harvestWindow: {
        start: Date;
        end: Date;
    };
}

export interface CreateCropPlanData {
    cropType: string;
    plantingDate: Date;
    location: {
        latitude: number;
        longitude: number;
        address?: string;
    };
    notes?: string;
    expectedYield?: number;
}

export interface CropPlanFilters {
    status?: CropPlan['status'][];
    cropTypes?: string[];
    dateRange?: {
        start: Date;
        end: Date;
    };
}