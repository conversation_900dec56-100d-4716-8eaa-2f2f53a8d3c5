import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Animated } from 'react-native';
import { VoiceService } from '../../services/voice';

interface QuickAction {
    id: string;
    title: string;
    description: string;
    icon: string;
    color: string;
    bgColor: string;
    voiceCommand: string;
    onPress: () => void;
}

interface QuickActionsPanelProps {
    onAnalyzePlant?: () => void;
    onVoiceCommand?: () => void;
    onViewProgress?: () => void;
}

export const QuickActionsPanel: React.FC<QuickActionsPanelProps> = ({
    onAnalyzePlant,
    onVoiceCommand,
    onViewProgress,
}) => {
    const [pressedAction, setPressedAction] = useState<string | null>(null);
    const [scaleAnims] = useState({
        analyze: new Animated.Value(1),
        voice: new Animated.Value(1),
        progress: new Animated.Value(1),
    });

    const voiceService = VoiceService.getInstance();

    const animatePress = (actionId: string, callback: () => void) => {
        setPressedAction(actionId);

        const anim = scaleAnims[actionId as keyof typeof scaleAnims];

        Animated.sequence([
            Animated.timing(anim, {
                toValue: 0.95,
                duration: 100,
                useNativeDriver: true,
            }),
            Animated.timing(anim, {
                toValue: 1,
                duration: 100,
                useNativeDriver: true,
            }),
        ]).start(() => {
            setPressedAction(null);
            callback();
        });
    };

    const handleAnalyzePlant = async () => {
        await voiceService.speak('Opening plant analysis camera');
        onAnalyzePlant?.();
    };

    const handleVoiceCommand = async () => {
        await voiceService.speak('Voice command mode activated. Listening for your command.');
        onVoiceCommand?.();
    };

    const handleViewProgress = async () => {
        await voiceService.speak('Opening progress charts and analytics');
        onViewProgress?.();
    };

    const quickActions: QuickAction[] = [
        {
            id: 'analyze',
            title: 'Analyze Plant',
            description: 'Take a photo to identify diseases or issues',
            icon: '📷',
            color: 'text-green-700',
            bgColor: 'bg-green-100',
            voiceCommand: 'analyze plant',
            onPress: () => animatePress('analyze', handleAnalyzePlant),
        },
        {
            id: 'voice',
            title: 'Voice Command',
            description: 'Ask questions or give voice commands',
            icon: '🎤',
            color: 'text-blue-700',
            bgColor: 'bg-blue-100',
            voiceCommand: 'voice command',
            onPress: () => animatePress('voice', handleVoiceCommand),
        },
        {
            id: 'progress',
            title: 'View Progress',
            description: 'Check your farming progress and analytics',
            icon: '📊',
            color: 'text-purple-700',
            bgColor: 'bg-purple-100',
            voiceCommand: 'view progress',
            onPress: () => animatePress('progress', handleViewProgress),
        },
    ];

    const handlePanelPress = async () => {
        const panelText = 'Quick actions panel. Available actions: Analyze Plant, Voice Command, and View Progress.';
        await voiceService.speak(panelText);
    };

    return (
        <View className="mx-4 mb-6">
            <TouchableOpacity
                onPress={handlePanelPress}
                className="mb-3"
                accessibilityLabel="Quick actions panel"
                accessibilityRole="button"
            >
                <Text className="text-lg font-bold text-gray-900 mb-1">Quick Actions</Text>
                <Text className="text-sm text-gray-600">
                    Tap any action or use voice commands
                </Text>
            </TouchableOpacity>

            <View className="bg-white rounded-xl p-4 shadow-sm">
                <View className="flex-row justify-between gap-3">
                    {quickActions.map((action) => {
                        const animKey = action.id as keyof typeof scaleAnims;
                        const isPressed = pressedAction === action.id;

                        return (
                            <Animated.View
                                key={action.id}
                                style={{ transform: [{ scale: scaleAnims[animKey] }] }}
                                className="flex-1"
                            >
                                <TouchableOpacity
                                    onPress={action.onPress}
                                    className={`${action.bgColor} rounded-xl p-4 items-center min-h-[120px] justify-center ${isPressed ? 'opacity-80' : ''
                                        }`}
                                    accessibilityLabel={`${action.title}. ${action.description}`}
                                    accessibilityRole="button"
                                    accessibilityHint={`Voice command: ${action.voiceCommand}`}
                                >
                                    {/* Icon */}
                                    <View className="mb-3">
                                        <Text className="text-3xl">{action.icon}</Text>
                                    </View>

                                    {/* Title */}
                                    <Text className={`${action.color} font-bold text-sm text-center mb-1`}>
                                        {action.title}
                                    </Text>

                                    {/* Description */}
                                    <Text className="text-xs text-gray-600 text-center leading-4">
                                        {action.description}
                                    </Text>

                                    {/* Voice Command Hint */}
                                    <View className="mt-2 px-2 py-1 bg-white bg-opacity-50 rounded-full">
                                        <Text className="text-xs text-gray-500 font-medium">
                                            "{action.voiceCommand}"
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            </Animated.View>
                        );
                    })}
                </View>

                {/* Voice Commands Help */}
                <View className="mt-4 pt-4 border-t border-gray-100">
                    <View className="flex-row items-center justify-center">
                        <Text className="text-xs text-gray-500 mr-2">💡</Text>
                        <Text className="text-xs text-gray-500 text-center">
                            Say "Hey Assistant" followed by any action name for voice control
                        </Text>
                    </View>
                </View>
            </View>
        </View>
    );
};