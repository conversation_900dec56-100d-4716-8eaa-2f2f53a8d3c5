import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { PointsTransaction } from '../../types/points';
import {
    getTransactionColor,
    getTransactionIcon,
    getTransactionSourceLabel,
    formatTimeAgo,
    formatPoints
} from '../../utils/points';

interface TransactionItemProps {
    transaction: PointsTransaction;
}

export const TransactionItem: React.FC<TransactionItemProps> = ({ transaction }) => {
    const color = getTransactionColor(transaction);
    const icon = getTransactionIcon(transaction.source);
    const sourceLabel = getTransactionSourceLabel(transaction.source);
    const timeAgo = formatTimeAgo(transaction.created_at);
    const isPositive = transaction.amount > 0;

    return (
        <View style={styles.container}>
            <View style={styles.iconContainer}>
                <Text style={styles.icon}>{icon}</Text>
            </View>

            <View style={styles.contentContainer}>
                <View style={styles.header}>
                    <Text style={styles.description} numberOfLines={1}>
                        {transaction.description}
                    </Text>
                    <Text style={[styles.amount, { color }]}>
                        {isPositive ? '+' : ''}{formatPoints(transaction.amount)}
                    </Text>
                </View>

                <View style={styles.footer}>
                    <Text style={styles.source}>{sourceLabel}</Text>
                    <Text style={styles.time}>{timeAgo}</Text>
                </View>

                {transaction.metadata && (
                    <View style={styles.metadataContainer}>
                        {transaction.metadata.task_id && (
                            <Text style={styles.metadata}>
                                Task: {transaction.metadata.task_id}
                            </Text>
                        )}
                        {transaction.metadata.achievement_id && (
                            <Text style={styles.metadata}>
                                Achievement: {transaction.metadata.achievement_id}
                            </Text>
                        )}
                        {transaction.metadata.post_id && (
                            <Text style={styles.metadata}>
                                Post: {transaction.metadata.post_id}
                            </Text>
                        )}
                    </View>
                )}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        backgroundColor: '#ffffff',
        borderRadius: 12,
        padding: 16,
        marginVertical: 4,
        borderWidth: 1,
        borderColor: '#f3f4f6',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 2,
    },
    iconContainer: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: '#f9fafb',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 12,
    },
    icon: {
        fontSize: 20,
    },
    contentContainer: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 4,
    },
    description: {
        fontSize: 16,
        fontWeight: '500',
        color: '#1f2937',
        flex: 1,
        marginRight: 8,
    },
    amount: {
        fontSize: 16,
        fontWeight: '600',
        minWidth: 60,
        textAlign: 'right',
    },
    footer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    source: {
        fontSize: 14,
        color: '#6b7280',
        fontWeight: '500',
    },
    time: {
        fontSize: 12,
        color: '#9ca3af',
    },
    metadataContainer: {
        marginTop: 8,
        paddingTop: 8,
        borderTopWidth: 1,
        borderTopColor: '#f3f4f6',
    },
    metadata: {
        fontSize: 12,
        color: '#6b7280',
        fontStyle: 'italic',
    },
});