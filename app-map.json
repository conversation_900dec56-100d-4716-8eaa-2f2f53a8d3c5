{"appInfo": {"name": "Smart Farming - المزرعة الذكية", "version": "1.0.0", "type": "React Native Expo App", "framework": "Expo Router", "stateManagement": "Zustand", "styling": "NativeWind (Tailwind CSS)", "backend": "Supabase", "language": "TypeScript"}, "navigation": {"structure": "File-based routing with Expo Router", "layouts": {"root": {"file": "app/_layout.tsx", "type": "Stack Navigator", "screens": ["index", "(auth)", "(tabs)", "ai-chat", "ai-diagnosis", "camera", "test-camera"]}, "auth": {"file": "app/(auth)/_layout.tsx", "type": "Stack Navigator", "screens": ["welcome", "personal-info", "farm-setup", "completion", "login", "forgot-password", "verify-reset-code", "new-password"]}, "tabs": {"file": "app/(tabs)/_layout.tsx", "type": "Tab Navigator", "screens": ["home", "crops", "ai", "store"]}}}, "screens": {"entry": {"file": "app/index.tsx", "purpose": "Entry point - redirects to home tab", "imports": ["expo-router"]}, "auth": {"welcome": {"file": "app/(auth)/welcome.tsx", "purpose": "Welcome screen for new users"}, "personal-info": {"file": "app/(auth)/personal-info.tsx", "purpose": "Personal information collection", "components": ["PersonalInfoForm"]}, "farm-setup": {"file": "app/(auth)/farm-setup.tsx", "purpose": "Farm setup and configuration", "components": ["FarmSetupForm"]}, "completion": {"file": "app/(auth)/completion.tsx", "purpose": "Setup completion confirmation"}, "login": {"file": "app/(auth)/login.tsx", "purpose": "User authentication"}, "forgot-password": {"file": "app/(auth)/forgot-password.tsx", "purpose": "Password reset initiation"}, "verify-reset-code": {"file": "app/(auth)/verify-reset-code.tsx", "purpose": "Password reset code verification"}, "new-password": {"file": "app/(auth)/new-password.tsx", "purpose": "New password creation"}}, "main": {"home": {"file": "app/(tabs)/home.tsx", "purpose": "Main dashboard", "components": ["WeatherWidget", "TodaysTasksSection", "QuickActionsPanel"], "services": ["weather", "tasks", "location"]}, "crops": {"file": "app/(tabs)/crops.tsx", "purpose": "Crop management and planning", "components": ["CropSelectionGrid", "CropGrowthTimeline", "TaskCalendar", "Harvest<PERSON><PERSON>ner", "YieldPredictionChart"], "services": ["cropPlanning", "tasks"]}, "ai": {"file": "app/(tabs)/ai.tsx", "purpose": "AI assistant interface", "components": ["ChatInput", "ConsultationCategories", "ConsultationHistory"], "services": ["ai/chatService", "voice"]}, "store": {"file": "app/(tabs)/store.tsx", "purpose": "Agricultural products marketplace", "components": ["ProductCard", "ProductSearch"], "services": ["store"]}}, "standalone": {"ai-chat": {"file": "app/ai-chat.tsx", "purpose": "Dedicated AI chat interface"}, "ai-diagnosis": {"file": "app/ai-diagnosis.tsx", "purpose": "AI-powered plant diagnosis"}, "camera": {"file": "app/camera.tsx", "purpose": "Camera interface for plant analysis", "components": ["CameraScreen", "CameraInterface"], "services": ["camera", "ai/imageAnalysis"]}, "test-camera": {"file": "app/test-camera.tsx", "purpose": "Camera testing interface"}}}, "components": {"ui": {"location": "src/components/ui/", "components": {"Button": {"file": "Button.tsx", "purpose": "Reusable button component"}, "Card": {"file": "Card.tsx", "purpose": "Card container component"}, "Input": {"file": "Input.tsx", "purpose": "Form input component"}, "Modal": {"file": "Modal.tsx", "purpose": "Modal dialog component"}, "ProgressIndicator": {"file": "ProgressIndicator.tsx", "purpose": "Progress tracking component"}, "VoiceButton": {"file": "VoiceButton.tsx", "purpose": "Voice input button"}, "ProductCard": {"file": "ProductCard.tsx", "purpose": "Product display card"}, "ProductSearch": {"file": "ProductSearch.tsx", "purpose": "Product search interface"}, "CropSelector": {"file": "CropSelector.tsx", "purpose": "Crop selection component"}, "TaskCard": {"file": "TaskCard.tsx", "purpose": "Task display card"}, "WeatherWidget": {"file": "WeatherWidget.tsx", "purpose": "Weather information display"}, "QuickActionsPanel": {"file": "QuickActionsPanel.tsx", "purpose": "Quick action buttons panel"}, "TodaysTasksSection": {"file": "TodaysTasksSection.tsx", "purpose": "Today's tasks display"}}}, "agricultural": {"location": "src/components/agricultural/", "components": {"TaskCard": {"file": "TaskCard.tsx", "purpose": "Agricultural task card"}, "WeatherWidget": {"file": "WeatherWidget.tsx", "purpose": "Weather widget for farming"}}}, "crops": {"location": "src/components/crops/", "components": {"CropGrowthTimeline": {"file": "CropGrowthTimeline.tsx", "purpose": "Crop growth stage visualization"}, "CropSelectionGrid": {"file": "CropSelectionGrid.tsx", "purpose": "Grid for selecting crops"}, "HarvestPlanner": {"file": "HarvestPlanner.tsx", "purpose": "Harvest planning interface"}, "LocationPicker": {"file": "LocationPicker.tsx", "purpose": "Location selection for crops"}, "PlantingDatePicker": {"file": "PlantingDatePicker.tsx", "purpose": "Planting date selection"}, "TaskCalendar": {"file": "TaskCalendar.tsx", "purpose": "Calendar view for tasks"}, "TaskDetailModal": {"file": "TaskDetailModal.tsx", "purpose": "Detailed task information modal"}, "YieldPredictionChart": {"file": "YieldPredictionChart.tsx", "purpose": "Yield prediction visualization"}}}, "camera": {"location": "src/components/camera/", "components": {"CameraScreen": {"file": "CameraScreen.tsx", "purpose": "Main camera screen"}, "CameraInterface": {"file": "CameraInterface.tsx", "purpose": "Camera controls interface"}, "AnalysisLoadingScreen": {"file": "AnalysisLoadingScreen.tsx", "purpose": "Loading screen during analysis"}, "AnalysisResultsScreen": {"file": "AnalysisResultsScreen.tsx", "purpose": "Display analysis results"}}}, "chat": {"location": "src/components/chat/", "components": {"ChatInput": {"file": "ChatInput.tsx", "purpose": "Chat message input"}, "ConsultationCategories": {"file": "ConsultationCategories.tsx", "purpose": "AI consultation categories"}, "ConsultationHistory": {"file": "ConsultationHistory.tsx", "purpose": "Chat history display"}}}, "forms": {"location": "src/components/forms/", "components": {"PersonalInfoForm": {"file": "PersonalInfoForm.tsx", "purpose": "Personal information form"}, "FarmSetupForm": {"file": "FarmSetupForm.tsx", "purpose": "Farm setup form"}}}, "navigation": {"location": "src/components/navigation/", "components": {"TabNavigation": {"file": "TabNavigation.tsx", "purpose": "Tab navigation component"}, "HeaderNavigation": {"file": "HeaderNavigation.tsx", "purpose": "Header navigation"}, "DrawerNavigation": {"file": "DrawerNavigation.tsx", "purpose": "Drawer navigation"}}}, "community": {"location": "src/components/community/", "components": {"CommunityPost": {"file": "CommunityPost.tsx", "purpose": "Community post display"}, "CommentItem": {"file": "CommentItem.tsx", "purpose": "Comment item component"}}}}, "services": {"ai": {"location": "src/services/ai/", "services": {"chatService": {"file": "chatService.ts", "purpose": "AI chat functionality"}, "imageAnalysis": {"file": "imageAnalysis.ts", "purpose": "AI image analysis for plants"}}}, "supabase": {"location": "src/services/supabase/", "services": {"client": {"file": "client.ts", "purpose": "Supabase client configuration"}, "auth": {"file": "auth.ts", "purpose": "Authentication services"}}}, "voice": {"location": "src/services/voice/", "services": {"voiceService": {"file": "voiceService.ts", "purpose": "Voice recognition and synthesis"}}}, "core": {"location": "src/services/", "services": {"camera": {"file": "camera.ts", "purpose": "Camera functionality"}, "cropPlanning": {"file": "cropPlanning.ts", "purpose": "Crop planning logic"}, "language": {"file": "language.ts", "purpose": "Localization services"}, "location": {"file": "location.ts", "purpose": "Location services"}, "store": {"file": "store.ts", "purpose": "Store/marketplace services"}, "tasks": {"file": "tasks.ts", "purpose": "Task management"}, "voice": {"file": "voice.ts", "purpose": "Voice services"}, "weather": {"file": "weather.ts", "purpose": "Weather data services"}}}}, "stores": {"location": "src/stores/", "stateManagement": "Zustand", "stores": {"auth": {"file": "auth.ts", "purpose": "Authentication state management"}, "community": {"file": "community.ts", "purpose": "Community features state"}, "voice": {"file": "voice.ts", "purpose": "Voice interaction state"}}}, "designSystem": {"location": "src/design-system/", "styling": "NativeWind (Tailwind CSS)", "configFile": "tailwind.config.js"}, "types": {"location": "src/types/", "purpose": "TypeScript type definitions"}, "hooks": {"location": "src/hooks/", "purpose": "Custom React hooks"}, "utils": {"location": "src/utils/", "purpose": "Utility functions"}, "data": {"location": "src/data/", "purpose": "Static data and configurations"}, "permissions": {"camera": "Plant, soil, and fertilizer analysis", "location": "Location-specific farming recommendations", "photos": "Crop analysis from photo library"}, "keyFeatures": {"authentication": "User registration and login", "farmSetup": "Farm configuration and setup", "cropManagement": "Crop planning and tracking", "aiAssistant": "AI-powered farming advice", "cameraAnalysis": "Plant and soil analysis via camera", "voiceInteraction": "Voice commands and responses", "weatherIntegration": "Weather data for farming decisions", "taskManagement": "Agricultural task scheduling", "marketplace": "Agricultural products store", "community": "Farmer community features"}, "dependencies": {"core": ["expo", "expo-router", "react", "react-native"], "ui": ["nativewind", "react-native-safe-area-context", "react-native-reanimated"], "backend": ["@supabase/supabase-js"], "features": ["expo-camera", "expo-image-picker", "expo-location", "expo-speech", "@react-native-async-storage/async-storage", "@react-native-community/datetimepicker"], "state": ["zustand"]}, "fileConnections": {"entryPoint": "app/index.tsx → app/(tabs)/home.tsx", "authFlow": "app/(auth)/welcome.tsx → personal-info → farm-setup → completion", "mainNavigation": "app/(tabs)/_layout.tsx → [home, crops, ai, store]", "componentExports": "src/components/index.ts → all component modules", "serviceLayer": "src/services/ → backend integrations", "stateManagement": "src/stores/ → Zustand stores", "designSystem": "src/design-system/ → styling and themes"}}