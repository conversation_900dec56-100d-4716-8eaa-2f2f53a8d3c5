import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, Pressable, Animated } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Button } from '../../src/components/ui/Button';
import { AppProvider, useApp } from '../../src/contexts/AppContext';

const WelcomeContent: React.FC = () => {
  const {
    t,
    isRTL,
    isVoiceEnabled,
    enableVoiceMode,
    disableVoiceMode,
    speak,
    setLanguage,
    currentLanguage,
  } = useApp();
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));
  const [showLanguageSelector, setShowLanguageSelector] = useState(false);

  useEffect(() => {
    // Animate entrance
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();

    // Speak welcome message if voice is enabled
    if (isVoiceEnabled) {
      speak(t('welcome.title') + '. ' + t('welcome.description'));
    }
  }, [isVoiceEnabled]);

  const handleVoiceModeToggle = async () => {
    if (isVoiceEnabled) {
      await disableVoiceMode();
    } else {
      await enableVoiceMode();
    }
  };

  const handleLanguageChange = async (language: 'en' | 'ar') => {
    await setLanguage(language);
    setShowLanguageSelector(false);

    // Speak confirmation in new language
    const message =
      language === 'ar' ? 'تم تغيير اللغة إلى العربية' : 'Language changed to English';
    await speak(message);
  };

  const handleContinueWithPhone = () => {
    if (isVoiceEnabled) {
      speak(t('personalInfo.title'));
    }
    router.push('/(auth)/personal-info?method=phone');
  };

  const handleContinueWithEmail = () => {
    if (isVoiceEnabled) {
      speak('إنشاء حساب بالبريد الإلكتروني');
    }
    router.push('/(auth)/register');
  };

  return (
    <SafeAreaView
      className={`flex-1 bg-gradient-to-b from-primary-50 to-white ${isRTL ? 'rtl' : 'ltr'}`}>
      <StatusBar style="dark" />

      <ScrollView
        className="flex-1"
        contentContainerStyle={{ flexGrow: 1 }}
        showsVerticalScrollIndicator={false}>
        <Animated.View
          className="flex-1 px-6 py-8"
          style={{
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          }}>
          {/* Header with Language Selector */}
          <View
            className={`mb-8 flex-row items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <View className="flex-1" />

            <Pressable
              onPress={() => setShowLanguageSelector(!showLanguageSelector)}
              className="rounded-full bg-white px-4 py-2 shadow-sm"
              accessibilityRole="button"
              accessibilityLabel={t('welcome.languageSelection')}
              accessibilityHint="Tap to change language">
              <Text className="text-sm font-medium text-earth-700">
                {currentLanguage === 'ar' ? 'العربية' : 'English'}
              </Text>
            </Pressable>
          </View>

          {/* Language Selector Dropdown */}
          {showLanguageSelector && (
            <Animated.View className="mb-6 rounded-xl bg-white p-4 shadow-lg">
              <Text
                className={`mb-3 text-center text-lg font-semibold text-earth-800 ${isRTL ? 'text-right' : 'text-left'}`}>
                {t('welcome.languageSelection')}
              </Text>

              <View className="gap-2">
                <Pressable
                  onPress={() => handleLanguageChange('en')}
                  className={`rounded-lg p-3 ${currentLanguage === 'en' ? 'bg-primary-100' : 'bg-earth-50'}`}
                  accessibilityRole="button"
                  accessibilityLabel="Select English">
                  <Text
                    className={`text-center font-medium ${currentLanguage === 'en' ? 'text-primary-700' : 'text-earth-700'}`}>
                    {t('welcome.english')}
                  </Text>
                </Pressable>

                <Pressable
                  onPress={() => handleLanguageChange('ar')}
                  className={`rounded-lg p-3 ${currentLanguage === 'ar' ? 'bg-primary-100' : 'bg-earth-50'}`}
                  accessibilityRole="button"
                  accessibilityLabel="اختر العربية">
                  <Text
                    className={`text-center font-medium ${currentLanguage === 'ar' ? 'text-primary-700' : 'text-earth-700'}`}>
                    {t('welcome.arabic')}
                  </Text>
                </Pressable>
              </View>
            </Animated.View>
          )}

          {/* App Logo and Title */}
          <View className="mb-8 items-center">
            <View className="mb-6 h-24 w-24 items-center justify-center rounded-full bg-primary-100">
              <Text className="text-4xl">🌾</Text>
            </View>

            <Text
              className={`mb-2 text-center text-3xl font-bold text-earth-900 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t('welcome.title')}
            </Text>

            <Text
              className={`text-center text-lg text-primary-600 ${isRTL ? 'text-right' : 'text-left'}`}>
              {t('welcome.subtitle')}
            </Text>
          </View>

          {/* Description */}
          <Text
            className={`mb-8 text-center text-base leading-relaxed text-earth-700 ${isRTL ? 'text-right' : 'text-left'}`}>
            {t('welcome.description')}
          </Text>

          {/* Voice Mode Toggle */}
          <View className="mb-8 rounded-xl bg-white p-6 shadow-sm">
            <View className={`flex-row items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <View className="mr-4 h-12 w-12 items-center justify-center rounded-full bg-secondary-100">
                <Text className="text-2xl">{isVoiceEnabled ? '🔊' : '🎤'}</Text>
              </View>

              <View className="flex-1">
                <Text
                  className={`mb-1 text-lg font-semibold text-earth-800 ${isRTL ? 'text-right' : 'text-left'}`}>
                  {t('welcome.enableVoiceMode')}
                </Text>
                <Text className={`text-sm text-earth-600 ${isRTL ? 'text-right' : 'text-left'}`}>
                  {t('welcome.voiceModeDescription')}
                </Text>
              </View>
            </View>

            <Button
              title={isVoiceEnabled ? t('common.cancel') : t('welcome.enableVoiceMode')}
              onPress={handleVoiceModeToggle}
              variant={isVoiceEnabled ? 'outline' : 'secondary'}
              className="mt-4"
              accessibilityLabel={isVoiceEnabled ? 'Disable voice mode' : 'Enable voice mode'}
              accessibilityHint="Toggle voice navigation and spoken responses"
              voiceFeedbackEnabled={isVoiceEnabled}
              onVoiceFeedback={speak}
            />
          </View>

          {/* Spacer to push buttons to bottom */}
          <View className="flex-1" />

          {/* Continue Buttons */}
          <View className="gap-4">
            <Button
              title={t('welcome.continueWithPhone')}
              onPress={handleContinueWithPhone}
              variant="primary"
              size="large"
              fullWidth
              icon={<Text className="text-xl">📱</Text>}
              accessibilityLabel="Continue registration with phone number"
              accessibilityHint="Proceed to registration using phone number"
              voiceFeedbackEnabled={isVoiceEnabled}
              onVoiceFeedback={speak}
            />

            <Button
              title={t('welcome.continueWithEmail')}
              onPress={handleContinueWithEmail}
              variant="outline"
              size="large"
              fullWidth
              icon={<Text className="text-xl">📧</Text>}
              accessibilityLabel="Continue registration with email address"
              accessibilityHint="Proceed to registration using email address"
              voiceFeedbackEnabled={isVoiceEnabled}
              onVoiceFeedback={speak}
            />
          </View>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default function Welcome() {
  return (
    <AppProvider>
      <WelcomeContent />
    </AppProvider>
  );
}
