import { create } from 'zustand';
import {
    PointsTransaction,
    PointsBalance,
    Achievement,
    UserAchievement,
    LeaderboardEntry,
    DailyStreak
} from '../types/points';
import { PointsService } from '../services/supabase/points';

interface PointsState {
    // State
    balance: PointsBalance | null;
    transactions: PointsTransaction[];
    achievements: UserAchievement[];
    availableAchievements: Achievement[];
    leaderboard: LeaderboardEntry[];
    dailyStreak: DailyStreak | null;
    isLoading: boolean;
    error: string | null;

    // Actions
    initializePoints: (userId: string) => Promise<void>;
    loadBalance: (userId: string) => Promise<void>;
    loadTransactions: (userId: string, limit?: number, offset?: number) => Promise<void>;
    loadAchievements: (userId: string) => Promise<void>;
    loadAvailableAchievements: () => Promise<void>;
    loadLeaderboard: (limit?: number, timeframe?: 'weekly' | 'monthly' | 'all_time') => Promise<void>;
    awardPoints: (userId: string, amount: number, source: PointsTransaction['source'], description: string, metadata?: any) => Promise<boolean>;
    spendPoints: (userId: string, amount: number, source: PointsTransaction['source'], description: string, metadata?: any) => Promise<boolean>;
    redeemPoints: (userId: string, rewardType: string, pointsCost: number) => Promise<boolean>;
    checkDailyCheckin: (userId: string) => Promise<boolean>;
    clearError: () => void;
    reset: () => void;
}

export const usePointsStore = create<PointsState>((set, get) => ({
    // Initial state
    balance: null,
    transactions: [],
    achievements: [],
    availableAchievements: [],
    leaderboard: [],
    dailyStreak: null,
    isLoading: false,
    error: null,

    // Initialize points system for new user
    initializePoints: async (userId: string) => {
        set({ isLoading: true, error: null });

        try {
            const { error } = await PointsService.initializeUserPoints(userId);

            if (error) {
                set({ error: error.message, isLoading: false });
                return;
            }

            // Load initial data
            await get().loadBalance(userId);
            await get().loadAchievements(userId);

            set({ isLoading: false });
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to initialize points',
                isLoading: false,
            });
        }
    },

    // Load user's points balance
    loadBalance: async (userId: string) => {
        set({ isLoading: true, error: null });

        try {
            const { balance, error } = await PointsService.getPointsBalance(userId);

            if (error) {
                set({ error: error.message, isLoading: false });
                return;
            }

            set({ balance, isLoading: false });
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to load balance',
                isLoading: false,
            });
        }
    },

    // Load transaction history
    loadTransactions: async (userId: string, limit = 50, offset = 0) => {
        set({ isLoading: true, error: null });

        try {
            const { transactions, error } = await PointsService.getPointsHistory(userId, limit, offset);

            if (error) {
                set({ error: error.message, isLoading: false });
                return;
            }

            set({
                transactions: offset === 0 ? transactions : [...get().transactions, ...transactions],
                isLoading: false
            });
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to load transactions',
                isLoading: false,
            });
        }
    },

    // Load user achievements
    loadAchievements: async (userId: string) => {
        set({ isLoading: true, error: null });

        try {
            const { achievements, error } = await PointsService.getUserAchievements(userId);

            if (error) {
                set({ error: error.message, isLoading: false });
                return;
            }

            set({ achievements, isLoading: false });
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to load achievements',
                isLoading: false,
            });
        }
    },

    // Load available achievements
    loadAvailableAchievements: async () => {
        try {
            const { achievements, error } = await PointsService.getAvailableAchievements();

            if (error) {
                set({ error: error.message });
                return;
            }

            set({ availableAchievements: achievements });
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to load available achievements',
            });
        }
    },

    // Load leaderboard
    loadLeaderboard: async (limit = 50, timeframe = 'all_time') => {
        set({ isLoading: true, error: null });

        try {
            const { leaderboard, error } = await PointsService.getLeaderboard(limit, timeframe);

            if (error) {
                set({ error: error.message, isLoading: false });
                return;
            }

            set({ leaderboard, isLoading: false });
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to load leaderboard',
                isLoading: false,
            });
        }
    },

    // Award points to user
    awardPoints: async (userId: string, amount: number, source: PointsTransaction['source'], description: string, metadata?: any) => {
        try {
            const { transaction, error } = await PointsService.awardPoints(userId, amount, source, description, metadata);

            if (error) {
                set({ error: error.message });
                return false;
            }

            // Update local state
            if (transaction) {
                set(state => ({
                    transactions: [transaction, ...state.transactions],
                    balance: state.balance ? {
                        ...state.balance,
                        total_points: state.balance.total_points + amount,
                        available_points: state.balance.available_points + amount,
                        lifetime_earned: state.balance.lifetime_earned + amount,
                    } : null,
                }));
            }

            // Reload achievements in case new ones were earned
            await get().loadAchievements(userId);

            return true;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to award points',
            });
            return false;
        }
    },

    // Spend points
    spendPoints: async (userId: string, amount: number, source: PointsTransaction['source'], description: string, metadata?: any) => {
        try {
            const { transaction, error } = await PointsService.spendPoints(userId, amount, source, description, metadata);

            if (error) {
                set({ error: error.message });
                return false;
            }

            // Update local state
            if (transaction) {
                set(state => ({
                    transactions: [transaction, ...state.transactions],
                    balance: state.balance ? {
                        ...state.balance,
                        total_points: state.balance.total_points - amount,
                        available_points: state.balance.available_points - amount,
                        lifetime_spent: state.balance.lifetime_spent + amount,
                    } : null,
                }));
            }

            return true;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to spend points',
            });
            return false;
        }
    },

    // Redeem points for rewards
    redeemPoints: async (userId: string, rewardType: string, pointsCost: number) => {
        try {
            const success = await get().spendPoints(
                userId,
                pointsCost,
                'ai_consultation', // This would vary based on reward type
                `Redeemed ${rewardType}`,
                { reward_type: rewardType }
            );

            if (success) {
                // Handle specific reward logic here
                // For example, if it's AI consultation credits, update usage limits
            }

            return success;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to redeem points',
            });
            return false;
        }
    },

    // Check daily check-in
    checkDailyCheckin: async (userId: string) => {
        try {
            const today = new Date().toISOString().split('T')[0];

            // Check if user already checked in today
            const recentTransactions = get().transactions.filter(t =>
                t.source === 'daily_checkin' &&
                t.created_at.startsWith(today)
            );

            if (recentTransactions.length > 0) {
                return false; // Already checked in today
            }

            const success = await get().awardPoints(
                userId,
                5,
                'daily_checkin',
                'Daily check-in bonus'
            );

            return success;
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to process daily check-in',
            });
            return false;
        }
    },

    // Clear error
    clearError: () => {
        set({ error: null });
    },

    // Reset store
    reset: () => {
        set({
            balance: null,
            transactions: [],
            achievements: [],
            availableAchievements: [],
            leaderboard: [],
            dailyStreak: null,
            isLoading: false,
            error: null,
        });
    },
}));