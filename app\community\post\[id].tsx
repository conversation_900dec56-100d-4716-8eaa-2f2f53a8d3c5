import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    TextInput,
    Alert,
    KeyboardAvoidingView,
    Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../../../src/design-system';
import { CommunityPost } from '../../../src/components/community/CommunityPost';
import { CommentItem } from '../../../src/components/community/CommentItem';
import { VoiceButton } from '../../../src/components/ui/VoiceButton';
import { useCommunityStore } from '../../../src/stores/community';
import { useVoiceStore } from '../../../src/stores/voice';

export default function PostDetailScreen() {
    const { id } = useLocalSearchParams<{ id: string }>();
    const [commentText, setCommentText] = useState('');
    const [isSubmittingComment, setIsSubmittingComment] = useState(false);

    const {
        posts,
        comments,
        fetchComments,
        addComment,
        likePost,
        sharePost,
        likeComment,
        reportPost,
        error,
        clearError,
    } = useCommunityStore();

    const { speak, isVoiceEnabled, startListening } = useVoiceStore();

    const post = posts.find(p => p.id === id);
    const postComments = comments[id] || [];

    useEffect(() => {
        if (id) {
            fetchComments(id);
        }
    }, [id]);

    useEffect(() => {
        if (error) {
            Alert.alert('Error', error);
            clearError();
        }
    }, [error]);

    const handleAddComment = async () => {
        if (!commentText.trim()) {
            Alert.alert('Empty Comment', 'Please enter a comment');
            return;
        }

        setIsSubmittingComment(true);

        try {
            await addComment(id, commentText.trim());
            setCommentText('');

            if (isVoiceEnabled) {
                speak('Comment added successfully');
            }
        } catch (error) {
            Alert.alert('Error', 'Failed to add comment');
        } finally {
            setIsSubmittingComment(false);
        }
    };

    const handleVoiceComment = async () => {
        try {
            if (isVoiceEnabled) {
                speak('Say your comment');
                const voiceText = await startListening();

                if (voiceText) {
                    setCommentText(voiceText);
                    speak('Comment text added');
                }
            }
        } catch (error) {
            Alert.alert('Voice Error', 'Failed to process voice input');
        }
    };

    const handleReportPost = () => {
        Alert.alert(
            'Report Post',
            'Why are you reporting this post?',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Spam',
                    onPress: () => reportPost(id, 'spam', 'This post appears to be spam'),
                },
                {
                    text: 'Inappropriate',
                    onPress: () => reportPost(id, 'inappropriate', 'This post contains inappropriate content'),
                },
                {
                    text: 'Misinformation',
                    onPress: () => reportPost(id, 'misinformation', 'This post contains false information'),
                },
            ]
        );
    };

    if (!post) {
        return (
            <SafeAreaView style={{ flex: 1, backgroundColor: 'white' }}>
                <View style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                    padding: 20,
                }}>
                    <Ionicons name="document-outline" size={64} color={colors.earth[300]} />
                    <Text style={{
                        fontSize: 18,
                        fontWeight: '600',
                        color: colors.earth[600],
                        marginTop: 16,
                        textAlign: 'center',
                    }}>
                        Post not found
                    </Text>
                    <TouchableOpacity
                        onPress={() => router.back()}
                        style={{
                            backgroundColor: colors.primary[500],
                            paddingHorizontal: 24,
                            paddingVertical: 12,
                            borderRadius: 24,
                            marginTop: 20,
                        }}
                    >
                        <Text style={{ color: 'white', fontWeight: '600' }}>
                            Go Back
                        </Text>
                    </TouchableOpacity>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView style={{ flex: 1, backgroundColor: colors.primary[50] }}>
            <KeyboardAvoidingView
                style={{ flex: 1 }}
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            >
                {/* Header */}
                <View style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    paddingHorizontal: 16,
                    paddingVertical: 12,
                    backgroundColor: 'white',
                    borderBottomWidth: 1,
                    borderBottomColor: colors.primary[100],
                }}>
                    <TouchableOpacity
                        onPress={() => router.back()}
                        accessibilityLabel="Go back"
                    >
                        <Ionicons name="arrow-back" size={24} color={colors.primary[900]} />
                    </TouchableOpacity>

                    <Text style={{
                        fontSize: 18,
                        fontWeight: '600',
                        color: colors.primary[900],
                    }}>
                        Post Details
                    </Text>

                    <TouchableOpacity
                        onPress={handleReportPost}
                        accessibilityLabel="Report post"
                    >
                        <Ionicons name="flag-outline" size={24} color={colors.earth[500]} />
                    </TouchableOpacity>
                </View>

                <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
                    {/* Post */}
                    <CommunityPost
                        post={post}
                        onPress={() => { }} // Already on detail screen
                        onLike={() => likePost(post.id)}
                        onShare={() => sharePost(post.id)}
                        onComment={() => { }} // Scroll to comments
                    />

                    {/* Comments Section */}
                    <View style={{
                        backgroundColor: 'white',
                        marginHorizontal: 16,
                        marginTop: 8,
                        borderRadius: 12,
                        padding: 16,
                    }}>
                        <Text style={{
                            fontSize: 18,
                            fontWeight: '600',
                            color: colors.primary[900],
                            marginBottom: 16,
                        }}>
                            Comments ({postComments.length})
                        </Text>

                        {/* Add Comment */}
                        <View style={{
                            flexDirection: 'row',
                            alignItems: 'flex-end',
                            marginBottom: 20,
                            gap: 8,
                        }}>
                            <TextInput
                                value={commentText}
                                onChangeText={setCommentText}
                                placeholder="Add a comment..."
                                multiline
                                style={{
                                    flex: 1,
                                    borderWidth: 1,
                                    borderColor: colors.primary[200],
                                    borderRadius: 20,
                                    paddingHorizontal: 16,
                                    paddingVertical: 12,
                                    fontSize: 16,
                                    maxHeight: 100,
                                }}
                                accessibilityLabel="Comment input"
                            />

                            <VoiceButton
                                onPress={handleVoiceComment}
                                size="small"
                            />

                            <TouchableOpacity
                                onPress={handleAddComment}
                                disabled={!commentText.trim() || isSubmittingComment}
                                style={{
                                    backgroundColor: (!commentText.trim() || isSubmittingComment)
                                        ? colors.earth[200]
                                        : colors.primary[500],
                                    paddingHorizontal: 16,
                                    paddingVertical: 12,
                                    borderRadius: 20,
                                }}
                                accessibilityLabel="Send comment"
                            >
                                <Ionicons
                                    name="send"
                                    size={20}
                                    color="white"
                                />
                            </TouchableOpacity>
                        </View>

                        {/* Comments List */}
                        {postComments.length === 0 ? (
                            <View style={{
                                alignItems: 'center',
                                paddingVertical: 40,
                            }}>
                                <Ionicons name="chatbubbles-outline" size={48} color={colors.earth[300]} />
                                <Text style={{
                                    fontSize: 16,
                                    color: colors.earth[500],
                                    marginTop: 12,
                                    textAlign: 'center',
                                }}>
                                    No comments yet
                                </Text>
                                <Text style={{
                                    fontSize: 14,
                                    color: colors.earth[400],
                                    marginTop: 4,
                                    textAlign: 'center',
                                }}>
                                    Be the first to share your thoughts!
                                </Text>
                            </View>
                        ) : (
                            <View>
                                {postComments.map((comment) => (
                                    <CommentItem
                                        key={comment.id}
                                        comment={comment}
                                        onLike={() => likeComment(comment.id)}
                                        onReply={() => {
                                            // For now, just focus on the comment input
                                            // In a full implementation, this would handle nested replies
                                        }}
                                    />
                                ))}
                            </View>
                        )}
                    </View>
                </ScrollView>
            </KeyboardAvoidingView>
        </SafeAreaView>
    );
}