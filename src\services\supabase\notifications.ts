import { supabase } from './client';
import { Database } from '../../types/database';

export interface Notification {
    id: string;
    userId: string;
    type: 'like' | 'comment' | 'follow' | 'message' | 'event_invite' | 'post_mention';
    title: string;
    message: string;
    data?: any; // Additional data like post ID, user ID, etc.
    read: boolean;
    createdAt: Date;
}

export class NotificationService {
    /**
     * Create a notification for a user
     */
    static async createNotification(
        userId: string,
        type: Notification['type'],
        title: string,
        message: string,
        data?: any
    ): Promise<{
        notification: Notification | null;
        error: string | null;
    }> {
        try {
            const { data: notificationData, error } = await supabase
                .from('notifications')
                .insert({
                    user_id: userId,
                    type,
                    title,
                    message,
                    data,
                    read: false,
                })
                .select()
                .single();

            if (error) {
                return { notification: null, error: error.message };
            }

            const notification: Notification = {
                id: notificationData.id,
                userId: notificationData.user_id,
                type: notificationData.type as any,
                title: notificationData.title,
                message: notificationData.message,
                data: notificationData.data,
                read: notificationData.read,
                createdAt: new Date(notificationData.created_at),
            };

            return { notification, error: null };
        } catch (error) {
            return { notification: null, error: (error as Error).message };
        }
    }

    /**
     * Get notifications for the current user
     */
    static async getUserNotifications(limit = 50): Promise<{
        notifications: Notification[];
        error: string | null;
    }> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                return { notifications: [], error: 'User not authenticated' };
            }

            const { data, error } = await supabase
                .from('notifications')
                .select('*')
                .eq('user_id', user.id)
                .order('created_at', { ascending: false })
                .limit(limit);

            if (error) {
                return { notifications: [], error: error.message };
            }

            const notifications: Notification[] = (data || []).map((notif: any) => ({
                id: notif.id,
                userId: notif.user_id,
                type: notif.type,
                title: notif.title,
                message: notif.message,
                data: notif.data,
                read: notif.read,
                createdAt: new Date(notif.created_at),
            }));

            return { notifications, error: null };
        } catch (error) {
            return { notifications: [], error: (error as Error).message };
        }
    }

    /**
     * Mark notifications as read
     */
    static async markAsRead(notificationIds: string[]): Promise<{
        success: boolean;
        error: string | null;
    }> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                return { success: false, error: 'User not authenticated' };
            }

            const { error } = await supabase
                .from('notifications')
                .update({ read: true })
                .in('id', notificationIds)
                .eq('user_id', user.id);

            if (error) {
                return { success: false, error: error.message };
            }

            return { success: true, error: null };
        } catch (error) {
            return { success: false, error: (error as Error).message };
        }
    }

    /**
     * Get unread notification count
     */
    static async getUnreadCount(): Promise<{
        count: number;
        error: string | null;
    }> {
        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                return { count: 0, error: 'User not authenticated' };
            }

            const { count, error } = await supabase
                .from('notifications')
                .select('*', { count: 'exact', head: true })
                .eq('user_id', user.id)
                .eq('read', false);

            if (error) {
                return { count: 0, error: error.message };
            }

            return { count: count || 0, error: null };
        } catch (error) {
            return { count: 0, error: (error as Error).message };
        }
    }

    /**
     * Delete old notifications (older than 30 days)
     */
    static async cleanupOldNotifications(): Promise<{
        success: boolean;
        error: string | null;
    }> {
        try {
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            const { error } = await supabase
                .from('notifications')
                .delete()
                .lt('created_at', thirtyDaysAgo.toISOString());

            if (error) {
                return { success: false, error: error.message };
            }

            return { success: true, error: null };
        } catch (error) {
            return { success: false, error: (error as Error).message };
        }
    }

    /**
     * Subscribe to real-time notifications
     */
    static subscribeToNotifications(
        userId: string,
        onNotification: (notification: Notification) => void
    ) {
        return supabase
            .channel('notifications')
            .on(
                'postgres_changes',
                {
                    event: 'INSERT',
                    schema: 'public',
                    table: 'notifications',
                    filter: `user_id=eq.${userId}`,
                },
                (payload) => {
                    const notification: Notification = {
                        id: payload.new.id,
                        userId: payload.new.user_id,
                        type: payload.new.type,
                        title: payload.new.title,
                        message: payload.new.message,
                        data: payload.new.data,
                        read: payload.new.read,
                        createdAt: new Date(payload.new.created_at),
                    };
                    onNotification(notification);
                }
            )
            .subscribe();
    }

    // Helper methods for creating specific notification types

    /**
     * Notify when someone likes a post
     */
    static async notifyPostLike(postOwnerId: string, likerName: string, postTitle: string, postId: string) {
        return this.createNotification(
            postOwnerId,
            'like',
            'New Like',
            `${likerName} liked your post "${postTitle}"`,
            { postId, type: 'post_like' }
        );
    }

    /**
     * Notify when someone comments on a post
     */
    static async notifyPostComment(postOwnerId: string, commenterName: string, postTitle: string, postId: string) {
        return this.createNotification(
            postOwnerId,
            'comment',
            'New Comment',
            `${commenterName} commented on your post "${postTitle}"`,
            { postId, type: 'post_comment' }
        );
    }

    /**
     * Notify when someone follows you
     */
    static async notifyNewFollower(userId: string, followerName: string, followerId: string) {
        return this.createNotification(
            userId,
            'follow',
            'New Follower',
            `${followerName} started following you`,
            { userId: followerId, type: 'new_follower' }
        );
    }

    /**
     * Notify when someone sends a direct message
     */
    static async notifyDirectMessage(recipientId: string, senderName: string, senderId: string) {
        return this.createNotification(
            recipientId,
            'message',
            'New Message',
            `${senderName} sent you a message`,
            { userId: senderId, type: 'direct_message' }
        );
    }

    /**
     * Notify when someone invites you to an event
     */
    static async notifyEventInvite(userId: string, organizerName: string, eventTitle: string, eventId: string) {
        return this.createNotification(
            userId,
            'event_invite',
            'Event Invitation',
            `${organizerName} invited you to "${eventTitle}"`,
            { eventId, type: 'event_invite' }
        );
    }
}