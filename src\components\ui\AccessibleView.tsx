/**
 * Accessible View Component
 * Enhanced view component with focus management and keyboard navigation
 */

import React, { useRef, useEffect } from 'react';
import { View, ViewProps, Pressable } from 'react-native';
import { useAccessibility } from '../../hooks/useAccessibility';
import { useFocusable } from '../../utils/keyboardNavigation';

interface AccessibleViewProps extends ViewProps {
    focusable?: boolean;
    focusOrder?: number;
    autoFocus?: boolean;
    onFocus?: () => void;
    onBlur?: () => void;
    onActivate?: () => void;
    semanticRole?: 'main' | 'navigation' | 'complementary' | 'banner' | 'contentinfo' | 'region';
    accessibilityLabel?: string;
    accessibilityHint?: string;
    children: React.ReactNode;
}

export const AccessibleView: React.FC<AccessibleViewProps> = ({
    focusable = false,
    focusOrder = 0,
    autoFocus = false,
    onFocus,
    onBlur,
    onActivate,
    semanticRole,
    accessibilityLabel,
    accessibilityHint,
    style,
    children,
    ...props
}) => {
    const viewRef = useRef<View>(null);
    const {
        getHighContrastColors,
        getAccessibilityProps,
        isKeyboardNavigationEnabled,
    } = useAccessibility();

    const { ref: focusRef, focus, blur } = useFocusable(
        `view-${Math.random().toString(36).substr(2, 9)}`,
        focusOrder,
        {
            onFocus: () => {
                onFocus?.();
            },
            onBlur: () => {
                onBlur?.();
            },
            onActivate: () => {
                onActivate?.();
            },
            autoFocus,
            disabled: !focusable || !isKeyboardNavigationEnabled,
        }
    );

    const highContrastColors = getHighContrastColors();

    const getFocusStyles = () => {
        if (!focusable || !highContrastColors) return {};

        return {
            borderWidth: 2,
            borderColor: 'transparent',
            borderRadius: 8,
        };
    };

    const getAccessibilityRole = () => {
        if (semanticRole) {
            const roleMap = {
                main: 'main',
                navigation: 'navigation',
                complementary: 'complementary',
                banner: 'banner',
                contentinfo: 'contentinfo',
                region: 'region',
            };
            return roleMap[semanticRole];
        }
        return focusable ? 'button' : undefined;
    };

    if (focusable && isKeyboardNavigationEnabled) {
        return (
            <Pressable
                ref={focusRef}
                style={[
                    getFocusStyles(),
                    style,
                ]}
                onPress={onActivate}
                onFocus={focus}
                onBlur={blur}
                {...getAccessibilityProps(
                    accessibilityLabel || 'Interactive area',
                    accessibilityHint,
                    getAccessibilityRole()
                )}
                {...props}
            >
                {children}
            </Pressable>
        );
    }

    return (
        <View
            ref={viewRef}
            style={style}
            {...(accessibilityLabel && getAccessibilityProps(
                accessibilityLabel,
                accessibilityHint,
                getAccessibilityRole()
            ))}
            {...props}
        >
            {children}
        </View>
    );
};