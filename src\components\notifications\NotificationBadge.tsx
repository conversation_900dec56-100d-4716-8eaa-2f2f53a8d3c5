import React from 'react';
import { View, Text } from 'react-native';
import { useNotificationHistory } from '../../stores/notifications';

interface NotificationBadgeProps {
    size?: 'small' | 'medium' | 'large';
    color?: string;
    textColor?: string;
    maxCount?: number;
    showZero?: boolean;
}

export const NotificationBadge: React.FC<NotificationBadgeProps> = ({
    size = 'medium',
    color = '#ef4444',
    textColor = '#ffffff',
    maxCount = 99,
    showZero = false,
}) => {
    const { unreadCount } = useNotificationHistory();

    if (unreadCount === 0 && !showZero) {
        return null;
    }

    const getSizeClasses = () => {
        switch (size) {
            case 'small':
                return 'w-4 h-4 min-w-4';
            case 'large':
                return 'w-8 h-8 min-w-8';
            default:
                return 'w-6 h-6 min-w-6';
        }
    };

    const getTextSize = () => {
        switch (size) {
            case 'small':
                return 'text-xs';
            case 'large':
                return 'text-sm';
            default:
                return 'text-xs';
        }
    };

    const displayCount = unreadCount > maxCount ? `${maxCount}+` : unreadCount.toString();

    return (
        <View
            className={`${getSizeClasses()} rounded-full items-center justify-center absolute -top-1 -right-1 z-10`}
            style={{ backgroundColor: color }}
        >
            <Text
                className={`${getTextSize()} font-bold`}
                style={{ color: textColor }}
            >
                {displayCount}
            </Text>
        </View>
    );
};

export default NotificationBadge;