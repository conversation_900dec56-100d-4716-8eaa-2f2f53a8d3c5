/**
 * Typography system optimized for accessibility and agricultural use
 * Voice-friendly sizes with clear hierarchy
 */

export const typography = {
  fontSizes: {
    xs: 12, // Captions
    sm: 14, // Small text
    base: 16, // Body text
    lg: 18, // Large body
    xl: 20, // Headings
    '2xl': 24, // Large headings
    '3xl': 30, // Page titles
    '4xl': 36, // Hero text
  },
  lineHeights: {
    xs: 16,
    sm: 20,
    base: 24,
    lg: 28,
    xl: 28,
    '2xl': 32,
    '3xl': 36,
    '4xl': 40,
  },
  fontWeights: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
  letterSpacing: {
    tight: -0.025,
    normal: 0,
    wide: 0.025,
  },
} as const;

export const textStyles = {
  // Headings
  h1: 'text-4xl font-bold text-earth-900',
  h2: 'text-3xl font-bold text-earth-900',
  h3: 'text-2xl font-semibold text-earth-800',
  h4: 'text-xl font-semibold text-earth-800',
  h5: 'text-lg font-medium text-earth-700',
  h6: 'text-base font-medium text-earth-700',

  // Body text
  bodyLarge: 'text-lg text-earth-700 leading-relaxed',
  body: 'text-base text-earth-700 leading-relaxed',
  bodySmall: 'text-sm text-earth-600 leading-relaxed',

  // Special text
  caption: 'text-xs text-earth-500 uppercase tracking-wide',
  label: 'text-sm font-medium text-earth-700',
  link: 'text-primary-600 font-medium underline',

  // Voice-friendly text (larger for accessibility)
  voiceLarge: 'text-2xl font-medium text-earth-800',
  voiceBody: 'text-xl text-earth-700',
  voiceSmall: 'text-lg text-earth-600',
} as const;

export type TextStyle = keyof typeof textStyles;
