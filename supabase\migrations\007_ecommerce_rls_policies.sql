-- Row Level Security policies for e-commerce tables
-- Ensures users can only access their own data while allowing public product access

-- Enable RLS on all e-commerce tables
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_variants ENABLE ROW LEVEL SECURITY;
ALTER TABLE shopping_carts ENABLE ROW LEVEL SECURITY;
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE wishlists ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_categories ENABLE ROW LEVEL SECURITY;

-- Products policies (public read, admin write)
CREATE POLICY "Products are publicly readable" ON products
    FOR SELECT USING (is_active = true);

CREATE POLICY "Authenticated users can read all products" ON products
    FOR SELECT USING (auth.role() = 'authenticated');

-- Product reviews policies
CREATE POLICY "Users can read all product reviews" ON product_reviews
    FOR SELECT USING (true);

CREATE POLICY "Users can create reviews for products they purchased" ON product_reviews
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        EXISTS (
            SELECT 1 FROM order_items oi
            JOIN orders o ON oi.order_id = o.id
            WHERE o.user_id = auth.uid() 
            AND oi.product_id = product_reviews.product_id
            AND o.status = 'delivered'
        )
    );

CREATE POLICY "Users can update their own reviews" ON product_reviews
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own reviews" ON product_reviews
    FOR DELETE USING (auth.uid() = user_id);

-- Product variants policies (public read)
CREATE POLICY "Product variants are publicly readable" ON product_variants
    FOR SELECT USING (true);

-- Shopping carts policies
CREATE POLICY "Users can access their own cart" ON shopping_carts
    FOR ALL USING (auth.uid() = user_id);

-- Cart items policies
CREATE POLICY "Users can access their own cart items" ON cart_items
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM shopping_carts sc
            WHERE sc.id = cart_items.cart_id
            AND sc.user_id = auth.uid()
        )
    );

-- Orders policies
CREATE POLICY "Users can access their own orders" ON orders
    FOR ALL USING (auth.uid() = user_id);

-- Order items policies
CREATE POLICY "Users can access their own order items" ON order_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders o
            WHERE o.id = order_items.order_id
            AND o.user_id = auth.uid()
        )
    );

-- Product recommendations policies
CREATE POLICY "Users can access their own recommendations" ON product_recommendations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can create recommendations" ON product_recommendations
    FOR INSERT WITH CHECK (true);

CREATE POLICY "System can update recommendations" ON product_recommendations
    FOR UPDATE USING (true);

CREATE POLICY "System can delete expired recommendations" ON product_recommendations
    FOR DELETE USING (expires_at < NOW());

-- Inventory transactions policies (read-only for users, admin access for modifications)
CREATE POLICY "Users can read inventory transactions" ON inventory_transactions
    FOR SELECT USING (true);

-- Wishlists policies
CREATE POLICY "Users can access their own wishlist" ON wishlists
    FOR ALL USING (auth.uid() = user_id);

-- Product categories policies (public read)
CREATE POLICY "Product categories are publicly readable" ON product_categories
    FOR SELECT USING (is_active = true);

-- Additional policies for admin access (when admin role is implemented)
-- These policies will be activated when admin authentication is set up

-- CREATE POLICY "Admins can manage products" ON products
--     FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- CREATE POLICY "Admins can manage inventory" ON inventory_transactions
--     FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- CREATE POLICY "Admins can view all orders" ON orders
--     FOR SELECT USING (auth.jwt() ->> 'role' = 'admin');

-- Function to check if user has purchased a product (for reviews)
CREATE OR REPLACE FUNCTION user_has_purchased_product(user_id UUID, product_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM order_items oi
        JOIN orders o ON oi.order_id = o.id
        WHERE o.user_id = user_has_purchased_product.user_id
        AND oi.product_id = user_has_purchased_product.product_id
        AND o.status = 'delivered'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's cart ID
CREATE OR REPLACE FUNCTION get_user_cart_id(user_id UUID)
RETURNS UUID AS $$
DECLARE
    cart_id UUID;
BEGIN
    SELECT id INTO cart_id
    FROM shopping_carts
    WHERE shopping_carts.user_id = get_user_cart_id.user_id;
    
    -- Create cart if it doesn't exist
    IF cart_id IS NULL THEN
        INSERT INTO shopping_carts (user_id)
        VALUES (get_user_cart_id.user_id)
        RETURNING id INTO cart_id;
    END IF;
    
    RETURN cart_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate cart total
CREATE OR REPLACE FUNCTION calculate_cart_total(cart_id UUID)
RETURNS DECIMAL(10,2) AS $$
DECLARE
    total DECIMAL(10,2);
BEGIN
    SELECT COALESCE(SUM(ci.quantity * ci.unit_price), 0)
    INTO total
    FROM cart_items ci
    WHERE ci.cart_id = calculate_cart_total.cart_id;
    
    RETURN total;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check product stock availability
CREATE OR REPLACE FUNCTION check_stock_availability(product_id UUID, requested_quantity INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
    available_stock INTEGER;
BEGIN
    SELECT stock_quantity INTO available_stock
    FROM products
    WHERE id = check_stock_availability.product_id
    AND is_active = true;
    
    RETURN COALESCE(available_stock, 0) >= requested_quantity;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION user_has_purchased_product(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_cart_id(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_cart_total(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION check_stock_availability(UUID, INTEGER) TO authenticated;