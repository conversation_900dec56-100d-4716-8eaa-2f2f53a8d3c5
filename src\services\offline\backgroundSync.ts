import { syncService } from './syncService';
import { connectivityService } from './connectivity';
import { offlineDatabase } from './database';
import { SyncResult } from './syncService';

export interface BackgroundSyncConfig {
    enabled: boolean;
    syncInterval: number; // milliseconds
    maxSyncAttempts: number;
    syncOnAppForeground: boolean;
    syncOnConnectivityRestore: boolean;
    quietHours: {
        enabled: boolean;
        startHour: number; // 0-23
        endHour: number; // 0-23
    };
    wifiOnly: boolean;
    batteryOptimized: boolean;
}

export interface BackgroundSyncStatus {
    isEnabled: boolean;
    nextSyncTime: number | null;
    lastSyncAttempt: number | null;
    lastSuccessfulSync: number | null;
    consecutiveFailures: number;
    isInQuietHours: boolean;
}

export class BackgroundSyncService {
    private config: BackgroundSyncConfig = {
        enabled: true,
        syncInterval: 5 * 60 * 1000, // 5 minutes
        maxSyncAttempts: 3,
        syncOnAppForeground: true,
        syncOnConnectivityRestore: true,
        quietHours: {
            enabled: false,
            startHour: 22, // 10 PM
            endHour: 6     // 6 AM
        },
        wifiOnly: false,
        batteryOptimized: true
    };

    private syncTimer: NodeJS.Timeout | null = null;
    private isRunning = false;
    private consecutiveFailures = 0;
    private lastSyncAttempt: number | null = null;
    private lastSuccessfulSync: number | null = null;
    private connectivityUnsubscribe: (() => void) | null = null;
    private statusListeners: ((status: BackgroundSyncStatus) => void)[] = [];

    constructor(config?: Partial<BackgroundSyncConfig>) {
        if (config) {
            this.config = { ...this.config, ...config };
        }
    }

    async initialize(): Promise<void> {
        console.log('Initializing background sync service...');

        // Initialize sync service
        await syncService.initialize();

        // Set up connectivity listener
        this.connectivityUnsubscribe = connectivityService.addListener((state) => {
            if (this.config.syncOnConnectivityRestore &&
                state.isConnected &&
                state.isInternetReachable &&
                this.shouldSync()) {

                console.log('Connectivity restored, triggering sync...');
                this.performBackgroundSync();
            }
        });

        // Start background sync if enabled
        if (this.config.enabled) {
            this.start();
        }

        console.log('Background sync service initialized');
    }

    start(): void {
        if (this.isRunning) {
            console.log('Background sync already running');
            return;
        }

        console.log('Starting background sync service');
        this.isRunning = true;
        this.scheduleNextSync();
        this.notifyStatusListeners();
    }

    stop(): void {
        if (!this.isRunning) {
            console.log('Background sync not running');
            return;
        }

        console.log('Stopping background sync service');
        this.isRunning = false;

        if (this.syncTimer) {
            clearTimeout(this.syncTimer);
            this.syncTimer = null;
        }

        this.notifyStatusListeners();
    }

    private scheduleNextSync(): void {
        if (!this.isRunning) return;

        if (this.syncTimer) {
            clearTimeout(this.syncTimer);
        }

        const delay = this.calculateNextSyncDelay();

        this.syncTimer = setTimeout(() => {
            this.performBackgroundSync();
        }, delay);

        console.log(`Next background sync scheduled in ${Math.round(delay / 1000)}s`);
        this.notifyStatusListeners();
    }

    private calculateNextSyncDelay(): number {
        let baseDelay = this.config.syncInterval;

        // Apply exponential backoff for consecutive failures
        if (this.consecutiveFailures > 0) {
            const backoffMultiplier = Math.min(Math.pow(2, this.consecutiveFailures), 16); // Max 16x delay
            baseDelay *= backoffMultiplier;
            console.log(`Applying backoff: ${backoffMultiplier}x delay due to ${this.consecutiveFailures} failures`);
        }

        // Battery optimization - increase interval when on cellular
        if (this.config.batteryOptimized && connectivityService.isCellularConnection()) {
            baseDelay *= 2;
            console.log('Battery optimization: doubling sync interval on cellular');
        }

        // Respect quiet hours
        if (this.config.quietHours.enabled && this.isInQuietHours()) {
            const nextActiveTime = this.getNextActiveTime();
            const timeUntilActive = nextActiveTime - Date.now();

            if (timeUntilActive > 0) {
                console.log(`In quiet hours, delaying sync until ${new Date(nextActiveTime).toLocaleTimeString()}`);
                return timeUntilActive;
            }
        }

        return Math.max(baseDelay, 30000); // Minimum 30 seconds
    }

    private async performBackgroundSync(): Promise<void> {
        if (!this.shouldSync()) {
            console.log('Skipping background sync - conditions not met');
            this.scheduleNextSync();
            return;
        }

        console.log('Performing background sync...');
        this.lastSyncAttempt = Date.now();

        try {
            // Check if there are items to sync
            const pendingItems = await offlineDatabase.getSyncQueue('pending');
            const failedItems = await offlineDatabase.getSyncQueue('failed');

            if (pendingItems.length === 0 && failedItems.length === 0) {
                console.log('No items to sync');
                this.consecutiveFailures = 0;
                this.scheduleNextSync();
                return;
            }

            // Perform incremental sync
            const result: SyncResult = await syncService.performIncrementalSync();

            if (result.success) {
                console.log(`Background sync successful: ${result.synced} items synced`);
                this.lastSuccessfulSync = Date.now();
                this.consecutiveFailures = 0;
            } else {
                console.log(`Background sync failed: ${result.failed} items failed`);
                this.consecutiveFailures++;
            }

        } catch (error) {
            console.error('Background sync error:', error);
            this.consecutiveFailures++;
        }

        // Schedule next sync
        this.scheduleNextSync();
        this.notifyStatusListeners();
    }

    private shouldSync(): boolean {
        // Check if sync is enabled
        if (!this.config.enabled || !this.isRunning) {
            return false;
        }

        // Check connectivity
        if (!connectivityService.isOnline()) {
            return false;
        }

        // Check WiFi-only setting
        if (this.config.wifiOnly && !connectivityService.isWifiConnection()) {
            console.log('WiFi-only mode enabled, but not on WiFi');
            return false;
        }

        // Check if sync is already in progress
        if (syncService.isSyncInProgress()) {
            console.log('Sync already in progress');
            return false;
        }

        // Check quiet hours
        if (this.config.quietHours.enabled && this.isInQuietHours()) {
            console.log('In quiet hours, skipping sync');
            return false;
        }

        // Check max sync attempts
        if (this.consecutiveFailures >= this.config.maxSyncAttempts) {
            console.log(`Max sync attempts (${this.config.maxSyncAttempts}) reached`);
            return false;
        }

        return true;
    }

    private isInQuietHours(): boolean {
        if (!this.config.quietHours.enabled) {
            return false;
        }

        const now = new Date();
        const currentHour = now.getHours();
        const { startHour, endHour } = this.config.quietHours;

        if (startHour <= endHour) {
            // Same day quiet hours (e.g., 14:00 - 18:00)
            return currentHour >= startHour && currentHour < endHour;
        } else {
            // Overnight quiet hours (e.g., 22:00 - 06:00)
            return currentHour >= startHour || currentHour < endHour;
        }
    }

    private getNextActiveTime(): number {
        const now = new Date();
        const { endHour } = this.config.quietHours;

        const nextActive = new Date(now);
        nextActive.setHours(endHour, 0, 0, 0);

        // If end hour is today but already passed, move to tomorrow
        if (nextActive <= now) {
            nextActive.setDate(nextActive.getDate() + 1);
        }

        return nextActive.getTime();
    }

    // Public API
    async triggerSync(): Promise<SyncResult> {
        console.log('Manual background sync triggered');

        if (!connectivityService.isOnline()) {
            throw new Error('Cannot sync while offline');
        }

        try {
            const result = await syncService.performIncrementalSync();

            if (result.success) {
                this.lastSuccessfulSync = Date.now();
                this.consecutiveFailures = 0;
            } else {
                this.consecutiveFailures++;
            }

            this.lastSyncAttempt = Date.now();
            this.notifyStatusListeners();

            return result;
        } catch (error) {
            this.consecutiveFailures++;
            this.lastSyncAttempt = Date.now();
            this.notifyStatusListeners();
            throw error;
        }
    }

    getStatus(): BackgroundSyncStatus {
        return {
            isEnabled: this.config.enabled && this.isRunning,
            nextSyncTime: this.getNextSyncTime(),
            lastSyncAttempt: this.lastSyncAttempt,
            lastSuccessfulSync: this.lastSuccessfulSync,
            consecutiveFailures: this.consecutiveFailures,
            isInQuietHours: this.isInQuietHours()
        };
    }

    private getNextSyncTime(): number | null {
        if (!this.isRunning || !this.syncTimer) {
            return null;
        }

        // This is an approximation since we don't store the exact scheduled time
        return Date.now() + this.calculateNextSyncDelay();
    }

    updateConfig(config: Partial<BackgroundSyncConfig>): void {
        const wasRunning = this.isRunning;

        if (wasRunning) {
            this.stop();
        }

        this.config = { ...this.config, ...config };

        if (config.enabled !== undefined && config.enabled && wasRunning) {
            this.start();
        }

        this.notifyStatusListeners();
    }

    getConfig(): BackgroundSyncConfig {
        return { ...this.config };
    }

    resetFailureCount(): void {
        console.log('Resetting consecutive failure count');
        this.consecutiveFailures = 0;
        this.notifyStatusListeners();
    }

    // Event handling
    addStatusListener(listener: (status: BackgroundSyncStatus) => void): () => void {
        this.statusListeners.push(listener);

        return () => {
            const index = this.statusListeners.indexOf(listener);
            if (index > -1) {
                this.statusListeners.splice(index, 1);
            }
        };
    }

    private notifyStatusListeners(): void {
        const status = this.getStatus();
        this.statusListeners.forEach(listener => {
            try {
                listener(status);
            } catch (error) {
                console.error('Error in background sync status listener:', error);
            }
        });
    }

    // App lifecycle methods
    onAppForeground(): void {
        if (this.config.syncOnAppForeground && this.shouldSync()) {
            console.log('App foregrounded, triggering sync...');
            setTimeout(() => {
                this.performBackgroundSync();
            }, 1000); // Small delay to allow app to settle
        }
    }

    onAppBackground(): void {
        // Optionally adjust sync behavior when app goes to background
        console.log('App backgrounded');
    }

    onLowBattery(): void {
        if (this.config.batteryOptimized) {
            console.log('Low battery detected, reducing sync frequency');
            // Temporarily increase sync interval
            const originalInterval = this.config.syncInterval;
            this.config.syncInterval *= 3;

            // Reset after 30 minutes
            setTimeout(() => {
                this.config.syncInterval = originalInterval;
                console.log('Battery optimization period ended, restoring normal sync frequency');
            }, 30 * 60 * 1000);
        }
    }

    // Cleanup
    destroy(): void {
        this.stop();

        if (this.connectivityUnsubscribe) {
            this.connectivityUnsubscribe();
            this.connectivityUnsubscribe = null;
        }

        this.statusListeners = [];
    }
}

export const backgroundSyncService = new BackgroundSyncService();