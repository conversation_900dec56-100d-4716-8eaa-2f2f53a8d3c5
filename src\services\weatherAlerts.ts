import { supabase } from './supabase/client';
import notificationService from './notifications';
import NotificationTemplateService from './notificationTemplates';
import { WeatherAlert } from '../types/notifications';

export interface WeatherCondition {
    temperature: number;
    humidity: number;
    windSpeed: number;
    precipitation: number;
    pressure: number;
    uvIndex: number;
    visibility: number;
    condition: string;
    timestamp: Date;
}

export interface WeatherForecast {
    date: Date;
    minTemp: number;
    maxTemp: number;
    condition: string;
    precipitationChance: number;
    windSpeed: number;
    humidity: number;
}

export interface AlertThreshold {
    id: string;
    alertType: WeatherAlert['alertType'];
    conditions: {
        temperature?: { min?: number; max?: number };
        windSpeed?: { max: number };
        precipitation?: { min: number; timeframe: number }; // mm in hours
        humidity?: { min?: number; max?: number };
        pressure?: { min: number };
        uvIndex?: { max: number };
    };
    severity: WeatherAlert['severity'];
    leadTime: number; // hours before event
    affectedCrops?: string[];
    recommendations: string[];
}

export class WeatherAlertService {
    private static instance: WeatherAlertService;
    private alertThresholds: AlertThreshold[] = [];

    static getInstance(): WeatherAlertService {
        if (!WeatherAlertService.instance) {
            WeatherAlertService.instance = new WeatherAlertService();
        }
        return WeatherAlertService.instance;
    }

    async initialize(): Promise<void> {
        try {
            await this.loadAlertThresholds();
            console.log('Weather alert service initialized');
        } catch (error) {
            console.error('Failed to initialize weather alert service:', error);
        }
    }

    private async loadAlertThresholds(): Promise<void> {
        // Define default alert thresholds
        this.alertThresholds = [
            {
                id: 'frost_warning',
                alertType: 'frost_warning',
                conditions: {
                    temperature: { min: -2, max: 4 }, // Celsius
                },
                severity: 'severe',
                leadTime: 12,
                affectedCrops: ['tomatoes', 'peppers', 'corn', 'beans'],
                recommendations: [
                    'Cover sensitive plants with frost cloth',
                    'Water plants before sunset to retain heat',
                    'Move potted plants indoors',
                    'Harvest mature crops before frost hits',
                ],
            },
            {
                id: 'heat_wave',
                alertType: 'heat_wave',
                conditions: {
                    temperature: { min: 35 }, // Celsius
                },
                severity: 'moderate',
                leadTime: 24,
                affectedCrops: ['lettuce', 'spinach', 'peas', 'cool-season crops'],
                recommendations: [
                    'Increase watering frequency',
                    'Provide shade cloth for sensitive crops',
                    'Harvest early morning or evening',
                    'Mulch around plants to retain moisture',
                ],
            },
            {
                id: 'severe_wind',
                alertType: 'severe_weather',
                conditions: {
                    windSpeed: { max: 50 }, // km/h
                },
                severity: 'severe',
                leadTime: 6,
                affectedCrops: ['corn', 'sunflowers', 'tall crops'],
                recommendations: [
                    'Stake tall plants securely',
                    'Harvest ripe fruits to prevent damage',
                    'Secure greenhouse structures',
                    'Remove loose objects from fields',
                ],
            },
            {
                id: 'drought_alert',
                alertType: 'drought_alert',
                conditions: {
                    precipitation: { min: 5, timeframe: 168 }, // Less than 5mm in 7 days
                },
                severity: 'moderate',
                leadTime: 0,
                recommendations: [
                    'Implement water conservation measures',
                    'Increase mulching to retain soil moisture',
                    'Consider drought-resistant crop varieties',
                    'Monitor soil moisture levels closely',
                ],
            },
            {
                id: 'flood_warning',
                alertType: 'flood_warning',
                conditions: {
                    precipitation: { min: 50, timeframe: 24 }, // More than 50mm in 24 hours
                },
                severity: 'severe',
                leadTime: 12,
                recommendations: [
                    'Ensure proper field drainage',
                    'Harvest crops if possible before flooding',
                    'Protect stored equipment and supplies',
                    'Plan for post-flood field recovery',
                ],
            },
        ];
    }

    async checkWeatherAlerts(
        location: { latitude: number; longitude: number; name: string },
        currentWeather: WeatherCondition,
        forecast: WeatherForecast[]
    ): Promise<WeatherAlert[]> {
        const alerts: WeatherAlert[] = [];

        try {
            for (const threshold of this.alertThresholds) {
                const alert = await this.evaluateThreshold(threshold, location, currentWeather, forecast);
                if (alert) {
                    alerts.push(alert);
                }
            }

            // Send alerts to users
            if (alerts.length > 0) {
                await this.sendWeatherAlerts(alerts, location);
            }

            return alerts;
        } catch (error) {
            console.error('Failed to check weather alerts:', error);
            return [];
        }
    }

    private async evaluateThreshold(
        threshold: AlertThreshold,
        location: { latitude: number; longitude: number; name: string },
        currentWeather: WeatherCondition,
        forecast: WeatherForecast[]
    ): Promise<WeatherAlert | null> {
        const conditions = threshold.conditions;
        let alertTriggered = false;
        let alertStartTime = new Date();
        let alertEndTime = new Date();

        // Check current conditions
        if (this.checkCurrentConditions(conditions, currentWeather)) {
            alertTriggered = true;
            alertStartTime = new Date();
            alertEndTime = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now
        }

        // Check forecast conditions
        const forecastAlert = this.checkForecastConditions(conditions, forecast, threshold.leadTime);
        if (forecastAlert) {
            alertTriggered = true;
            alertStartTime = forecastAlert.startTime;
            alertEndTime = forecastAlert.endTime;
        }

        if (!alertTriggered) {
            return null;
        }

        // Get affected crops for this location
        const affectedCrops = await this.getAffectedCropsForLocation(location, threshold.affectedCrops);

        return {
            id: `${threshold.id}_${Date.now()}`,
            location,
            alertType: threshold.alertType,
            severity: threshold.severity,
            title: this.generateAlertTitle(threshold.alertType, threshold.severity),
            description: this.generateAlertDescription(threshold.alertType, currentWeather, forecast),
            startTime: alertStartTime,
            endTime: alertEndTime,
            recommendations: threshold.recommendations,
            affectedCrops,
        };
    }

    private checkCurrentConditions(conditions: AlertThreshold['conditions'], weather: WeatherCondition): boolean {
        // Temperature checks
        if (conditions.temperature) {
            if (conditions.temperature.min !== undefined && weather.temperature < conditions.temperature.min) {
                return true;
            }
            if (conditions.temperature.max !== undefined && weather.temperature > conditions.temperature.max) {
                return true;
            }
        }

        // Wind speed check
        if (conditions.windSpeed && weather.windSpeed > conditions.windSpeed.max) {
            return true;
        }

        // Humidity checks
        if (conditions.humidity) {
            if (conditions.humidity.min !== undefined && weather.humidity < conditions.humidity.min) {
                return true;
            }
            if (conditions.humidity.max !== undefined && weather.humidity > conditions.humidity.max) {
                return true;
            }
        }

        // Pressure check
        if (conditions.pressure && weather.pressure < conditions.pressure.min) {
            return true;
        }

        // UV Index check
        if (conditions.uvIndex && weather.uvIndex > conditions.uvIndex.max) {
            return true;
        }

        return false;
    }

    private checkForecastConditions(
        conditions: AlertThreshold['conditions'],
        forecast: WeatherForecast[],
        leadTime: number
    ): { startTime: Date; endTime: Date } | null {
        const alertWindow = new Date(Date.now() + leadTime * 60 * 60 * 1000);

        for (const day of forecast) {
            if (day.date <= alertWindow) {
                // Check temperature conditions
                if (conditions.temperature) {
                    if (conditions.temperature.min !== undefined && day.minTemp < conditions.temperature.min) {
                        return {
                            startTime: day.date,
                            endTime: new Date(day.date.getTime() + 24 * 60 * 60 * 1000),
                        };
                    }
                    if (conditions.temperature.max !== undefined && day.maxTemp > conditions.temperature.max) {
                        return {
                            startTime: day.date,
                            endTime: new Date(day.date.getTime() + 24 * 60 * 60 * 1000),
                        };
                    }
                }

                // Check wind speed
                if (conditions.windSpeed && day.windSpeed > conditions.windSpeed.max) {
                    return {
                        startTime: day.date,
                        endTime: new Date(day.date.getTime() + 24 * 60 * 60 * 1000),
                    };
                }

                // Check precipitation for flood warning
                if (conditions.precipitation && conditions.precipitation.min) {
                    const expectedPrecipitation = (day.precipitationChance / 100) * 20; // Rough estimate
                    if (expectedPrecipitation > conditions.precipitation.min) {
                        return {
                            startTime: day.date,
                            endTime: new Date(day.date.getTime() + conditions.precipitation.timeframe * 60 * 60 * 1000),
                        };
                    }
                }
            }
        }

        return null;
    }

    private async getAffectedCropsForLocation(
        location: { latitude: number; longitude: number; name: string },
        defaultCrops?: string[]
    ): Promise<string[]> {
        try {
            // Get user's active crop plans for this location
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return defaultCrops || [];

            const { data: cropPlans } = await supabase
                .from('crop_plans')
                .select('crop_type')
                .eq('user_id', user.id)
                .eq('status', 'active');

            if (cropPlans && cropPlans.length > 0) {
                return cropPlans.map(plan => plan.crop_type);
            }

            return defaultCrops || [];
        } catch (error) {
            console.error('Failed to get affected crops:', error);
            return defaultCrops || [];
        }
    }

    private generateAlertTitle(alertType: WeatherAlert['alertType'], severity: WeatherAlert['severity']): string {
        const severityText = severity.charAt(0).toUpperCase() + severity.slice(1);

        switch (alertType) {
            case 'frost_warning':
                return `${severityText} Frost Warning`;
            case 'heat_wave':
                return `${severityText} Heat Wave Alert`;
            case 'severe_weather':
                return `${severityText} Weather Alert`;
            case 'drought_alert':
                return `${severityText} Drought Conditions`;
            case 'flood_warning':
                return `${severityText} Flood Warning`;
            default:
                return `${severityText} Weather Alert`;
        }
    }

    private generateAlertDescription(
        alertType: WeatherAlert['alertType'],
        currentWeather: WeatherCondition,
        forecast: WeatherForecast[]
    ): string {
        switch (alertType) {
            case 'frost_warning':
                return `Frost conditions expected with temperatures dropping to ${Math.round(currentWeather.temperature)}°C. Protect sensitive crops immediately.`;
            case 'heat_wave':
                return `Extreme heat expected with temperatures reaching ${Math.round(currentWeather.temperature)}°C. Increase watering and provide shade.`;
            case 'severe_weather':
                return `Severe weather conditions with winds up to ${Math.round(currentWeather.windSpeed)} km/h. Secure crops and equipment.`;
            case 'drought_alert':
                return `Extended dry period detected. Current humidity: ${Math.round(currentWeather.humidity)}%. Implement water conservation measures.`;
            case 'flood_warning':
                return `Heavy rainfall expected. Precipitation: ${Math.round(currentWeather.precipitation)}mm. Ensure proper drainage.`;
            default:
                return 'Weather conditions may affect your crops. Take appropriate precautions.';
        }
    }

    private async sendWeatherAlerts(
        alerts: WeatherAlert[],
        location: { latitude: number; longitude: number; name: string }
    ): Promise<void> {
        try {
            // Get users in the affected area
            const affectedUsers = await this.getUsersInArea(location);

            for (const user of affectedUsers) {
                for (const alert of alerts) {
                    // Create notification
                    const notification = NotificationTemplateService.createWeatherAlertNotification(alert);

                    // Send notification
                    await notificationService.sendLocalNotification(
                        notification.title,
                        notification.body,
                        notification.data,
                        {
                            priority: notification.priority,
                            sound: notification.sound,
                        }
                    );

                    // Store alert in database
                    await supabase
                        .from('weather_alerts')
                        .insert({
                            user_id: user.id,
                            alert_data: alert,
                            sent_at: new Date().toISOString(),
                        });
                }
            }
        } catch (error) {
            console.error('Failed to send weather alerts:', error);
        }
    }

    private async getUsersInArea(
        location: { latitude: number; longitude: number; name: string },
        radiusKm: number = 50
    ): Promise<Array<{ id: string }>> {
        try {
            // This would use PostGIS functions to find users within radius
            // For now, return all users as a placeholder
            const { data: users } = await supabase
                .from('user_profiles')
                .select('id')
                .not('farm_location', 'is', null);

            return users || [];
        } catch (error) {
            console.error('Failed to get users in area:', error);
            return [];
        }
    }

    async createCustomAlert(
        userId: string,
        alertType: WeatherAlert['alertType'],
        title: string,
        description: string,
        recommendations: string[],
        severity: WeatherAlert['severity'] = 'moderate'
    ): Promise<void> {
        try {
            const alert: WeatherAlert = {
                id: `custom_${Date.now()}`,
                location: { latitude: 0, longitude: 0, name: 'Custom Alert' },
                alertType,
                severity,
                title,
                description,
                startTime: new Date(),
                endTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
                recommendations,
            };

            const notification = NotificationTemplateService.createWeatherAlertNotification(alert);

            await notificationService.sendLocalNotification(
                notification.title,
                notification.body,
                notification.data,
                {
                    priority: notification.priority,
                    sound: notification.sound,
                }
            );

            // Store alert
            await supabase
                .from('weather_alerts')
                .insert({
                    user_id: userId,
                    alert_data: alert,
                    sent_at: new Date().toISOString(),
                });
        } catch (error) {
            console.error('Failed to create custom alert:', error);
            throw error;
        }
    }

    async getAlertHistory(userId: string, limit: number = 20): Promise<WeatherAlert[]> {
        try {
            const { data: alerts } = await supabase
                .from('weather_alerts')
                .select('alert_data')
                .eq('user_id', userId)
                .order('sent_at', { ascending: false })
                .limit(limit);

            return alerts?.map(alert => alert.alert_data) || [];
        } catch (error) {
            console.error('Failed to get alert history:', error);
            return [];
        }
    }
}

export const weatherAlertService = WeatherAlertService.getInstance();
export default weatherAlertService;