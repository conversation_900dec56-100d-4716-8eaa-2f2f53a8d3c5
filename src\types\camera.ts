export type CaptureMode = 'plant' | 'soil' | 'fertilizer';

export interface CameraSettings {
    flashMode: 'on' | 'off' | 'auto';
    focusMode: 'on' | 'off';
}

export interface CaptureGuideline {
    mode: CaptureMode;
    title: string;
    tips: string[];
    voiceInstructions: string;
}

export interface ImageAnalysisRequest {
    imageUri: string;
    mode: CaptureMode;
    location?: {
        latitude: number;
        longitude: number;
    };
}

export interface AnalysisProgress {
    percentage: number;
    message: string;
    voiceMessage: string;
}

export interface Issue {
    id: string;
    name: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    confidence: number;
    description: string;
}

export interface Recommendation {
    id: string;
    title: string;
    description: string;
    priority: 'low' | 'medium' | 'high';
    actionType: 'treatment' | 'prevention' | 'monitoring';
    estimatedCost?: number;
    productRecommendations?: string[];
}

export interface ImageAnalysisResult {
    id: string;
    imageUri: string;
    mode: CaptureMode;
    timestamp: Date;
    issues: Issue[];
    recommendations: Recommendation[];
    overallHealth: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
    confidence: number;
    summary: string;
}