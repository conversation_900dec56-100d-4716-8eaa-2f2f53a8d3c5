import React, { useEffect } from 'react';
import { ScrollView, View, Text, RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WeatherWidget, TodaysTasksSection, QuickActionsPanel } from '../../src/components/ui';
import { VoiceService } from '../../src/services/voice';
import { useState } from 'react';

export default function HomeScreen() {
    const [refreshing, setRefreshing] = useState(false);
    const voiceService = VoiceService.getInstance();

    useEffect(() => {
        // Initialize voice service
        voiceService.initialize();

        // Welcome message when screen loads
        const welcomeUser = async () => {
            await voiceService.speak('Welcome to your farming dashboard. Here you can view weather, manage tasks, and access quick actions.');
        };

        // Delay welcome message to avoid conflicts with navigation
        setTimeout(welcomeUser, 1000);
    }, []);

    const handleRefresh = async () => {
        setRefreshing(true);
        await voiceService.speak('Refreshing dashboard data');

        // Simulate refresh delay
        setTimeout(() => {
            setRefreshing(false);
        }, 2000);
    };

    const handleAnalyzePlant = () => {
        // TODO: Navigate to camera/analysis screen
        console.log('Navigate to plant analysis');
    };

    const handleVoiceCommand = () => {
        // TODO: Open voice command interface
        console.log('Open voice command interface');
    };

    const handleViewProgress = () => {
        // TODO: Navigate to progress/analytics screen
        console.log('Navigate to progress screen');
    };

    const handleViewAllTasks = () => {
        // TODO: Navigate to tasks screen
        console.log('Navigate to all tasks screen');
    };

    return (
        <SafeAreaView className="flex-1 bg-gray-50">
            <ScrollView
                className="flex-1"
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        tintColor="#22c55e"
                        colors={['#22c55e']}
                    />
                }
                showsVerticalScrollIndicator={false}
            >
                {/* Header */}
                <View className="px-4 pt-4 pb-2">
                    <Text className="text-2xl font-bold text-gray-900 mb-1">
                        Good Morning! 🌅
                    </Text>
                    <Text className="text-gray-600">
                        Let's check on your farm today
                    </Text>
                </View>

                {/* Weather Widget */}
                <WeatherWidget
                    onLocationPress={() => console.log('Location pressed')}
                    onWeatherPress={() => console.log('Weather pressed')}
                />

                {/* Today's Tasks Section */}
                <TodaysTasksSection
                    onViewAllTasks={handleViewAllTasks}
                />

                {/* Quick Actions Panel */}
                <QuickActionsPanel
                    onAnalyzePlant={handleAnalyzePlant}
                    onVoiceCommand={handleVoiceCommand}
                    onViewProgress={handleViewProgress}
                />

                {/* Bottom Spacing */}
                <View className="h-8" />
            </ScrollView>
        </SafeAreaView>
    );
}