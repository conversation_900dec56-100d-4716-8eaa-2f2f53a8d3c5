#!/usr/bin/env node

/**
 * Production Database Migration Script
 * This script applies all database migrations to the production Supabase instance
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.EXPO_PUBLIC_SUPABASE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables:');
  console.error('   - EXPO_PUBLIC_SUPABASE_URL');
  console.error('   - EXPO_PUBLIC_SUPABASE_ROLE_KEY');
  process.exit(1);
}

// Initialize Supabase client with service role key for admin operations
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

// Migration files in order
const migrationFiles = [
  '001_initial_schema.sql',
  '002_rls_policies.sql',
  '003_auth_setup.sql',
  '004_profile_functions.sql',
  '005_ecommerce_enhancement.sql',
  '006_sample_products.sql',
  '007_ecommerce_rls_policies.sql',
  '20240816000001_add_community_features.sql',
  '20241216000001_create_notification_tables.sql',
  '20241216000001_points_and_subscriptions.sql',
];

async function readMigrationFile(filename) {
  const filePath = path.join(__dirname, '..', 'supabase', 'migrations', filename);

  if (!fs.existsSync(filePath)) {
    console.warn(`⚠️  Migration file not found: ${filename}`);
    return null;
  }

  return fs.readFileSync(filePath, 'utf8');
}

async function executeSQLCommand(sql, description) {
  try {
    console.log(`🔄 Executing: ${description}`);

    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });

    if (error) {
      console.error(`❌ Error in ${description}:`, error.message);
      return false;
    }

    console.log(`✅ Successfully executed: ${description}`);
    return true;
  } catch (error) {
    console.error(`❌ Exception in ${description}:`, error.message);
    return false;
  }
}

async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...');

  try {
    // First create the exec_sql function if it doesn't exist
    const createFunctionSQL = `
      CREATE OR REPLACE FUNCTION exec_sql(sql_query text)
      RETURNS text
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $function$
      BEGIN
        EXECUTE sql_query;
        RETURN 'SUCCESS';
      EXCEPTION
        WHEN OTHERS THEN
          RETURN 'ERROR: ' || SQLERRM;
      END;
      $function$;
    `;

    // Try to create the function first
    await supabase.rpc('exec', { sql: createFunctionSQL });

    // Test with a simple query
    const { data, error } = await supabase.rpc('exec_sql', {
      sql_query: 'SELECT 1 as test;',
    });

    if (error) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }

    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection exception:', error.message);
    return false;
  }
}

async function checkTableExists(tableName) {
  try {
    const { data, error } = await supabase.rpc('exec_sql', {
      sql_query: `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_name = '${tableName}' 
        AND table_schema = 'public';
      `,
    });

    if (error) {
      console.error(`❌ Error checking table ${tableName}:`, error.message);
      return false;
    }

    return data && data.length > 0;
  } catch (error) {
    console.error(`❌ Exception checking table ${tableName}:`, error.message);
    return false;
  }
}

async function verifyMigrations() {
  console.log('\n🔍 Verifying migration results...');

  const expectedTables = [
    'users',
    'user_profiles',
    'crop_plans',
    'tasks',
    'chat_sessions',
    'chat_messages',
    'community_posts',
    'post_comments',
    'products',
    'orders',
    'order_items',
    'points_transactions',
    'points_balances',
    'achievements',
    'user_achievements',
    'subscription_plans',
    'user_subscriptions',
    'notifications',
    'push_tokens',
  ];

  let allTablesExist = true;

  for (const table of expectedTables) {
    const exists = await checkTableExists(table);
    if (exists) {
      console.log(`✅ Table exists: ${table}`);
    } else {
      console.log(`❌ Table missing: ${table}`);
      allTablesExist = false;
    }
  }

  return allTablesExist;
}

async function createExecSQLFunction() {
  console.log('🔧 Creating exec_sql function for migrations...');

  const createFunctionSQL = `
    CREATE OR REPLACE FUNCTION exec_sql(sql_query text)
    RETURNS text
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $function$
    BEGIN
      EXECUTE sql_query;
      RETURN 'SUCCESS';
    EXCEPTION
      WHEN OTHERS THEN
        RETURN 'ERROR: ' || SQLERRM;
    END;
    $function$;
  `;

  try {
    const { error } = await supabase.rpc('exec', { sql: createFunctionSQL });
    if (error) {
      console.error('❌ Failed to create exec_sql function:', error.message);
      return false;
    }
    console.log('✅ exec_sql function created successfully');
    return true;
  } catch (error) {
    console.error('❌ Exception creating exec_sql function:', error.message);
    return false;
  }
}

async function applyMigrations() {
  console.log('\n🚀 Starting production database migration...\n');

  // Test connection first
  const connectionOk = await testDatabaseConnection();
  if (!connectionOk) {
    console.error('❌ Cannot proceed without database connection');
    process.exit(1);
  }

  // Helper function is already created in testDatabaseConnection

  let successCount = 0;
  let failureCount = 0;

  for (const filename of migrationFiles) {
    console.log(`\n📄 Processing migration: ${filename}`);

    const sql = await readMigrationFile(filename);
    if (!sql) {
      console.log(`⏭️  Skipping missing file: ${filename}`);
      continue;
    }

    // Split SQL into individual statements
    const statements = sql
      .split(';')
      .map((stmt) => stmt.trim())
      .filter((stmt) => stmt.length > 0 && !stmt.startsWith('--'));

    let fileSuccess = true;

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.length === 0) continue;

      const success = await executeSQLCommand(
        statement + ';',
        `${filename} - Statement ${i + 1}/${statements.length}`
      );

      if (!success) {
        fileSuccess = false;
        // Continue with other statements in the file
      }
    }

    if (fileSuccess) {
      successCount++;
      console.log(`✅ Migration completed: ${filename}`);
    } else {
      failureCount++;
      console.log(`❌ Migration had errors: ${filename}`);
    }
  }

  console.log('\n📊 Migration Summary:');
  console.log(`   ✅ Successful migrations: ${successCount}`);
  console.log(`   ❌ Failed migrations: ${failureCount}`);

  // Verify the results
  const allTablesExist = await verifyMigrations();

  if (allTablesExist) {
    console.log('\n🎉 All migrations completed successfully!');
    console.log('✅ Database schema is ready for production use.');
  } else {
    console.log('\n⚠️  Some tables are missing. Please check the migration logs.');
  }

  return allTablesExist;
}

// Run the migration
if (require.main === module) {
  applyMigrations()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ Migration failed with exception:', error);
      process.exit(1);
    });
}

module.exports = { applyMigrations, testDatabaseConnection, verifyMigrations };
