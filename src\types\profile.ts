export interface GeoLocation {
    latitude: number;
    longitude: number;
}

export interface UserProfile {
    id: string;
    farm_location?: string | null; // PostGIS POINT format
    crop_types?: string[] | null;
    experience_level?: 'beginner' | 'intermediate' | 'expert' | null;
    preferred_language: string;
    voice_enabled: boolean;
    points: number;
    subscription_tier: string;
    subscription_expires_at?: string | null;
}

export interface ProfileUpdateData {
    farm_location?: GeoLocation;
    crop_types?: string[];
    experience_level?: 'beginner' | 'intermediate' | 'expert';
    preferred_language?: string;
    voice_enabled?: boolean;
}

export interface FarmLocation {
    latitude: number;
    longitude: number;
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
}

export interface CropType {
    id: string;
    name: string;
    category: 'vegetable' | 'fruit' | 'grain' | 'legume' | 'herb' | 'other';
    difficulty: 'beginner' | 'intermediate' | 'expert';
    season: 'spring' | 'summer' | 'fall' | 'winter' | 'year-round';
    description?: string;
}

export interface ExperienceLevel {
    level: 'beginner' | 'intermediate' | 'expert';
    label: string;
    description: string;
    yearsRange: string;
}

export interface SubscriptionTier {
    id: string;
    name: string;
    price: number;
    currency: string;
    duration: 'monthly' | 'yearly';
    features: string[];
    pointsIncluded: number;
    aiConsultationsLimit: number;
    priority: number;
}

export interface UserStats {
    total_points: number;
    completed_tasks: number;
    active_crop_plans: number;
    community_posts: number;
    days_active: number;
}

export interface NearbyFarmer {
    id: string;
    farm_location: string;
    crop_types: string[] | null;
    experience_level: 'beginner' | 'intermediate' | 'expert' | null;
    preferred_language: string;
    distance_km: number;
}

export interface CropRecommendation {
    crop_name: string;
    difficulty_level: 'beginner' | 'intermediate' | 'expert';
    season: string;
    description: string;
}

export interface ProfileImage {
    uri: string;
    type: string;
    name: string;
    size?: number;
}

export interface ProfileFormData {
    firstName: string;
    lastName: string;
    farmLocation?: FarmLocation;
    cropTypes: string[];
    experienceLevel: 'beginner' | 'intermediate' | 'expert';
    preferredLanguage: string;
    voiceEnabled: boolean;
}

export interface LocationPermission {
    granted: boolean;
    canAskAgain: boolean;
    status: 'granted' | 'denied' | 'undetermined';
}

export interface ProfileValidationErrors {
    firstName?: string;
    lastName?: string;
    farmLocation?: string;
    cropTypes?: string;
    experienceLevel?: string;
    preferredLanguage?: string;
}

// Constants
export const EXPERIENCE_LEVELS: ExperienceLevel[] = [
    {
        level: 'beginner',
        label: 'Beginner',
        description: 'New to farming or gardening',
        yearsRange: '0-2 years',
    },
    {
        level: 'intermediate',
        label: 'Intermediate',
        description: 'Some farming experience',
        yearsRange: '2-5 years',
    },
    {
        level: 'expert',
        label: 'Expert',
        description: 'Experienced farmer',
        yearsRange: '5+ years',
    },
];

export const COMMON_CROP_TYPES: CropType[] = [
    {
        id: 'tomatoes',
        name: 'Tomatoes',
        category: 'vegetable',
        difficulty: 'beginner',
        season: 'spring',
        description: 'Popular and versatile vegetable',
    },
    {
        id: 'corn',
        name: 'Corn',
        category: 'grain',
        difficulty: 'intermediate',
        season: 'summer',
        description: 'Staple grain crop',
    },
    {
        id: 'wheat',
        name: 'Wheat',
        category: 'grain',
        difficulty: 'intermediate',
        season: 'fall',
        description: 'Primary grain for bread production',
    },
    {
        id: 'lettuce',
        name: 'Lettuce',
        category: 'vegetable',
        difficulty: 'beginner',
        season: 'spring',
        description: 'Fast-growing leafy green',
    },
    {
        id: 'beans',
        name: 'Beans',
        category: 'legume',
        difficulty: 'beginner',
        season: 'summer',
        description: 'Nitrogen-fixing legume',
    },
    {
        id: 'potatoes',
        name: 'Potatoes',
        category: 'vegetable',
        difficulty: 'intermediate',
        season: 'spring',
        description: 'Versatile root vegetable',
    },
    {
        id: 'rice',
        name: 'Rice',
        category: 'grain',
        difficulty: 'expert',
        season: 'summer',
        description: 'Water-intensive grain crop',
    },
    {
        id: 'apples',
        name: 'Apples',
        category: 'fruit',
        difficulty: 'expert',
        season: 'year-round',
        description: 'Tree fruit requiring long-term care',
    },
];

export const SUBSCRIPTION_TIERS: SubscriptionTier[] = [
    {
        id: 'free',
        name: 'Free',
        price: 0,
        currency: 'USD',
        duration: 'monthly',
        features: [
            'Basic crop planning',
            '5 AI consultations per month',
            'Weather alerts',
            'Community access',
        ],
        pointsIncluded: 100,
        aiConsultationsLimit: 5,
        priority: 0,
    },
    {
        id: 'basic',
        name: 'Basic',
        price: 9.99,
        currency: 'USD',
        duration: 'monthly',
        features: [
            'Advanced crop planning',
            '25 AI consultations per month',
            'Image analysis',
            'Priority support',
            'Offline access',
        ],
        pointsIncluded: 500,
        aiConsultationsLimit: 25,
        priority: 1,
    },
    {
        id: 'premium',
        name: 'Premium',
        price: 19.99,
        currency: 'USD',
        duration: 'monthly',
        features: [
            'Unlimited AI consultations',
            'Advanced analytics',
            'Custom recommendations',
            'Expert support',
            'API access',
        ],
        pointsIncluded: 1000,
        aiConsultationsLimit: -1, // Unlimited
        priority: 2,
    },
    {
        id: 'pro',
        name: 'Pro',
        price: 49.99,
        currency: 'USD',
        duration: 'monthly',
        features: [
            'Everything in Premium',
            'Multi-farm management',
            'Team collaboration',
            'White-label options',
            'Priority development',
        ],
        pointsIncluded: 2500,
        aiConsultationsLimit: -1, // Unlimited
        priority: 3,
    },
];

export const SUPPORTED_LANGUAGES = [
    { code: 'en', name: 'English', nativeName: 'English' },
    { code: 'ar', name: 'Arabic', nativeName: 'العربية' },
    { code: 'es', name: 'Spanish', nativeName: 'Español' },
    { code: 'fr', name: 'French', nativeName: 'Français' },
    { code: 'hi', name: 'Hindi', nativeName: 'हिन्दी' },
    { code: 'pt', name: 'Portuguese', nativeName: 'Português' },
    { code: 'zh', name: 'Chinese', nativeName: '中文' },
];