import React, { useState } from 'react';
import { Pressable, Text, View, ActivityIndicator } from 'react-native';
import { touchTargets } from '../../design-system';
import { useAccessibility } from '../../hooks/useAccessibility';
import { FocusIndicator } from './FocusIndicator';
import { useFocusable } from '../../utils/keyboardNavigation';

export interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  className?: string;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  testID?: string;
  // Voice feedback support
  voiceFeedbackEnabled?: boolean;
  onVoiceFeedback?: (text: string) => void;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  className = '',
  accessibilityLabel,
  accessibilityHint,
  testID,
  voiceFeedbackEnabled = false,
  onVoiceFeedback,
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const {
    getScaledFontSize,
    getScaledTouchTarget,
    getHighContrastColors,
    isVoiceEnabled,
    speakText,
    getAccessibilityProps,
    isKeyboardNavigationEnabled,
  } = useAccessibility();

  const buttonId = `button-${title.replace(/\s+/g, '-').toLowerCase()}`;

  const { ref: focusRef } = useFocusable(buttonId, 0, {
    onFocus: () => setIsFocused(true),
    onBlur: () => setIsFocused(false),
    onActivate: handlePress,
    disabled: disabled || loading || !isKeyboardNavigationEnabled,
  });

  const highContrastColors = getHighContrastColors();
  const getVariantStyles = () => {
    if (highContrastColors) {
      switch (variant) {
        case 'primary':
          return { backgroundColor: highContrastColors.primary, borderColor: highContrastColors.primary };
        case 'secondary':
          return { backgroundColor: highContrastColors.secondary, borderColor: highContrastColors.secondary };
        case 'outline':
          return { backgroundColor: 'transparent', borderColor: highContrastColors.primary };
        case 'ghost':
          return { backgroundColor: 'transparent', borderColor: 'transparent' };
        case 'danger':
          return { backgroundColor: highContrastColors.error, borderColor: highContrastColors.error };
        default:
          return { backgroundColor: highContrastColors.primary, borderColor: highContrastColors.primary };
      }
    }

    switch (variant) {
      case 'primary':
        return 'bg-primary-600 border-primary-600';
      case 'secondary':
        return 'bg-secondary-500 border-secondary-500';
      case 'outline':
        return 'bg-transparent border-primary-600';
      case 'ghost':
        return 'bg-transparent border-transparent';
      case 'danger':
        return 'bg-red-600 border-red-600';
      default:
        return 'bg-primary-600 border-primary-600';
    }
  };

  const getSizeStyles = () => {
    const baseHeight = {
      small: touchTargets.minimum,
      medium: touchTargets.comfortable,
      large: touchTargets.large,
    }[size];

    const scaledHeight = getScaledTouchTarget(baseHeight);

    switch (size) {
      case 'small':
        return `px-4 py-2 min-h-[${scaledHeight}px]`;
      case 'medium':
        return `px-6 py-3 min-h-[${scaledHeight}px]`;
      case 'large':
        return `px-8 py-4 min-h-[${scaledHeight}px]`;
      default:
        return `px-6 py-3 min-h-[${scaledHeight}px]`;
    }
  };

  const getTextVariantStyles = () => {
    if (highContrastColors) {
      switch (variant) {
        case 'primary':
        case 'secondary':
        case 'danger':
          return { color: highContrastColors.background };
        case 'outline':
        case 'ghost':
          return { color: highContrastColors.primary };
        default:
          return { color: highContrastColors.background };
      }
    }

    switch (variant) {
      case 'primary':
      case 'secondary':
      case 'danger':
        return 'text-white';
      case 'outline':
        return 'text-primary-600';
      case 'ghost':
        return 'text-primary-600';
      default:
        return 'text-white';
    }
  };

  const getTextSizeStyles = () => {
    const baseSizes = {
      small: 14,
      medium: 16,
      large: 18,
    };

    const scaledSize = getScaledFontSize(baseSizes[size]);

    return {
      fontSize: scaledSize,
      fontWeight: size === 'small' ? '500' : '600',
    };
  };

  const isDisabled = disabled || loading;

  const handlePress = async () => {
    // Voice feedback
    if (voiceFeedbackEnabled && onVoiceFeedback) {
      onVoiceFeedback(`Button ${title} pressed`);
    } else if (isVoiceEnabled) {
      await speakText(`${title} button pressed`);
    }

    onPress();
  };

  const variantStyles = getVariantStyles();
  const textVariantStyles = getTextVariantStyles();
  const textSizeStyles = getTextSizeStyles();

  return (
    <FocusIndicator visible={isFocused && isKeyboardNavigationEnabled}>
      <Pressable
        ref={focusRef}
        onPress={handlePress}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        disabled={isDisabled}
        {...getAccessibilityProps(
          accessibilityLabel || title,
          accessibilityHint || `Tap to ${title.toLowerCase()}`,
          'button'
        )}
        accessibilityState={{ disabled: isDisabled }}
        testID={testID}
        style={[
          {
            borderWidth: 2,
            borderRadius: 12,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: isDisabled ? 0.5 : 1,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
          },
          typeof variantStyles === 'object' ? variantStyles : {},
          fullWidth && { width: '100%' },
        ]}
        className={`
          ${typeof variantStyles === 'string' ? variantStyles : ''}
          ${getSizeStyles()}
          ${fullWidth ? 'w-full' : ''}
          flex-row items-center
          justify-center rounded-xl border-2
          ${isDisabled ? 'opacity-50' : 'active:opacity-80'}
          shadow-md
          ${className}
        `}>
        {loading ? (
          <ActivityIndicator
            size="small"
            color={
              highContrastColors
                ? (variant === 'outline' || variant === 'ghost' ? highContrastColors.primary : highContrastColors.background)
                : (variant === 'outline' || variant === 'ghost' ? '#16a34a' : 'white')
            }
          />
        ) : (
          <View className="flex-row items-center justify-center gap-2">
            {icon && iconPosition === 'left' && icon}
            <Text
              style={[
                typeof textVariantStyles === 'object' ? textVariantStyles : {},
                textSizeStyles,
              ]}
              className={typeof textVariantStyles === 'string' ? textVariantStyles : ''}
            >
              {title}
            </Text>
            {icon && iconPosition === 'right' && icon}
          </View>
        )}
      </Pressable>
    </FocusIndicator>
  );
};
