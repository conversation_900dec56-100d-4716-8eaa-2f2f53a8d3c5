import React from 'react';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { CameraScreen } from '../src/components/camera';
import { CaptureMode, ImageAnalysisResult } from '../src/types/camera';

export default function CameraPage() {
    const router = useRouter();
    const params = useLocalSearchParams();

    // Get initial mode from params, default to 'plant'
    const initialMode = (params.mode as CaptureMode) || 'plant';

    const handleClose = () => {
        router.back();
    };

    const handleSaveResult = (result: ImageAnalysisResult) => {
        // TODO: Implement saving to database or local storage
        console.log('Saving result:', result);
    };

    const handleBuyTreatment = (recommendations: any[]) => {
        // TODO: Navigate to store with recommended products
        console.log('Buy treatment for:', recommendations);
        router.push('/store');
    };

    return (
        <CameraScreen
            initialMode={initialMode}
            onClose={handleClose}
            onSaveResult={handleSaveResult}
            onBuyTreatment={handleBuyTreatment}
        />
    );
}