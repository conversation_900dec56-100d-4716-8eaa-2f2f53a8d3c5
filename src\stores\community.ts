import { create } from 'zustand';
import { CommunityService, PostWithAuthor, CommentWithAuthor } from '../services/supabase/community';

export interface CommunityPost {
    id: string;
    title: string;
    content: string;
    author: {
        id: string;
        name: string;
        avatar?: string;
        location?: string;
        experienceLevel?: 'beginner' | 'intermediate' | 'expert';
    };
    images?: string[];
    location?: {
        latitude: number;
        longitude: number;
        address?: string;
    };
    likes: number;
    comments: number;
    shares: number;
    isLiked: boolean;
    createdAt: Date;
    updatedAt: Date;
}

export interface Comment {
    id: string;
    postId: string;
    author: {
        id: string;
        name: string;
        avatar?: string;
    };
    content: string;
    likes: number;
    isLiked: boolean;
    replies?: Comment[];
    createdAt: Date;
}

export interface CreatePostData {
    title: string;
    content: string;
    images?: string[];
    location?: {
        latitude: number;
        longitude: number;
    };
}

export interface CommunityStore {
    // Posts
    posts: CommunityPost[];
    loading: boolean;
    error: string | null;

    // Comments
    comments: { [postId: string]: Comment[] };

    // Post Management
    fetchPosts: (filter?: 'all' | 'local' | 'following') => Promise<void>;
    createPost: (data: CreatePostData) => Promise<void>;
    updatePost: (postId: string, updates: { title?: string; content?: string; images?: string[] }) => Promise<void>;
    deletePost: (postId: string) => Promise<void>;

    // Engagement
    likePost: (postId: string) => Promise<void>;
    sharePost: (postId: string) => Promise<void>;
    reportPost: (postId: string, reason: string, description?: string) => Promise<void>;

    // Comments
    fetchComments: (postId: string) => Promise<void>;
    addComment: (postId: string, content: string, parentId?: string) => Promise<void>;
    likeComment: (commentId: string) => Promise<void>;

    // Utility
    clearError: () => void;
}

// Mock data for development
const mockPosts: CommunityPost[] = [
    {
        id: '1',
        title: 'Great harvest this season! 🌽',
        content: 'Just finished harvesting my corn field. The yield was amazing this year thanks to the new irrigation system. Anyone else having a good season?',
        author: {
            id: 'user1',
            name: 'Ahmed Hassan',
            location: 'Cairo, Egypt',
            experienceLevel: 'intermediate',
        },
        images: [
            'https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=400',
            'https://images.unsplash.com/photo-1625246333195-78d9c38ad449?w=400',
        ],
        location: {
            latitude: 30.0444,
            longitude: 31.2357,
            address: 'Cairo, Egypt',
        },
        likes: 24,
        comments: 8,
        shares: 3,
        isLiked: false,
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
    },
    {
        id: '2',
        title: 'Need help with tomato disease',
        content: 'My tomato plants are showing yellow spots on the leaves. Has anyone dealt with this before? I\'m worried it might spread to the whole crop.',
        author: {
            id: 'user2',
            name: 'Fatima Al-Zahra',
            location: 'Alexandria, Egypt',
            experienceLevel: 'beginner',
        },
        images: [
            'https://images.unsplash.com/photo-1592841200221-21e1c0d36875?w=400',
        ],
        likes: 12,
        comments: 15,
        shares: 2,
        isLiked: true,
        createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
        updatedAt: new Date(Date.now() - 5 * 60 * 60 * 1000),
    },
    {
        id: '3',
        title: 'Sharing my organic fertilizer recipe',
        content: 'After years of experimenting, I\'ve perfected my organic fertilizer mix. It\'s made from kitchen scraps, grass clippings, and a secret ingredient. Happy to share the recipe!',
        author: {
            id: 'user3',
            name: 'Mohamed Salah',
            location: 'Giza, Egypt',
            experienceLevel: 'expert',
        },
        likes: 45,
        comments: 23,
        shares: 12,
        isLiked: false,
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
        updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    },
];

const mockComments: { [postId: string]: Comment[] } = {
    '1': [
        {
            id: 'c1',
            postId: '1',
            author: {
                id: 'user4',
                name: 'Sara Ahmed',
            },
            content: 'Congratulations! What irrigation system did you use?',
            likes: 3,
            isLiked: false,
            createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
        },
        {
            id: 'c2',
            postId: '1',
            author: {
                id: 'user5',
                name: 'Omar Khaled',
            },
            content: 'Amazing results! I\'m planning to upgrade my irrigation too.',
            likes: 1,
            isLiked: true,
            createdAt: new Date(Date.now() - 30 * 60 * 1000),
        },
    ],
    '2': [
        {
            id: 'c3',
            postId: '2',
            author: {
                id: 'user6',
                name: 'Dr. Amina Hassan',
            },
            content: 'This looks like early blight. You should remove affected leaves and apply copper fungicide.',
            likes: 8,
            isLiked: false,
            createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000),
        },
    ],
};

// Helper function to transform API data to store format
const transformPostData = (apiPost: PostWithAuthor): CommunityPost => ({
    id: apiPost.id,
    title: apiPost.title,
    content: apiPost.content || '',
    author: apiPost.author,
    images: apiPost.image_urls || undefined,
    location: apiPost.location ? {
        // Parse PostGIS POINT format
        latitude: 0, // Would need to parse from PostGIS format
        longitude: 0,
        address: undefined,
    } : undefined,
    likes: apiPost.likes_count,
    comments: apiPost.comments_count,
    shares: 0, // Would come from post_shares table
    isLiked: false, // Would need to check post_likes table
    createdAt: new Date(apiPost.created_at),
    updatedAt: new Date(apiPost.created_at),
});

const transformCommentData = (apiComment: CommentWithAuthor): Comment => ({
    id: apiComment.id,
    postId: apiComment.post_id,
    author: apiComment.author,
    content: apiComment.content,
    likes: 0, // Would come from comment_likes table
    isLiked: false, // Would need to check comment_likes table
    createdAt: new Date(apiComment.created_at),
});

export const useCommunityStore = create<CommunityStore>((set, get) => ({
    posts: [],
    loading: false,
    error: null,
    comments: {},

    fetchPosts: async (filter = 'all') => {
        set({ loading: true, error: null });

        try {
            const filters: any = {};

            if (filter === 'local') {
                // Would need user's location for proximity filtering
                filters.location = {
                    latitude: 30.0444, // Cairo coordinates as example
                    longitude: 31.2357,
                    radius: 50, // 50km radius
                };
            } else if (filter === 'following') {
                // Would need to get user's following list
                filters.following = []; // Empty for now
            }

            const { posts: apiPosts, error } = await CommunityService.fetchPosts(filters);

            if (error) {
                set({ error, loading: false });
                return;
            }

            const transformedPosts = apiPosts.map(transformPostData);
            set({ posts: transformedPosts, loading: false });
        } catch (error) {
            set({ error: 'Failed to fetch posts', loading: false });
        }
    },

    createPost: async (data) => {
        set({ loading: true, error: null });

        try {
            const { post, error } = await CommunityService.createPost(data);

            if (error) {
                set({ error, loading: false });
                return;
            }

            if (post) {
                const transformedPost = transformPostData(post);
                set(state => ({
                    posts: [transformedPost, ...state.posts],
                    loading: false,
                }));
            }
        } catch (error) {
            set({ error: 'Failed to create post', loading: false });
        }
    },

    updatePost: async (postId, updates) => {
        set({ loading: true, error: null });

        try {
            const { post, error } = await CommunityService.updatePost(postId, updates);

            if (error) {
                set({ error, loading: false });
                return;
            }

            if (post) {
                const transformedPost = transformPostData(post);
                set(state => ({
                    posts: state.posts.map(p => p.id === postId ? transformedPost : p),
                    loading: false,
                }));
            }
        } catch (error) {
            set({ error: 'Failed to update post', loading: false });
        }
    },

    deletePost: async (postId) => {
        set({ loading: true, error: null });

        try {
            const { success, error } = await CommunityService.deletePost(postId);

            if (error) {
                set({ error, loading: false });
                return;
            }

            if (success) {
                set(state => ({
                    posts: state.posts.filter(p => p.id !== postId),
                    loading: false,
                }));
            }
        } catch (error) {
            set({ error: 'Failed to delete post', loading: false });
        }
    },

    likePost: async (postId) => {
        try {
            // Optimistic update
            set(state => ({
                posts: state.posts.map(post =>
                    post.id === postId
                        ? {
                            ...post,
                            isLiked: !post.isLiked,
                            likes: post.isLiked ? post.likes - 1 : post.likes + 1,
                        }
                        : post
                ),
            }));

            const { liked, likesCount, error } = await CommunityService.togglePostLike(postId);

            if (error) {
                // Revert optimistic update
                set(state => ({
                    posts: state.posts.map(post =>
                        post.id === postId
                            ? {
                                ...post,
                                isLiked: !post.isLiked,
                                likes: post.isLiked ? post.likes + 1 : post.likes - 1,
                            }
                            : post
                    ),
                    error,
                }));
                return;
            }

            // Update with actual values from server
            set(state => ({
                posts: state.posts.map(post =>
                    post.id === postId
                        ? { ...post, isLiked: liked, likes: likesCount }
                        : post
                ),
            }));
        } catch (error) {
            set({ error: 'Failed to like post' });
        }
    },

    sharePost: async (postId) => {
        try {
            // Optimistic update
            set(state => ({
                posts: state.posts.map(post =>
                    post.id === postId
                        ? { ...post, shares: post.shares + 1 }
                        : post
                ),
            }));

            // For now, just simulate the API call
            // In a real implementation, this would track shares
            await new Promise(resolve => setTimeout(resolve, 500));
        } catch (error) {
            // Revert on error
            set(state => ({
                posts: state.posts.map(post =>
                    post.id === postId
                        ? { ...post, shares: post.shares - 1 }
                        : post
                ),
                error: 'Failed to share post',
            }));
        }
    },

    reportPost: async (postId, reason, description) => {
        try {
            const { success, error } = await CommunityService.reportPost(postId, reason, description);

            if (error) {
                set({ error });
                return;
            }

            if (success) {
                // Could show a success message or remove the post from view
                set({ error: null });
            }
        } catch (error) {
            set({ error: 'Failed to report post' });
        }
    },

    fetchComments: async (postId) => {
        try {
            const { comments: apiComments, error } = await CommunityService.fetchComments(postId);

            if (error) {
                set({ error });
                return;
            }

            const transformedComments = apiComments.map(transformCommentData);

            set(state => ({
                comments: {
                    ...state.comments,
                    [postId]: transformedComments,
                },
            }));
        } catch (error) {
            set({ error: 'Failed to fetch comments' });
        }
    },

    addComment: async (postId, content, parentId) => {
        try {
            const { comment, error } = await CommunityService.addComment(postId, content);

            if (error) {
                set({ error });
                return;
            }

            if (comment) {
                const transformedComment = transformCommentData(comment);

                set(state => ({
                    comments: {
                        ...state.comments,
                        [postId]: [...(state.comments[postId] || []), transformedComment],
                    },
                    posts: state.posts.map(post =>
                        post.id === postId
                            ? { ...post, comments: post.comments + 1 }
                            : post
                    ),
                }));
            }
        } catch (error) {
            set({ error: 'Failed to add comment' });
        }
    },

    likeComment: async (commentId) => {
        try {
            // For now, just optimistic update
            // In a real implementation, this would call a comment like service
            set(state => {
                const newComments = { ...state.comments };
                Object.keys(newComments).forEach(postId => {
                    newComments[postId] = newComments[postId].map(comment =>
                        comment.id === commentId
                            ? {
                                ...comment,
                                isLiked: !comment.isLiked,
                                likes: comment.isLiked ? comment.likes - 1 : comment.likes + 1,
                            }
                            : comment
                    );
                });
                return { comments: newComments };
            });

            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 500));
        } catch (error) {
            set({ error: 'Failed to like comment' });
        }
    },

    clearError: () => set({ error: null }),
}));